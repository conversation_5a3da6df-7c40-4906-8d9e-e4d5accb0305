﻿using DeviceId;
using DeviceId.Components;
using HX.Experiment.Shared.Model;
using HX.Experiment.Shared.Services;
using HX.Experiment.Window;
using HX.Experiment.Window.Services;
using Microsoft.Extensions.DependencyInjection;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;

public class DeviceService : IDeviceService
{
    private readonly StateService _stateService;
    private readonly UDiskService _UDiskService;

    public event Action<List<DiskInfo>> OnUDiskAfterInsertedEvent;

    public DeviceService(StateService state)
    {
        _stateService = state;
        _UDiskService = MainApp.ServiceProvider.GetRequiredService<UDiskService>();
        _UDiskService.OnUDiskAfterInsertedEvent += OnDeviceInserted;
        ComputerId = new DeviceIdBuilder()
                .AddMachineName()
                .AddMacAddress()
                .ToString();
    }
    public DeviceModel CurrentDevice { get; set; }
    public DiskInfo CurrentLinkedDevice => _UDiskService.DiskList.FirstOrDefault();

    public string ComputerId { get; set; }



    /// <summary>
    ///  刷新已连接设备
    /// </summary>
    public async Task RefreshLinkedDeviceAsync()
    {

    }

    public void InitDevice()
    {

    }

    public void ClearData(string deviceSN)
    {
       
    }

    public void OnDeviceInserted(List<DiskInfo> diskInfos)
    {
        OnUDiskAfterInsertedEvent?.Invoke(diskInfos);
        foreach (var diskInfo in diskInfos)
        {
            _=AddDevice(diskInfo);
        }
    }


	private const string DeviceTypeId = "2407160100000001";
    public async Task AddDevice(DiskInfo diskInfo)
    {
        DeviceModel newDevice = new DeviceModel()
        {
            DeviceSN = diskInfo.ShowName,
            ChipSN = diskInfo.ShowName,
            TypeId = DeviceTypeId,
        };

        var isExist = await _stateService.PostAsJsonAsync<DataModel<DeviceModel>>("/api/Core/FormData/Get", newDevice);
        if (isExist?.Data?.Data != null)
        {
            return;
        }
        var result = await _stateService.PostAsJsonAsync<DataModel<DeviceModel>>("api/v2/IoT/Devices/Add", newDevice);
        var device = result?.Data?.Data;
    }
    


}