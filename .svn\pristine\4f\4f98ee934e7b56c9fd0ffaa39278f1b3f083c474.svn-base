﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<!--<Nullable>enable</Nullable>-->
		<ImplicitUsings>enable</ImplicitUsings>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
		<DefaultAppHostRuntimeIdentifier>win-x64</DefaultAppHostRuntimeIdentifier>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
	  <PackageReference Include="Makaretu.Dns.Multicast" Version="0.27.0" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.35" />
	  <PackageReference Include="Microsoft.Fast.Components.FluentUI" Version="3.7.8" />
	  <PackageReference Include="MQTTnet" Version="4.3.3.952" />
	  <PackageReference Include="PDFsharp" Version="6.1.1" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="wwwroot\css\hx.hrv.css">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Include="..\.dockerignore">
	    <Link>.dockerignore</Link>
	    <DependentUpon>Dockerfile</DependentUpon>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <_ContentIncludedByDefault Remove="Pages\Client\Account\List.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Components\HRVCheckTimeButton.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Components\HRVHeader.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Components\HRVSourceGroupButton.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatus.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusCard.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusCardContent.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusCardHead.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusUserInfoCard.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\DeviceStatusDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\ExportRecordDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\ModifyPasswordDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\PatientDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\SelectedDeviceDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\PatientRecord\List.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\PatientRecord\RecordList.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Patient\List.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoring.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringCard.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringCardHead.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringLargeLineChart.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringLineChart.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Report\PreviewReportDetail.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Report\ReportDetail.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Report\ShowReportDetail.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\SystemConfig\DeviceList.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\SystemConfig\HxSystemConfig.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\TestAlgorithms.razor" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\HX.Base\HX.Base.Shared\HX.Base.Shared.csproj" />
	  <ProjectReference Include="..\HX.HRV.Shared\HX.HRV.Shared.csproj" />
	  <ProjectReference Include="..\UFU.IoT.HRV\UFU.IoT\UFU.IoT.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="Dockerfile">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>
