using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using HX.Experiment.Shared.Model;
using Masa.Blazor;
using Microsoft.AspNetCore.Components;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace HX.Experiment.Shared.Pages;

public partial class ClassManager : ComponentBase
{
    private DataOptions _options = new(1, 5);

    private async Task HandleOnOptionsUpdate(DataOptions options)
    {
        _options = options;
        await InitStudentsAsync();
    }

    private List<DataModel<StudentInfo>> Students = new();


    protected override async Task OnInitializedAsync()
    {
        await InitClassAsync();

        await base.OnInitializedAsync();
    }

    private bool showEditStudentDialog = false;

    private DataModel<StudentInfo> editStudent = new();


    private List<DataModel<ClassInfo>> classes = new()
    {
    };


    private DataModel<ClassInfo>? EditClass = new()
    {
        Data = new ClassInfo()
    };

    private DataModel<ClassInfo>? selectedClass;
    private bool showEditClassDialog = false;

    private async Task SwicthClass(DataModel<ClassInfo> classItem)
    {
        selectedClass = classItem;
        await InitStudentsAsync();
    }

    private void ShowCreateClassDialog()
    {
        EditClass = new()
        {
            Data = new ClassInfo()
        };
        showEditClassDialog = true;
    }


    private void HandleAddStudentOnClick()
    {
        showEditStudentDialog = true;
        editStudent = new()
        {
            Data = new StudentInfo()
            {
                ClassId = selectedClass.Id
            }
        };
    }

    private void HandleEditStudentOnClick(DataModel<StudentInfo> student)
    {
        showEditStudentDialog = true;
        editStudent = student;
    }

    private async Task HandleEditClass()
    {
        var classJson = JsonTool.Serialize(EditClass.Data);
        var dataJson = JsonDocument.Parse(classJson);
        var data = new DataModel<JsonDocument>
        {
            FormId = _classFormId,
            OrganId = state.Permissions?.User?.Organ?.Id ?? "01",
            UserId = state.Permissions?.User?.Id ?? "01",
            Data = dataJson
        };
        var httpContent = JsonContent.Create(data);
        HttpResponseMessage res;
        if (!string.IsNullOrEmpty(EditClass.Id))
        {
            var url = "/api/Core/FormData/Edit";
            res = await state.PostAsync(url, httpContent,
                new Dictionary<string, string> { { "formId", _classFormId } });
        }
        else
        {
            var url = "/api/Core/FormData/Add";
            res = await state.PostAsync(url, httpContent,
                new Dictionary<string, string> { { "formId", _classFormId } });
        }

        if (res.IsSuccessStatusCode)
        {
            await InitClassAsync();
            await PopupService.EnqueueSnackbarAsync("操作成功", AlertTypes.Success);
            showEditClassDialog = false;
        }
        else
        {
            await PopupService.EnqueueSnackbarAsync("操作失败", AlertTypes.Error);
        }
    }

    private void HandleEditClassClick(DataModel<ClassInfo> classItem)
    {
        EditClass = classItem;
        showEditClassDialog = true;
    }

    private async Task DeleteFormDataById(string id, string formId)
    {
        var url = "/api/Core/FormData/Delete";
        var res = await state.PostAsJsonAsync<object>(url, new List<string> { id }, new Dictionary<string, string>
        {
            {
                "formId",
                formId
            }
        });
        var msg = res.Success ? "删除成功" : "删除失败";
        await PopupService
            .EnqueueSnackbarAsync(msg, res.Success ? AlertTypes.Success : AlertTypes.Error);
    }


    private async Task InitClassAsync()
    {
        var url = "/api/Core/FormData/List";
        var res = await state.GetAsync(url, new Dictionary<string, string>()
        {
            { "formId", _classFormId },
        });
        if (res.StatusCode == HttpStatusCode.OK)
        {
            var content = await res.Content.ReadAsStringAsync();
            var data = JsonTool.Deserialize<List<DataModel<ClassInfo>>>(content);
            classes = data;
            if (classes.Count > 0)
            {
                selectedClass = classes.FirstOrDefault();
                await InitStudentsAsync();
            }
        }
    }


    private int _total;
    private int _itemsPerPage = 5;
    private int _page = 1;


    private async Task InitStudentsAsync()
    {
        var url = "api/v2/HX_Experiment/StudentInfo/List";
        var res = await state.GetAsJsonAsync<List<DataModel<StudentInfo>>>(url, new Dictionary<string, string>()
        {
            { nameof(StudentInfo.ClassId), selectedClass?.Id ?? string.Empty },
            { "page", _options.Page.ToString() },
            { "pageSize", _options.ItemsPerPage.ToString() }
        });
        if (res.Success)
        {
            Students = res.Data;
            _total =res.Page?.TotalCount??0;
            _page= res.Page?.Page??1;
        }
    }

    private void ImportStudents()
    {
    }

    private readonly string _studentFormId = "2507280500000001";
    private readonly string _classFormId = "2507280300000002";


    private async Task HandleEditStudent()
    {
        var studentJson = JsonTool.Serialize(editStudent.Data);
        var dataJson = JsonDocument.Parse(studentJson);
        var data = new DataModel<JsonDocument>
        {
            FormId = _studentFormId,
            OrganId = state.Permissions?.User?.Organ?.Id ?? "01",
            UserId = state.Permissions?.User?.Id ?? "01",
            Data = dataJson
        };
        var httpContent = JsonContent.Create(data);
        HttpResponseMessage res;
        if (!string.IsNullOrEmpty(editStudent.Id))
        {
            var url = "/api/Core/FormData/Edit";
            res = await state.PostAsync(url, httpContent,
                new Dictionary<string, string> { { "formId", _studentFormId } });
        }
        else
        {
            var url = "/api/Core/FormData/Add";
            res = await state.PostAsync(url, httpContent,
                new Dictionary<string, string> { { "formId", _studentFormId } });
        }

        if (res.IsSuccessStatusCode)
        {
            await InitClassAsync();
            await PopupService.EnqueueSnackbarAsync("操作成功", AlertTypes.Success);
            showEditClassDialog = false;
        }
        else
        {
            await PopupService.EnqueueSnackbarAsync("操作失败", AlertTypes.Error);
        }
    }
}