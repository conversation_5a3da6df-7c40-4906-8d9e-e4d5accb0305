using  HX.HRV.Shared;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using System.Reflection;
using UFU.CoreFX.Shared;

namespace HX.HRV.SCI.Shared
{
	public class Program
	{
		public static async Task Main(string[] args)
		{
			// var builder = WebAssemblyHostBuilder.CreateDefault(args);
			// builder.RootComponents.Add<App>("#app");
			// builder.RootComponents.Add<HeadOutlet>("head::after");
			// builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });
			// builder.Services.AddMasaBlazor();
			// await builder.Build().RunAsync();
			Assembly.Load("HX.HRV.Shared");
			var as1= typeof(HX.HRV.Shared.Module);
			await new HXBlazorApp().StartAsync(args);
		}

		private class HXBlazorApp : BlazorApp
		{
			public new async Task StartAsync(string[] args)
			{
				var builder = WebAssemblyHostBuilder.CreateDefault(args);
				Init(builder);
				builder.Services.AddSingleton(builder.Configuration);
				ConfigureServices(builder.Services);
				var app = builder.Build();
				ConfigureApplication(app);
				await app.RunAsync();
			}
		}
	}
}
