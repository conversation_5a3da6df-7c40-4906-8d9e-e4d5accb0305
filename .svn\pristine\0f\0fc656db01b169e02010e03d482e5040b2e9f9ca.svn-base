﻿using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using AntDesign;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;
using FormItem = AntDesign.FormItem;

namespace UFU.IoT.Shared.Pages.Device;

public partial class RealTimeData : ComponentBase
{
    [Parameter] [FromRoute] public string DeviceId { get; set; }
    private static readonly string BaseUrl = "/api/v2.0/IoT/DataItems/";
    private List<DataItem> _dataItems = new();
    private JsonNode DataJson { get; set; }
    private JsonObject TemplateDataJson { get; set; } = new JsonObject();
    [Inject] private StateService StateService { get; set; }
    private Dictionary<string, List<DataItem>> _groupData = new();
    private readonly ClientWebSocket _clientWebSocket = new ClientWebSocket();

    [Parameter]
    [SupplyParameterFromQuery(Name = "typeId")]
    public string TypeId { get; set; }

    private DeviceModel DeviceModel { get; set; }
    private const string _defaultKey = @"未分组";
    private readonly List<DataItem> _checkedDataItems = new();

    private async Task InitDataListAsync()
    {
        var res = await StateService.GetAsJsonAsync<List<DataItem>>(BaseUrl + "List", new Dictionary<string, string>()
        {
            { "typeId", TypeId },
        });
        if (res.Success)
        {
            _dataItems = res.Data;
            _dataItems.AddRange(DataItemDefaultData.DefaultData);
            var group = _dataItems.GroupBy(m => m.GroupName ?? string.Empty, m => m);
            _groupData = group
                .ToDictionary(m => string.IsNullOrEmpty(m.Key) ? "未分组" : m.Key.Trim(), m => m.ToList());
        }
    }

    private async Task InitDeviceModelAsync()
    {
        var res = await StateService.GetAsJsonAsync<DataModel<DeviceModel>>($"/api/v2/IoT/Devices/Get/{DeviceId}");
        if (res.Success)
        {
            DeviceModel = res.Data?.Data;
            LatestData = res.Data?.Data?.LatestData;
        }
    }

    public LatestDataModel LatestData { get; set; }

    protected override async Task OnInitializedAsync()
    {
        DataJson = new JsonObject();
        await InitDataListAsync();
        await ConnectWebSocket();
        await InitDeviceModelAsync();
        await base.OnInitializedAsync();
    }

    private async Task ConnectWebSocket()
    {
        if (_clientWebSocket.State != WebSocketState.Open)
        {
            await _clientWebSocket.ConnectAsync(
                new Uri($"ws://192.168.0.104:5001/iot/admin/v1?isNew=true&deviceId={DeviceId}&token={StateService.Token}"),
                CancellationToken.None);
        }

        _ = Task.Run(ReceiveMessagesAsync);
    }
	// Receive Messages
	private async Task ReceiveMessagesAsync()
	{
		try
		{
			var receiveBuffer = new byte[2048];
			while (_clientWebSocket.State == WebSocketState.Open)
			{
				var receiveResult =
					await _clientWebSocket.ReceiveAsync(new ArraySegment<byte>(receiveBuffer), CancellationToken.None);
				if (receiveResult.MessageType == WebSocketMessageType.Close)
				{
					await _clientWebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, string.Empty,
						CancellationToken.None);
				}
				else
				{
					var receivedMessage = Encoding.UTF8.GetString(receiveBuffer, 0, receiveResult.Count);
					var json = JsonNode.Parse(receivedMessage);
					var newRealDic = JsonToNestedDictionary(receivedMessage);
					RealTimeDictionaries.Add(newRealDic);
					SourceReceiveDataJson.Add(newRealDic, json);
					await InvokeAsync(StateHasChanged);
				}
			}
		}
		catch (Exception e)
		{
			await Message.Error("接收数据失败：" + e.Message);
		}
	}


	//  根据数据项，渲染对应的组件
	private RenderFragment RenderInput(DataItem field, string groupKey) => builder =>
    {
        var index = 0;
        // 复选框
        void HandleCheckChange(bool isChecked)
        {
            if (isChecked)
            {
                _checkedDataItems.Add(field);
            }
            else
            {
                _checkedDataItems.Remove(field);
            }
        }
        void HandleChange<T>(T value)
        {
            if (groupKey != _defaultKey)
            {
                DataJson[groupKey] ??= new JsonObject();
                if (value is bool bl)
                {
                    DataJson[groupKey][field.Key] = JsonValue.Create(bl?1:0);
                    return;
                }
                DataJson[groupKey][field.Key] = JsonValue.Create(value);
            }
            else
            {
                if (value is bool bl)
                {
                    DataJson[field.Key]= JsonValue.Create(bl?1:0);
                    return;
                }
                DataJson[field.Key] = JsonValue.Create(value);
            }
        }
        builder.OpenComponent<FormItem>(index++);
        builder.AddAttribute(index++, "LabelAlign", AntLabelAlignType.Right);
        builder.AddAttribute(index++, "LabelTemplate", (RenderFragment)(treeBuilder =>
        {
            treeBuilder.OpenComponent<Checkbox>(index++);
            treeBuilder.AddAttribute(index++, "OnChange", EventCallback.Factory.Create<bool>(this, HandleCheckChange));
            treeBuilder.AddAttribute(index++, "Checked", _checkedDataItems.Any(checkDataItem =>
            {
                if (string.IsNullOrEmpty(checkDataItem.GroupName)||checkDataItem.GroupName == _defaultKey)
                {
                    return checkDataItem.Key == field.Key;
                }
                return checkDataItem.GroupName == groupKey && checkDataItem.Key == field.Key;
            }));
            treeBuilder.AddAttribute(index++, "ChildContent",
                (RenderFragment)(renderTreeBuilder => renderTreeBuilder.AddContent(index++, field.Key)));
            treeBuilder.CloseComponent();
        }));
        builder.AddAttribute(index++, "ChildContent", (RenderFragment)(treeBuilder =>
        {

            JsonNode jsonNode = null;
            if (groupKey == _defaultKey||string.IsNullOrEmpty(groupKey))
            {
                jsonNode = DataJson[field.Key];
            }
            else
            {
                jsonNode= DataJson[groupKey]?[field.Key];
            }
         
            object PraseValue<T>(object value)
            {
                if (value == null)
                    return null;
                object jsonValue=null;
                try
                {
                    if (value is JsonElement)
                    {
                        var node = JsonNode.Parse(((JsonElement)value).GetRawText());
                        jsonValue = node.GetValue<T>();
                    }
                    else if (value is JsonValue)
                    {
                        jsonValue = ((JsonValue)value).GetValue<T>();
                    }
                    else
                    {
                        jsonValue = value;
                    }
                }
                catch (Exception e)
                {
                    return default;
                }
                return jsonValue;
            }
        
            switch (field.DataType)
            {
                case DataItemType.Int:
                    if (field.DataLength == 64)
                    {
                        treeBuilder.OpenComponent<Input<long>>(index++);
                        treeBuilder.AddAttribute(index++, "OnChange",
                            EventCallback.Factory.Create<long>(this, HandleChange));
                    }
                    else
                    {
                        treeBuilder.OpenComponent<Input<int>>(index++);
                        treeBuilder.AddAttribute(index++, "OnChange",
                            EventCallback.Factory.Create<int>(this, HandleChange));
                    }
             
                    if (jsonNode != null)
                    {
                        treeBuilder.AddAttribute(index++, "Value",
                            field.DataLength == 64 ? PraseValue<long>(jsonNode) : PraseValue<int>(jsonNode));
                    }
                    treeBuilder.CloseComponent();
                    break;
                case DataItemType.Longitude:
                case DataItemType.Latitude:
                case DataItemType.Float:
                    treeBuilder.OpenComponent<Input<float>>(index++);
                    treeBuilder.AddAttribute(index++, "OnChange",
                        EventCallback.Factory.Create<float>(this, HandleChange));
                    if (jsonNode != null)
                    {
                        treeBuilder.AddAttribute(index++,"Value", PraseValue<float>(jsonNode));
                    }
                    treeBuilder.CloseComponent();
                    break;

                case DataItemType.Text:
                    treeBuilder.OpenComponent<Input<string>>(index++);
                    treeBuilder.AddAttribute(index++, "OnChange",
                        EventCallback.Factory.Create<string>(this, HandleChange));
                    if (jsonNode != null)
                    {
                        treeBuilder.AddAttribute(index++,"Value", PraseValue<string>(jsonNode));
                    }
                    treeBuilder.CloseComponent();
                    break;

                case DataItemType.Bool:
                    treeBuilder.OpenComponent<Switch>(index++);
                    treeBuilder.AddAttribute(index++, "OnChange",
                        EventCallback.Factory.Create<bool>(this, HandleChange));
                    if (jsonNode != null)
                    {
                        var value = PraseValue<int>(jsonNode);
                        treeBuilder.AddAttribute(index++,"Value",value!=null&&(int)value==1 );
                    }
                    treeBuilder.CloseComponent();
                    break;

                case DataItemType.Enum:
                    treeBuilder.OpenComponent<RadioGroup<int>>(index++);
                    treeBuilder.AddAttribute(index++, "OnChange",
                        EventCallback.Factory.Create<int>(this, HandleChange));
                    treeBuilder.AddAttribute(index++, "ChildContent", (RenderFragment)(childBuilder =>
                    {
                        var enumIndex = 0;
                        foreach (var enumVal in field.EnumValues)
                        {
                            childBuilder.OpenComponent<Radio<int>>(enumIndex++);
                            childBuilder.AddAttribute(enumIndex++, "Value", enumVal.Value);
                            childBuilder.AddAttribute(enumIndex++, "ChildContent", (RenderFragment)(contentBuilder =>
                            {
                                contentBuilder.AddContent(enumIndex++,
                                    enumVal.ShowName); // Add the display name as ChildContent
                            }));
                            childBuilder.CloseComponent();
                        }
                    }));
                    treeBuilder.CloseComponent();
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }));
        builder.CloseComponent();
    };

    private string GetTemplateDataJson()
    {
        SetTemplateDataJson();
        return TemplateDataJson.ToJsonString();
    }

    private void SetTemplateDataJson()
    {
        TemplateDataJson = new JsonObject();
        foreach (var checkedDataItem in _checkedDataItems)
        {
            if (!string.IsNullOrEmpty(checkedDataItem.GroupName)&&checkedDataItem.GroupName != _defaultKey)
            {
                DataJson[checkedDataItem.GroupName] ??= new JsonObject();
                DataJson[checkedDataItem.GroupName][checkedDataItem.Key] ??= JsonValue.Create(string.Empty);
                TemplateDataJson[checkedDataItem.GroupName] ??= new JsonObject();
                var element = DataJson[checkedDataItem.GroupName][checkedDataItem.Key];
                var value = element.GetValue<object>();
                TemplateDataJson[checkedDataItem.GroupName][checkedDataItem.Key] =PraseElement(value);
            }
            else
            {
                DataJson[checkedDataItem.Key] ??= JsonValue.Create(string.Empty);
                TemplateDataJson[checkedDataItem.Key] = PraseElement(DataJson[checkedDataItem.Key].GetValue<object>());
            }
        }

        JsonValue PraseElement(object value)
        {
            JsonValue jsonValue=null;
            if (value is JsonElement)
            {
                var node = JsonNode.Parse(((JsonElement)value).GetRawText());
                jsonValue=node.AsValue();
            }
            else if (value is JsonValue)
            {
                jsonValue=(JsonValue)value;
            }
            else
            {
                jsonValue=JsonValue.Create(value);
            }
            return jsonValue;
        }
    }

   


    private async Task SendTemplateData()
    {
        var buffer = Encoding.UTF8.GetBytes(TemplateDataJson.ToJsonString());
        await _clientWebSocket.SendAsync(buffer, WebSocketMessageType.Text, true, CancellationToken.None);
    }

    private List<Dictionary<string, Dictionary<string, string>>> RealTimeDictionaries { get; set; } =
        new List<Dictionary<string, Dictionary<string, string>>>();

    private Dictionary<string, Dictionary<string, string>> JsonToNestedDictionary(string json)
    {
        var rootNode = JsonNode.Parse(json) as JsonObject;
        var result = new Dictionary<string, Dictionary<string, string>>();
        var ungrouped = new Dictionary<string, string>();

        if (rootNode == null)
        {
            return result; // 如果解析失败或根节点不是 JsonObject，则返回空字典
        }

        foreach (var property in rootNode)
        {
            if (property.Value is JsonObject nestedObject)
            {
                var nestedDict = new Dictionary<string, string>();
                foreach (var nestedProperty in nestedObject)
                {
                    nestedDict[nestedProperty.Key] = nestedProperty.Value?.ToString() ?? string.Empty;
                }
                result[property.Key] = nestedDict;
            }
            else
            {
                ungrouped[property.Key] = property.Value?.ToString() ?? string.Empty;
            }
        }

        if (ungrouped.Count > 0)
        {
            result["未分组"] = ungrouped;
        }

        return result;
    }

    
    private Dictionary<object,JsonNode> SourceReceiveDataJson { get; set; }= new Dictionary<object, JsonNode>();
    
    public void ConvertToJsonNode(Dictionary<string, Dictionary<string, string>> dictionary)
    {
        DataJson=SourceReceiveDataJson[dictionary];
        foreach (var group in dictionary)
        {
            var groupKey = group.Key;
            foreach (var item in group.Value)
            {
                DataItem dataItem;
                dataItem = groupKey == _defaultKey
                    ? _dataItems.FirstOrDefault(m => m.Key == item.Key && (string.IsNullOrEmpty(m.GroupName)))
                    : _dataItems.FirstOrDefault(m => m.Key == item.Key && m.GroupName == groupKey);
                if (dataItem != null)
                {
                    if (string.IsNullOrEmpty(dataItem.GroupName))
                    {
                        dataItem.GroupName = _defaultKey;
                    }
                    _checkedDataItems.Add(dataItem);
                }
            }
        }
    }
}