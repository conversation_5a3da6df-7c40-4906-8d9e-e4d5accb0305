﻿using HX.HRV.SCI.Shared.Pages.Client.Dialog;
using HX.HRV.SCI.Shared.Pages.Client.RealTimeMonitoring;
using HX.HRV.Shared.Pages;
using Masa.Blazor;
using Masa.Blazor.Popup;
using Masa.Blazor.Presets;
using Microsoft.Extensions.DependencyInjection;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared;
using HX.HRV.Shared;
namespace HX.HRV.SCI.Shared
{
    /// <summary>
    /// 模块配置
    /// </summary>
    public class Module : BaseModule
    {
        /// <summary>
        /// 模块名
        /// </summary>
        public override string Name { get; set; } = "多参数生理及行为研究平台共享";
        public override string Icon { get; set; } = "iconfont icon-admin";
        public override void Configure(IServiceCollection services)
        {
            var _ = typeof(HX.HRV.Shared.Module);
            HX.HRV.SCI.Shared.Module _sharedModule;
            HX.HRV.Shared.Module _hrvSharedModule;
            ConfigureItems.Add(new ConfigureItem
            {
                Name = "多参数生理及行为研究平台共享",
                Order = 907,
                Services = (serviceCollection) =>
                {
					BlazorApp.SetModuleLayout("多参数生理及行为研究平台共享", typeof(HRVDefaultLayout));
					serviceCollection.AddTransient<HRV.Shared.Pages.Client.Dialog.PatientDialog, PatientDialog>();
					serviceCollection.AddTransient<HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringComponents, RealTimeMonitoringComponents>();
                    serviceCollection.AddTransient<HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringChartRow, RealTimeMonitoringChartRow>();
                    serviceCollection.AddTransient<HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringPageAppend, RealTimeMonitoringPageAppend>();
                    serviceCollection.AddTransient<HRV.Shared.Pages.Client.Dialog.DeviceStatusDialog, DeviceStatusDialog>();
                    serviceCollection.AddTransient<HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringHead, RealTimeMonitoringHead>();
                    services.AddScoped<PatientListViewModel>();
                    services.AddTransient<DataAnalysisViewModel>();
                    
                    services.AddMasaBlazor(options =>
                    {
                        options.Locale = new Locale("zh-CN");
                        options.ConfigureIcons(IconSet.MaterialDesignIcons);
                        options.Defaults = new Dictionary<string, IDictionary<string, object?>?>()
                        {
                            {
                                PopupComponents.SNACKBAR, new Dictionary<string, object?>()
                                {
                                    { nameof(PEnqueuedSnackbars.Closeable), true },
                                    { nameof(PEnqueuedSnackbars.MaxCount), 1 },
                                    { nameof(PEnqueuedSnackbars.Position), SnackPosition.Center }
                                }
                            },
                            {
                                PopupComponents.PROGRESS_CIRCULAR, new Dictionary<string, object?>()
                                {
                                    { nameof(ProgressCircularOptions.Color), "blue" },
                                }
                            },
                            {
                                PopupComponents.PROGRESS_LINEAR, new Dictionary<string, object?>()
                                {
                                    { nameof(ProgressLinearOptions.Color), "blue" },
                                }
                            },
                            {
                                PopupComponents.CONFIRM, new Dictionary<string, object?>()
                                {
                                    {
                                        nameof(PromptOptions.OkProps), (Action<ModalButtonProps>)(u =>
                                        {
                                            u.Class = "text-capitalize";
                                            u.Style = "Style=\"font-size: 1.25rem;\"";
                                        })
                                    },
                                    {
                                        nameof(ConfirmOptions.CancelProps),
                                        (Action<ModalButtonProps>)(u => { u.Style = "Style=\"font-size: 1.25rem;\""; }
                                        )
                                    },
                                }
                            }
                        };
                        options.ConfigureTheme(theme =>
                        {
                            theme.Themes.Light.Primary = "#4f33ff";
                            theme.Themes.Light.Secondary = "#5e5c71";
                            theme.Themes.Light.Accent = "#006C4F";
                            theme.Themes.Light.Error = "#BA1A1A";
                            theme.Themes.Light.OnSurface = "#1C1B1F";
                        });
                    });
                    //services.Rep lace(ServiceDescriptor.Transient<PatientDialogComponent, PatientDialog>());
                    //services.Replace(ServiceDescriptor.Transient<HX.HRV.Shared.Pages.Client.Dialog.PatientDialog, PatientDialog>());
                }
            });
        }

    }
}