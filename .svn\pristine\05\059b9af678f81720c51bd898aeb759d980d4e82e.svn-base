﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using HX.HRV.Shared.Models;
using UFU.CoreFX.Models;

namespace HX.HRV.SCI.Shared.Models;



/// <summary>
/// 事件标签
/// </summary>
[DataEntity("2501030100000004")]
public class DataAnalysisRecordModel
{
    public string Id { get; set; }
    public string RecordId { get; set; }
    public string Name { get; set; }
    public long  StartIndex { get; set; }
    public long EndIndex { get; set; }

    public DateTime StartTime { get; set; }
    
    public DateTime EndTime { get; set; }
    public ReportDataStatistics Statistics { get; set; }
    [NotMapped]
    public PatientModel Patient { get; set; }
    [NotMapped]
    public PatientRecordModel PatientRecord { get; set; }

    public string GetPath(string id)
    {
        var binPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin","analysis");
        if (!Directory.Exists(binPath))
        {
            Directory.CreateDirectory(binPath);
        }
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;
        var day = DateTime.Now.Day;
        var dir = Path.Combine(binPath, $"{year}", $"{month}-{day}",id);
        return dir;
    }
}