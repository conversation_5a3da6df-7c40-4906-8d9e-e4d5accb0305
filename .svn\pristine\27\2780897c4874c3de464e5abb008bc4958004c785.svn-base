﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using AntDesign;
using HX.HRV.SCI.Shared.Models;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Services;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Pages.Core.Form;
using UFU.CoreFX.Shared.Pages.Core.Role;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.SCI.Web.Services;

public static class ALGDataAnalysisService
{
	private static DataRepository db;

	public static void Use()
	{
		db = new DataRepository(UserInfo.System);
		HXAdminWebSocket.OnReceiveUserMsgToALG += SendAnalysisDataToALG;
	}

	private static void SendAnalysisDataToALG(IWebSocket webSocket, string json)
	{
		var jsonNode = JsonNode.Parse(json);
		var recordId = jsonNode["Data"]?["RecordId"].GetValue<string>();
		var startTimeStr = jsonNode["Data"]?["StartTime"]?.GetValue<string>();
		var endTimeStr = jsonNode["Data"]?["EndTime"]?.GetValue<string>();
		var startTime = DateTime.ParseExact(startTimeStr, "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
		var endTime = DateTime.ParseExact(endTimeStr, "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
		var name = jsonNode["Data"]?["Name"]?.GetValue<string>();
		// db = new DataRepository(UserInfo.System);
		var record = db.Query<PatientRecordModel>(PermissionSwitch.Off)
			.FirstOrDefault(m => m.Id == recordId);
		if (record == null) return;
		if (startTime == default || endTime == default)
		{
			LogTool.Logger.Error("开始和结束时间必须大于0");
		}

		var id = UId.GetNewLocalId();
		var dataAnalysis = new DataModel<DataAnalysisRecordModel>()
		{
			Id = id,
			AddTime = DateTime.Now,
			UpdateTime = DateTime.Now,
			OrganId = record.OrganId,
			UserId = record.UserId,
			Data = new DataAnalysisRecordModel
			{
				Id = id,
				Name = name,
				RecordId = recordId,
				StartTime = startTime,
				EndTime = endTime
			}
		};
		var path = record.Data.GetRecordDirectoryPath();
		var ppgDic = FileDataHelper
			.ProcessFilesAsync(path, "*PPG*.csv", startTime, endTime)
			.GetAwaiter().GetResult();
		
		
		
		ppgDic = ppgDic.OrderBy(m => DateTime.ParseExact(m.Split(",")[0], "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture)).ToList();
		if (ppgDic.Count == 0)
		{
			LogTool.Logger.Error("没有PPG数据");
			return;
		}
		
		
		var dic = new Dictionary<string, List<object>>
		{
			["PPG_G"] = new List<object>(),
			["PPG_R"] = new List<object>(),
			["PPG_I"] = new List<object>(),
		};
		var timeData = new List<object>();
		foreach (var str in ppgDic)
		{
			var valuelist = str.Split(",");
			timeData.Add(valuelist[0]);
			dic["PPG_G"].Add(uint.Parse(valuelist[1].Trim('\r')));
			dic["PPG_R"].Add(uint.Parse(valuelist[2].Trim('\r')));
			dic["PPG_I"].Add(uint.Parse(valuelist[3].Trim('\r')));
		}

		var ppgGBytes = dic["PPG_G"];
		var ppgRBytes = dic["PPG_R"];
		var ppgIBytes = dic["PPG_I"];
		db.Add(dataAnalysis);
		db.SaveChanges();
		var dir = dataAnalysis.Data.GetPath(id);
		var dicData = new Dictionary<string, List<uint>>
		{
			["PPG_G"] = ppgGBytes.Cast<uint>().ToList(),
			["PPG_R"] = ppgRBytes.Cast<uint>().ToList(),
			["PPG_I"] = ppgIBytes.Cast<uint>().ToList(),
		};

		var pageSize = 500*60;
		var page = 0;
		var totalPage = (ppgGBytes.Count + pageSize - 1) / pageSize;
		while (page <= totalPage)
		{
			page++;
			var senddic = new Dictionary<string, List<uint>>
			{
				["PPG_G"] = dicData["PPG_G"].Skip(page * pageSize).Take(pageSize).ToList(),
				["PPG_R"] = dicData["PPG_R"].Skip(page * pageSize).Take(pageSize).ToList(),
				["PPG_I"] = dicData["PPG_I"].Skip(page * pageSize).Take(pageSize).ToList()
			};
			 AlgWebSocketClient.SendMsgToAlg(id, senddic, record.Data.PpgRate,
				record.Data.DeviceStartCheckTime != DateTime.MinValue
					? record.Data.DeviceStartCheckTime.ToUnixMs()
					: record.Data.CollectStartTime.ToUnixMs(),
				totalPage == page, callback: OnReceiveFromAlgAnalysisData);
		}

		var csv = new Dictionary<string, List<object>>
		{
			["Time"] = timeData,
			["PPG_G"] = ppgGBytes,
			["PPG_R"] = ppgRBytes,
			["PPG_I"] = ppgIBytes,
		};
		FileDataHelper.SaveDataToCsv(dir, HXDataType.PPG.ToString(), csv,
			new List<string>() { "Time", "PPG_G", "PPG_R", "PPG_I" });
		var edaDic = FileDataHelper
			.ProcessFilesAsync(path, "*EDA*.csv", startTime, endTime)
			.GetAwaiter().GetResult();
		edaDic = edaDic.OrderBy(m => DateTime.ParseExact(m.Split(",")[0], "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture)).ToList();
		
		var gsrGData = new List<object>();
		timeData = new List<object>();
		var markData = new Dictionary<string, string>();
		var index = 0;
		foreach (var str in edaDic)
		{
			index++;
			var valuelist = str.Split(",");
			var time = valuelist[0];
			timeData.Add(valuelist[0]);
			gsrGData.Add(float.Parse(valuelist[1].Trim('\r')));
			if (valuelist.Length > 2)
			{
				var mark = valuelist[2].Trim('\r');
				if (!string.IsNullOrEmpty(mark))
				{
					markData.Add(time, mark);
				}
			}
		}

		if (markData.Count > 0)
		{
			foreach (var (key, value) in markData)
			{
				if (DateTime.TryParseExact(key, "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture,
						DateTimeStyles.None, out var dateTime))
				{
					var markDataJson = new
					{
						MessageType = 3,
						ClientId = id,
						Algorithm = "GSRSeparated",
						Data = new
						{
							AlgorithmAction = "tick",
							TickTag = value,
							TickTime = dateTime.ToUnix()
						}
					};
					var jsonMarkBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(markDataJson));
					 AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonMarkBytes, new byte[] { }, recordId,
						callback: OnReceiveFromAlgAnalysisData);
				}
			}
		}

		if (gsrGData.Count > 0)
		{
			var edaPageSize = 50*60;
			var edapage = 0;
			var edaTotalPage = (gsrGData.Count + edaPageSize - 1) / edaPageSize;
			while (edapage < edaTotalPage)
			{
				var sendData = gsrGData.Skip(edapage * edaPageSize).Take(edaPageSize).ToList();
				edapage++;
				byte[] databytes = new byte[sendData.Count() * sizeof(float)];
				var data = sendData.Cast<float>().ToArray();
				Buffer.BlockCopy(data, 0, databytes, 0, databytes.Length);
				var jsonData = new
				{
					MessageType = 3,
					ClientId = id,
					Algorithm = "GSRSeparated",
					IsEnd = edapage == edaTotalPage,
					StartTime = startTime.ToUnixMs(),
					Time = startTime.ToUnixMs(),
					Data = new { gsr_Length = databytes.Length, }
				};
				Console.WriteLine(
					$"【SendAnalysisDataToALG】 startTimeStr:{startTime},endTime：{endTimeStr}，databytes.Length：{databytes.Length}");
				var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
				 AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, databytes, recordId,
					callback: OnReceiveFromAlgAnalysisData);
			}
		}

		csv = new Dictionary<string, List<object>>
		{
			["Time"] = timeData,
			["EDA"] = gsrGData,
		};
		FileDataHelper.SaveDataToCsv(dir, nameof(HXDataType.EDA), csv, new List<string>() { "Time", "EDA" });
		
		
		
		
		var imuDic = FileDataHelper
			.ProcessFilesAsync(path, $"*{HXDataType.IMU}*.csv", startTime, endTime)
			.GetAwaiter().GetResult();
		imuDic=imuDic.OrderBy( m => DateTime.ParseExact(m.Split(",")[0], "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture)).ToList();
		var imuCSVDictionary = new Dictionary<string, List<object>>
		{
			["ACC-X"] = new List<object>(),
			["ACC-Y"] = new List<object>(),
			["ACC-Z"] = new List<object>(),
		};
		timeData = new List<object>();
		foreach (var str in imuDic)
		{
			var valuelist = str.Split(",");
			timeData.Add(valuelist[0]);
			imuCSVDictionary["ACC-X"].Add(float.Parse(valuelist[4].Trim('\r')));
			imuCSVDictionary["ACC-Y"].Add(float.Parse(valuelist[5].Trim('\r')));
			imuCSVDictionary["ACC-Z"].Add(float.Parse(valuelist[6].Trim('\r')));
		}

		var imuCsv = new Dictionary<string, List<object>>
		{
			["Time"] = timeData,
			["ACC-X"] = imuCSVDictionary["ACC-X"],
			["ACC-Y"] = imuCSVDictionary["ACC-Y"],
			["ACC-Z"] = imuCSVDictionary["ACC-Z"],
		};
		FileDataHelper.SaveDataToCsv(dir, nameof(HXDataType.IMU), imuCsv,
			new List<string>() { "Time", "ACC-X", "ACC-Y", "ACC-Z" });

		
		
		var bmpDic = FileDataHelper
			.ProcessFilesAsync(path, $"*{HXDataType.HRSPO2}*.csv", startTime, endTime)
			.GetAwaiter().GetResult();
		bmpDic=bmpDic.OrderBy( m => DateTime.ParseExact(m.Split(",")[0], "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture)).ToList();
		var bmpCSVDictionary = new Dictionary<string, List<object>>
		{
			["Bmp"] = new List<object>(),
		};
		timeData = new List<object>();
		foreach (var str in bmpDic)
		{
			var valuelist = str.Split(",");
			timeData.Add(valuelist[0]);
			bmpCSVDictionary["Bmp"].Add(uint.Parse(valuelist[2].Trim('\r')));
		}

		var bmpCsv = new Dictionary<string, List<object>>
		{
			["Time"] = timeData,
			["Bmp"] = bmpCSVDictionary["Bmp"],
		};
		FileDataHelper.SaveDataToCsv(dir, nameof(HXDataType.HRSPO2), bmpCsv, new List<string>() { "Time", "Bmp" });
        _ = Task.Run(async () =>
        {
            await Task.Delay(10 * 1000);
            await AlgWebSocketClient.ExeGenerateEmotionAsync(id, dataAnalysis.Data.GetPath(id));
        });
	}

	private static void OnReceiveFromAlgAnalysisData(ParsedPacket data)
	{
		var jsonData = JsonNode.Parse(data.JsonContent);
		var algorithm = jsonData?["Algorithm"]?.GetValue<string>();
		if (algorithm == HrvVariable.HrvVariableInstance.PPGALG)
		{
			LogTool.Logger.Information($"【ALGDataAnalysisService】收到PPG数据{data.JsonContent}");
			AnalysisPPGData(data);
		}
		else if (algorithm == "GSRSeparated")
		{
			LogTool.Logger.Information($"【ALGDataAnalysisService】收到GSR数据{data.JsonContent}");
			AnalysisGsrData(data);
		}
		else if (algorithm == "EmotionCalculator")
		{
			LogTool.Logger.Information($"【ALGDataAnalysisService】收到Emotion数据{data.JsonContent}");
			ParseEmotionCalculatorData(data);
		}
	}

	private static void AnalysisPPGData(ParsedPacket data)
	{
		var jsonData = JsonNode.Parse(data.JsonContent);
		var clientId = jsonData?["ClientId"]?.ToString();
		 // db = new DataRepository();
		var recordData = db
			.Query<DataAnalysisRecordModel>(PermissionSwitch.Off)
			.FirstOrDefault(m => m.Id == clientId);
		if (recordData?.Data == null)
		{
			LogTool.Logger.Error($"不存在分析记录{clientId}");
			return;
		}

		if (jsonData["Data"].AsObject().ContainsKey("data_index"))
		{
			var statistics = AlgWebSocketClient.GetDataStatistics(data, jsonData);
			
			var orgRecordData = db
				.Query<PatientRecordModel>( PermissionSwitch.Off)
				.FirstOrDefault(m => m.Id==recordData.Data.RecordId);
			if (orgRecordData != null)
			{
				var path = orgRecordData.Data.GetRecordDirectoryPath();
				var hrList = FileDataHelper.ProcessFilesAsync(path, $"*{HXDataType.HRSPO2}*.csv",recordData.Data.StartTime,
					recordData.Data.EndTime).ConfigureAwait(false).GetAwaiter().GetResult();
				var hrData = new List<int>();
				foreach (var str in hrList)
				{
					var valuelist = str.Split(",");
					if(valuelist.Length>=2 && int.TryParse(valuelist[1].Trim('\r'),out var hr))
					{
						hrData.Add(hr);
					}
					else
					{
						 hrData.Add(0);
					}
				}
				statistics.HRList = hrData;
			}
			var  HRImage =ImageHelper.GenerateLineImageUrl(statistics.HRList?.Select(m=>(float)m)?.ToArray(),"HR", recordData.Id, 800, 600);
			if (recordData.Data.Statistics==null)
				recordData.Data.Statistics = new ReportDataStatistics();
			recordData.Data.Statistics.HRImage = HRImage; 
			recordData.Data.Statistics.StatisticsDictionary = statistics.StatisticsDictionary;
			recordData.Data.Statistics.HRList= statistics.HRList;
			recordData.Data.Statistics.PsdList= statistics.PsdList;
			recordData.Data.Statistics.NniList= statistics.NniList;
			recordData.Data.Statistics.FrequencyList= statistics.FrequencyList;
			recordData.UpdateTime = DateTime.Now;
			db.Update(recordData);
			db.SaveChanges();
			var resultData = new
			{
				Record = recordData,
				Function = "FromALG"
			};
			_ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(resultData));
		}
	}


	private static void AnalysisGsrData(ParsedPacket data)
	{
		var jsonData = JsonNode.Parse(data.JsonContent);
		var clientId = jsonData?["ClientId"]?.ToString();
		if (string.IsNullOrEmpty(clientId))
		{
			LogTool.Logger.Information($"AnalysisGsrData：收到未知消息{data.JsonContent}");
			return;
		}
		var recordId = jsonData?["ClientId"]?.ToString();
		var scrLength = jsonData?["Data"]?["eda_tonic_len"]?.GetValue<int>() ?? 0;
		var sclLength = jsonData?["Data"]?["eda_phasic_len"]?.GetValue<int>() ?? 0;
		if (scrLength == 0 || sclLength == 0 ||
			jsonData?["Data"]?.AsObject()?.ContainsKey("eda_time_domain") == false) return;
		var denoisedSignal = jsonData?["Data"]?["denoised_signal_len"]?.GetValue<int>() ?? 0;
		var denoisedSignalLength = denoisedSignal * sizeof(int);
		var sclData = new float[sclLength];
		var sclByteDataLength = sclLength * sizeof(float);
		var sclByteData = data.BinaryData
			.Skip(denoisedSignalLength)
			.Take(sclByteDataLength)
			.ToArray();
		using (var ms = new MemoryStream(sclByteData))
		{
			using (var br = new BinaryReader(ms))
			{
				for (int i = 0; i < sclData.Length; i++)
				{
					sclData[i] = br.ReadSingle();
				}
			}
		}
		var scrData = new float[scrLength];
		var scrByteDataLength = scrLength * sizeof(float);
		var scrBytes = data.BinaryData.Skip(denoisedSignalLength + sclByteDataLength)
			.Take(scrByteDataLength)
			.ToArray();
		using (var ms = new MemoryStream(scrBytes))
		{
			using (var br = new BinaryReader(ms))
			{
				for (int i = 0; i < scrData.Length; i++)
				{
					scrData[i] = br.ReadSingle();
				}
			}
		}
		// using var db = new DataRepository();
		var recordData = db
			.Query<DataAnalysisRecordModel>(PermissionSwitch.Off)
			.FirstOrDefault(m => m.Id == clientId);
		if (recordData?.Data == null)
		{
			LogTool.Logger.Error($"【AnalysisGsrData】不存在分析记录{clientId}");
			return;
		}

		var path = recordData.Data.GetPath(recordId);
		var recordEdaTimeDomin = JsonTool.Deserialize<List<EdaTimeDomain>>
			(jsonData["Data"]?["eda_time_domain"].ToJsonString());
		var edaScrResult = JsonTool.Deserialize<List<EdaSCRResult>>
			(jsonData["Data"]?["eda_SCR_results"].ToJsonString());
		var edaEventResult = JsonTool
			.Deserialize<List<EdaEventResult>>(jsonData["Data"]?["eda_event_results"].ToJsonString());
		recordData.Data.EdaTimeDomain = recordEdaTimeDomin;
		recordData.Data.EdaSCRResults = edaScrResult;
		recordData.Data.EdaEventResults = edaEventResult;
		recordData.UpdateTime = DateTime.Now;
		
		
		var allSeconds = recordData.Data.EndTime-recordData.Data.StartTime ;
		
		LogTool.Logger.Information($"allSeconds:{allSeconds},allSeconds.TotalSeconds:{allSeconds.TotalSeconds}");
		 FileDataHelper.SaveDataToCsvTask(
			path,
			recordData.Data.StartTime.ToUnix(),
			"SCL&SCR",
			new Dictionary<string, List<float>>
			{
				{ "SCL", sclData.ToList() },
				{ "SCR", scrData.ToList() }
			},
			secondsCount:allSeconds.TotalSeconds
		).ConfigureAwait(false).GetAwaiter().GetResult();
		var edaDatas =  CSVDataHelper
			.GetDirectoryEDAData(recordData.Data.GetPath(recordData.Id)).ConfigureAwait(false).GetAwaiter().GetResult();;
		var lineY = edaDatas
			.Select(m => m.EDA).ToList();
		var image =  ImageHelper.GenerateLineImageUrl(lineY.ToArray(), 
			"EDA", recordData.Id, 800, 300);
		var scrImage =  ImageHelper.GenerateLineImageUrl( 
			"SCR_SCL", 
			recordData.Id, 
			dataY:new []
			{
				new KeyValuePair<string, List<float>>("SCR", scrData.ToList()),
				new KeyValuePair<string, List<float>>("SCL", sclData.ToList())
			});
		recordData.Data.EdaImageUrl = image;
		recordData.Data.SCL_SCRImageUrl= scrImage;
		db.Update(recordData);
		db.SaveChanges();
		_ =HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(new
		{
			Record = recordData,
			Function = "FromALG",
			Category = "GSR"
		}));
	}


	private static void ParseEmotionCalculatorData(ParsedPacket data)
	{
		var jsonData = JsonNode.Parse(data.JsonContent);
		var clientId = jsonData?["ClientId"]?.ToString();
		if (string.IsNullOrEmpty(clientId))
		{
			LogTool.Logger.Information($"EmotionCalculator：收到未知消息{data.JsonContent}");
			return;
		}

		var recordId = jsonData?["ClientId"]?.ToString();
		var calculatorData = jsonData?["Data"]?.AsObject();
		if (string.IsNullOrEmpty(recordId) || calculatorData is { Count: <= 0 }) return;
		// using var db = new DataRepository();
		var record = db
			.Query<DataAnalysisRecordModel>(PermissionSwitch.Off)
			.FirstOrDefault(m => m.Id == clientId);
		if (calculatorData?.ContainsKey("depress_prediction")==false)
		{
			return;
		}
		var depress_prediction = calculatorData?["depress_prediction"]?.GetValue<int>() == 1;
		var depressConfidence = calculatorData?["depress_confidence"]?.GetValue<float>();
		//是否抑郁
		var depressLevelPrediction = calculatorData?["depressLevelPrediction"]?.GetValue<int>();
		//置信概率 
		var depressLevelConfidence = calculatorData?["depressLevelConfidence"]?.GetValue<float>();
		//情绪效价
		var valenceScore = calculatorData?["valence_score"]?.GetValue<float>() ?? 0;
		var valenceScoreType = calculatorData?["valence_score_type"]?.GetValue<int>() ?? 0;
		//情绪唤醒度
		var arousalScore = calculatorData?["arousal_score"]?.GetValue<float>() ?? 0;
		var arousalScoreType = calculatorData?["arousal_score_type"]?.GetValue<float>() ?? 0;

		if (record == null) return;
		record.Data.Emotion = new EmotionDataModel()
		{
			DepressPrediction = depress_prediction,
			DepressConfidence = depressConfidence ?? 0,
			DepressLevelConfidence = depressLevelConfidence ?? 0,
			DepressLevelPrediction = depressLevelPrediction ?? 0,
			ValenceScore = valenceScore,
			ArousalScore = arousalScore,
			ArousalScoreType = (int)arousalScoreType,
			ValenceScoreType = valenceScoreType
		};
		db.Update(record, PermissionSwitch.Off);
		db.SaveChanges();
	}
}