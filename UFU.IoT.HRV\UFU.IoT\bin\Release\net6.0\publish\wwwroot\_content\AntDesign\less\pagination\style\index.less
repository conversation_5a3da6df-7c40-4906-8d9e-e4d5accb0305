@import '../../style/themes/index';
@import '../../style/mixins/index';
@import '../../input/style/mixin';

@pagination-prefix-cls: ~'@{ant-prefix}-pagination';

.@{pagination-prefix-cls} {
  .reset-component();

  ul,
  ol {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  &::after {
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
    content: ' ';
  }

  &-total-text {
    display: inline-block;
    height: @pagination-item-size;
    margin-right: 8px;
    line-height: @pagination-item-size - 2px;
    vertical-align: middle;
  }

  &-item {
    display: inline-block;
    min-width: @pagination-item-size;
    height: @pagination-item-size;
    margin-right: 8px;
    font-family: @pagination-font-family;
    line-height: @pagination-item-size - 2px;
    text-align: center;
    vertical-align: middle;
    list-style: none;
    background-color: @pagination-item-bg;
    border: @border-width-base @border-style-base @border-color-base;
    border-radius: @border-radius-base;
    outline: 0;
    cursor: pointer;
    user-select: none;

    a {
      display: block;
      padding: 0 6px;
      color: @text-color;
      transition: none;

      &:hover {
        text-decoration: none;
      }
    }

    &:hover {
      border-color: @primary-color;
      transition: all 0.3s;

      a {
        color: @primary-color;
      }
    }

    // cannot merge with `&:hover`
    // see https://github.com/ant-design/ant-design/pull/34002
    &:focus-visible {
      border-color: @primary-color;
      transition: all 0.3s;

      a {
        color: @primary-color;
      }
    }

    &-active {
      font-weight: @pagination-font-weight-active;
      background: @pagination-item-bg-active;
      border-color: @primary-color;

      a {
        color: @primary-color;
      }

      &:hover {
        border-color: @primary-5;
      }

      &:focus-visible {
        border-color: @primary-5;
      }

      &:hover a {
        color: @primary-5;
      }

      &:focus-visible a {
        color: @primary-5;
      }
    }
  }

  &-jump-prev,
  &-jump-next {
    outline: 0;
    .@{pagination-prefix-cls}-item-container {
      position: relative;

      .@{pagination-prefix-cls}-item-link-icon {
        color: @primary-color;
        font-size: @font-size-sm;
        letter-spacing: -1px;
        opacity: 0;
        transition: all 0.2s;

        &-svg {
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          margin: auto;
        }
      }

      .@{pagination-prefix-cls}-item-ellipsis {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: block;
        margin: auto;
        color: @disabled-color;
        font-family: Arial, Helvetica, sans-serif;
        letter-spacing: 2px;
        text-align: center;
        text-indent: 0.13em;
        opacity: 1;
        transition: all 0.2s;
      }
    }

    &:hover {
      .@{pagination-prefix-cls}-item-link-icon {
        opacity: 1;
      }
      .@{pagination-prefix-cls}-item-ellipsis {
        opacity: 0;
      }
    }

    &:focus-visible {
      .@{pagination-prefix-cls}-item-link-icon {
        opacity: 1;
      }
      .@{pagination-prefix-cls}-item-ellipsis {
        opacity: 0;
      }
    }
  }

  &-prev,
  &-jump-prev,
  &-jump-next {
    margin-right: 8px;
  }

  &-prev,
  &-next,
  &-jump-prev,
  &-jump-next {
    display: inline-block;
    min-width: @pagination-item-size;
    height: @pagination-item-size;
    color: @text-color;
    font-family: @pagination-font-family;
    line-height: @pagination-item-size;
    text-align: center;
    vertical-align: middle;
    list-style: none;
    border-radius: @border-radius-base;
    cursor: pointer;
    transition: all 0.3s;
  }

  &-prev,
  &-next {
    font-family: Arial, Helvetica, sans-serif;
    outline: 0;

    button {
      color: @text-color;
      cursor: pointer;
      user-select: none;
    }

    &:hover button {
      border-color: @primary-5;
    }

    .@{pagination-prefix-cls}-item-link {
      display: block;
      width: 100%;
      height: 100%;
      padding: 0;
      font-size: 12px;
      text-align: center;
      background-color: @pagination-item-link-bg;
      border: @border-width-base @border-style-base @border-color-base;
      border-radius: @border-radius-base;
      outline: none;
      transition: all 0.3s;
    }

    &:focus-visible .@{pagination-prefix-cls}-item-link {
      color: @primary-color;
      border-color: @primary-color;
    }

    &:hover .@{pagination-prefix-cls}-item-link {
      color: @primary-color;
      border-color: @primary-color;
    }
  }

  &-disabled {
    &,
    &:hover {
      cursor: not-allowed;
      .@{pagination-prefix-cls}-item-link {
        color: @disabled-color;
        border-color: @border-color-base;
        cursor: not-allowed;
      }
    }

    &:focus-visible {
      cursor: not-allowed;
      .@{pagination-prefix-cls}-item-link {
        color: @disabled-color;
        border-color: @border-color-base;
        cursor: not-allowed;
      }
    }
  }

  &-slash {
    margin: 0 10px 0 5px;
  }

  &-options {
    display: inline-block;
    margin-left: 16px;
    vertical-align: middle;

    // IE11 css hack. `*::-ms-backdrop,` is a must have
    @media all and (-ms-high-contrast: none) {
      *::-ms-backdrop,
      & {
        vertical-align: top;
      }
    }

    &-size-changer.@{ant-prefix}-select {
      display: inline-block;
      width: auto;
    }

    &-quick-jumper {
      display: inline-block;
      height: @input-height-base;
      margin-left: @margin-xs;
      line-height: @input-height-base;
      vertical-align: top;

      input {
        .input();

        width: 50px;
        height: @input-height-base;
        margin: 0 8px;
      }
    }
  }

  &-simple &-prev,
  &-simple &-next {
    height: @pagination-item-size-sm;
    line-height: @pagination-item-size-sm;
    vertical-align: top;
    .@{pagination-prefix-cls}-item-link {
      height: @pagination-item-size-sm;
      background-color: transparent;
      border: 0;

      &::after {
        height: @pagination-item-size-sm;
        line-height: @pagination-item-size-sm;
      }
    }
  }

  &-simple &-simple-pager {
    display: inline-block;
    height: @pagination-item-size-sm;
    margin-right: 8px;

    input {
      box-sizing: border-box;
      height: 100%;
      margin-right: 8px;
      padding: 0 6px;
      text-align: center;
      background-color: @pagination-item-input-bg;
      border: @border-width-base @border-style-base @border-color-base;
      border-radius: @border-radius-base;
      outline: none;
      transition: border-color 0.3s;

      &:hover {
        border-color: @primary-color;
      }

      &:focus {
        border-color: @primary-color-hover;
        box-shadow: @input-outline-offset @outline-blur-size @outline-width @primary-color-outline;
      }

      &[disabled] {
        color: @disabled-color;
        background: @disabled-bg;
        border-color: @border-color-base;
        cursor: not-allowed;
      }
    }
  }

  &&-mini &-total-text,
  &&-mini &-simple-pager {
    height: @pagination-item-size-sm;
    line-height: @pagination-item-size-sm;
  }

  &&-mini &-item {
    min-width: @pagination-item-size-sm;
    height: @pagination-item-size-sm;
    margin: 0;
    line-height: @pagination-item-size-sm - 2px;
  }

  &&-mini &-item:not(&-item-active) {
    background: transparent;
    border-color: transparent;
  }

  &&-mini &-prev,
  &&-mini &-next {
    min-width: @pagination-item-size-sm;
    height: @pagination-item-size-sm;
    margin: 0;
    line-height: @pagination-item-size-sm;
  }

  &&-mini &-prev &-item-link,
  &&-mini &-next &-item-link {
    background: transparent;
    border-color: transparent;

    &::after {
      height: @pagination-item-size-sm;
      line-height: @pagination-item-size-sm;
    }
  }

  &&-mini &-jump-prev,
  &&-mini &-jump-next {
    height: @pagination-item-size-sm;
    margin-right: 0;
    line-height: @pagination-item-size-sm;
  }

  &&-mini &-options {
    margin-left: 2px;

    &-size-changer {
      top: @pagination-mini-options-size-changer-top;
    }

    &-quick-jumper {
      height: @pagination-item-size-sm;
      line-height: @pagination-item-size-sm;

      input {
        .input-sm();

        width: 44px;
        height: @input-height-sm;
      }
    }
  }

  // ============================ Disabled ============================
  &&-disabled {
    cursor: not-allowed;

    .@{pagination-prefix-cls}-item {
      background: @disabled-bg;
      border-color: @border-color-base;
      cursor: not-allowed;

      a {
        color: @disabled-color;
        background: transparent;
        border: none;
        cursor: not-allowed;
      }

      &-active {
        background: @pagination-item-disabled-bg-active;

        a {
          color: @pagination-item-disabled-color-active;
        }
      }
    }

    .@{pagination-prefix-cls}-item-link {
      color: @disabled-color;
      background: @disabled-bg;
      border-color: @border-color-base;
      cursor: not-allowed;
      .@{pagination-prefix-cls}-simple& {
        background: transparent;
      }
    }

    .@{pagination-prefix-cls}-item-link-icon {
      opacity: 0;
    }

    .@{pagination-prefix-cls}-item-ellipsis {
      opacity: 1;
    }

    .@{pagination-prefix-cls}-simple-pager {
      color: @disabled-color;
    }
  }
}

@media only screen and (max-width: @screen-lg) {
  .@{pagination-prefix-cls}-item {
    &-after-jump-prev,
    &-before-jump-next {
      display: none;
    }
  }
}

@media only screen and (max-width: @screen-sm) {
  .@{pagination-prefix-cls}-options {
    display: none;
  }
}

@import './rtl';
