﻿using HX.HRV.Shared.Models;
using Microsoft.Extensions.DependencyInjection;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;

namespace HX.HRV.Shared.Services;

/// <summary>
///  内部 HttpClient
/// </summary>
public class InternalHttpClientService
{
    private readonly StateService _stateService;

    /// <summary>
    /// 设备列表URL
    /// </summary>
    private const string DeviceListUrl = "/api/v2.0/IoT/Devices/GetAllList";
    /// <summary>
    /// 
    /// </summary>
    /// <param name="stateService"></param>
    public InternalHttpClientService(IServiceProvider  serviceProvider)
    {
        _stateService = serviceProvider.GetRequiredService<StateService>();
    }

    #region 检测记录

    public async Task<DataModel<PatientRecordModel>> GetPatientRecordModelDetailById(string id)
    {
        var res = await _stateService
            .GetAsJsonAsync<DataModel<PatientRecordModel>>(
                "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string>()
                {
                    { "id", id }
                });
        
        return res?.Data;
    }
    
    /// <summary>
    ///  获取患者检测记录列表
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    public async Task<Result<List<DataModel<PatientRecordModel>>>> GetPatientRecordModelListByParams(
        Dictionary<string, string> dic)
    {
        var res = await _stateService
            .GetAsJsonAsync<List<DataModel<PatientRecordModel>>>(
                "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelListByParams", dic);
        return res;
    }
    public async Task<Result<DataModel<PatientRecordModel>>> AddRecordAsync(PatientRecordModel recordModel)
    {
        Result<DataModel<PatientRecordModel>> result;
        //新增
        result = await _stateService
            .PostAsJsonAsync<DataModel<PatientRecordModel>>("/api/v2.0/HRV_HX/PatientRecord/Add",
                recordModel);
        return result;
    }

    /// <summary>
    ///  删除检测记录
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<bool> DeletePatientRecordAsync(string id)
    {
        var res = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/HRV_HX/PatientRecord/Delete?id={id}");
        return res.Success && res.Data;
    }

    #endregion

    #region 患者信息

    /// <summary>
    /// 获取患者列表
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    public async Task<Result<List<DataModel<PatientModel>>>> GetPatientModelList(Dictionary<string, string> dic)
    {
        var res = await _stateService
            .GetAsJsonAsync<List<DataModel<PatientModel>>>("/api/v2.0/HRV_HX/Patient/GetPatientList", dic);
        return res;
    }

    /// <summary>
    ///  删除患者
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<bool> DeletePatientAsync(string id)
    {
        var res = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/HRV_HX/Patient/Delete?id={id}");
        return res.Success && res.Data;
    }
    /// <summary>
    ///  删除患者
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<Result<DataModel<PatientModel>>> AddOrEditPatientAsync(PatientModel patientModel)
    {
        Result<DataModel<PatientModel>> result;
        if (string.IsNullOrEmpty(patientModel.Id))
        {
            //新增
            result = await _stateService
                .PostAsJsonAsync<DataModel<PatientModel>>("/api/v2.0/HRV_HX/Patient/Add",
                    patientModel);
        }
        else
        {
            //编辑
            result = await _stateService.PostAsJsonAsync<DataModel<PatientModel>>(
                "/api/v2.0/HRV_HX/Patient/Edit?id=" + patientModel.Id,
                patientModel);
        }
        return result;
    }
    /// <summary>
    /// 获取患者详情
    /// </summary>
    /// <param name="cardId"></param>
    /// <returns></returns>
    public async Task<Result<DataModel<PatientModel>>> GetPatientByCardAsync(string cardId)
    {
        var res = await _stateService
            .GetAsJsonAsync<DataModel<PatientModel>>("/api/v2.0/HRV_HX/Patient/GetPatientModelDetail?cardId=" + cardId, null);
        return res;
    }
 
    

    #endregion
    
    /// <summary>
    /// 获取配置信息
    /// </summary>
    /// <returns></returns>
    public async Task<Result<DataModel<HxSystemConfigModel>>> GetSystemConfigAsync()
    {
        var res = await _stateService
            .GetAsJsonAsync<DataModel<HxSystemConfigModel>>("/api/v2.0/HRV_HX/HxSystemConfig/GetSystemConfigModelDetail");
        return res;
    }
    /// <summary>
    /// 获取配置信息
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    public async Task<Result<DataModel<HxSystemConfigModel>>> EditSystemConfigAsync(HxSystemConfigModel HxSystemConfigModel)
    {
        var result = await _stateService.PostAsJsonAsync<DataModel<HxSystemConfigModel>>(
            "/api/v2.0/HRV_HX/HxSystemConfig/Edit" ,
            HxSystemConfigModel);
        return result;
    }


    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <returns></returns>
    public async Task<List<RoleModel>> GetRoleList()
    {
        var res = await _stateService
            .GetAsJsonAsync<List<RoleModel>>($"/api/v2/HRV_HX/OrgUser/RoleList");
        return res?.Data;
    }
    /// <summary>
    ///  删除用户
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<bool> DeleteUserAsync(string userId)
    {
        
        var result = await _stateService.PostAsJsonAsync<bool>($"/api/v2/HRV_HX/OrgUser/Delete?userId={userId}");
        return result.Success;
    }

    #region 导出任务
    /// <summary>
    ///  获取导出任务列表
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    public async Task<Result<List<DataModel<HxExportTaskModel>>>> GetExportTaskList(
        Dictionary<string, string> dic)
    {
        var res = await _stateService
            .GetAsJsonAsync<List<DataModel<HxExportTaskModel>>>(
                "/api/v2.0/HRV_HX/HxExportTask/GetHxExportTaskModelList", dic);
        return res;
    }
  
    #endregion


}