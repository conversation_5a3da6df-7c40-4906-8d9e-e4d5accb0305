﻿@page "/Client/SCI/DataAnalysis2/{RecordId}"
@attribute [Permission("数据分析", Icon = "shuju", MenuOrder = "5")]
@inject IJSRuntime JS
@using System.Reactive
@using System.Reactive.Disposables
@using System.Text.Json
@using System.Text.Json.Nodes
@using HX.Base.Shared.Components
@using Masa.Blazor
@using Masa.Blazor.Presets
@using Microsoft.JSInterop
@using ReactiveUI
@using UFU.CoreFX.Permission
@inherits ReactiveUI.Blazor.ReactiveInjectableComponentBase<PatientListViewModel>
<style>

    .hrv-report-fields {
        display: flex;
        flex-wrap: wrap; /* 支持换行布局 */
        border: 1px solid #ddd; /* 可选：边框 */
        background-color: #f9f9f9; /* 背景色 */
        padding: 0.5rem;
        font-size: 1.2rem;
        flex-flow: row;
        width: 100%;
        justify-content: space-between;
    }

    .field-item {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        align-content: stretch;
    }

    .hrv-report-label {
        font-weight: bold;
        color: #333;
        text-align: end;
    }

    .hrv-report-value {
        color: #555;
        text-align: left;
    }

    .data-analysis-table {

    }

    .data-analysis-table th,
    .data-analysis-table td {
        border-bottom: 1px solid #ddd;
        padding: 0.125em;
        width: 10em;
    }

    .data-analysis-table tr:first-child {
        border-top: 1px solid #ddd;
    }

    .data-analysis-table th {
        background-color: #e1ebf6;
        color: #333;
    }

    .section-title {
        font-weight: bold;
        color: #3982e3;
        text-align: left;
        font-size: 1.5rem;
        margin-top: 0.5rem;
    }

</Style>


@if (ViewModel.PatientRecordModel != null)
{
    var patient = ViewModel.PatientRecordModel.Patient;
    var record = ViewModel.PatientRecordModel;
    <div class="hrv-report-fields">
        <div class="field-item">
            <div class="hrv-report-label">被试编号：</div>
            <div class="hrv-report-value">@patient.OutpatientNumberString</div>
        </div>
        <div class="field-item">
            <div class="hrv-report-label">姓名：</div>
            <div class="hrv-report-value">@patient.Name</div>
        </div>
        <div class="field-item">
            <div class="hrv-report-label">报告编号：</div>
            <div class="hrv-report-value">@record.RecordCode</div>
        </div>
        <div class="field-item">
            <div class="hrv-report-label">开始采集时间：</div>
            <div class="hrv-report-value">@record.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss")</div>
        </div>
        <div class="field-item">
            <div class="hrv-report-label">结束采集时间：</div>
            <div class="hrv-report-value">@record.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss")</div>
        </div>
    </div>
}

<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>

<MStepper Style="height: 100%;font-size: 20px" Value="@ViewModel.Step">
    <MStepperHeader>
        <MStepperStep Step="1" Complete="ViewModel.Step > 1">选择数据</MStepperStep>
        <MDivider></MDivider>
        <MStepperStep Step="2" Complete="ViewModel.Step > 2">分析</MStepperStep>
    </MStepperHeader>

    <MStepperItems>
        <MStepperContent Step="1">
            <div Class="d-flex justify-center align-center">
                <MContainer class="d-flex justify-center align-center" Style="width:60%">
                    <MLabel>分析时段:</MLabel>
                    <PDateDigitalClockPicker @bind-Value="ViewModel.SegmentStartDate" Step="@TimeSpan.FromMinutes(1)" TimeFormat="TimeFormat.Hr24">
                        <PDefaultDateTimePickerActivator Filled SoloInverted Dense Clearable />
                    </PDateDigitalClockPicker>
                    -
                    <PDateDigitalClockPicker @bind-Value="ViewModel.SegmentEndDate" Step="@TimeSpan.FromMinutes(1)" TimeFormat="TimeFormat.Hr24">
                        <PDefaultDateTimePickerActivator Filled SoloInverted Dense Clearable />
                    </PDateDigitalClockPicker>
                    
                    <MButton Type="primary" Style="margin-left: 10px" 
                             OnClick="() => ViewModel.ConfirmDateRangeCommand.Execute()"
                            >
                        确定
                    </MButton>
                    <div>
                        <MButton Color="primary" Style="margin-left: 10px;"
                                 OnClick="() => ViewModel.StartAnalysisCommand.Execute()"
                                 >
                            分析
                        </MButton>
                    </div>
                </MContainer>
            </div>
            <div style="display: flex">
                <div style="flex:5">
                    <div style="display: flex;padding:  0em 2rem;width: 100% ;justify-content: center;flex-direction: column;  ">
                        <div style="width:100%;height: 650px">
                            <HXEchartSegmentLazyComponent @ref="_echartSegmentComponent"
                                                          OnDataZoom="arg => ViewModel.OnChartDataZoom(arg)" />
                        </div>
                    </div>
                </div>
            </div>
        </MStepperContent>
        <MStepperContent Step="2">
            @if (ViewModel.Statistics != null)
            {
                <MTabs @bind-Value="ViewModel.SelectedTab" Centered SliderColor="1f73d2">
                    <MTab Value=@("心率变异性分析")>心率变异性分析</MTab>
                    @if(ViewModel.IsEdaAnalysisAvailable)
                    {
                        <MTab Value=@("皮肤电分析")>皮肤电分析</MTab>
                    }
                </MTabs>
                <MTabsItems Value="@ViewModel.SelectedTab">
                    <MTabItem Value="@("心率变异性分析")">
                        </MTabItem>
                    <MTabItem Value="@("皮肤电分析")">
                        </MTabItem>
                </MTabsItems>
            }
            <div style="width: 100%;display: flex; flex-flow: row;
            justify-content: flex-end;margin-bottom: 1rem;">
                <MButton OnClick="() => ViewModel.PreviousStepCommand.Execute()">上一步</MButton>
                <MButton Color="primary" Style="margin-left: 10px"
                         OnClick="() => ViewModel.ExportCommand.Execute()"
                         >
                    导出
                </MButton>
            </div>
        </MStepperContent>
    </MStepperItems>
</MStepper>

<PModal @bind-Value="isNameModalVisible"
        Persistent
        Title="请输入分析名称"
        Width="500"
        OnSave="HandleNameSaved"
        OnCancel="HandleNameCancelled">
    <MRow>
        <MCol Cols="12">
            <MTextField @bind-Value="analysisNameInput"
                        Label="分析名称"
                        Placeholder="请输入分析名称"
                        Dense
                        Outlined />
        </MCol>
    </MRow>
</PModal>

@code {
    [Parameter] public string RecordId { get; set; }

    [Inject] public DataAnalysisViewModel ViewModel { get; set; }
    [Inject] public IPopupService PopupService { get; set; }

    private HXEchartSegmentLazyComponent _echartSegmentComponent;

    // 用于处理弹窗交互的本地状态
    private bool isNameModalVisible;
    private string analysisNameInput;
    private IInteractionContext<Unit,string> nameInteractionContext;

    protected override void OnInitialized()
    {
        base.OnInitialized();
        // 将路由参数传递给ViewModel
        ViewModel.RecordId = RecordId;
        // ViewModel属性由DI容器自动设置
    }
    
    protected override void OnAfterRender(bool firstRender)
    {
        base.OnAfterRender(firstRender);
        if (firstRender)
        {
            ViewModel.InitializeCommand.Execute();
            // 视图激活时，注册所有交互处理器
            this.WhenActivated(disposables =>
            {
                // 1. 处理“获取分析名称”的交互请求
                ViewModel.GetAnalysisName.RegisterHandler(context =>
                {
                    nameInteractionContext = context;
                    analysisNameInput = ""; // 重置输入
                    isNameModalVisible = true; // 显示弹窗
                    StateHasChanged();
                }).DisposeWith(disposables);

                // 2. 处理“显示Snackbar”的交互请求
                ViewModel.ShowSnackbar.RegisterHandler(context =>
                {
                    PopupService.EnqueueSnackbarAsync(context.Input.Message, context.Input.Type);
                    context.SetOutput(Unit.Default); // 通知ViewModel交互已完成
                }).DisposeWith(disposables);

                // 3. 处理"发送数据到图表组件"的交互请求
                ViewModel.SendToChartComponent.RegisterHandler(async context =>
                {
                    var isEnd = false;
                    try
                    {
                        var segmentData = JsonNode.Parse(context.Input);
                        if (segmentData != null && _echartSegmentComponent != null)
                        {
                            var type = segmentData["Type"]?.GetValue<string>();
                            var data = segmentData["data"]?.AsArray()?.GetValues<string>()?.ToList();
                            isEnd = segmentData["IsEnd"]?.GetValue<bool>() == true;

                            if (data != null)
                            {
                                var reData = data.Select(m => m.Split(",")).ToList();
                                int multiplierIndex = type == "PPG" ? 0 : 1;
                                await _echartSegmentComponent.UpdateChartData(reData, isEnd, multiplierindex: multiplierIndex);
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"JSON parsing error in View: {ex.Message}");
                    }
                    context.SetOutput(isEnd); // 将isEnd状态返回给调用方
                }).DisposeWith(disposables);
            });
        }
    }

    // 弹窗“保存”按钮的回调
    private void HandleNameSaved()
    {
        isNameModalVisible = false;
        nameInteractionContext?.SetOutput(analysisNameInput); // 将输入值返回给ViewModel
        nameInteractionContext = null;
        StateHasChanged();
    }

    // 弹窗“取消”按钮的回调
    private void HandleNameCancelled()
    {
        isNameModalVisible = false;
        nameInteractionContext?.SetOutput(null); // 返回null表示取消
        nameInteractionContext = null;
        StateHasChanged();
    }
}

