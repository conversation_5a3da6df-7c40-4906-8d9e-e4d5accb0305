﻿using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using PdfSharp;
using System.Globalization;
using System.Text.Json.Nodes;
using UFU.CoreFX.Data;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web.Services
{
    /// <summary>
    /// 
    /// </summary>
    [Permission("汇心WebSocket", IsWebSocket = true, AllowAllUser = true, AllowAnonymous = true,
        Url = "/iot/hx_hrv_sci/v1")]
    public class DataAnalysisWebSocket : IWebSocket
    {
        /// <summary>
        /// 
        /// </summary>
        public static void Use()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public override void Close()
        {
            base.Close();
        }

        /// <summary>
        /// 连接
        /// </summary>
        public override void OnConnect()
        {
            base.OnConnect();
        }

        /// <summary>
        /// 收到文本消息
        /// </summary>
        /// <param name="data"></param>
        public override void OnReciveText(string data)
        {
            Task.Run(async () =>
            {
                var json = JsonTool.Deserialize<JsonNode>(data);
                DateTime segmentStartTime = default;
                DateTime segmentEndTime = default;
                var startTime = json["startTime"]?.ToString();
                var endTime = json["endTime"]?.ToString();
                var recordId = json["recordId"]?.ToString();
                if (!string.IsNullOrEmpty(startTime))
                {
                    segmentStartTime = DateTime.ParseExact(startTime, "yyyy-MM-dd HH:mm:ss.fff",
                        CultureInfo.InvariantCulture);
                }

                if (!string.IsNullOrEmpty(endTime))
                {
                    segmentEndTime =
                        DateTime.ParseExact(endTime, "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
                }

                var db = new DataRepository();
                var record = await db.Query<PatientRecordModel>()
                    .FirstOrDefaultAsync(m => m.Id == recordId);
                var filePath = record.Data.GetRecordDirectoryPath();
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var ppgData = await FileDataHelper.ProcessFilesAsync(filePath, "*PPG*.csv", segmentStartTime,
                            segmentEndTime);
                        
                        
                        var pageSize = 500 * 60;
                        if (ppgData.Count > 0)
                        {
                            ppgData=ppgData.Select(m=>string.Join(",",m.Split(",").Take(2).ToArray())).ToList();
                            var size = 0;
                            while (size < ppgData.Count)
                            {
                                size += pageSize;
                                if (size >= ppgData.Count)
                                {
                                    await this.SendTextAsync(JsonTool.SerializeIgnoreNull(new
                                    {
                                        Type = "PPG",
                                        data = ppgData.GetRange(size - pageSize, size>ppgData.Count? ppgData.Count-(size-pageSize):pageSize),
                                        IsEnd = true,
                                    }));
                                    break;
                                }
                                else
                                {
                                    await this.SendTextAsync(JsonTool.SerializeIgnoreNull(new
                                    {
                                        Type = "PPG",
                                        data = ppgData.GetRange(size - pageSize, pageSize)
                                    }));
                                }
                            }
                        }
                        else
                        {
                            await this.SendTextAsync(JsonTool.SerializeIgnoreNull(new
                            {
                                Type = "PPG",
                                data = ppgData,
                                IsEnd = true,
                                NoData = true
                            }));
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error($" {ex.StackTrace.ToString()}【DataAnalysisWebSocket.OnReciveText】 EDA ");
                    }
                });
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var edaData =
                            await FileDataHelper.ProcessFilesAsync(filePath, "*EDA*.csv", segmentStartTime,
                                segmentEndTime);
                        var pageSize = 50 * 60;
                        if (edaData.Count > 0)
                        {
                            var skip = 0;
                            while (skip < edaData.Count)
                            {
                                skip += pageSize;
                                if (skip >= edaData.Count)
                                {
                                    await this.SendTextAsync(JsonTool.SerializeIgnoreNull(new
                                    {
                                        Type = "EDA",
                                        data = edaData.GetRange(skip - pageSize,  skip>edaData.Count? edaData.Count-(skip-pageSize):pageSize),
                                        IsEnd = true
                                    }));
                                    break;
                                }
                                else
                                {
                                    await this.SendTextAsync(JsonTool.SerializeIgnoreNull(new
                                    {
                                        Type = "EDA",
                                        data = edaData.GetRange(skip - pageSize, pageSize)
                                    }));
                                }
                            }
                        }
                        else
                        {
                            await this.SendTextAsync(JsonTool.SerializeIgnoreNull(new
                            {
                                Type = "EDA",
                                data = edaData,
                                IsEnd = true,
                                NoData = true
                            }));
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error($" {ex.StackTrace.ToString()}【DataAnalysisWebSocket.OnReciveText】 EDA ");
                    }
                });
            });
        }
    }
}