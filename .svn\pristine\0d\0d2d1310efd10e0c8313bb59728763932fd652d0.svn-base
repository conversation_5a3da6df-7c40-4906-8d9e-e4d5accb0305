using System;
using System.Text.Json;
using System.Text.Json.Nodes;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace HX.Experiment.Web.Service.Services;

/// <summary>
/// 
/// </summary>
public interface ISocketHandleService
{
    JsonNode ParseByteData( Span<byte> bytes ,JsonNode jsonNode=null);

    JsonNode ParseTextData(JsonNode jsonNode,string data);
    
    void OnReceiveUserTextMsg( string data);
    
    void OnReceiveUserBinaryMsg(Span<byte> bytes,JsonNode jsonNode=null);

    void OnConnect(IWebSocket socket);

    event Action<DeviceModel> OnDeviceConnected;
}
