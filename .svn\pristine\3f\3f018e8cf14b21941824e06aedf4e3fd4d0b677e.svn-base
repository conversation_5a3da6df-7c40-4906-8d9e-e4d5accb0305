﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UFU.CoreFX.Models;

namespace HX.Experiment.Shared.Model
{
    [DataEntity("2507290600000001")]
    public class ExperimentTask
    {

        public string Icon { get; set; }

        public string Name { get; set; }

        public TaskTypeEnum TaskType { get; set; }

        public string Description { get; set; }

        /// <summary>
        /// 可执行文件路径
        /// </summary>
        public string CommandPath { get; set; }
        
        public string Href { get; set; }
    }
}
