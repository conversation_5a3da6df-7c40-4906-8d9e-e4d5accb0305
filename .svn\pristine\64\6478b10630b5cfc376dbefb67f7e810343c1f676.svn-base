﻿using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using FluentValidation.Validators;
using HX.HRV.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
namespace HX.HRV.Web.Areas.HRV_HX.Controllers;

/// <summary>
/// 设备管理
/// </summary>
[Area("HRV_HX")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class PatientController : Controller
{
    private readonly DataRepository _context;
    private readonly CoreDbContext _coreContext;
    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// 数据管理
    /// </summary>
    /// <param name="context"></param>
    /// <param name="coreContext"></param>
    public PatientController(DataRepository context, CoreDbContext coreContext)
    {
        _context = context;
        _coreContext = coreContext;

        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.IgnoreNullValues = true;
    }


    /// <summary>
    ///详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性患者管理接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<PatientModel>>> GetPatientModelDetail(string cardId)
    {
        var patientModel = await _context.Query<PatientModel>()
            .FirstOrDefaultAsync(m => m.Data.CardId == cardId);
        return new Result<DataModel<PatientModel>>(patientModel);
    }

    /// <summary>
    /// 产品管理接口/添加
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Permission("心率变异性患者管理接口/添加", IsAPI = true)]
    public async Task<Result<DataModel<PatientModel>>> Add([FromBody] PatientModel dataModel)
    {
        UserInfo userInfo = ViewBag.User;
        
        
        
        
        
        
        if (string.IsNullOrEmpty(dataModel.OutpatientNumberString))
        {
            return new Result<DataModel<PatientModel>>("请输入被试编号!");
        }
        // else
        // {
        //     if (!CardIdCheck(dataModel.CardId.ToCharArray()))
        //     {
        //         return new Result<DataModel<PatientModel>>("身份证号格式错误!");
        //     }
        // }

        // var query = await _context.Query<PatientModel>()
        //     .FirstOrDefaultAsync(m => m.Data.OutpatientNumberString == dataModel.OutpatientNumberString);
        // if (query != null)
        // {
        //     return new Result<DataModel<PatientModel>>("该身份证号已存在!");
        // }

        if (!string.IsNullOrEmpty(dataModel.OutpatientNumberString))
        {
            var queryRecord = await _context.Query<PatientModel>()
                .FirstOrDefaultAsync(m => m.Data.OutpatientNumberString == dataModel.OutpatientNumberString);
            if (queryRecord != null)
            {
                return new Result<DataModel<PatientModel>>("该被试编号已存在!");
            }
        }


        var result = new Result<DataModel<PatientModel>>();
        dataModel.Id = UId.GetNewId();
        var model = new DataModel<PatientModel>
        {
            Data = dataModel,
            Id = UId.GetNewId(),
            OrganId = userInfo?.Organ?.GetTopOrganId(),
            UserId = userInfo?.Id,
        };
        model.Id = UId.GetNewId();
        model.AddTime = model.AddTime;
        model.UpdateTime = model.UpdateTime;
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }

        _context.Add(model);
        await _context.SaveChangesAsync();
        return result;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id">18</param>
    /// <returns></returns>
    public bool CardIdCheck(char[] id)
    {
        if (id == null || id.Length != 18)
        {
            return false;
        }

        int sum = 0;
        for (int index = 0; index < 17; index++)
        {
            if (!char.IsDigit(id[index]))
            {
                return false;
            }

            sum += (int)Math.Pow(2, 17 - index) % 11 * (id[index] - '0');
        }

        int num = (12 - sum % 11) % 11;
        return num < 10 ? num == id[17] - '0' : id[17] == 'X';
    }

    /// <summary>
    /// 产品管理接口/编辑
    /// </summary>
    /// <param name="id">编号</param>
    /// <param name="PatientModel">产品信息</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性患者管理接口/编辑", IsAPI = true)]
    public async Task<Result<DataModel<PatientModel>>> Edit(string id, [FromBody] PatientModel PatientModel)
    {
        var result = new Result<DataModel<PatientModel>>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(PatientModel, result))
        {
            return result;
        }

        var model = await _context.Query<PatientModel>().FirstOrDefaultAsync(m => m.Data.Id == id);
        if (model == null)
        {
            result.AddError("患者信息不存在");
            return result;
        }

        model.Data = PatientModel;
        _context.Update(model);
        await _context.SaveChangesAsync();
        result.Data = model;
        return result;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [Permission("心率变异性患者管理接口/列表", IsAPI = true)]
    public async Task<Result<PageList<DataModel<PatientModel>>>> GetPatientList(
        [FromQuery] PatientModel patientModel, [FromQuery] int? page = 1, [FromQuery] int? pageSize = 100)
    {
        var query = _context.Query<PatientModel>();

        if (!string.IsNullOrEmpty(patientModel.CardId?.Trim()))
        {
            patientModel.CardId = patientModel.CardId.Trim();
            query = query.Where(m => m.Data.CardId.Contains(patientModel.CardId));
        }

        if (!string.IsNullOrEmpty(patientModel.Name?.Trim()))
        {
            query = query.Where(m => m.Data.Name.Contains(patientModel.Name));
        }

        if (patientModel.Age != default)
        {
            query = query.Where(m => patientModel.Age == m.Data.Age);
        }

        if (!string.IsNullOrEmpty(patientModel.OutpatientNumberString?.Trim()))
        {
            query = query.Where(m => m.Data.OutpatientNumberString.Contains(patientModel.OutpatientNumberString));
        }

        if (patientModel.Sex != default)
        {
            query = query.Where(m => m.Data.Sex == patientModel.Sex);
        }

        if (!string.IsNullOrEmpty(patientModel.Source?.Trim()))
        {
            query = query.Where(m => m.Data.Name.Contains(patientModel.Source.Trim()));
        }
        UserInfo userInfo = ViewBag.User;

        if (!userInfo.Name.Equals("Admin", StringComparison.OrdinalIgnoreCase))
        {
            query = query.Where(m => m.UserId==userInfo.Id);
        }

        var dataModels = await query.ToPageListAsync(pageIndex: page.Value, pageSize: pageSize.Value);
        return new Result<PageList<DataModel<PatientModel>>>()
            { Success = true, Data = dataModels, Page = dataModels.PageInfo };
        ;
    }

    /// <summary>
    /// 患者管理/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性患者管理接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string id)
    {
        var result = new Result<bool>();
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<PatientModel>().FirstOrDefaultAsync(m => m.Data.Id == id);
        if (patient == null)
        {
            result.AddError("患者信息不存在");
            return result;
        }

        _context.Remove(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        return result;
    }
}