﻿using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Services;
using UFU.IoT.Shared.Models;

namespace UFU.IoT.Areas.IoT.Controllers.V2;

/// <summary>
///  命令配置
/// </summary>
[Area("IoT")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class CommandConfigController : Controller
{
    private readonly DataRepository _context;
    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// ctor
    /// </summary>
    /// <param name="context"></param>
    public CommandConfigController(DataRepository context)
    {
        _context = context;
        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    }

    [HttpGet()]
    [Permission("命令配置/列表", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<List<DataModel<CommandConfigModel>>>> GetPageList(int count, int? page,
        int? pageSize = null,
        string orderBy = "")
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Query<CommandConfigModel>();
        var list = await query
            .ToPageListAsync(page, pageSize, orderBy);
        return new Result<List<DataModel<CommandConfigModel>>>() { Success = true, Data = list,Page = list.PageInfo };
    }


    [HttpGet("{id}")]
    [Permission("命令配置/详情", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<CommandConfigModel>> GetCommandConfig(string id)
    { 
        var result = new Result<CommandConfigModel>();
        var query = _context.Query<CommandConfigModel>();
        var model = await query
            .FirstOrDefaultAsync(m => m.Id == id);
        if (model == null)
        {
            result.AddError("数据不存在");
            return result;
        }
        result.Data = model.Data;
        result.Success = true;
        return result;
    }

    /// <summary>
    /// 添加命令配置
    /// </summary>
    [HttpPost, Permission("命令配置/添加", IsAPI = true)]
    public async Task<Result<CommandConfigModel>> AddCommandConfig([FromBody] CommandConfigModel model)
    {
        var result = new Result<CommandConfigModel>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }

        UserInfo userInfo = ViewBag.User;
        var id = UId.GetNewId();
        model.Id = id;
        var datamodel = new DataModel<CommandConfigModel>()
        {
            Id = id,
            Data = model,
            OrganId = userInfo.Organ.GetTopOrganId(),
            UserId = userInfo.Id,
            AddTime = DateTime.Now,
            UpdateTime = DateTime.Now,
        };
        _context.Add(datamodel);
        await _context.SaveChangesAsync();
        result.Data = model;
        result.Success = true;
        return result;
    }

    /// <summary>
    /// 更新命令配置
    /// </summary>
    [HttpPost, Permission("命令配置/编辑", IsAPI = true)]
    public async Task<Result<CommandConfigModel>> UpdateCommandConfig([FromBody] CommandConfigModel model)
    {
        var result = new Result<CommandConfigModel>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }

        var datamodel = _context
            .Query<CommandConfigModel>()
            .FirstOrDefault(m => m.Id == model.Id);
        if (datamodel == null)
        {
            result.Success = false;
            result.Message = "数据不存在";
            return result;
        }
        datamodel.Data = model;
        datamodel.UpdateTime = DateTime.Now;
        _context.Update(datamodel);
        await _context.SaveChangesAsync();
        result.Data = model;
        result.Success = true;
        return result;
    }
       
    /// <summary>
    /// 设备类型列表
    /// </summary>
    [HttpGet]
    [Permission("命令配置/列表", IsAPI = true)]
    public async Task<Result<List<CommandConfigModel>>> GetAllCommandConfig()
    {
        var result = new Result<List<CommandConfigModel>>();
        var deviceTypes = await _context.Query<CommandConfigModel>().Select(m => m.Data).ToListAsync();
        result.Data = deviceTypes;
        return result;
    }
}