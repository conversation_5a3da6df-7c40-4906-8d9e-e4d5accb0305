﻿//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Concurrent;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using UFU.CoreFX.Data;
//using UFU.CoreFX.Models;
//using UFU.CoreFX.Permission;
//using UFU.CoreFX.Services;
//using UFU.CoreFX.Utils;
//using UFU.IoT.Models;

//namespace UFU.IoT.Services
//{
//    /// <summary>
//    /// 物联网用户WebSocket
//    /// </summary>
//    [Permission("物联网用户WebSocket", Url = "/iot/user/v1", AllowAllUser = true)]
//    public class UserWebSocket : IWebSocket
//    {
//        /// <summary>
//        /// 设备编号
//        /// </summary>
//        public List<string> DeviceIds { get; set; } = new List<string>();
//        /// <summary>
//        /// 连接
//        /// </summary>
//        public override void OnConnect()
//        {
//            if (User == null)
//            {
//                Close();
//                return;
//            }
//            GetUserDevices();
//            IoTService.Users.AddOrUpdate(ConnectId, this, (key, value) => this);
//        }
//        /// <summary>
//        /// 收到文本消息
//        /// </summary>
//        /// <param name="data"></param>
//        public override void OnReciveText(string data)
//        {
//            var wsmsg = JObject.Parse(data);
//            var method = wsmsg["Func"]?.ToString();
//            if (method == "Msg")
//            {
//                var msg = wsmsg["Data"].ToObject<Msg>();
//                if (msg?.Device?.Id != null && DeviceIds.Contains(msg.Device.Id))
//                {
//                    IoTService.ReceiveUserMsg(this, msg);
//                }
//                else
//                {
//                    var result = new WebSocketMsg1 { Func = "Error", Data = new { Code = 403, Msg = "Forbidden" } };
//                    SendTextAsync(JsonTool.SerializeIgnoreNull(result)).Wait();
//                }
//            }
//            else if (method == "Raw")
//            {
//                IoTService.ReceiveUserRawData(this, data);
//            }
//        }
//        /// <summary>
//        /// 关闭连接
//        /// </summary>
//        public override void Close()
//        {
//            IoTService.Users.TryRemove(ConnectId, out UserWebSocket uws);
//            base.Close();
//        }

//        //获取用户设备
//        private void GetUserDevices()
//        {
//            var userPlaceId = this.Context.Request.Query["userPlaceId"].FirstOrDefault();
//            var placeId = this.Context.Request.Query["placeId"].FirstOrDefault();
//            var deviceId = this.Context.Request.Query["deviceId"].FirstOrDefault();
//            var organId = this.Context.Request.Query["organId"].FirstOrDefault();
//            var organPath = this.Context.Request.Query["organPath"].FirstOrDefault();

//            if (!string.IsNullOrWhiteSpace(userPlaceId))
//            {
//                using (DataRepository db = new DataRepository(UserInfo.System))
//                {
//                    var userPlace = db.UserPlaces.Where(m => m.Id == userPlaceId && m.UserId == UserId).FirstOrDefault();
//                    if (userPlace != null)
//                    {
//                        DeviceIds = db.PlaceDevices.Where(m => m.PlaceId == userPlace.PlaceId).Select(m => m.DeviceId).ToList();
//                    }
//                }
//            }
//            else
//            {
//                using (CoreDbContext _coreContext = new CoreDbContext())
//                using (DataRepository _context = new DataRepository())
//                {
//                    ////筛选出有权限的场地或者关联的用户场地
//                    //List<string> organs = OrganService.GetMyOrganIdsAsync(User, organId, organPath, _coreContext).Result;
//                    //使用LEFT JOIN 查出所有场地及场地用户
//                    var query = from Place in _context.Places
//                                from UserPlace in _context.UserPlaces.Where(m => m.PlaceId == Place.Id && m.UserId == User.Id).DefaultIfEmpty()
//                                select new PlaceUserModel
//                                {
//                                    Id = UserPlace.Id,
//                                    Name = UserPlace.Name ?? Place.Name,
//                                    Place = Place,
//                                    PlaceId = Place.Id,
//                                    IsAdmin = UserPlace.IsAdmin,
//                                    IsDefault = UserPlace.IsDefault,
//                                    UserId = UserPlace.UserId,
//                                    AddTime = UserPlace.AddTime,
//                                    UpdateTime = UserPlace.UpdateTime,
//                                    Version = UserPlace.Version,
//                                };
//                    //query = query.Where(m => organs.Contains(m.Place.OrganId) || m.UserId == User.Id);
//                    var placeIds = query.Select(m => m.PlaceId);
//                    if (!string.IsNullOrWhiteSpace(placeId))
//                    {
//                        placeIds = placeIds.Where(m => m == placeId);
//                    }
//                    var query1 = _context.PlaceDevices.Where(m => placeIds.Contains(m.PlaceId));
//                    if (!string.IsNullOrWhiteSpace(deviceId))
//                    {
//                        query1 = query1.Where(m => m.DeviceId == deviceId);
//                    }
//                    DeviceIds = query1.Select(m => m.DeviceId).ToList();
//                }
//            }
//        }
//    }
//}
