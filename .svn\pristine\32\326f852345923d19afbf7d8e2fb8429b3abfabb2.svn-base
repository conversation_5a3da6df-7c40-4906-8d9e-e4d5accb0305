﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using UFU.CoreFX.Utils;

namespace UFU.IoT.Services
{
    /// <summary>
    /// TcpSocket服务端
    /// </summary>
    public class TcpSocketServer
    {
        private const int Backlog = 100;
        private const int BufferSize = 1024 * 10;
        readonly EndPoint localEndPoint;

        /// <summary>
        /// 服务器是否已启动
        /// </summary>
        public bool IsServerStart { get; private set; }
        /// <summary>
        /// 连接建立事件
        /// </summary>
        public event EventHandler<SocketAsyncEventArgs> OnConnected;
        /// <summary>
        /// 断开连接事件
        /// </summary>
        public event EventHandler<SocketAsyncEventArgs> OnDisconnected;
        /// <summary>
        /// 收到数据事件
        /// </summary>
        public event EventHandler<SocketAsyncEventArgs> OnReceived;
        /// <summary>
        /// 发送成功事件
        /// </summary>
        public event EventHandler<SocketAsyncEventArgs> OnSent;
        /// <summary>
        /// TcpSocket服务
        /// </summary>
        /// <param name="endPoint"></param>
        public TcpSocketServer(IPEndPoint endPoint)
        {
            localEndPoint = endPoint;
        }

        /// <summary>
        /// 开启Socket监听,死循环
        /// </summary>
        public void Start()
        {
            try
            {
                var listener = new Socket(localEndPoint.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
                listener.Bind(localEndPoint);    //绑定本地地址进行监听
                listener.Listen(Backlog);        //设置监听数量
                IsServerStart = true;
                while (true)
                {
                    try
                    {
                        Socket client = listener.Accept();
                        ProcessAccept(client);
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, "TcpSocketServer建立连接异常");
                        Task.Delay(100).Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                IsServerStart = false;
                LogTool.Logger.Error(ex, "TcpSocketServer监听异常");
            }
        }

        /// <summary>
        /// 执行异步发送消息
        /// </summary>
        /// <param name="sendArgs"></param>
        public void SendAsync(SocketAsyncEventArgs sendArgs)
        {
            bool willRaiseEvent = sendArgs.AcceptSocket.SendAsync(sendArgs);
            if (!willRaiseEvent)
            {
                ProcessSend(sendArgs);
            }
        }

        /// <summary>
        /// 关闭连接
        /// </summary>
        /// <param name="e"></param>
        public void Close(SocketAsyncEventArgs e)
        {
            try
            {
                using (e.AcceptSocket)
                {
                    if (e.AcceptSocket.Connected)
                    {
                        e.AcceptSocket.Shutdown(SocketShutdown.Both);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss:fff")}]关闭TCP连接异常：{ex.Message}");
            }

            try
            {
                OnDisconnected?.Invoke(this, e);
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, "TCP断开连接事件异常");
            }
        }

        /// <summary>
        /// 处理新连接
        /// </summary>
        /// <param name="client"></param>
        private void ProcessAccept(Socket client)
        {
            //创建一个接收对象
            SocketAsyncEventArgs receiveSaea = new SocketAsyncEventArgs();
            receiveSaea.AcceptSocket = client;
            receiveSaea.RemoteEndPoint = client.RemoteEndPoint;
            receiveSaea.SetBuffer(new byte[BufferSize], 0, BufferSize);   //设置接收缓冲区,必须设置
            receiveSaea.Completed += new EventHandler<SocketAsyncEventArgs>(IOCompleted);

            //触发连接事件
            try
            {
                OnConnected?.Invoke(this, receiveSaea);
            }
            catch { }

            //开始接收客户端数据     
            bool willRaiseEvent = receiveSaea.AcceptSocket.ReceiveAsync(receiveSaea);
            if (!willRaiseEvent)
            {
                ProcessReceived(receiveSaea);
            }
        }

        /// <summary>
        /// IO处理完成事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void IOCompleted(object sender, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.Receive:
                    ProcessReceived(e);
                    break;
                case SocketAsyncOperation.Send:
                    ProcessSend(e);
                    break;
                default:
                    Console.WriteLine("The last operation completed on the socket was not a receive or send");
                    Close(e);
                    break;
            }
        }

        /// <summary>
        /// 发送结束处理
        /// </summary>
        /// <param name="e"></param>
        private void ProcessSend(SocketAsyncEventArgs e)
        {
            try
            {
                if (e.SocketError != SocketError.Success)
                {
                    Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss:fff")}]TCP发送数据失败");
                    Close(e);
                    return;
                }

                try
                {
                    //触发事件
                    OnSent?.Invoke(this, e);
                }
                catch { }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, "ProcessSend");
            }
        }

        /// <summary>
        /// 收到消息处理
        /// </summary>
        /// <param name="e"></param>
        private void ProcessReceived(SocketAsyncEventArgs e)
        {
            try
            {
                //如果接收的字节长度为0，则判断远端服务器关闭连接
                if (e.BytesTransferred <= 0 || e.SocketError != SocketError.Success)
                {
                    //Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss:fff")}]TCP接收数据失败");
                    Close(e);
                    return;
                }

                try
                {
                    //接收数据并触发事件
                    //byte[] buffer = new byte[e.BytesTransferred];
                    //Buffer.BlockCopy(e.Buffer, 0, buffer, 0, e.BytesTransferred);      
                    OnReceived?.Invoke(this, e);
                }
                catch { }

                //异步接收下一次的数据
                bool willRaiseEvent = e.AcceptSocket.ReceiveAsync(e);
                if (!willRaiseEvent)
                {
                    ProcessReceived(e);
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, "ProcessReceived");
                Close(e);
            }
        }
    }
}
