﻿using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using HX.HRV.Shared.Models;
using UFU.IoT.Models;
using System.Diagnostics;
using DeviceId;

namespace HX.HRV.Web.Services;

public class RsaEncryptionService
{
    private RSA _publicKeyInstance;
    
    public RsaEncryptionService()
    {
        LoadPublicKeyFromPem();
        

    }
    
    public void LoadPublicKeyFromPem()
    {
        var pemContents = File.ReadAllText("hx_gen_pub_key.pem");
        if (string.IsNullOrEmpty(pemContents)) throw new ArgumentNullException(nameof(pemContents));
        _publicKeyInstance = RSA.Create();
        try
        {
            _publicKeyInstance.ImportFromPem(pemContents);
        }
        catch (Exception ex)
        {
            throw new CryptographicException($"从 PEM 导入公钥失败。错误: {ex.Message}", ex);
        }
    }
    public static bool VerifyData(string originalData, string signedData, RSA publicKeyInstance)
    {
        if (publicKeyInstance == null) throw new ArgumentNullException(nameof(publicKeyInstance));
        byte[] dataBytes = Encoding.UTF8.GetBytes(originalData);
        byte[] signatureBytes = Convert.FromBase64String(signedData);
        return publicKeyInstance.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
    }
    
    /// <summary>
    /// 验证并解析许可证密钥
    /// </summary>
    /// <param name="licenseContents"></param>
    /// <returns></returns>
    public LicenseData VerifyAndParseLicenseKey(string licenseContents)
    {
        if (string.IsNullOrEmpty(licenseContents))
        {
            return null;
        }

        string[] parts = licenseContents.Split('.');
        if (parts.Length != 2)
        {
            return null;
        }
        string base64JsonData = parts[0];
        string signature = parts[1];
        string jsonData;
        try
        {
            byte[] jsonBytes = Convert.FromBase64String(base64JsonData);
            jsonData = Encoding.UTF8.GetString(jsonBytes);
        }
        catch (FormatException ex)
        {
            return null;
        }
        bool isValid = VerifyData(jsonData, signature, _publicKeyInstance);
        if (isValid)
        {
            try
            {
                LicenseData licenseDetails = JsonSerializer.Deserialize<LicenseData>(jsonData);
                return licenseDetails;
            }
            catch (JsonException ex)
            {
                return null;
            }
        }
        else
        {
            return null;
        }
    }
}