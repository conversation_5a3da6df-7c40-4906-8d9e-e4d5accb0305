{"version": 3, "targets": {".NETCoreApp,Version=v2.2": {"Enums.NET/4.0.0": {"type": "package", "dependencies": {"System.ComponentModel.Annotations": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.0/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "FastExpressionCompiler/4.0.1": {"type": "package", "dependencies": {"System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "compile": {"lib/netstandard2.0/FastExpressionCompiler.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FastExpressionCompiler.dll": {"related": ".xml"}}}, "FluentValidation/11.8.1": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FluentValidation.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "compile": {"lib/netstandard2.0/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/2.2.0": {"type": "package", "compile": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.NETCore.App/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.8", "Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3"}, "compile": {"ref/netcoreapp2.2/Microsoft.CSharp.dll": {"related": ".xml"}, "ref/netcoreapp2.2/Microsoft.VisualBasic.dll": {"related": ".xml"}, "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.AppContext.dll": {}, "ref/netcoreapp2.2/System.Buffers.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Concurrent.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Immutable.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.NonGeneric.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Specialized.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.dll": {"related": ".Concurrent.xml;.Immutable.xml;.NonGeneric.xml;.Specialized.xml;.xml"}, "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.dll": {"related": ".Annotations.xml;.EventBasedAsync.xml;.Primitives.xml;.TypeConverter.xml;.xml"}, "ref/netcoreapp2.2/System.Configuration.dll": {}, "ref/netcoreapp2.2/System.Console.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Core.dll": {}, "ref/netcoreapp2.2/System.Data.Common.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Data.dll": {"related": ".Common.xml"}, "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Debug.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Process.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Tools.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Drawing.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Drawing.dll": {"related": ".Primitives.xml"}, "ref/netcoreapp2.2/System.Dynamic.Runtime.dll": {}, "ref/netcoreapp2.2/System.Globalization.Calendars.dll": {}, "ref/netcoreapp2.2/System.Globalization.Extensions.dll": {}, "ref/netcoreapp2.2/System.Globalization.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.Compression.dll": {"related": ".xml;.ZipFile.xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.dll": {"related": ".DriveInfo.xml;.Watcher.xml;.xml"}, "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.Pipes.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll": {}, "ref/netcoreapp2.2/System.IO.dll": {"related": ".Compression.xml;.Compression.ZipFile.xml;.FileSystem.DriveInfo.xml;.FileSystem.Watcher.xml;.FileSystem.xml;.IsolatedStorage.xml;.MemoryMappedFiles.xml;.Pipes.xml"}, "ref/netcoreapp2.2/System.Linq.Expressions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.Parallel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.Queryable.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.dll": {"related": ".Expressions.xml;.Parallel.xml;.Queryable.xml;.xml"}, "ref/netcoreapp2.2/System.Memory.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Http.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.HttpListener.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Mail.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.NameResolution.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.NetworkInformation.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Ping.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Requests.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Security.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.ServicePoint.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Sockets.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebClient.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebProxy.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebSockets.dll": {"related": ".Client.xml;.xml"}, "ref/netcoreapp2.2/System.Net.dll": {"related": ".Http.xml;.HttpListener.xml;.Mail.xml;.NameResolution.xml;.NetworkInformation.xml;.Ping.xml;.Primitives.xml;.Requests.xml;.Security.xml;.ServicePoint.xml;.Sockets.xml;.WebClient.xml;.WebHeaderCollection.xml;.WebProxy.xml;.WebSockets.Client.xml;.WebSockets.xml"}, "ref/netcoreapp2.2/System.Numerics.Vectors.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Numerics.dll": {"related": ".Vectors.xml"}, "ref/netcoreapp2.2/System.ObjectModel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.dll": {"related": ".ILGeneration.xml;.Lightweight.xml;.xml"}, "ref/netcoreapp2.2/System.Reflection.Extensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.Metadata.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.dll": {"related": ".DispatchProxy.xml;.Emit.ILGeneration.xml;.Emit.Lightweight.xml;.Emit.xml;.Metadata.xml;.Primitives.xml;.TypeExtensions.xml"}, "ref/netcoreapp2.2/System.Resources.Reader.dll": {}, "ref/netcoreapp2.2/System.Resources.ResourceManager.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Resources.Writer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Handles.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.InteropServices.dll": {"related": ".RuntimeInformation.xml;.WindowsRuntime.xml;.xml"}, "ref/netcoreapp2.2/System.Runtime.Loader.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Numerics.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.dll": {"related": ".Formatters.xml;.Json.xml;.Primitives.xml;.Xml.xml"}, "ref/netcoreapp2.2/System.Runtime.dll": {"related": ".CompilerServices.VisualC.xml;.Extensions.xml;.InteropServices.RuntimeInformation.xml;.InteropServices.WindowsRuntime.xml;.InteropServices.xml;.Loader.xml;.Numerics.xml;.Serialization.Formatters.xml;.Serialization.Json.xml;.Serialization.Primitives.xml;.Serialization.Xml.xml;.xml"}, "ref/netcoreapp2.2/System.Security.Claims.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Principal.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.SecureString.dll": {}, "ref/netcoreapp2.2/System.Security.dll": {"related": ".Claims.xml;.Cryptography.Algorithms.xml;.Cryptography.Csp.xml;.Cryptography.Encoding.xml;.Cryptography.Primitives.xml;.Cryptography.X509Certificates.xml;.Principal.xml"}, "ref/netcoreapp2.2/System.ServiceModel.Web.dll": {}, "ref/netcoreapp2.2/System.ServiceProcess.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Text.Encoding.dll": {"related": ".Extensions.xml"}, "ref/netcoreapp2.2/System.Text.RegularExpressions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Overlapped.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.dll": {"related": ".Dataflow.xml;.Extensions.xml;.Parallel.xml;.xml"}, "ref/netcoreapp2.2/System.Threading.Thread.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.ThreadPool.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Timer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.dll": {"related": ".Overlapped.xml;.Tasks.Dataflow.xml;.Tasks.Extensions.xml;.Tasks.Parallel.xml;.Tasks.xml;.Thread.xml;.ThreadPool.xml;.Timer.xml;.xml"}, "ref/netcoreapp2.2/System.Transactions.Local.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Transactions.dll": {"related": ".Local.xml"}, "ref/netcoreapp2.2/System.ValueTuple.dll": {}, "ref/netcoreapp2.2/System.Web.HttpUtility.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Web.dll": {"related": ".HttpUtility.xml"}, "ref/netcoreapp2.2/System.Windows.dll": {}, "ref/netcoreapp2.2/System.Xml.Linq.dll": {}, "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.Serialization.dll": {}, "ref/netcoreapp2.2/System.Xml.XDocument.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.XPath.dll": {"related": ".XDocument.xml;.xml"}, "ref/netcoreapp2.2/System.Xml.XmlDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.dll": {"related": ".ReaderWriter.xml;.XDocument.xml;.XmlSerializer.xml;.XPath.XDocument.xml;.XPath.xml"}, "ref/netcoreapp2.2/System.dll": {"related": ".Buffers.xml;.Collections.Concurrent.xml;.Collections.Immutable.xml;.Collections.NonGeneric.xml;.Collections.Specialized.xml;.Collections.xml;.ComponentModel.Annotations.xml;.ComponentModel.EventBasedAsync.xml;.ComponentModel.Primitives.xml;.ComponentModel.TypeConverter.xml;.ComponentModel.xml;.Console.xml;.Data.Common.xml;.Diagnostics.Contracts.xml;.Diagnostics.Debug.xml;.Diagnostics.DiagnosticSource.xml;.Diagnostics.FileVersionInfo.xml;.Diagnostics.Process.xml;.Diagnostics.StackTrace.xml;.Diagnostics.TextWriterTraceListener.xml;.Diagnostics.Tools.xml;.Diagnostics.TraceSource.xml;.Diagnostics.Tracing.xml;.Drawing.Primitives.xml;.IO.Compression.xml;.IO.Compression.ZipFile.xml;.IO.FileSystem.DriveInfo.xml;.IO.FileSystem.Watcher.xml;.IO.FileSystem.xml;.IO.IsolatedStorage.xml;.IO.MemoryMappedFiles.xml;.IO.Pipes.xml;.Linq.Expressions.xml;.Linq.Parallel.xml;.Linq.Queryable.xml;.Linq.xml;.Memory.xml;.Net.Http.xml;.Net.HttpListener.xml;.Net.Mail.xml;.Net.NameResolution.xml;.Net.NetworkInformation.xml;.Net.Ping.xml;.Net.Primitives.xml;.Net.Requests.xml;.Net.Security.xml;.Net.ServicePoint.xml;.Net.Sockets.xml;.Net.WebClient.xml;.Net.WebHeaderCollection.xml;.Net.WebProxy.xml;.Net.WebSockets.Client.xml;.Net.WebSockets.xml;.Numerics.Vectors.xml;.ObjectModel.xml;.Reflection.DispatchProxy.xml;.Reflection.Emit.ILGeneration.xml;.Reflection.Emit.Lightweight.xml;.Reflection.Emit.xml;.Reflection.Metadata.xml;.Reflection.Primitives.xml;.Reflection.TypeExtensions.xml;.Resources.ResourceManager.xml;.Resources.Writer.xml;.Runtime.CompilerServices.VisualC.xml;.Runtime.Extensions.xml;.Runtime.InteropServices.RuntimeInformation.xml;.Runtime.InteropServices.WindowsRuntime.xml;.Runtime.InteropServices.xml;.Runtime.Loader.xml;.Runtime.Numerics.xml;.Runtime.Serialization.Formatters.xml;.Runtime.Serialization.Json.xml;.Runtime.Serialization.Primitives.xml;.Runtime.Serialization.Xml.xml;.Runtime.xml;.Security.Claims.xml;.Security.Cryptography.Algorithms.xml;.Security.Cryptography.Csp.xml;.Security.Cryptography.Encoding.xml;.Security.Cryptography.Primitives.xml;.Security.Cryptography.X509Certificates.xml;.Security.Principal.xml;.Text.Encoding.Extensions.xml;.Text.RegularExpressions.xml;.Threading.Overlapped.xml;.Threading.Tasks.Dataflow.xml;.Threading.Tasks.Extensions.xml;.Threading.Tasks.Parallel.xml;.Threading.Tasks.xml;.Threading.Thread.xml;.Threading.ThreadPool.xml;.Threading.Timer.xml;.Threading.xml;.Transactions.Local.xml;.Web.HttpUtility.xml;.Xml.ReaderWriter.xml;.Xml.XDocument.xml;.Xml.XmlSerializer.xml;.Xml.XPath.XDocument.xml;.Xml.XPath.xml"}, "ref/netcoreapp2.2/WindowsBase.dll": {}, "ref/netcoreapp2.2/mscorlib.dll": {}, "ref/netcoreapp2.2/netstandard.dll": {}}, "build": {"build/netcoreapp2.2/Microsoft.NETCore.App.props": {}, "build/netcoreapp2.2/Microsoft.NETCore.App.targets": {}}}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"type": "package"}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.8"}}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.8"}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "NETStandard.Library/2.0.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"build/netstandard2.0/NETStandard.Library.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.6.0": {"type": "package", "dependencies": {"Enums.NET": "4.0.0", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.0", "Portable.BouncyCastle": "1.9.0", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0-beta18", "SixLabors.ImageSharp": "2.1.3", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".deps.json;.pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".dll.config;.pdb"}, "lib/netstandard2.0/NPOI.dll": {"related": ".OOXML.deps.json;.OOXML.pdb;.OOXML.xml;.OpenXml4Net.pdb;.OpenXmlFormats.dll.config;.OpenXmlFormats.pdb;.pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".deps.json;.pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".dll.config;.pdb"}, "lib/netstandard2.0/NPOI.dll": {"related": ".OOXML.deps.json;.OOXML.pdb;.OOXML.xml;.OpenXml4Net.pdb;.OpenXmlFormats.dll.config;.OpenXmlFormats.pdb;.pdb;.xml"}}}, "Portable.BouncyCastle/1.9.0": {"type": "package", "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}}, "RulesEngine/5.0.3": {"type": "package", "dependencies": {"FastExpressionCompiler": "4.0.1", "FluentValidation": "11.8.1", "Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "System.Linq": "4.3.0", "System.Linq.Dynamic.Core": "1.3.7", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/RulesEngine.dll": {}}, "runtime": {"lib/netstandard2.0/RulesEngine.dll": {}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "SharpZipLib/1.3.3": {"type": "package", "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0-beta18": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.IO.Compression": "4.3.0", "System.IO.UnmanagedMemoryStream": "4.3.0", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.3": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netcoreapp2.1/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.ComponentModel.Annotations/4.6.0": {"type": "package", "compile": {"ref/netstandard2.0/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.ComponentModel.Annotations.dll": {}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Formats.Asn1/6.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.UnmanagedMemoryStream/4.3.0": {"type": "package", "dependencies": {"System.Buffers": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.UnmanagedMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.UnmanagedMemoryStream.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.3.7": {"type": "package", "compile": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets": {}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/6.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Formats.Asn1": "6.0.0", "System.Memory": "4.5.4", "System.Security.Cryptography.Cng": "5.0.0"}, "compile": {"lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "compile": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets": {}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Permissions.targets": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Threading.Tasks.Parallel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.Parallel.dll": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "UFU.CoreFX/5.0.3": {"type": "package"}, "UFU.IoT.UI/2.3.1": {"type": "package", "compile": {"lib/netstandard2.0/UFU.IoT.UI.dll": {}}, "runtime": {"lib/netstandard2.0/UFU.IoT.UI.dll": {}}}, "UFU.IoT.Shared/1.0.0": {"type": "project", "framework": "Unsupported,Version=v0.0", "compile": {"bin/placeholder/UFU.IoT.Shared.dll": {}}, "runtime": {"bin/placeholder/UFU.IoT.Shared.dll": {}}}}, ".NETCoreApp,Version=v2.2/linux-x64": {"Enums.NET/4.0.0": {"type": "package", "dependencies": {"System.ComponentModel.Annotations": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.0/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "FastExpressionCompiler/4.0.1": {"type": "package", "dependencies": {"System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "compile": {"lib/netstandard2.0/FastExpressionCompiler.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FastExpressionCompiler.dll": {"related": ".xml"}}}, "FluentValidation/11.8.1": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FluentValidation.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "compile": {"lib/netstandard2.0/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/2.2.0": {"type": "package", "compile": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.NETCore.App/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.8", "Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3", "runtime.linux-x64.Microsoft.NETCore.App": "2.2.8"}, "compile": {"ref/netcoreapp2.2/Microsoft.CSharp.dll": {"related": ".xml"}, "ref/netcoreapp2.2/Microsoft.VisualBasic.dll": {"related": ".xml"}, "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.AppContext.dll": {}, "ref/netcoreapp2.2/System.Buffers.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Concurrent.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Immutable.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.NonGeneric.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Specialized.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.dll": {"related": ".Concurrent.xml;.Immutable.xml;.NonGeneric.xml;.Specialized.xml;.xml"}, "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.dll": {"related": ".Annotations.xml;.EventBasedAsync.xml;.Primitives.xml;.TypeConverter.xml;.xml"}, "ref/netcoreapp2.2/System.Configuration.dll": {}, "ref/netcoreapp2.2/System.Console.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Core.dll": {}, "ref/netcoreapp2.2/System.Data.Common.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Data.dll": {"related": ".Common.xml"}, "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Debug.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Process.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Tools.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Drawing.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Drawing.dll": {"related": ".Primitives.xml"}, "ref/netcoreapp2.2/System.Dynamic.Runtime.dll": {}, "ref/netcoreapp2.2/System.Globalization.Calendars.dll": {}, "ref/netcoreapp2.2/System.Globalization.Extensions.dll": {}, "ref/netcoreapp2.2/System.Globalization.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.Compression.dll": {"related": ".xml;.ZipFile.xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.dll": {"related": ".DriveInfo.xml;.Watcher.xml;.xml"}, "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.Pipes.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll": {}, "ref/netcoreapp2.2/System.IO.dll": {"related": ".Compression.xml;.Compression.ZipFile.xml;.FileSystem.DriveInfo.xml;.FileSystem.Watcher.xml;.FileSystem.xml;.IsolatedStorage.xml;.MemoryMappedFiles.xml;.Pipes.xml"}, "ref/netcoreapp2.2/System.Linq.Expressions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.Parallel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.Queryable.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.dll": {"related": ".Expressions.xml;.Parallel.xml;.Queryable.xml;.xml"}, "ref/netcoreapp2.2/System.Memory.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Http.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.HttpListener.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Mail.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.NameResolution.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.NetworkInformation.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Ping.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Requests.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Security.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.ServicePoint.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Sockets.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebClient.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebProxy.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebSockets.dll": {"related": ".Client.xml;.xml"}, "ref/netcoreapp2.2/System.Net.dll": {"related": ".Http.xml;.HttpListener.xml;.Mail.xml;.NameResolution.xml;.NetworkInformation.xml;.Ping.xml;.Primitives.xml;.Requests.xml;.Security.xml;.ServicePoint.xml;.Sockets.xml;.WebClient.xml;.WebHeaderCollection.xml;.WebProxy.xml;.WebSockets.Client.xml;.WebSockets.xml"}, "ref/netcoreapp2.2/System.Numerics.Vectors.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Numerics.dll": {"related": ".Vectors.xml"}, "ref/netcoreapp2.2/System.ObjectModel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.dll": {"related": ".ILGeneration.xml;.Lightweight.xml;.xml"}, "ref/netcoreapp2.2/System.Reflection.Extensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.Metadata.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.dll": {"related": ".DispatchProxy.xml;.Emit.ILGeneration.xml;.Emit.Lightweight.xml;.Emit.xml;.Metadata.xml;.Primitives.xml;.TypeExtensions.xml"}, "ref/netcoreapp2.2/System.Resources.Reader.dll": {}, "ref/netcoreapp2.2/System.Resources.ResourceManager.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Resources.Writer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Handles.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.InteropServices.dll": {"related": ".RuntimeInformation.xml;.WindowsRuntime.xml;.xml"}, "ref/netcoreapp2.2/System.Runtime.Loader.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Numerics.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.dll": {"related": ".Formatters.xml;.Json.xml;.Primitives.xml;.Xml.xml"}, "ref/netcoreapp2.2/System.Runtime.dll": {"related": ".CompilerServices.VisualC.xml;.Extensions.xml;.InteropServices.RuntimeInformation.xml;.InteropServices.WindowsRuntime.xml;.InteropServices.xml;.Loader.xml;.Numerics.xml;.Serialization.Formatters.xml;.Serialization.Json.xml;.Serialization.Primitives.xml;.Serialization.Xml.xml;.xml"}, "ref/netcoreapp2.2/System.Security.Claims.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Principal.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.SecureString.dll": {}, "ref/netcoreapp2.2/System.Security.dll": {"related": ".Claims.xml;.Cryptography.Algorithms.xml;.Cryptography.Csp.xml;.Cryptography.Encoding.xml;.Cryptography.Primitives.xml;.Cryptography.X509Certificates.xml;.Principal.xml"}, "ref/netcoreapp2.2/System.ServiceModel.Web.dll": {}, "ref/netcoreapp2.2/System.ServiceProcess.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Text.Encoding.dll": {"related": ".Extensions.xml"}, "ref/netcoreapp2.2/System.Text.RegularExpressions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Overlapped.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.dll": {"related": ".Dataflow.xml;.Extensions.xml;.Parallel.xml;.xml"}, "ref/netcoreapp2.2/System.Threading.Thread.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.ThreadPool.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Timer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.dll": {"related": ".Overlapped.xml;.Tasks.Dataflow.xml;.Tasks.Extensions.xml;.Tasks.Parallel.xml;.Tasks.xml;.Thread.xml;.ThreadPool.xml;.Timer.xml;.xml"}, "ref/netcoreapp2.2/System.Transactions.Local.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Transactions.dll": {"related": ".Local.xml"}, "ref/netcoreapp2.2/System.ValueTuple.dll": {}, "ref/netcoreapp2.2/System.Web.HttpUtility.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Web.dll": {"related": ".HttpUtility.xml"}, "ref/netcoreapp2.2/System.Windows.dll": {}, "ref/netcoreapp2.2/System.Xml.Linq.dll": {}, "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.Serialization.dll": {}, "ref/netcoreapp2.2/System.Xml.XDocument.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.XPath.dll": {"related": ".XDocument.xml;.xml"}, "ref/netcoreapp2.2/System.Xml.XmlDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.dll": {"related": ".ReaderWriter.xml;.XDocument.xml;.XmlSerializer.xml;.XPath.XDocument.xml;.XPath.xml"}, "ref/netcoreapp2.2/System.dll": {"related": ".Buffers.xml;.Collections.Concurrent.xml;.Collections.Immutable.xml;.Collections.NonGeneric.xml;.Collections.Specialized.xml;.Collections.xml;.ComponentModel.Annotations.xml;.ComponentModel.EventBasedAsync.xml;.ComponentModel.Primitives.xml;.ComponentModel.TypeConverter.xml;.ComponentModel.xml;.Console.xml;.Data.Common.xml;.Diagnostics.Contracts.xml;.Diagnostics.Debug.xml;.Diagnostics.DiagnosticSource.xml;.Diagnostics.FileVersionInfo.xml;.Diagnostics.Process.xml;.Diagnostics.StackTrace.xml;.Diagnostics.TextWriterTraceListener.xml;.Diagnostics.Tools.xml;.Diagnostics.TraceSource.xml;.Diagnostics.Tracing.xml;.Drawing.Primitives.xml;.IO.Compression.xml;.IO.Compression.ZipFile.xml;.IO.FileSystem.DriveInfo.xml;.IO.FileSystem.Watcher.xml;.IO.FileSystem.xml;.IO.IsolatedStorage.xml;.IO.MemoryMappedFiles.xml;.IO.Pipes.xml;.Linq.Expressions.xml;.Linq.Parallel.xml;.Linq.Queryable.xml;.Linq.xml;.Memory.xml;.Net.Http.xml;.Net.HttpListener.xml;.Net.Mail.xml;.Net.NameResolution.xml;.Net.NetworkInformation.xml;.Net.Ping.xml;.Net.Primitives.xml;.Net.Requests.xml;.Net.Security.xml;.Net.ServicePoint.xml;.Net.Sockets.xml;.Net.WebClient.xml;.Net.WebHeaderCollection.xml;.Net.WebProxy.xml;.Net.WebSockets.Client.xml;.Net.WebSockets.xml;.Numerics.Vectors.xml;.ObjectModel.xml;.Reflection.DispatchProxy.xml;.Reflection.Emit.ILGeneration.xml;.Reflection.Emit.Lightweight.xml;.Reflection.Emit.xml;.Reflection.Metadata.xml;.Reflection.Primitives.xml;.Reflection.TypeExtensions.xml;.Resources.ResourceManager.xml;.Resources.Writer.xml;.Runtime.CompilerServices.VisualC.xml;.Runtime.Extensions.xml;.Runtime.InteropServices.RuntimeInformation.xml;.Runtime.InteropServices.WindowsRuntime.xml;.Runtime.InteropServices.xml;.Runtime.Loader.xml;.Runtime.Numerics.xml;.Runtime.Serialization.Formatters.xml;.Runtime.Serialization.Json.xml;.Runtime.Serialization.Primitives.xml;.Runtime.Serialization.Xml.xml;.Runtime.xml;.Security.Claims.xml;.Security.Cryptography.Algorithms.xml;.Security.Cryptography.Csp.xml;.Security.Cryptography.Encoding.xml;.Security.Cryptography.Primitives.xml;.Security.Cryptography.X509Certificates.xml;.Security.Principal.xml;.Text.Encoding.Extensions.xml;.Text.RegularExpressions.xml;.Threading.Overlapped.xml;.Threading.Tasks.Dataflow.xml;.Threading.Tasks.Extensions.xml;.Threading.Tasks.Parallel.xml;.Threading.Tasks.xml;.Threading.Thread.xml;.Threading.ThreadPool.xml;.Threading.Timer.xml;.Threading.xml;.Transactions.Local.xml;.Web.HttpUtility.xml;.Xml.ReaderWriter.xml;.Xml.XDocument.xml;.Xml.XmlSerializer.xml;.Xml.XPath.XDocument.xml;.Xml.XPath.xml"}, "ref/netcoreapp2.2/WindowsBase.dll": {}, "ref/netcoreapp2.2/mscorlib.dll": {}, "ref/netcoreapp2.2/netstandard.dll": {}}, "build": {"build/netcoreapp2.2/Microsoft.NETCore.App.props": {}, "build/netcoreapp2.2/Microsoft.NETCore.App.targets": {}}}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"type": "package", "dependencies": {"runtime.linux-x64.Microsoft.NETCore.DotNetAppHost": "2.2.8"}}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.8", "runtime.linux-x64.Microsoft.NETCore.DotNetHostPolicy": "2.2.8"}}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.8", "runtime.linux-x64.Microsoft.NETCore.DotNetHostResolver": "2.2.8"}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "NETStandard.Library/2.0.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"build/netstandard2.0/NETStandard.Library.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.6.0": {"type": "package", "dependencies": {"Enums.NET": "4.0.0", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.0", "Portable.BouncyCastle": "1.9.0", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0-beta18", "SixLabors.ImageSharp": "2.1.3", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".deps.json;.pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".dll.config;.pdb"}, "lib/netstandard2.0/NPOI.dll": {"related": ".OOXML.deps.json;.OOXML.pdb;.OOXML.xml;.OpenXml4Net.pdb;.OpenXmlFormats.dll.config;.OpenXmlFormats.pdb;.pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".deps.json;.pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".dll.config;.pdb"}, "lib/netstandard2.0/NPOI.dll": {"related": ".OOXML.deps.json;.OOXML.pdb;.OOXML.xml;.OpenXml4Net.pdb;.OpenXmlFormats.dll.config;.OpenXmlFormats.pdb;.pdb;.xml"}}}, "Portable.BouncyCastle/1.9.0": {"type": "package", "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}}, "RulesEngine/5.0.3": {"type": "package", "dependencies": {"FastExpressionCompiler": "4.0.1", "FluentValidation": "11.8.1", "Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "System.Linq": "4.3.0", "System.Linq.Dynamic.Core": "1.3.7", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/RulesEngine.dll": {}}, "runtime": {"lib/netstandard2.0/RulesEngine.dll": {}}}, "runtime.linux-x64.Microsoft.NETCore.App/2.2.8": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.CSharp.dll": {"related": ".ni.{5446b4ac-9e78-4b30-93dc-c51c36d6c090}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.VisualBasic.dll": {"related": ".ni.{031a11ae-2ba5-4aa0-a923-78f3f5a75b42}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.Win32.Primitives.dll": {"related": ".ni.{a57916d6-0a0e-4632-8bc9-984f01bceb94}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.Win32.Registry.dll": {"related": ".ni.{1a52b890-2931-4de7-8b9e-7ba908853ce8}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/SOS.NETCore.dll": {"related": ".ni.{f6fbff4e-31a2-4e95-b4c4-676c01ac0d9b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.AppContext.dll": {"related": ".ni.{3a7851da-2fa5-4f41-848d-c5af73db6609}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Buffers.dll": {"related": ".ni.{c1fb38d8-63a8-4e1a-ad62-25daf8a21828}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Concurrent.dll": {"related": ".ni.{54366e28-9847-4ec1-9fd2-f5defa7bc6c9}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Immutable.dll": {"related": ".ni.{d0c3f9a5-c5ea-4d28-8354-5e981301087f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.NonGeneric.dll": {"related": ".ni.{d7558cc2-1dc6-4999-8d30-afbf9f6e16d1}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Specialized.dll": {"related": ".ni.{3808edd5-db6f-4814-b004-2fbb299f9e9b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.dll": {"related": ".Concurrent.ni.{54366e28-9847-4ec1-9fd2-f5defa7bc6c9}.map;.Immutable.ni.{d0c3f9a5-c5ea-4d28-8354-5e981301087f}.map;.ni.{3aba3dc7-64ff-4c25-ba74-5e2df535ff88}.map;.NonGeneric.ni.{d7558cc2-1dc6-4999-8d30-afbf9f6e16d1}.map;.Specialized.ni.{3808edd5-db6f-4814-b004-2fbb299f9e9b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.Annotations.dll": {"related": ".ni.{ae6fad0f-dfbb-4d86-8113-78d449943ac9}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll": {"related": ".ni.{59cd88e8-6124-4f6c-be99-ee046ba987ce}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll": {"related": ".ni.{7c12aa4b-9a58-40a2-b4eb-b9e66f9c6869}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.Primitives.dll": {"related": ".ni.{286e75f4-2705-490a-bc1a-a8fbc098d876}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.TypeConverter.dll": {"related": ".ni.{a919e8ed-ca2a-4779-be12-33c0189e4896}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.dll": {"related": ".Annotations.ni.{ae6fad0f-dfbb-4d86-8113-78d449943ac9}.map;.DataAnnotations.ni.{59cd88e8-6124-4f6c-be99-ee046ba987ce}.map;.EventBasedAsync.ni.{7c12aa4b-9a58-40a2-b4eb-b9e66f9c6869}.map;.ni.{552215b8-4ba4-4dad-8010-a16e72f6e9fc}.map;.Primitives.ni.{286e75f4-2705-490a-bc1a-a8fbc098d876}.map;.TypeConverter.ni.{a919e8ed-ca2a-4779-be12-33c0189e4896}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Configuration.dll": {"related": ".ni.{ecde7dc1-8b6b-4d81-a9d7-c629b55ee651}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Console.dll": {"related": ".ni.{5d6acd80-8ee6-4898-9a98-945168d82e65}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Core.dll": {"related": ".ni.{8c78ba34-ca25-4a12-b216-350dcb7d928c}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Data.Common.dll": {"related": ".ni.{f2c1bc24-b1b2-4e4e-b63d-aa6a4dc39253}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Data.dll": {"related": ".Common.ni.{f2c1bc24-b1b2-4e4e-b63d-aa6a4dc39253}.map;.ni.{73053d68-355d-4ec7-8cce-b4c89b682f90}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Contracts.dll": {"related": ".ni.{aa2991c5-64ab-4e3c-ba38-ea24b822d531}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Debug.dll": {"related": ".ni.{f83ea2b0-fa16-4a44-9310-c60d108d0212}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll": {"related": ".ni.{be006aed-e4aa-4b59-a375-becd975f8d54}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll": {"related": ".ni.{1450afc6-fa62-417c-ba84-ccde672e6d55}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Process.dll": {"related": ".ni.{6d49b1f8-f69e-4f71-9920-3026d4edac2a}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.StackTrace.dll": {"related": ".ni.{156a719e-9ce2-4b8e-99c1-8b67c54b1a1b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll": {"related": ".ni.{4f43da9a-5b13-46e2-95ea-26577d76fcaf}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Tools.dll": {"related": ".ni.{250d4416-6696-40f3-b63b-df67d97c68d0}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.TraceSource.dll": {"related": ".ni.{b962e1c1-9f1a-4d90-9ea6-c93aefa111aa}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Tracing.dll": {"related": ".ni.{9137fcd5-4efc-41bc-8e00-6f1f92a6eab5}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Drawing.Primitives.dll": {"related": ".ni.{634b0194-9bcb-4fd3-b173-b1aea9eaebb8}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Drawing.dll": {"related": ".ni.{a6ca4f6a-8c35-4080-8be8-3395b398253c}.map;.Primitives.ni.{634b0194-9bcb-4fd3-b173-b1aea9eaebb8}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Dynamic.Runtime.dll": {"related": ".ni.{e8714437-9a05-4b03-85ac-60dd526bde03}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.Calendars.dll": {"related": ".ni.{404fb648-5455-43c7-b116-2f0a2dbe9c90}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.Extensions.dll": {"related": ".ni.{ee7e0755-a9ad-41bd-8a2f-53fb1f3211c7}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.dll": {"related": ".Calendars.ni.{404fb648-5455-43c7-b116-2f0a2dbe9c90}.map;.Extensions.ni.{ee7e0755-a9ad-41bd-8a2f-53fb1f3211c7}.map;.ni.{52c8ed56-f56b-457c-914a-7fa98bce6383}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.Brotli.dll": {"related": ".ni.{91225069-55d0-4e2c-9151-70cdc63f47eb}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.FileSystem.dll": {"related": ".ni.{ce9039ed-d311-418d-9c57-5533b409cc90}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.ZipFile.dll": {"related": ".ni.{9e0a832a-4ad4-450f-a88e-4588d5699755}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.dll": {"related": ".Brotli.ni.{91225069-55d0-4e2c-9151-70cdc63f47eb}.map;.FileSystem.ni.{ce9039ed-d311-418d-9c57-5533b409cc90}.map;.ni.{4185ccce-85a0-49c0-a6b4-ce8eb86febfa}.map;.ZipFile.ni.{9e0a832a-4ad4-450f-a88e-4588d5699755}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.AccessControl.dll": {"related": ".ni.{fd35313f-4132-4965-af69-3379cecb562f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll": {"related": ".ni.{c3f9070a-1271-49d0-acd0-24c000809ae0}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.Primitives.dll": {"related": ".ni.{41bd4ec1-f271-4129-8ecc-1419fb644845}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.Watcher.dll": {"related": ".ni.{595565be-382f-4521-8290-d6b020b5d835}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.dll": {"related": ".AccessControl.ni.{fd35313f-4132-4965-af69-3379cecb562f}.map;.DriveInfo.ni.{c3f9070a-1271-49d0-acd0-24c000809ae0}.map;.ni.{9f88d287-1acf-4595-a2e3-9bb9acdbfcc5}.map;.Primitives.ni.{41bd4ec1-f271-4129-8ecc-1419fb644845}.map;.Watcher.ni.{595565be-382f-4521-8290-d6b020b5d835}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.IsolatedStorage.dll": {"related": ".ni.{91b52fec-9194-4481-b672-74fb90312812}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.MemoryMappedFiles.dll": {"related": ".ni.{e2a02424-48f5-4a24-9e41-50ac5839a11f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Pipes.AccessControl.dll": {"related": ".ni.{03105a94-6390-41f6-9f96-0aff4144106e}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Pipes.dll": {"related": ".AccessControl.ni.{03105a94-6390-41f6-9f96-0aff4144106e}.map;.ni.{70b2dd94-c605-4c09-bede-6ace64340d50}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll": {"related": ".ni.{ac2348c9-2c5b-4fcf-9903-7ec9dfea7402}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.dll": {"related": ".Compression.Brotli.ni.{91225069-55d0-4e2c-9151-70cdc63f47eb}.map;.Compression.FileSystem.ni.{ce9039ed-d311-418d-9c57-5533b409cc90}.map;.Compression.ni.{4185ccce-85a0-49c0-a6b4-ce8eb86febfa}.map;.Compression.ZipFile.ni.{9e0a832a-4ad4-450f-a88e-4588d5699755}.map;.FileSystem.AccessControl.ni.{fd35313f-4132-4965-af69-3379cecb562f}.map;.FileSystem.DriveInfo.ni.{c3f9070a-1271-49d0-acd0-24c000809ae0}.map;.FileSystem.ni.{9f88d287-1acf-4595-a2e3-9bb9acdbfcc5}.map;.FileSystem.Primitives.ni.{41bd4ec1-f271-4129-8ecc-1419fb644845}.map;.FileSystem.Watcher.ni.{595565be-382f-4521-8290-d6b020b5d835}.map;.IsolatedStorage.ni.{91b52fec-9194-4481-b672-74fb90312812}.map;.MemoryMappedFiles.ni.{e2a02424-48f5-4a24-9e41-50ac5839a11f}.map;.ni.{b652629a-e572-43a5-bab2-a78f9d9260a6}.map;.Pipes.AccessControl.ni.{03105a94-6390-41f6-9f96-0aff4144106e}.map;.Pipes.ni.{70b2dd94-c605-4c09-bede-6ace64340d50}.map;.UnmanagedMemoryStream.ni.{ac2348c9-2c5b-4fcf-9903-7ec9dfea7402}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Expressions.dll": {"related": ".ni.{062d78d0-7030-480d-9bc8-da39a2d972c9}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Parallel.dll": {"related": ".ni.{3cf8e3a1-ff00-4baa-8ad8-109dcedd67b2}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Queryable.dll": {"related": ".ni.{1a7ab755-6a5b-4ffd-b913-e071c92a11b2}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.dll": {"related": ".Expressions.ni.{062d78d0-7030-480d-9bc8-da39a2d972c9}.map;.ni.{ec5b84c1-fa57-4c97-a8ed-c32f4e907871}.map;.Parallel.ni.{3cf8e3a1-ff00-4baa-8ad8-109dcedd67b2}.map;.Queryable.ni.{1a7ab755-6a5b-4ffd-b913-e071c92a11b2}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Memory.dll": {"related": ".ni.{5ce2446d-fe40-485f-a7ac-4322bf8d023e}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Http.dll": {"related": ".ni.{b2e51289-34a0-4ba9-813b-48f2290b225c}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.HttpListener.dll": {"related": ".ni.{0a73e81b-2a90-4011-a662-383a8c0f60dd}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Mail.dll": {"related": ".ni.{6a8f2583-a963-43df-b5e8-452f077e8cfd}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.NameResolution.dll": {"related": ".ni.{99d82576-3a25-4823-a64e-cb4125ae3d15}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.NetworkInformation.dll": {"related": ".ni.{f1450592-0022-4c26-9638-cba7692623bb}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Ping.dll": {"related": ".ni.{8940c0a8-598c-4d87-bbd0-846e948e59de}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Primitives.dll": {"related": ".ni.{9c967619-c1aa-4d25-ad03-b042ed00c7b7}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Requests.dll": {"related": ".ni.{3ce4d211-8261-4a8f-a721-274f67b07b23}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Security.dll": {"related": ".ni.{597e4348-4977-41ba-a120-f93fd19cf149}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.ServicePoint.dll": {"related": ".ni.{8f27e0c5-c213-4b74-9e77-8c7008e04a3d}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Sockets.dll": {"related": ".ni.{47d8a4e7-a08b-4c39-8fc0-2f76b8f40f5f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebClient.dll": {"related": ".ni.{61751156-5cc1-49be-8551-0473146da16f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebHeaderCollection.dll": {"related": ".ni.{5eee16d7-3e6f-4bf7-b3f6-5e99dcee836e}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebProxy.dll": {"related": ".ni.{65269d7c-cdd4-4f7a-8c59-5bfa949ba55d}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebSockets.Client.dll": {"related": ".ni.{d1651eba-4f3a-44a6-b449-97943ca4657f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebSockets.dll": {"related": ".Client.ni.{d1651eba-4f3a-44a6-b449-97943ca4657f}.map;.ni.{af889f66-a047-4637-a282-c08dbe893ce4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.dll": {"related": ".Http.ni.{b2e51289-34a0-4ba9-813b-48f2290b225c}.map;.HttpListener.ni.{0a73e81b-2a90-4011-a662-383a8c0f60dd}.map;.Mail.ni.{6a8f2583-a963-43df-b5e8-452f077e8cfd}.map;.NameResolution.ni.{99d82576-3a25-4823-a64e-cb4125ae3d15}.map;.NetworkInformation.ni.{f1450592-0022-4c26-9638-cba7692623bb}.map;.ni.{958fd545-869a-4b20-a988-5a80d875375c}.map;.Ping.ni.{8940c0a8-598c-4d87-bbd0-846e948e59de}.map;.Primitives.ni.{9c967619-c1aa-4d25-ad03-b042ed00c7b7}.map;.Requests.ni.{3ce4d211-8261-4a8f-a721-274f67b07b23}.map;.Security.ni.{597e4348-4977-41ba-a120-f93fd19cf149}.map;.ServicePoint.ni.{8f27e0c5-c213-4b74-9e77-8c7008e04a3d}.map;.Sockets.ni.{47d8a4e7-a08b-4c39-8fc0-2f76b8f40f5f}.map;.WebClient.ni.{61751156-5cc1-49be-8551-0473146da16f}.map;.WebHeaderCollection.ni.{5eee16d7-3e6f-4bf7-b3f6-5e99dcee836e}.map;.WebProxy.ni.{65269d7c-cdd4-4f7a-8c59-5bfa949ba55d}.map;.WebSockets.Client.ni.{d1651eba-4f3a-44a6-b449-97943ca4657f}.map;.WebSockets.ni.{af889f66-a047-4637-a282-c08dbe893ce4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Numerics.Vectors.dll": {"related": ".ni.{23bb31ce-879b-4e44-a25f-0832b68553aa}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Numerics.dll": {"related": ".ni.{25eb404a-7669-411d-ad41-6b4d138acd7d}.map;.Vectors.ni.{23bb31ce-879b-4e44-a25f-0832b68553aa}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ObjectModel.dll": {"related": ".ni.{6b322332-a4ef-4bd3-bf81-9b0de8c67ab0}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.DataContractSerialization.dll": {"related": ".ni.{5d178b45-4654-453d-b3c9-73c7eec4924d}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Uri.dll": {"related": ".ni.{b2591560-5aa5-49fd-8e4d-b04f9f0b82ad}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Xml.Linq.dll": {"related": ".ni.{b1af64a0-e893-4f39-8d35-46be00f04e23}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Xml.dll": {"related": ".Linq.ni.{b1af64a0-e893-4f39-8d35-46be00f04e23}.map;.ni.{491da070-17e9-44aa-8cdc-738318374574}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.DispatchProxy.dll": {"related": ".ni.{b873e227-bfab-4b4c-8789-9d53e3a66b08}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll": {"related": ".ni.{b15af431-5bdb-4646-a5ec-7255b8d45c2e}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll": {"related": ".ni.{4ef94769-d2d3-4a2a-bb71-dd1759e9c853}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.dll": {"related": ".ILGeneration.ni.{b15af431-5bdb-4646-a5ec-7255b8d45c2e}.map;.Lightweight.ni.{4ef94769-d2d3-4a2a-bb71-dd1759e9c853}.map;.ni.{659d953e-aafc-4bf1-ae68-295ac449fbc2}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Extensions.dll": {"related": ".ni.{03a3aac4-3683-4b3b-8ecc-efe571ad5892}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Metadata.dll": {"related": ".ni.{aeaf0e2c-5d86-46d8-bfdb-8e5f8221eb1b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Primitives.dll": {"related": ".ni.{3a43fcff-54f8-45e3-b1ff-6dafb34bb183}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.TypeExtensions.dll": {"related": ".ni.{cb01e343-31a3-4216-86f6-d83da360063c}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.dll": {"related": ".DispatchProxy.ni.{b873e227-bfab-4b4c-8789-9d53e3a66b08}.map;.Emit.ILGeneration.ni.{b15af431-5bdb-4646-a5ec-7255b8d45c2e}.map;.Emit.Lightweight.ni.{4ef94769-d2d3-4a2a-bb71-dd1759e9c853}.map;.Emit.ni.{659d953e-aafc-4bf1-ae68-295ac449fbc2}.map;.Extensions.ni.{03a3aac4-3683-4b3b-8ecc-efe571ad5892}.map;.Metadata.ni.{aeaf0e2c-5d86-46d8-bfdb-8e5f8221eb1b}.map;.ni.{a7a8aec7-2851-4307-ac68-0b4795954ea9}.map;.Primitives.ni.{3a43fcff-54f8-45e3-b1ff-6dafb34bb183}.map;.TypeExtensions.ni.{cb01e343-31a3-4216-86f6-d83da360063c}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.Reader.dll": {"related": ".ni.{9ddfc97b-786d-4a0c-8dfa-b179ea6e7adb}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.ResourceManager.dll": {"related": ".ni.{f1a2dbb4-9243-4525-ba41-a827b1484a03}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.Writer.dll": {"related": ".ni.{dafa6c70-40b1-475e-bbc5-9a820c7a4733}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll": {"related": ".ni.{962ae5ca-5d61-495a-84f1-d063859d03b1}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Extensions.dll": {"related": ".ni.{496d5832-3672-42de-82e5-f0937829480f}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Handles.dll": {"related": ".ni.{edd0a82f-ed63-4ebe-8331-9417dd502adc}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll": {"related": ".ni.{28f7985c-42ed-4e63-838e-4b21fdbf8f06}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll": {"related": ".ni.{2bb0b44a-a28e-4849-a6dd-a769da96e867}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.dll": {"related": ".ni.{860ea5a4-0966-4a36-ab8f-f5880498802d}.map;.RuntimeInformation.ni.{28f7985c-42ed-4e63-838e-4b21fdbf8f06}.map;.WindowsRuntime.ni.{2bb0b44a-a28e-4849-a6dd-a769da96e867}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Loader.dll": {"related": ".ni.{466cb8c6-f5f8-4036-8b28-dfa30e290d14}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Numerics.dll": {"related": ".ni.{8ef36233-dde0-4376-b7ce-6717c3d2df62}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll": {"related": ".ni.{6eb5f4e1-03fd-434f-bd36-0f9a2df3e21b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Json.dll": {"related": ".ni.{fb898814-c753-4216-bf57-0f5bdc02f950}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll": {"related": ".ni.{0add7c60-c3c5-4981-a6e3-58bfaed57dd1}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Xml.dll": {"related": ".ni.{b3e06eb4-7f0c-4d18-8926-0c7e2df33768}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.dll": {"related": ".Formatters.ni.{6eb5f4e1-03fd-434f-bd36-0f9a2df3e21b}.map;.Json.ni.{fb898814-c753-4216-bf57-0f5bdc02f950}.map;.ni.{fbd03e7a-08d4-4fbf-b04e-f66767c5175d}.map;.Primitives.ni.{0add7c60-c3c5-4981-a6e3-58bfaed57dd1}.map;.Xml.ni.{b3e06eb4-7f0c-4d18-8926-0c7e2df33768}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.dll": {"related": ".CompilerServices.VisualC.ni.{962ae5ca-5d61-495a-84f1-d063859d03b1}.map;.Extensions.ni.{496d5832-3672-42de-82e5-f0937829480f}.map;.Handles.ni.{edd0a82f-ed63-4ebe-8331-9417dd502adc}.map;.InteropServices.ni.{860ea5a4-0966-4a36-ab8f-f5880498802d}.map;.InteropServices.RuntimeInformation.ni.{28f7985c-42ed-4e63-838e-4b21fdbf8f06}.map;.InteropServices.WindowsRuntime.ni.{2bb0b44a-a28e-4849-a6dd-a769da96e867}.map;.Loader.ni.{466cb8c6-f5f8-4036-8b28-dfa30e290d14}.map;.ni.{248b9ac7-a403-473e-9e7c-41b45addf1e1}.map;.Numerics.ni.{8ef36233-dde0-4376-b7ce-6717c3d2df62}.map;.Serialization.Formatters.ni.{6eb5f4e1-03fd-434f-bd36-0f9a2df3e21b}.map;.Serialization.Json.ni.{fb898814-c753-4216-bf57-0f5bdc02f950}.map;.Serialization.ni.{fbd03e7a-08d4-4fbf-b04e-f66767c5175d}.map;.Serialization.Primitives.ni.{0add7c60-c3c5-4981-a6e3-58bfaed57dd1}.map;.Serialization.Xml.ni.{b3e06eb4-7f0c-4d18-8926-0c7e2df33768}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.AccessControl.dll": {"related": ".ni.{eb01bf40-20fd-4050-acc6-2ce4dd92f302}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Claims.dll": {"related": ".ni.{c4c249e6-d234-426a-88fd-d7259eb56ecd}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll": {"related": ".ni.{87d0509f-685c-4064-834f-67a67cf9f715}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Cng.dll": {"related": ".ni.{06abdb3e-c613-4a0e-8c4c-0834ec48cbf7}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Csp.dll": {"related": ".ni.{15232058-4ed5-48dd-99e5-09dc303a5be9}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Encoding.dll": {"related": ".ni.{22ff94a2-c18e-4689-b210-60aecc7186b5}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.OpenSsl.dll": {"related": ".ni.{6d3bc01f-b826-49e6-a9d4-763be48caf23}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Primitives.dll": {"related": ".ni.{e36a71fc-b7f9-401a-b4de-a93f048e79e0}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll": {"related": ".ni.{be508089-8185-402b-8e4d-39c1697747f3}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Principal.Windows.dll": {"related": ".ni.{5359ed79-ade5-4464-b6a1-ce0c87fc3c09}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Principal.dll": {"related": ".ni.{1669d8a0-1a28-46f1-b4fc-7f5346dde322}.map;.Windows.ni.{5359ed79-ade5-4464-b6a1-ce0c87fc3c09}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.SecureString.dll": {"related": ".ni.{e484103d-605d-4d33-96eb-de2c49346805}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.dll": {"related": ".AccessControl.ni.{eb01bf40-20fd-4050-acc6-2ce4dd92f302}.map;.Claims.ni.{c4c249e6-d234-426a-88fd-d7259eb56ecd}.map;.Cryptography.Algorithms.ni.{87d0509f-685c-4064-834f-67a67cf9f715}.map;.Cryptography.Cng.ni.{06abdb3e-c613-4a0e-8c4c-0834ec48cbf7}.map;.Cryptography.Csp.ni.{15232058-4ed5-48dd-99e5-09dc303a5be9}.map;.Cryptography.Encoding.ni.{22ff94a2-c18e-4689-b210-60aecc7186b5}.map;.Cryptography.OpenSsl.ni.{6d3bc01f-b826-49e6-a9d4-763be48caf23}.map;.Cryptography.Primitives.ni.{e36a71fc-b7f9-401a-b4de-a93f048e79e0}.map;.Cryptography.X509Certificates.ni.{be508089-8185-402b-8e4d-39c1697747f3}.map;.ni.{10b474ee-e56c-421b-bc25-1e6a1c0d6043}.map;.Principal.ni.{1669d8a0-1a28-46f1-b4fc-7f5346dde322}.map;.Principal.Windows.ni.{5359ed79-ade5-4464-b6a1-ce0c87fc3c09}.map;.SecureString.ni.{e484103d-605d-4d33-96eb-de2c49346805}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ServiceModel.Web.dll": {"related": ".ni.{8afa0f81-64bb-47a0-a857-4fee301671f0}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ServiceProcess.dll": {"related": ".ni.{75e1d847-328b-46b6-a5b1-09aa70158fbc}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.Encoding.Extensions.dll": {"related": ".ni.{9681e200-823e-481f-a3ef-4371d5720816}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.Encoding.dll": {"related": ".Extensions.ni.{9681e200-823e-481f-a3ef-4371d5720816}.map;.ni.{f22a369d-6448-4e52-a0c3-9ed29d716d16}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.RegularExpressions.dll": {"related": ".ni.{bf365ffb-cb72-4cea-95c5-17fd8fe7b177}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Overlapped.dll": {"related": ".ni.{80f01b78-96ac-4d7a-b4e2-acdb55b59e28}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll": {"related": ".ni.{0727fe8d-7820-4fcb-919b-572f8030a1e4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Extensions.dll": {"related": ".ni.{43aacac2-4f91-47dc-b83c-cc52cef06f47}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Parallel.dll": {"related": ".ni.{fb77da04-cd2d-42a5-baee-e1d9df7568d9}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.dll": {"related": ".Dataflow.ni.{0727fe8d-7820-4fcb-919b-572f8030a1e4}.map;.Extensions.ni.{43aacac2-4f91-47dc-b83c-cc52cef06f47}.map;.ni.{7a25ce6e-844f-47d3-b3c6-3e36ebd57535}.map;.Parallel.ni.{fb77da04-cd2d-42a5-baee-e1d9df7568d9}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Thread.dll": {"related": ".ni.{f90a6cb4-54a7-4c9f-87ea-25ed0ce39f64}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.ThreadPool.dll": {"related": ".ni.{8e2767c6-29f5-43d0-bbab-1993cc512fe8}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Timer.dll": {"related": ".ni.{0571fa8c-c382-467f-9459-9ac90ab204ae}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.dll": {"related": ".ni.{f2f1c0ce-24de-4331-a38c-2c487f37be75}.map;.Overlapped.ni.{80f01b78-96ac-4d7a-b4e2-acdb55b59e28}.map;.Tasks.Dataflow.ni.{0727fe8d-7820-4fcb-919b-572f8030a1e4}.map;.Tasks.Extensions.ni.{43aacac2-4f91-47dc-b83c-cc52cef06f47}.map;.Tasks.ni.{7a25ce6e-844f-47d3-b3c6-3e36ebd57535}.map;.Tasks.Parallel.ni.{fb77da04-cd2d-42a5-baee-e1d9df7568d9}.map;.Thread.ni.{f90a6cb4-54a7-4c9f-87ea-25ed0ce39f64}.map;.ThreadPool.ni.{8e2767c6-29f5-43d0-bbab-1993cc512fe8}.map;.Timer.ni.{0571fa8c-c382-467f-9459-9ac90ab204ae}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Transactions.Local.dll": {"related": ".ni.{b1afe66c-1c66-48cc-bf0c-250a80d533e3}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Transactions.dll": {"related": ".Local.ni.{b1afe66c-1c66-48cc-bf0c-250a80d533e3}.map;.ni.{e34512ea-c4fd-481d-9ed4-c50c470ad637}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.ValueTuple.dll": {"related": ".ni.{53c64b34-741b-43ef-a103-19c163cbc08b}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Web.HttpUtility.dll": {"related": ".ni.{32e6b061-d82b-423f-b5bb-e4f3117a6ce5}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Web.dll": {"related": ".HttpUtility.ni.{32e6b061-d82b-423f-b5bb-e4f3117a6ce5}.map;.ni.{3d2b9612-ed6a-4e8b-8e8c-48d01547c414}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Windows.dll": {"related": ".ni.{6b1287f0-7485-4bd0-9ff4-fc0302baf186}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.Linq.dll": {"related": ".ni.{de49ee51-7175-4a1c-b5c8-b75e424b8503}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.ReaderWriter.dll": {"related": ".ni.{2cf751a4-1a96-44a5-932d-d926bc8311ea}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.Serialization.dll": {"related": ".ni.{7e495f1e-9e57-4b23-acd9-6e1d93d1789c}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XDocument.dll": {"related": ".ni.{3ca9a3fd-5150-4f45-8f8b-014f2f9ba5a1}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XPath.XDocument.dll": {"related": ".ni.{300ef4f1-6391-4cde-966c-31b686d959b4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XPath.dll": {"related": ".ni.{4f87680f-1a23-4e5a-9c3d-b09c30430aad}.map;.XDocument.ni.{300ef4f1-6391-4cde-966c-31b686d959b4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XmlDocument.dll": {"related": ".ni.{516f65fa-c1bd-4693-abf3-18147520f75d}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XmlSerializer.dll": {"related": ".ni.{b8b08991-e6bc-4d85-a194-858135944317}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.dll": {"related": ".Linq.ni.{de49ee51-7175-4a1c-b5c8-b75e424b8503}.map;.ni.{b479a61e-8614-478e-88a9-854e59e3c823}.map;.ReaderWriter.ni.{2cf751a4-1a96-44a5-932d-d926bc8311ea}.map;.Serialization.ni.{7e495f1e-9e57-4b23-acd9-6e1d93d1789c}.map;.XDocument.ni.{3ca9a3fd-5150-4f45-8f8b-014f2f9ba5a1}.map;.XmlDocument.ni.{516f65fa-c1bd-4693-abf3-18147520f75d}.map;.XmlSerializer.ni.{b8b08991-e6bc-4d85-a194-858135944317}.map;.XPath.ni.{4f87680f-1a23-4e5a-9c3d-b09c30430aad}.map;.XPath.XDocument.ni.{300ef4f1-6391-4cde-966c-31b686d959b4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/System.dll": {"related": ".AppContext.ni.{3a7851da-2fa5-4f41-848d-c5af73db6609}.map;.Buffers.ni.{c1fb38d8-63a8-4e1a-ad62-25daf8a21828}.map;.Collections.Concurrent.ni.{54366e28-9847-4ec1-9fd2-f5defa7bc6c9}.map;.Collections.Immutable.ni.{d0c3f9a5-c5ea-4d28-8354-5e981301087f}.map;.Collections.ni.{3aba3dc7-64ff-4c25-ba74-5e2df535ff88}.map;.Collections.NonGeneric.ni.{d7558cc2-1dc6-4999-8d30-afbf9f6e16d1}.map;.Collections.Specialized.ni.{3808edd5-db6f-4814-b004-2fbb299f9e9b}.map;.ComponentModel.Annotations.ni.{ae6fad0f-dfbb-4d86-8113-78d449943ac9}.map;.ComponentModel.DataAnnotations.ni.{59cd88e8-6124-4f6c-be99-ee046ba987ce}.map;.ComponentModel.EventBasedAsync.ni.{7c12aa4b-9a58-40a2-b4eb-b9e66f9c6869}.map;.ComponentModel.ni.{552215b8-4ba4-4dad-8010-a16e72f6e9fc}.map;.ComponentModel.Primitives.ni.{286e75f4-2705-490a-bc1a-a8fbc098d876}.map;.ComponentModel.TypeConverter.ni.{a919e8ed-ca2a-4779-be12-33c0189e4896}.map;.Configuration.ni.{ecde7dc1-8b6b-4d81-a9d7-c629b55ee651}.map;.Console.ni.{5d6acd80-8ee6-4898-9a98-945168d82e65}.map;.Core.ni.{8c78ba34-ca25-4a12-b216-350dcb7d928c}.map;.Data.Common.ni.{f2c1bc24-b1b2-4e4e-b63d-aa6a4dc39253}.map;.Data.ni.{73053d68-355d-4ec7-8cce-b4c89b682f90}.map;.Diagnostics.Contracts.ni.{aa2991c5-64ab-4e3c-ba38-ea24b822d531}.map;.Diagnostics.Debug.ni.{f83ea2b0-fa16-4a44-9310-c60d108d0212}.map;.Diagnostics.DiagnosticSource.ni.{be006aed-e4aa-4b59-a375-becd975f8d54}.map;.Diagnostics.FileVersionInfo.ni.{1450afc6-fa62-417c-ba84-ccde672e6d55}.map;.Diagnostics.Process.ni.{6d49b1f8-f69e-4f71-9920-3026d4edac2a}.map;.Diagnostics.StackTrace.ni.{156a719e-9ce2-4b8e-99c1-8b67c54b1a1b}.map;.Diagnostics.TextWriterTraceListener.ni.{4f43da9a-5b13-46e2-95ea-26577d76fcaf}.map;.Diagnostics.Tools.ni.{250d4416-6696-40f3-b63b-df67d97c68d0}.map;.Diagnostics.TraceSource.ni.{b962e1c1-9f1a-4d90-9ea6-c93aefa111aa}.map;.Diagnostics.Tracing.ni.{9137fcd5-4efc-41bc-8e00-6f1f92a6eab5}.map;.Drawing.ni.{a6ca4f6a-8c35-4080-8be8-3395b398253c}.map;.Drawing.Primitives.ni.{634b0194-9bcb-4fd3-b173-b1aea9eaebb8}.map;.Dynamic.Runtime.ni.{e8714437-9a05-4b03-85ac-60dd526bde03}.map;.Globalization.Calendars.ni.{404fb648-5455-43c7-b116-2f0a2dbe9c90}.map;.Globalization.Extensions.ni.{ee7e0755-a9ad-41bd-8a2f-53fb1f3211c7}.map;.Globalization.ni.{52c8ed56-f56b-457c-914a-7fa98bce6383}.map;.IO.Compression.Brotli.ni.{91225069-55d0-4e2c-9151-70cdc63f47eb}.map;.IO.Compression.FileSystem.ni.{ce9039ed-d311-418d-9c57-5533b409cc90}.map;.IO.Compression.ni.{4185ccce-85a0-49c0-a6b4-ce8eb86febfa}.map;.IO.Compression.ZipFile.ni.{9e0a832a-4ad4-450f-a88e-4588d5699755}.map;.IO.FileSystem.AccessControl.ni.{fd35313f-4132-4965-af69-3379cecb562f}.map;.IO.FileSystem.DriveInfo.ni.{c3f9070a-1271-49d0-acd0-24c000809ae0}.map;.IO.FileSystem.ni.{9f88d287-1acf-4595-a2e3-9bb9acdbfcc5}.map;.IO.FileSystem.Primitives.ni.{41bd4ec1-f271-4129-8ecc-1419fb644845}.map;.IO.FileSystem.Watcher.ni.{595565be-382f-4521-8290-d6b020b5d835}.map;.IO.IsolatedStorage.ni.{91b52fec-9194-4481-b672-74fb90312812}.map;.IO.MemoryMappedFiles.ni.{e2a02424-48f5-4a24-9e41-50ac5839a11f}.map;.IO.ni.{b652629a-e572-43a5-bab2-a78f9d9260a6}.map;.IO.Pipes.AccessControl.ni.{03105a94-6390-41f6-9f96-0aff4144106e}.map;.IO.Pipes.ni.{70b2dd94-c605-4c09-bede-6ace64340d50}.map;.IO.UnmanagedMemoryStream.ni.{ac2348c9-2c5b-4fcf-9903-7ec9dfea7402}.map;.Linq.Expressions.ni.{062d78d0-7030-480d-9bc8-da39a2d972c9}.map;.Linq.ni.{ec5b84c1-fa57-4c97-a8ed-c32f4e907871}.map;.Linq.Parallel.ni.{3cf8e3a1-ff00-4baa-8ad8-109dcedd67b2}.map;.Linq.Queryable.ni.{1a7ab755-6a5b-4ffd-b913-e071c92a11b2}.map;.Memory.ni.{5ce2446d-fe40-485f-a7ac-4322bf8d023e}.map;.Net.Http.ni.{b2e51289-34a0-4ba9-813b-48f2290b225c}.map;.Net.HttpListener.ni.{0a73e81b-2a90-4011-a662-383a8c0f60dd}.map;.Net.Mail.ni.{6a8f2583-a963-43df-b5e8-452f077e8cfd}.map;.Net.NameResolution.ni.{99d82576-3a25-4823-a64e-cb4125ae3d15}.map;.Net.NetworkInformation.ni.{f1450592-0022-4c26-9638-cba7692623bb}.map;.Net.ni.{958fd545-869a-4b20-a988-5a80d875375c}.map;.Net.Ping.ni.{8940c0a8-598c-4d87-bbd0-846e948e59de}.map;.Net.Primitives.ni.{9c967619-c1aa-4d25-ad03-b042ed00c7b7}.map;.Net.Requests.ni.{3ce4d211-8261-4a8f-a721-274f67b07b23}.map;.Net.Security.ni.{597e4348-4977-41ba-a120-f93fd19cf149}.map;.Net.ServicePoint.ni.{8f27e0c5-c213-4b74-9e77-8c7008e04a3d}.map;.Net.Sockets.ni.{47d8a4e7-a08b-4c39-8fc0-2f76b8f40f5f}.map;.Net.WebClient.ni.{61751156-5cc1-49be-8551-0473146da16f}.map;.Net.WebHeaderCollection.ni.{5eee16d7-3e6f-4bf7-b3f6-5e99dcee836e}.map;.Net.WebProxy.ni.{65269d7c-cdd4-4f7a-8c59-5bfa949ba55d}.map;.Net.WebSockets.Client.ni.{d1651eba-4f3a-44a6-b449-97943ca4657f}.map;.Net.WebSockets.ni.{af889f66-a047-4637-a282-c08dbe893ce4}.map;.ni.{aef768c7-c1ac-4dd5-bf87-1bdca558f74e}.map;.Numerics.ni.{25eb404a-7669-411d-ad41-6b4d138acd7d}.map;.Numerics.Vectors.ni.{23bb31ce-879b-4e44-a25f-0832b68553aa}.map;.ObjectModel.ni.{6b322332-a4ef-4bd3-bf81-9b0de8c67ab0}.map;.Private.DataContractSerialization.ni.{5d178b45-4654-453d-b3c9-73c7eec4924d}.map;.Private.Uri.ni.{b2591560-5aa5-49fd-8e4d-b04f9f0b82ad}.map;.Private.Xml.Linq.ni.{b1af64a0-e893-4f39-8d35-46be00f04e23}.map;.Private.Xml.ni.{491da070-17e9-44aa-8cdc-738318374574}.map;.Reflection.DispatchProxy.ni.{b873e227-bfab-4b4c-8789-9d53e3a66b08}.map;.Reflection.Emit.ILGeneration.ni.{b15af431-5bdb-4646-a5ec-7255b8d45c2e}.map;.Reflection.Emit.Lightweight.ni.{4ef94769-d2d3-4a2a-bb71-dd1759e9c853}.map;.Reflection.Emit.ni.{659d953e-aafc-4bf1-ae68-295ac449fbc2}.map;.Reflection.Extensions.ni.{03a3aac4-3683-4b3b-8ecc-efe571ad5892}.map;.Reflection.Metadata.ni.{aeaf0e2c-5d86-46d8-bfdb-8e5f8221eb1b}.map;.Reflection.ni.{a7a8aec7-2851-4307-ac68-0b4795954ea9}.map;.Reflection.Primitives.ni.{3a43fcff-54f8-45e3-b1ff-6dafb34bb183}.map;.Reflection.TypeExtensions.ni.{cb01e343-31a3-4216-86f6-d83da360063c}.map;.Resources.Reader.ni.{9ddfc97b-786d-4a0c-8dfa-b179ea6e7adb}.map;.Resources.ResourceManager.ni.{f1a2dbb4-9243-4525-ba41-a827b1484a03}.map;.Resources.Writer.ni.{dafa6c70-40b1-475e-bbc5-9a820c7a4733}.map;.Runtime.CompilerServices.VisualC.ni.{962ae5ca-5d61-495a-84f1-d063859d03b1}.map;.Runtime.Extensions.ni.{496d5832-3672-42de-82e5-f0937829480f}.map;.Runtime.Handles.ni.{edd0a82f-ed63-4ebe-8331-9417dd502adc}.map;.Runtime.InteropServices.ni.{860ea5a4-0966-4a36-ab8f-f5880498802d}.map;.Runtime.InteropServices.RuntimeInformation.ni.{28f7985c-42ed-4e63-838e-4b21fdbf8f06}.map;.Runtime.InteropServices.WindowsRuntime.ni.{2bb0b44a-a28e-4849-a6dd-a769da96e867}.map;.Runtime.Loader.ni.{466cb8c6-f5f8-4036-8b28-dfa30e290d14}.map;.Runtime.ni.{248b9ac7-a403-473e-9e7c-41b45addf1e1}.map;.Runtime.Numerics.ni.{8ef36233-dde0-4376-b7ce-6717c3d2df62}.map;.Runtime.Serialization.Formatters.ni.{6eb5f4e1-03fd-434f-bd36-0f9a2df3e21b}.map;.Runtime.Serialization.Json.ni.{fb898814-c753-4216-bf57-0f5bdc02f950}.map;.Runtime.Serialization.ni.{fbd03e7a-08d4-4fbf-b04e-f66767c5175d}.map;.Runtime.Serialization.Primitives.ni.{0add7c60-c3c5-4981-a6e3-58bfaed57dd1}.map;.Runtime.Serialization.Xml.ni.{b3e06eb4-7f0c-4d18-8926-0c7e2df33768}.map;.Security.AccessControl.ni.{eb01bf40-20fd-4050-acc6-2ce4dd92f302}.map;.Security.Claims.ni.{c4c249e6-d234-426a-88fd-d7259eb56ecd}.map;.Security.Cryptography.Algorithms.ni.{87d0509f-685c-4064-834f-67a67cf9f715}.map;.Security.Cryptography.Cng.ni.{06abdb3e-c613-4a0e-8c4c-0834ec48cbf7}.map;.Security.Cryptography.Csp.ni.{15232058-4ed5-48dd-99e5-09dc303a5be9}.map;.Security.Cryptography.Encoding.ni.{22ff94a2-c18e-4689-b210-60aecc7186b5}.map;.Security.Cryptography.OpenSsl.ni.{6d3bc01f-b826-49e6-a9d4-763be48caf23}.map;.Security.Cryptography.Primitives.ni.{e36a71fc-b7f9-401a-b4de-a93f048e79e0}.map;.Security.Cryptography.X509Certificates.ni.{be508089-8185-402b-8e4d-39c1697747f3}.map;.Security.ni.{10b474ee-e56c-421b-bc25-1e6a1c0d6043}.map;.Security.Principal.ni.{1669d8a0-1a28-46f1-b4fc-7f5346dde322}.map;.Security.Principal.Windows.ni.{5359ed79-ade5-4464-b6a1-ce0c87fc3c09}.map;.Security.SecureString.ni.{e484103d-605d-4d33-96eb-de2c49346805}.map;.ServiceModel.Web.ni.{8afa0f81-64bb-47a0-a857-4fee301671f0}.map;.ServiceProcess.ni.{75e1d847-328b-46b6-a5b1-09aa70158fbc}.map;.Text.Encoding.Extensions.ni.{9681e200-823e-481f-a3ef-4371d5720816}.map;.Text.Encoding.ni.{f22a369d-6448-4e52-a0c3-9ed29d716d16}.map;.Text.RegularExpressions.ni.{bf365ffb-cb72-4cea-95c5-17fd8fe7b177}.map;.Threading.ni.{f2f1c0ce-24de-4331-a38c-2c487f37be75}.map;.Threading.Overlapped.ni.{80f01b78-96ac-4d7a-b4e2-acdb55b59e28}.map;.Threading.Tasks.Dataflow.ni.{0727fe8d-7820-4fcb-919b-572f8030a1e4}.map;.Threading.Tasks.Extensions.ni.{43aacac2-4f91-47dc-b83c-cc52cef06f47}.map;.Threading.Tasks.ni.{7a25ce6e-844f-47d3-b3c6-3e36ebd57535}.map;.Threading.Tasks.Parallel.ni.{fb77da04-cd2d-42a5-baee-e1d9df7568d9}.map;.Threading.Thread.ni.{f90a6cb4-54a7-4c9f-87ea-25ed0ce39f64}.map;.Threading.ThreadPool.ni.{8e2767c6-29f5-43d0-bbab-1993cc512fe8}.map;.Threading.Timer.ni.{0571fa8c-c382-467f-9459-9ac90ab204ae}.map;.Transactions.Local.ni.{b1afe66c-1c66-48cc-bf0c-250a80d533e3}.map;.Transactions.ni.{e34512ea-c4fd-481d-9ed4-c50c470ad637}.map;.ValueTuple.ni.{53c64b34-741b-43ef-a103-19c163cbc08b}.map;.Web.HttpUtility.ni.{32e6b061-d82b-423f-b5bb-e4f3117a6ce5}.map;.Web.ni.{3d2b9612-ed6a-4e8b-8e8c-48d01547c414}.map;.Windows.ni.{6b1287f0-7485-4bd0-9ff4-fc0302baf186}.map;.Xml.Linq.ni.{de49ee51-7175-4a1c-b5c8-b75e424b8503}.map;.Xml.ni.{b479a61e-8614-478e-88a9-854e59e3c823}.map;.Xml.ReaderWriter.ni.{2cf751a4-1a96-44a5-932d-d926bc8311ea}.map;.Xml.Serialization.ni.{7e495f1e-9e57-4b23-acd9-6e1d93d1789c}.map;.Xml.XDocument.ni.{3ca9a3fd-5150-4f45-8f8b-014f2f9ba5a1}.map;.Xml.XmlDocument.ni.{516f65fa-c1bd-4693-abf3-18147520f75d}.map;.Xml.XmlSerializer.ni.{b8b08991-e6bc-4d85-a194-858135944317}.map;.Xml.XPath.ni.{4f87680f-1a23-4e5a-9c3d-b09c30430aad}.map;.Xml.XPath.XDocument.ni.{300ef4f1-6391-4cde-966c-31b686d959b4}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/WindowsBase.dll": {"related": ".ni.{4a6141df-2307-4e5b-91d3-3fe4e349e593}.map"}, "runtimes/linux-x64/lib/netcoreapp2.2/mscorlib.dll": {}, "runtimes/linux-x64/lib/netcoreapp2.2/netstandard.dll": {"related": ".ni.{193b1740-5ea8-4a13-9047-df5263aef333}.map"}}, "native": {"runtimes/linux-x64/native/System.Globalization.Native.so": {}, "runtimes/linux-x64/native/System.IO.Compression.Native.a": {}, "runtimes/linux-x64/native/System.IO.Compression.Native.so": {}, "runtimes/linux-x64/native/System.Native.a": {}, "runtimes/linux-x64/native/System.Native.so": {}, "runtimes/linux-x64/native/System.Net.Http.Native.a": {}, "runtimes/linux-x64/native/System.Net.Http.Native.so": {}, "runtimes/linux-x64/native/System.Net.Security.Native.a": {}, "runtimes/linux-x64/native/System.Net.Security.Native.so": {}, "runtimes/linux-x64/native/System.Private.CoreLib.dll": {}, "runtimes/linux-x64/native/System.Security.Cryptography.Native.OpenSsl.a": {}, "runtimes/linux-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {}, "runtimes/linux-x64/native/createdump": {}, "runtimes/linux-x64/native/libclrjit.so": {}, "runtimes/linux-x64/native/libcoreclr.so": {}, "runtimes/linux-x64/native/libcoreclrtraceptprovider.so": {}, "runtimes/linux-x64/native/libdbgshim.so": {}, "runtimes/linux-x64/native/libmscordaccore.so": {}, "runtimes/linux-x64/native/libmscordbi.so": {}, "runtimes/linux-x64/native/libsos.so": {}, "runtimes/linux-x64/native/libsosplugin.so": {}, "runtimes/linux-x64/native/sosdocsunix.txt": {}}}, "runtime.linux-x64.Microsoft.NETCore.DotNetAppHost/2.2.8": {"type": "package", "native": {"runtimes/linux-x64/native/apphost": {}}}, "runtime.linux-x64.Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.8"}, "native": {"runtimes/linux-x64/native/libhostpolicy.so": {}}}, "runtime.linux-x64.Microsoft.NETCore.DotNetHostResolver/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.8"}, "native": {"runtimes/linux-x64/native/libhostfxr.so": {}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "SharpZipLib/1.3.3": {"type": "package", "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0-beta18": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.IO.Compression": "4.3.0", "System.IO.UnmanagedMemoryStream": "4.3.0", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.3": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netcoreapp2.1/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.ComponentModel.Annotations/4.6.0": {"type": "package", "compile": {"ref/netstandard2.0/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.ComponentModel.Annotations.dll": {}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Formats.Asn1/6.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtime": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.UnmanagedMemoryStream/4.3.0": {"type": "package", "dependencies": {"System.Buffers": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.UnmanagedMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.UnmanagedMemoryStream.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.3.7": {"type": "package", "compile": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets": {}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets": {}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}}, "System.Security.Cryptography.Pkcs/6.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Formats.Asn1": "6.0.0", "System.Memory": "4.5.4", "System.Security.Cryptography.Cng": "5.0.0"}, "compile": {"lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets": {}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets": {}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "compile": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets": {}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Security.Permissions.targets": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Threading.Tasks.Parallel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.Parallel.dll": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "UFU.CoreFX/5.0.3": {"type": "package"}, "UFU.IoT.UI/2.3.1": {"type": "package", "compile": {"lib/netstandard2.0/UFU.IoT.UI.dll": {}}, "runtime": {"lib/netstandard2.0/UFU.IoT.UI.dll": {}}}, "UFU.IoT.Shared/1.0.0": {"type": "project", "framework": "Unsupported,Version=v0.0", "compile": {"bin/placeholder/UFU.IoT.Shared.dll": {}}, "runtime": {"bin/placeholder/UFU.IoT.Shared.dll": {}}}}}, "libraries": {"Enums.NET/4.0.0": {"sha512": "d47SgeuGxKpalKhYoHqFkDPmO9SoE3amSwVNDoUdy4d675/tX7bLyZFHdjfo3Tobth9Y80VnjfasQ/PD4LqUuA==", "type": "package", "path": "enums.net/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "enums.net.4.0.0.nupkg.sha512", "enums.net.nuspec", "lib/net45/Enums.NET.dll", "lib/net45/Enums.NET.pdb", "lib/net45/Enums.NET.xml", "lib/netcoreapp3.0/Enums.NET.dll", "lib/netcoreapp3.0/Enums.NET.pdb", "lib/netcoreapp3.0/Enums.NET.xml", "lib/netstandard1.0/Enums.NET.dll", "lib/netstandard1.0/Enums.NET.pdb", "lib/netstandard1.0/Enums.NET.xml", "lib/netstandard1.1/Enums.NET.dll", "lib/netstandard1.1/Enums.NET.pdb", "lib/netstandard1.1/Enums.NET.xml", "lib/netstandard1.3/Enums.NET.dll", "lib/netstandard1.3/Enums.NET.pdb", "lib/netstandard1.3/Enums.NET.xml", "lib/netstandard2.0/Enums.NET.dll", "lib/netstandard2.0/Enums.NET.pdb", "lib/netstandard2.0/Enums.NET.xml", "lib/netstandard2.1/Enums.NET.dll", "lib/netstandard2.1/Enums.NET.pdb", "lib/netstandard2.1/Enums.NET.xml"]}, "FastExpressionCompiler/4.0.1": {"sha512": "NU0XPFo2DvtgH4XmMFRSz9ZQMtv1VGM0ZyCvM2u9Bp/zavxkEoAm906Ep+ukVnXhQGy7qjoVVSbnyzrVxP2zPw==", "type": "package", "path": "fastexpressioncompiler/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "FastExpressionCompiler.snk", "LICENSE/LICENSE", "fastexpressioncompiler.4.0.1.nupkg.sha512", "fastexpressioncompiler.nuspec", "lib/net45/FastExpressionCompiler.dll", "lib/net45/FastExpressionCompiler.xml", "lib/net6.0/FastExpressionCompiler.dll", "lib/net6.0/FastExpressionCompiler.xml", "lib/net7.0/FastExpressionCompiler.dll", "lib/net7.0/FastExpressionCompiler.xml", "lib/netstandard2.0/FastExpressionCompiler.dll", "lib/netstandard2.0/FastExpressionCompiler.xml", "lib/netstandard2.1/FastExpressionCompiler.dll", "lib/netstandard2.1/FastExpressionCompiler.xml", "logo.png", "readme.md"]}, "FluentValidation/11.8.1": {"sha512": "N72rnlE99XYB7EGA1u9y7m7kNTTynqOPBhZqDE8zr1Y0aSR4t5si94LRA7UVdAV09GaXWCErW+EiFhfbg3DSbg==", "type": "package", "path": "fluentvalidation/11.8.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.8.1.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/net7.0/FluentValidation.dll", "lib/net7.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "MathNet.Numerics.Signed/4.15.0": {"sha512": "LFjukMRatkg9dgRM7U/gM4uKgaWAX7E0lt3fsVDTPdtBIVuh7uPlksDie290br1/tv1a4Ar/Bz9ywCPSL8PhHg==", "type": "package", "path": "mathnet.numerics.signed/4.15.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net40/MathNet.Numerics.dll", "lib/net40/MathNet.Numerics.xml", "lib/net461/MathNet.Numerics.dll", "lib/net461/MathNet.Numerics.xml", "lib/netstandard1.3/MathNet.Numerics.dll", "lib/netstandard1.3/MathNet.Numerics.xml", "lib/netstandard2.0/MathNet.Numerics.dll", "lib/netstandard2.0/MathNet.Numerics.xml", "mathnet.numerics.signed.4.15.0.nupkg.sha512", "mathnet.numerics.signed.nuspec"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.IO.RecyclableMemoryStream/2.2.0": {"sha512": "uyjY/cqomw1irT4L7lDeg4sJ36MsjHg3wKqpGrBAdzvZaxo85yMF+sAA9RIzTV92fDxuUzjqksMqA0+SNMkMgA==", "type": "package", "path": "microsoft.io.recyclablememorystream/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net462/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.2.2.0.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.NETCore.App/2.2.8": {"sha512": "GOxlvyc8hFrnhDjYlm25JJ7PwoyeoOpZzcg6ZgF8n8l6VxezNupRkkTeA2ek1WsspN0CdAoA8e7iDVNU84/F+Q==", "type": "package", "path": "microsoft.netcore.app/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "Microsoft.NETCore.App.versions.txt", "THIRD-PARTY-NOTICES.TXT", "build/netcoreapp2.2/Microsoft.NETCore.App.PlatformManifest.txt", "build/netcoreapp2.2/Microsoft.NETCore.App.props", "build/netcoreapp2.2/Microsoft.NETCore.App.targets", "microsoft.netcore.app.2.2.8.nupkg.sha512", "microsoft.netcore.app.nuspec", "ref/netcoreapp2.2/Microsoft.CSharp.dll", "ref/netcoreapp2.2/Microsoft.CSharp.xml", "ref/netcoreapp2.2/Microsoft.VisualBasic.dll", "ref/netcoreapp2.2/Microsoft.VisualBasic.xml", "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll", "ref/netcoreapp2.2/Microsoft.Win32.Primitives.xml", "ref/netcoreapp2.2/System.AppContext.dll", "ref/netcoreapp2.2/System.Buffers.dll", "ref/netcoreapp2.2/System.Buffers.xml", "ref/netcoreapp2.2/System.Collections.Concurrent.dll", "ref/netcoreapp2.2/System.Collections.Concurrent.xml", "ref/netcoreapp2.2/System.Collections.Immutable.dll", "ref/netcoreapp2.2/System.Collections.Immutable.xml", "ref/netcoreapp2.2/System.Collections.NonGeneric.dll", "ref/netcoreapp2.2/System.Collections.NonGeneric.xml", "ref/netcoreapp2.2/System.Collections.Specialized.dll", "ref/netcoreapp2.2/System.Collections.Specialized.xml", "ref/netcoreapp2.2/System.Collections.dll", "ref/netcoreapp2.2/System.Collections.xml", "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll", "ref/netcoreapp2.2/System.ComponentModel.Annotations.xml", "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll", "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll", "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.xml", "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll", "ref/netcoreapp2.2/System.ComponentModel.Primitives.xml", "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll", "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.xml", "ref/netcoreapp2.2/System.ComponentModel.dll", "ref/netcoreapp2.2/System.ComponentModel.xml", "ref/netcoreapp2.2/System.Configuration.dll", "ref/netcoreapp2.2/System.Console.dll", "ref/netcoreapp2.2/System.Console.xml", "ref/netcoreapp2.2/System.Core.dll", "ref/netcoreapp2.2/System.Data.Common.dll", "ref/netcoreapp2.2/System.Data.Common.xml", "ref/netcoreapp2.2/System.Data.dll", "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll", "ref/netcoreapp2.2/System.Diagnostics.Contracts.xml", "ref/netcoreapp2.2/System.Diagnostics.Debug.dll", "ref/netcoreapp2.2/System.Diagnostics.Debug.xml", "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll", "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.xml", "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll", "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.xml", "ref/netcoreapp2.2/System.Diagnostics.Process.dll", "ref/netcoreapp2.2/System.Diagnostics.Process.xml", "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll", "ref/netcoreapp2.2/System.Diagnostics.StackTrace.xml", "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll", "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.xml", "ref/netcoreapp2.2/System.Diagnostics.Tools.dll", "ref/netcoreapp2.2/System.Diagnostics.Tools.xml", "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll", "ref/netcoreapp2.2/System.Diagnostics.TraceSource.xml", "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll", "ref/netcoreapp2.2/System.Diagnostics.Tracing.xml", "ref/netcoreapp2.2/System.Drawing.Primitives.dll", "ref/netcoreapp2.2/System.Drawing.Primitives.xml", "ref/netcoreapp2.2/System.Drawing.dll", "ref/netcoreapp2.2/System.Dynamic.Runtime.dll", "ref/netcoreapp2.2/System.Globalization.Calendars.dll", "ref/netcoreapp2.2/System.Globalization.Extensions.dll", "ref/netcoreapp2.2/System.Globalization.dll", "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll", "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll", "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll", "ref/netcoreapp2.2/System.IO.Compression.ZipFile.xml", "ref/netcoreapp2.2/System.IO.Compression.dll", "ref/netcoreapp2.2/System.IO.Compression.xml", "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll", "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.xml", "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll", "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll", "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.xml", "ref/netcoreapp2.2/System.IO.FileSystem.dll", "ref/netcoreapp2.2/System.IO.FileSystem.xml", "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll", "ref/netcoreapp2.2/System.IO.IsolatedStorage.xml", "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll", "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.xml", "ref/netcoreapp2.2/System.IO.Pipes.dll", "ref/netcoreapp2.2/System.IO.Pipes.xml", "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll", "ref/netcoreapp2.2/System.IO.dll", "ref/netcoreapp2.2/System.Linq.Expressions.dll", "ref/netcoreapp2.2/System.Linq.Expressions.xml", "ref/netcoreapp2.2/System.Linq.Parallel.dll", "ref/netcoreapp2.2/System.Linq.Parallel.xml", "ref/netcoreapp2.2/System.Linq.Queryable.dll", "ref/netcoreapp2.2/System.Linq.Queryable.xml", "ref/netcoreapp2.2/System.Linq.dll", "ref/netcoreapp2.2/System.Linq.xml", "ref/netcoreapp2.2/System.Memory.dll", "ref/netcoreapp2.2/System.Memory.xml", "ref/netcoreapp2.2/System.Net.Http.dll", "ref/netcoreapp2.2/System.Net.Http.xml", "ref/netcoreapp2.2/System.Net.HttpListener.dll", "ref/netcoreapp2.2/System.Net.HttpListener.xml", "ref/netcoreapp2.2/System.Net.Mail.dll", "ref/netcoreapp2.2/System.Net.Mail.xml", "ref/netcoreapp2.2/System.Net.NameResolution.dll", "ref/netcoreapp2.2/System.Net.NameResolution.xml", "ref/netcoreapp2.2/System.Net.NetworkInformation.dll", "ref/netcoreapp2.2/System.Net.NetworkInformation.xml", "ref/netcoreapp2.2/System.Net.Ping.dll", "ref/netcoreapp2.2/System.Net.Ping.xml", "ref/netcoreapp2.2/System.Net.Primitives.dll", "ref/netcoreapp2.2/System.Net.Primitives.xml", "ref/netcoreapp2.2/System.Net.Requests.dll", "ref/netcoreapp2.2/System.Net.Requests.xml", "ref/netcoreapp2.2/System.Net.Security.dll", "ref/netcoreapp2.2/System.Net.Security.xml", "ref/netcoreapp2.2/System.Net.ServicePoint.dll", "ref/netcoreapp2.2/System.Net.ServicePoint.xml", "ref/netcoreapp2.2/System.Net.Sockets.dll", "ref/netcoreapp2.2/System.Net.Sockets.xml", "ref/netcoreapp2.2/System.Net.WebClient.dll", "ref/netcoreapp2.2/System.Net.WebClient.xml", "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll", "ref/netcoreapp2.2/System.Net.WebHeaderCollection.xml", "ref/netcoreapp2.2/System.Net.WebProxy.dll", "ref/netcoreapp2.2/System.Net.WebProxy.xml", "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll", "ref/netcoreapp2.2/System.Net.WebSockets.Client.xml", "ref/netcoreapp2.2/System.Net.WebSockets.dll", "ref/netcoreapp2.2/System.Net.WebSockets.xml", "ref/netcoreapp2.2/System.Net.dll", "ref/netcoreapp2.2/System.Numerics.Vectors.dll", "ref/netcoreapp2.2/System.Numerics.Vectors.xml", "ref/netcoreapp2.2/System.Numerics.dll", "ref/netcoreapp2.2/System.ObjectModel.dll", "ref/netcoreapp2.2/System.ObjectModel.xml", "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll", "ref/netcoreapp2.2/System.Reflection.DispatchProxy.xml", "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll", "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.xml", "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll", "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.xml", "ref/netcoreapp2.2/System.Reflection.Emit.dll", "ref/netcoreapp2.2/System.Reflection.Emit.xml", "ref/netcoreapp2.2/System.Reflection.Extensions.dll", "ref/netcoreapp2.2/System.Reflection.Metadata.dll", "ref/netcoreapp2.2/System.Reflection.Metadata.xml", "ref/netcoreapp2.2/System.Reflection.Primitives.dll", "ref/netcoreapp2.2/System.Reflection.Primitives.xml", "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll", "ref/netcoreapp2.2/System.Reflection.TypeExtensions.xml", "ref/netcoreapp2.2/System.Reflection.dll", "ref/netcoreapp2.2/System.Resources.Reader.dll", "ref/netcoreapp2.2/System.Resources.ResourceManager.dll", "ref/netcoreapp2.2/System.Resources.ResourceManager.xml", "ref/netcoreapp2.2/System.Resources.Writer.dll", "ref/netcoreapp2.2/System.Resources.Writer.xml", "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll", "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.xml", "ref/netcoreapp2.2/System.Runtime.Extensions.dll", "ref/netcoreapp2.2/System.Runtime.Extensions.xml", "ref/netcoreapp2.2/System.Runtime.Handles.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.xml", "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcoreapp2.2/System.Runtime.InteropServices.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.xml", "ref/netcoreapp2.2/System.Runtime.Loader.dll", "ref/netcoreapp2.2/System.Runtime.Loader.xml", "ref/netcoreapp2.2/System.Runtime.Numerics.dll", "ref/netcoreapp2.2/System.Runtime.Numerics.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Json.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.dll", "ref/netcoreapp2.2/System.Runtime.dll", "ref/netcoreapp2.2/System.Runtime.xml", "ref/netcoreapp2.2/System.Security.Claims.dll", "ref/netcoreapp2.2/System.Security.Claims.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Csp.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.xml", "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll", "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.xml", "ref/netcoreapp2.2/System.Security.Principal.dll", "ref/netcoreapp2.2/System.Security.Principal.xml", "ref/netcoreapp2.2/System.Security.SecureString.dll", "ref/netcoreapp2.2/System.Security.dll", "ref/netcoreapp2.2/System.ServiceModel.Web.dll", "ref/netcoreapp2.2/System.ServiceProcess.dll", "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll", "ref/netcoreapp2.2/System.Text.Encoding.Extensions.xml", "ref/netcoreapp2.2/System.Text.Encoding.dll", "ref/netcoreapp2.2/System.Text.RegularExpressions.dll", "ref/netcoreapp2.2/System.Text.RegularExpressions.xml", "ref/netcoreapp2.2/System.Threading.Overlapped.dll", "ref/netcoreapp2.2/System.Threading.Overlapped.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.xml", "ref/netcoreapp2.2/System.Threading.Tasks.dll", "ref/netcoreapp2.2/System.Threading.Tasks.xml", "ref/netcoreapp2.2/System.Threading.Thread.dll", "ref/netcoreapp2.2/System.Threading.Thread.xml", "ref/netcoreapp2.2/System.Threading.ThreadPool.dll", "ref/netcoreapp2.2/System.Threading.ThreadPool.xml", "ref/netcoreapp2.2/System.Threading.Timer.dll", "ref/netcoreapp2.2/System.Threading.Timer.xml", "ref/netcoreapp2.2/System.Threading.dll", "ref/netcoreapp2.2/System.Threading.xml", "ref/netcoreapp2.2/System.Transactions.Local.dll", "ref/netcoreapp2.2/System.Transactions.Local.xml", "ref/netcoreapp2.2/System.Transactions.dll", "ref/netcoreapp2.2/System.ValueTuple.dll", "ref/netcoreapp2.2/System.Web.HttpUtility.dll", "ref/netcoreapp2.2/System.Web.HttpUtility.xml", "ref/netcoreapp2.2/System.Web.dll", "ref/netcoreapp2.2/System.Windows.dll", "ref/netcoreapp2.2/System.Xml.Linq.dll", "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll", "ref/netcoreapp2.2/System.Xml.ReaderWriter.xml", "ref/netcoreapp2.2/System.Xml.Serialization.dll", "ref/netcoreapp2.2/System.Xml.XDocument.dll", "ref/netcoreapp2.2/System.Xml.XDocument.xml", "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll", "ref/netcoreapp2.2/System.Xml.XPath.XDocument.xml", "ref/netcoreapp2.2/System.Xml.XPath.dll", "ref/netcoreapp2.2/System.Xml.XPath.xml", "ref/netcoreapp2.2/System.Xml.XmlDocument.dll", "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll", "ref/netcoreapp2.2/System.Xml.XmlSerializer.xml", "ref/netcoreapp2.2/System.Xml.dll", "ref/netcoreapp2.2/System.dll", "ref/netcoreapp2.2/WindowsBase.dll", "ref/netcoreapp2.2/mscorlib.dll", "ref/netcoreapp2.2/netstandard.dll", "runtime.json"]}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"sha512": "Lh1F6z41levvtfC3KuuiQe9ppWKRP1oIB42vP1QNQE4uumo95h+LpjPDeysX1DlTjCzG0BVGSUEpCW5fHkni7w==", "type": "package", "path": "microsoft.netcore.dotnetapphost/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnetapphost.2.2.8.nupkg.sha512", "microsoft.netcore.dotnetapphost.nuspec", "runtime.json"]}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"sha512": "rOHr0Dk87vaiq9d1hMpXETB4IKq1jIiPQlVKNUjRGilK/cjOcadhsk+1MsrJ/GnM3eovhy8zW2PGkN8pYEolnw==", "type": "package", "path": "microsoft.netcore.dotnethostpolicy/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnethostpolicy.2.2.8.nupkg.sha512", "microsoft.netcore.dotnethostpolicy.nuspec", "runtime.json"]}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"sha512": "culLr+x2GvUkXVGi4ULZ7jmWJEhuAMyS7iTWBlkWnqbKtYJ36ZlgHbw/6qTm82790gJemEFeo9RehDwfRXfJzA==", "type": "package", "path": "microsoft.netcore.dotnethostresolver/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnethostresolver.2.2.8.nupkg.sha512", "microsoft.netcore.dotnethostresolver.nuspec", "runtime.json"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/2.0.0": {"sha512": "odP/tJj1z6GylFpNo7pMtbd/xQgTC3Ex2If63dRTL38bBNMwsBnJ+RceUIyHdRBC0oik/3NehYT+oECwBhIM3Q==", "type": "package", "path": "microsoft.netcore.targets/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.targets.2.0.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "NETStandard.Library/2.0.3": {"sha512": "st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "type": "package", "path": "netstandard.library/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/NETStandard.Library.targets", "build/netstandard2.0/ref/Microsoft.Win32.Primitives.dll", "build/netstandard2.0/ref/System.AppContext.dll", "build/netstandard2.0/ref/System.Collections.Concurrent.dll", "build/netstandard2.0/ref/System.Collections.NonGeneric.dll", "build/netstandard2.0/ref/System.Collections.Specialized.dll", "build/netstandard2.0/ref/System.Collections.dll", "build/netstandard2.0/ref/System.ComponentModel.Composition.dll", "build/netstandard2.0/ref/System.ComponentModel.EventBasedAsync.dll", "build/netstandard2.0/ref/System.ComponentModel.Primitives.dll", "build/netstandard2.0/ref/System.ComponentModel.TypeConverter.dll", "build/netstandard2.0/ref/System.ComponentModel.dll", "build/netstandard2.0/ref/System.Console.dll", "build/netstandard2.0/ref/System.Core.dll", "build/netstandard2.0/ref/System.Data.Common.dll", "build/netstandard2.0/ref/System.Data.dll", "build/netstandard2.0/ref/System.Diagnostics.Contracts.dll", "build/netstandard2.0/ref/System.Diagnostics.Debug.dll", "build/netstandard2.0/ref/System.Diagnostics.FileVersionInfo.dll", "build/netstandard2.0/ref/System.Diagnostics.Process.dll", "build/netstandard2.0/ref/System.Diagnostics.StackTrace.dll", "build/netstandard2.0/ref/System.Diagnostics.TextWriterTraceListener.dll", "build/netstandard2.0/ref/System.Diagnostics.Tools.dll", "build/netstandard2.0/ref/System.Diagnostics.TraceSource.dll", "build/netstandard2.0/ref/System.Diagnostics.Tracing.dll", "build/netstandard2.0/ref/System.Drawing.Primitives.dll", "build/netstandard2.0/ref/System.Drawing.dll", "build/netstandard2.0/ref/System.Dynamic.Runtime.dll", "build/netstandard2.0/ref/System.Globalization.Calendars.dll", "build/netstandard2.0/ref/System.Globalization.Extensions.dll", "build/netstandard2.0/ref/System.Globalization.dll", "build/netstandard2.0/ref/System.IO.Compression.FileSystem.dll", "build/netstandard2.0/ref/System.IO.Compression.ZipFile.dll", "build/netstandard2.0/ref/System.IO.Compression.dll", "build/netstandard2.0/ref/System.IO.FileSystem.DriveInfo.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Primitives.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Watcher.dll", "build/netstandard2.0/ref/System.IO.FileSystem.dll", "build/netstandard2.0/ref/System.IO.IsolatedStorage.dll", "build/netstandard2.0/ref/System.IO.MemoryMappedFiles.dll", "build/netstandard2.0/ref/System.IO.Pipes.dll", "build/netstandard2.0/ref/System.IO.UnmanagedMemoryStream.dll", "build/netstandard2.0/ref/System.IO.dll", "build/netstandard2.0/ref/System.Linq.Expressions.dll", "build/netstandard2.0/ref/System.Linq.Parallel.dll", "build/netstandard2.0/ref/System.Linq.Queryable.dll", "build/netstandard2.0/ref/System.Linq.dll", "build/netstandard2.0/ref/System.Net.Http.dll", "build/netstandard2.0/ref/System.Net.NameResolution.dll", "build/netstandard2.0/ref/System.Net.NetworkInformation.dll", "build/netstandard2.0/ref/System.Net.Ping.dll", "build/netstandard2.0/ref/System.Net.Primitives.dll", "build/netstandard2.0/ref/System.Net.Requests.dll", "build/netstandard2.0/ref/System.Net.Security.dll", "build/netstandard2.0/ref/System.Net.Sockets.dll", "build/netstandard2.0/ref/System.Net.WebHeaderCollection.dll", "build/netstandard2.0/ref/System.Net.WebSockets.Client.dll", "build/netstandard2.0/ref/System.Net.WebSockets.dll", "build/netstandard2.0/ref/System.Net.dll", "build/netstandard2.0/ref/System.Numerics.dll", "build/netstandard2.0/ref/System.ObjectModel.dll", "build/netstandard2.0/ref/System.Reflection.Extensions.dll", "build/netstandard2.0/ref/System.Reflection.Primitives.dll", "build/netstandard2.0/ref/System.Reflection.dll", "build/netstandard2.0/ref/System.Resources.Reader.dll", "build/netstandard2.0/ref/System.Resources.ResourceManager.dll", "build/netstandard2.0/ref/System.Resources.Writer.dll", "build/netstandard2.0/ref/System.Runtime.CompilerServices.VisualC.dll", "build/netstandard2.0/ref/System.Runtime.Extensions.dll", "build/netstandard2.0/ref/System.Runtime.Handles.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.RuntimeInformation.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.dll", "build/netstandard2.0/ref/System.Runtime.Numerics.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Formatters.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Json.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Primitives.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Xml.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.dll", "build/netstandard2.0/ref/System.Runtime.dll", "build/netstandard2.0/ref/System.Security.Claims.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Algorithms.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Csp.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Encoding.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Primitives.dll", "build/netstandard2.0/ref/System.Security.Cryptography.X509Certificates.dll", "build/netstandard2.0/ref/System.Security.Principal.dll", "build/netstandard2.0/ref/System.Security.SecureString.dll", "build/netstandard2.0/ref/System.ServiceModel.Web.dll", "build/netstandard2.0/ref/System.Text.Encoding.Extensions.dll", "build/netstandard2.0/ref/System.Text.Encoding.dll", "build/netstandard2.0/ref/System.Text.RegularExpressions.dll", "build/netstandard2.0/ref/System.Threading.Overlapped.dll", "build/netstandard2.0/ref/System.Threading.Tasks.Parallel.dll", "build/netstandard2.0/ref/System.Threading.Tasks.dll", "build/netstandard2.0/ref/System.Threading.Thread.dll", "build/netstandard2.0/ref/System.Threading.ThreadPool.dll", "build/netstandard2.0/ref/System.Threading.Timer.dll", "build/netstandard2.0/ref/System.Threading.dll", "build/netstandard2.0/ref/System.Transactions.dll", "build/netstandard2.0/ref/System.ValueTuple.dll", "build/netstandard2.0/ref/System.Web.dll", "build/netstandard2.0/ref/System.Windows.dll", "build/netstandard2.0/ref/System.Xml.Linq.dll", "build/netstandard2.0/ref/System.Xml.ReaderWriter.dll", "build/netstandard2.0/ref/System.Xml.Serialization.dll", "build/netstandard2.0/ref/System.Xml.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.dll", "build/netstandard2.0/ref/System.Xml.XmlDocument.dll", "build/netstandard2.0/ref/System.Xml.XmlSerializer.dll", "build/netstandard2.0/ref/System.Xml.dll", "build/netstandard2.0/ref/System.dll", "build/netstandard2.0/ref/mscorlib.dll", "build/netstandard2.0/ref/netstandard.dll", "build/netstandard2.0/ref/netstandard.xml", "lib/netstandard1.0/_._", "netstandard.library.2.0.3.nupkg.sha512", "netstandard.library.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NPOI/2.6.0": {"sha512": "Pwjo65CUH3MiRnBEbVo8ff31ZrDGdGyyFJyAEncmbTQ0/gYgDkBUnGKm20aLpdwCpPNLzvapZm8v5tx4S6qAWg==", "type": "package", "path": "npoi/2.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "Read Me.txt", "lib/net472/NPOI.OOXML.XML", "lib/net472/NPOI.OOXML.dll", "lib/net472/NPOI.OOXML.pdb", "lib/net472/NPOI.OpenXml4Net.XML", "lib/net472/NPOI.OpenXml4Net.dll", "lib/net472/NPOI.OpenXml4Net.pdb", "lib/net472/NPOI.OpenXmlFormats.dll", "lib/net472/NPOI.OpenXmlFormats.dll.config", "lib/net472/NPOI.OpenXmlFormats.pdb", "lib/net472/NPOI.XML", "lib/net472/NPOI.dll", "lib/net472/NPOI.pdb", "lib/net6.0/NPOI.OOXML.deps.json", "lib/net6.0/NPOI.OOXML.dll", "lib/net6.0/NPOI.OOXML.pdb", "lib/net6.0/NPOI.OOXML.xml", "lib/net6.0/NPOI.OpenXml4Net.dll", "lib/net6.0/NPOI.OpenXml4Net.pdb", "lib/net6.0/NPOI.OpenXmlFormats.dll", "lib/net6.0/NPOI.OpenXmlFormats.dll.config", "lib/net6.0/NPOI.OpenXmlFormats.pdb", "lib/net6.0/NPOI.dll", "lib/net6.0/NPOI.pdb", "lib/net6.0/NPOI.xml", "lib/netstandard2.0/NPOI.OOXML.deps.json", "lib/netstandard2.0/NPOI.OOXML.dll", "lib/netstandard2.0/NPOI.OOXML.pdb", "lib/netstandard2.0/NPOI.OOXML.xml", "lib/netstandard2.0/NPOI.OpenXml4Net.dll", "lib/netstandard2.0/NPOI.OpenXml4Net.pdb", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll.config", "lib/netstandard2.0/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.0/NPOI.dll", "lib/netstandard2.0/NPOI.pdb", "lib/netstandard2.0/NPOI.xml", "lib/netstandard2.1/NPOI.OOXML.deps.json", "lib/netstandard2.1/NPOI.OOXML.dll", "lib/netstandard2.1/NPOI.OOXML.pdb", "lib/netstandard2.1/NPOI.OOXML.xml", "lib/netstandard2.1/NPOI.OpenXml4Net.dll", "lib/netstandard2.1/NPOI.OpenXml4Net.pdb", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll.config", "lib/netstandard2.1/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.1/NPOI.dll", "lib/netstandard2.1/NPOI.pdb", "lib/netstandard2.1/NPOI.xml", "logo/120_120.jpg", "logo/240_240.png", "logo/32_32.jpg", "logo/60_60.jpg", "npoi.2.6.0.nupkg.sha512", "npoi.nuspec"]}, "Portable.BouncyCastle/1.9.0": {"sha512": "eZZBCABzVOek+id9Xy04HhmgykF0wZg9wpByzrWN7q8qEI0Qen9b7tfd7w8VA3dOeesumMG7C5ZPy0jk7PSRHw==", "type": "package", "path": "portable.bouncycastle/1.9.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/BouncyCastle.Crypto.dll", "lib/net40/BouncyCastle.Crypto.xml", "lib/netstandard2.0/BouncyCastle.Crypto.dll", "lib/netstandard2.0/BouncyCastle.Crypto.xml", "portable.bouncycastle.1.9.0.nupkg.sha512", "portable.bouncycastle.nuspec"]}, "RulesEngine/5.0.3": {"sha512": "BF0CCNXzLA89PlUwcwOWPadfp8Q2e5p6joP//EdSJ9m9Ne2PREkI33BqIw5FhGe7+pSekyPeGWI7daxIgefHVw==", "type": "package", "path": "rulesengine/5.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net6.0/RulesEngine.dll", "lib/netstandard2.0/RulesEngine.dll", "rulesengine.5.0.3.nupkg.sha512", "rulesengine.nuspec"]}, "runtime.linux-x64.Microsoft.NETCore.App/2.2.8": {"sha512": "8bByMNcOVfmxtyk46y6bKHXI1G0k8c0P2hxPaw75o4ARDCe+oA/Qb+iMwJidtTKICjBu8NFLrdiDR9/rHzKKUg==", "type": "package", "path": "runtime.linux-x64.microsoft.netcore.app/2.2.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "Microsoft.NETCore.App.versions.txt", "THIRD-PARTY-NOTICES.TXT", "ref/netstandard/_._", "runtime.linux-x64.microsoft.netcore.app.2.2.8.nupkg.sha512", "runtime.linux-x64.microsoft.netcore.app.nuspec", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.CSharp.dll", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.CSharp.ni.{5446b4ac-9e78-4b30-93dc-c51c36d6c090}.map", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.VisualBasic.dll", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.VisualBasic.ni.{031a11ae-2ba5-4aa0-a923-78f3f5a75b42}.map", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.Win32.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.Win32.Primitives.ni.{a57916d6-0a0e-4632-8bc9-984f01bceb94}.map", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.Win32.Registry.dll", "runtimes/linux-x64/lib/netcoreapp2.2/Microsoft.Win32.Registry.ni.{1a52b890-2931-4de7-8b9e-7ba908853ce8}.map", "runtimes/linux-x64/lib/netcoreapp2.2/SOS.NETCore.dll", "runtimes/linux-x64/lib/netcoreapp2.2/SOS.NETCore.ni.{f6fbff4e-31a2-4e95-b4c4-676c01ac0d9b}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.AppContext.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.AppContext.ni.{3a7851da-2fa5-4f41-848d-c5af73db6609}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Buffers.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Buffers.ni.{c1fb38d8-63a8-4e1a-ad62-25daf8a21828}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Concurrent.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Concurrent.ni.{54366e28-9847-4ec1-9fd2-f5defa7bc6c9}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Immutable.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Immutable.ni.{d0c3f9a5-c5ea-4d28-8354-5e981301087f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.NonGeneric.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.NonGeneric.ni.{d7558cc2-1dc6-4999-8d30-afbf9f6e16d1}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Specialized.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.Specialized.ni.{3808edd5-db6f-4814-b004-2fbb299f9e9b}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Collections.ni.{3aba3dc7-64ff-4c25-ba74-5e2df535ff88}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.Annotations.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.Annotations.ni.{ae6fad0f-dfbb-4d86-8113-78d449943ac9}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.DataAnnotations.ni.{59cd88e8-6124-4f6c-be99-ee046ba987ce}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.EventBasedAsync.ni.{7c12aa4b-9a58-40a2-b4eb-b9e66f9c6869}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.Primitives.ni.{286e75f4-2705-490a-bc1a-a8fbc098d876}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.TypeConverter.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.TypeConverter.ni.{a919e8ed-ca2a-4779-be12-33c0189e4896}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ComponentModel.ni.{552215b8-4ba4-4dad-8010-a16e72f6e9fc}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Configuration.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Configuration.ni.{ecde7dc1-8b6b-4d81-a9d7-c629b55ee651}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Console.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Console.ni.{5d6acd80-8ee6-4898-9a98-945168d82e65}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Core.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Core.ni.{8c78ba34-ca25-4a12-b216-350dcb7d928c}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Data.Common.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Data.Common.ni.{f2c1bc24-b1b2-4e4e-b63d-aa6a4dc39253}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Data.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Data.ni.{73053d68-355d-4ec7-8cce-b4c89b682f90}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Contracts.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Contracts.ni.{aa2991c5-64ab-4e3c-ba38-ea24b822d531}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Debug.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Debug.ni.{f83ea2b0-fa16-4a44-9310-c60d108d0212}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.DiagnosticSource.ni.{be006aed-e4aa-4b59-a375-becd975f8d54}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.FileVersionInfo.ni.{1450afc6-fa62-417c-ba84-ccde672e6d55}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Process.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Process.ni.{6d49b1f8-f69e-4f71-9920-3026d4edac2a}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.StackTrace.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.StackTrace.ni.{156a719e-9ce2-4b8e-99c1-8b67c54b1a1b}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.ni.{4f43da9a-5b13-46e2-95ea-26577d76fcaf}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Tools.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Tools.ni.{250d4416-6696-40f3-b63b-df67d97c68d0}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.TraceSource.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.TraceSource.ni.{b962e1c1-9f1a-4d90-9ea6-c93aefa111aa}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Tracing.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Diagnostics.Tracing.ni.{9137fcd5-4efc-41bc-8e00-6f1f92a6eab5}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Drawing.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Drawing.Primitives.ni.{634b0194-9bcb-4fd3-b173-b1aea9eaebb8}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Drawing.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Drawing.ni.{a6ca4f6a-8c35-4080-8be8-3395b398253c}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Dynamic.Runtime.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Dynamic.Runtime.ni.{e8714437-9a05-4b03-85ac-60dd526bde03}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.Calendars.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.Calendars.ni.{404fb648-5455-43c7-b116-2f0a2dbe9c90}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.Extensions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.Extensions.ni.{ee7e0755-a9ad-41bd-8a2f-53fb1f3211c7}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Globalization.ni.{52c8ed56-f56b-457c-914a-7fa98bce6383}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.Brotli.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.Brotli.ni.{91225069-55d0-4e2c-9151-70cdc63f47eb}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.FileSystem.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.FileSystem.ni.{ce9039ed-d311-418d-9c57-5533b409cc90}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.ZipFile.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.ZipFile.ni.{9e0a832a-4ad4-450f-a88e-4588d5699755}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Compression.ni.{4185ccce-85a0-49c0-a6b4-ce8eb86febfa}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.AccessControl.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.AccessControl.ni.{fd35313f-4132-4965-af69-3379cecb562f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.DriveInfo.ni.{c3f9070a-1271-49d0-acd0-24c000809ae0}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.Primitives.ni.{41bd4ec1-f271-4129-8ecc-1419fb644845}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.Watcher.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.Watcher.ni.{595565be-382f-4521-8290-d6b020b5d835}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.FileSystem.ni.{9f88d287-1acf-4595-a2e3-9bb9acdbfcc5}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.IsolatedStorage.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.IsolatedStorage.ni.{91b52fec-9194-4481-b672-74fb90312812}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.MemoryMappedFiles.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.MemoryMappedFiles.ni.{e2a02424-48f5-4a24-9e41-50ac5839a11f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Pipes.AccessControl.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Pipes.AccessControl.ni.{03105a94-6390-41f6-9f96-0aff4144106e}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Pipes.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.Pipes.ni.{70b2dd94-c605-4c09-bede-6ace64340d50}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.UnmanagedMemoryStream.ni.{ac2348c9-2c5b-4fcf-9903-7ec9dfea7402}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.IO.ni.{b652629a-e572-43a5-bab2-a78f9d9260a6}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Expressions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Expressions.ni.{062d78d0-7030-480d-9bc8-da39a2d972c9}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Parallel.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Parallel.ni.{3cf8e3a1-ff00-4baa-8ad8-109dcedd67b2}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Queryable.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.Queryable.ni.{1a7ab755-6a5b-4ffd-b913-e071c92a11b2}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Linq.ni.{ec5b84c1-fa57-4c97-a8ed-c32f4e907871}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Memory.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Memory.ni.{5ce2446d-fe40-485f-a7ac-4322bf8d023e}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Http.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Http.ni.{b2e51289-34a0-4ba9-813b-48f2290b225c}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.HttpListener.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.HttpListener.ni.{0a73e81b-2a90-4011-a662-383a8c0f60dd}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Mail.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Mail.ni.{6a8f2583-a963-43df-b5e8-452f077e8cfd}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.NameResolution.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.NameResolution.ni.{99d82576-3a25-4823-a64e-cb4125ae3d15}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.NetworkInformation.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.NetworkInformation.ni.{f1450592-0022-4c26-9638-cba7692623bb}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Ping.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Ping.ni.{8940c0a8-598c-4d87-bbd0-846e948e59de}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Primitives.ni.{9c967619-c1aa-4d25-ad03-b042ed00c7b7}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Requests.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Requests.ni.{3ce4d211-8261-4a8f-a721-274f67b07b23}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Security.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Security.ni.{597e4348-4977-41ba-a120-f93fd19cf149}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.ServicePoint.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.ServicePoint.ni.{8f27e0c5-c213-4b74-9e77-8c7008e04a3d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Sockets.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.Sockets.ni.{47d8a4e7-a08b-4c39-8fc0-2f76b8f40f5f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebClient.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebClient.ni.{61751156-5cc1-49be-8551-0473146da16f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebHeaderCollection.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebHeaderCollection.ni.{5eee16d7-3e6f-4bf7-b3f6-5e99dcee836e}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebProxy.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebProxy.ni.{65269d7c-cdd4-4f7a-8c59-5bfa949ba55d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebSockets.Client.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebSockets.Client.ni.{d1651eba-4f3a-44a6-b449-97943ca4657f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebSockets.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.WebSockets.ni.{af889f66-a047-4637-a282-c08dbe893ce4}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Net.ni.{958fd545-869a-4b20-a988-5a80d875375c}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Numerics.Vectors.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Numerics.Vectors.ni.{23bb31ce-879b-4e44-a25f-0832b68553aa}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Numerics.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Numerics.ni.{25eb404a-7669-411d-ad41-6b4d138acd7d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ObjectModel.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ObjectModel.ni.{6b322332-a4ef-4bd3-bf81-9b0de8c67ab0}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.DataContractSerialization.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.DataContractSerialization.ni.{5d178b45-4654-453d-b3c9-73c7eec4924d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Uri.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Uri.ni.{b2591560-5aa5-49fd-8e4d-b04f9f0b82ad}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Xml.Linq.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Xml.Linq.ni.{b1af64a0-e893-4f39-8d35-46be00f04e23}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Xml.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Private.Xml.ni.{491da070-17e9-44aa-8cdc-738318374574}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.DispatchProxy.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.DispatchProxy.ni.{b873e227-bfab-4b4c-8789-9d53e3a66b08}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.ILGeneration.ni.{b15af431-5bdb-4646-a5ec-7255b8d45c2e}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.Lightweight.ni.{4ef94769-d2d3-4a2a-bb71-dd1759e9c853}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Emit.ni.{659d953e-aafc-4bf1-ae68-295ac449fbc2}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Extensions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Extensions.ni.{03a3aac4-3683-4b3b-8ecc-efe571ad5892}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Metadata.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Metadata.ni.{aeaf0e2c-5d86-46d8-bfdb-8e5f8221eb1b}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.Primitives.ni.{3a43fcff-54f8-45e3-b1ff-6dafb34bb183}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.TypeExtensions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.TypeExtensions.ni.{cb01e343-31a3-4216-86f6-d83da360063c}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Reflection.ni.{a7a8aec7-2851-4307-ac68-0b4795954ea9}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.Reader.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.Reader.ni.{9ddfc97b-786d-4a0c-8dfa-b179ea6e7adb}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.ResourceManager.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.ResourceManager.ni.{f1a2dbb4-9243-4525-ba41-a827b1484a03}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.Writer.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Resources.Writer.ni.{dafa6c70-40b1-475e-bbc5-9a820c7a4733}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.ni.{962ae5ca-5d61-495a-84f1-d063859d03b1}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Extensions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Extensions.ni.{496d5832-3672-42de-82e5-f0937829480f}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Handles.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Handles.ni.{edd0a82f-ed63-4ebe-8331-9417dd502adc}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.ni.{28f7985c-42ed-4e63-838e-4b21fdbf8f06}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.ni.{2bb0b44a-a28e-4849-a6dd-a769da96e867}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.InteropServices.ni.{860ea5a4-0966-4a36-ab8f-f5880498802d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Loader.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Loader.ni.{466cb8c6-f5f8-4036-8b28-dfa30e290d14}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Numerics.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Numerics.ni.{8ef36233-dde0-4376-b7ce-6717c3d2df62}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Formatters.ni.{6eb5f4e1-03fd-434f-bd36-0f9a2df3e21b}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Json.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Json.ni.{fb898814-c753-4216-bf57-0f5bdc02f950}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Primitives.ni.{0add7c60-c3c5-4981-a6e3-58bfaed57dd1}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Xml.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.Xml.ni.{b3e06eb4-7f0c-4d18-8926-0c7e2df33768}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.Serialization.ni.{fbd03e7a-08d4-4fbf-b04e-f66767c5175d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Runtime.ni.{248b9ac7-a403-473e-9e7c-41b45addf1e1}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.AccessControl.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.AccessControl.ni.{eb01bf40-20fd-4050-acc6-2ce4dd92f302}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Claims.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Claims.ni.{c4c249e6-d234-426a-88fd-d7259eb56ecd}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Algorithms.ni.{87d0509f-685c-4064-834f-67a67cf9f715}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Cng.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Cng.ni.{06abdb3e-c613-4a0e-8c4c-0834ec48cbf7}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Csp.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Csp.ni.{15232058-4ed5-48dd-99e5-09dc303a5be9}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Encoding.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Encoding.ni.{22ff94a2-c18e-4689-b210-60aecc7186b5}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.OpenSsl.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.OpenSsl.ni.{6d3bc01f-b826-49e6-a9d4-763be48caf23}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Primitives.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.Primitives.ni.{e36a71fc-b7f9-401a-b4de-a93f048e79e0}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Cryptography.X509Certificates.ni.{be508089-8185-402b-8e4d-39c1697747f3}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Principal.Windows.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Principal.Windows.ni.{5359ed79-ade5-4464-b6a1-ce0c87fc3c09}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Principal.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.Principal.ni.{1669d8a0-1a28-46f1-b4fc-7f5346dde322}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.SecureString.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.SecureString.ni.{e484103d-605d-4d33-96eb-de2c49346805}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Security.ni.{10b474ee-e56c-421b-bc25-1e6a1c0d6043}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ServiceModel.Web.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ServiceModel.Web.ni.{8afa0f81-64bb-47a0-a857-4fee301671f0}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ServiceProcess.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ServiceProcess.ni.{75e1d847-328b-46b6-a5b1-09aa70158fbc}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.Encoding.Extensions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.Encoding.Extensions.ni.{9681e200-823e-481f-a3ef-4371d5720816}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.Encoding.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.Encoding.ni.{f22a369d-6448-4e52-a0c3-9ed29d716d16}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.RegularExpressions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Text.RegularExpressions.ni.{bf365ffb-cb72-4cea-95c5-17fd8fe7b177}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Overlapped.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Overlapped.ni.{80f01b78-96ac-4d7a-b4e2-acdb55b59e28}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Dataflow.ni.{0727fe8d-7820-4fcb-919b-572f8030a1e4}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Extensions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Extensions.ni.{43aacac2-4f91-47dc-b83c-cc52cef06f47}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Parallel.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.Parallel.ni.{fb77da04-cd2d-42a5-baee-e1d9df7568d9}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Tasks.ni.{7a25ce6e-844f-47d3-b3c6-3e36ebd57535}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Thread.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Thread.ni.{f90a6cb4-54a7-4c9f-87ea-25ed0ce39f64}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.ThreadPool.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.ThreadPool.ni.{8e2767c6-29f5-43d0-bbab-1993cc512fe8}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Timer.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.Timer.ni.{0571fa8c-c382-467f-9459-9ac90ab204ae}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Threading.ni.{f2f1c0ce-24de-4331-a38c-2c487f37be75}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Transactions.Local.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Transactions.Local.ni.{b1afe66c-1c66-48cc-bf0c-250a80d533e3}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Transactions.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Transactions.ni.{e34512ea-c4fd-481d-9ed4-c50c470ad637}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.ValueTuple.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ValueTuple.ni.{53c64b34-741b-43ef-a103-19c163cbc08b}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Web.HttpUtility.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Web.HttpUtility.ni.{32e6b061-d82b-423f-b5bb-e4f3117a6ce5}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Web.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Web.ni.{3d2b9612-ed6a-4e8b-8e8c-48d01547c414}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Windows.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Windows.ni.{6b1287f0-7485-4bd0-9ff4-fc0302baf186}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.Linq.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.Linq.ni.{de49ee51-7175-4a1c-b5c8-b75e424b8503}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.ReaderWriter.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.ReaderWriter.ni.{2cf751a4-1a96-44a5-932d-d926bc8311ea}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.Serialization.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.Serialization.ni.{7e495f1e-9e57-4b23-acd9-6e1d93d1789c}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XDocument.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XDocument.ni.{3ca9a3fd-5150-4f45-8f8b-014f2f9ba5a1}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XPath.XDocument.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XPath.XDocument.ni.{300ef4f1-6391-4cde-966c-31b686d959b4}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XPath.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XPath.ni.{4f87680f-1a23-4e5a-9c3d-b09c30430aad}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XmlDocument.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XmlDocument.ni.{516f65fa-c1bd-4693-abf3-18147520f75d}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XmlSerializer.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.XmlSerializer.ni.{b8b08991-e6bc-4d85-a194-858135944317}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.Xml.ni.{b479a61e-8614-478e-88a9-854e59e3c823}.map", "runtimes/linux-x64/lib/netcoreapp2.2/System.dll", "runtimes/linux-x64/lib/netcoreapp2.2/System.ni.{aef768c7-c1ac-4dd5-bf87-1bdca558f74e}.map", "runtimes/linux-x64/lib/netcoreapp2.2/WindowsBase.dll", "runtimes/linux-x64/lib/netcoreapp2.2/WindowsBase.ni.{4a6141df-2307-4e5b-91d3-3fe4e349e593}.map", "runtimes/linux-x64/lib/netcoreapp2.2/mscorlib.dll", "runtimes/linux-x64/lib/netcoreapp2.2/netstandard.dll", "runtimes/linux-x64/lib/netcoreapp2.2/netstandard.ni.{193b1740-5ea8-4a13-9047-df5263aef333}.map", "runtimes/linux-x64/native/System.Globalization.Native.so", "runtimes/linux-x64/native/System.IO.Compression.Native.a", "runtimes/linux-x64/native/System.IO.Compression.Native.so", "runtimes/linux-x64/native/System.Native.a", "runtimes/linux-x64/native/System.Native.so", "runtimes/linux-x64/native/System.Net.Http.Native.a", "runtimes/linux-x64/native/System.Net.Http.Native.so", "runtimes/linux-x64/native/System.Net.Security.Native.a", "runtimes/linux-x64/native/System.Net.Security.Native.so", "runtimes/linux-x64/native/System.Private.CoreLib.dll", "runtimes/linux-x64/native/System.Security.Cryptography.Native.OpenSsl.a", "runtimes/linux-x64/native/System.Security.Cryptography.Native.OpenSsl.so", "runtimes/linux-x64/native/createdump", "runtimes/linux-x64/native/libclrjit.so", "runtimes/linux-x64/native/libcoreclr.so", "runtimes/linux-x64/native/libcoreclrtraceptprovider.so", "runtimes/linux-x64/native/libdbgshim.so", "runtimes/linux-x64/native/libmscordaccore.so", "runtimes/linux-x64/native/libmscordbi.so", "runtimes/linux-x64/native/libsos.so", "runtimes/linux-x64/native/libsosplugin.so", "runtimes/linux-x64/native/sosdocsunix.txt", "tools/crossgen"]}, "runtime.linux-x64.Microsoft.NETCore.DotNetAppHost/2.2.8": {"sha512": "4ddMGErcPf38AZIS1RpKmk4HBEnqObsZB1EzpEz0xWUK6ek/8ShnC667k13SoxWXa0mq1ZFXJX83eISJm0pkpg==", "type": "package", "path": "runtime.linux-x64.microsoft.netcore.dotnetapphost/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.microsoft.netcore.dotnetapphost.2.2.8.nupkg.sha512", "runtime.linux-x64.microsoft.netcore.dotnetapphost.nuspec", "runtimes/linux-x64/native/apphost", "version.txt"]}, "runtime.linux-x64.Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"sha512": "304x/iQW4OS2VyqHOW0JkFICvU8K3aeUvaMTWIbnKYQQLLEYLP4meXdoqlbRVAB4EWSnMz2pJYGLL7Rrtppnpw==", "type": "package", "path": "runtime.linux-x64.microsoft.netcore.dotnethostpolicy/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.microsoft.netcore.dotnethostpolicy.2.2.8.nupkg.sha512", "runtime.linux-x64.microsoft.netcore.dotnethostpolicy.nuspec", "runtimes/linux-x64/native/libhostpolicy.so", "version.txt"]}, "runtime.linux-x64.Microsoft.NETCore.DotNetHostResolver/2.2.8": {"sha512": "k8j4nfWK2R6PASV0ZhsLbE2USgUAaJ53hDkN+HVWcu9jiRlHATbX4oHnV2daZ5xvbiJR5AZixes2MoA4p1hNIg==", "type": "package", "path": "runtime.linux-x64.microsoft.netcore.dotnethostresolver/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.microsoft.netcore.dotnethostresolver.2.2.8.nupkg.sha512", "runtime.linux-x64.microsoft.netcore.dotnethostresolver.nuspec", "runtimes/linux-x64/native/libhostfxr.so", "version.txt"]}, "runtime.native.System/4.3.0": {"sha512": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "type": "package", "path": "runtime.native.system/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.3.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.IO.Compression/4.3.0": {"sha512": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "type": "package", "path": "runtime.native.system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.io.compression.4.3.0.nupkg.sha512", "runtime.native.system.io.compression.nuspec"]}, "SharpZipLib/1.3.3": {"sha512": "N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "type": "package", "path": "sharpziplib/1.3.3", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net45/ICSharpCode.SharpZipLib.dll", "lib/net45/ICSharpCode.SharpZipLib.pdb", "lib/net45/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.3.3.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.Fonts/1.0.0-beta18": {"sha512": "evykNmy/kEE9EAEKgZm3MNUYXuMHFfmcLUNPw7Ho5q7OI96GFkkIxBm+QaKOTPBKw+L0AjKOs+ArVg8P40Ac9g==", "type": "package", "path": "sixlabors.fonts/1.0.0-beta18", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.0-beta18.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/2.1.3": {"sha512": "8yonNRWX3vUE9k29ta0Hbfa0AEc0hbDjSH/nZ3vOTJEXmY6hLnGsjDKoz96Z+AgOsrdkAu6PdL/Ebaf70aitzw==", "type": "package", "path": "sixlabors.imagesharp/2.1.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/SixLabors.ImageSharp.dll", "lib/net472/SixLabors.ImageSharp.xml", "lib/netcoreapp2.1/SixLabors.ImageSharp.dll", "lib/netcoreapp2.1/SixLabors.ImageSharp.xml", "lib/netcoreapp3.1/SixLabors.ImageSharp.dll", "lib/netcoreapp3.1/SixLabors.ImageSharp.xml", "lib/netstandard2.0/SixLabors.ImageSharp.dll", "lib/netstandard2.0/SixLabors.ImageSharp.xml", "lib/netstandard2.1/SixLabors.ImageSharp.dll", "lib/netstandard2.1/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.2.1.3.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.3.0": {"sha512": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "type": "package", "path": "system.collections.concurrent/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.3.0.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.ComponentModel.Annotations/4.6.0": {"sha512": "pOd+UhZ3X8xfwKDlgAzowUJNjp8VYVmOHZm++vCd0kq1HZ0zK3mNo2yRXjYgv7Ik/Xi43fmJfND2PLEsQSALCg==", "type": "package", "path": "system.componentmodel.annotations/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.6.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/6.0.0": {"sha512": "7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "type": "package", "path": "system.configuration.configurationmanager/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Dynamic.Runtime/4.3.0": {"sha512": "SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "type": "package", "path": "system.dynamic.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll", "system.dynamic.runtime.4.3.0.nupkg.sha512", "system.dynamic.runtime.nuspec"]}, "System.Formats.Asn1/6.0.0": {"sha512": "T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "type": "package", "path": "system.formats.asn1/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Formats.Asn1.dll", "lib/net461/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.6.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Compression/4.3.0": {"sha512": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "type": "package", "path": "system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll", "system.io.compression.4.3.0.nupkg.sha512", "system.io.compression.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.IO.UnmanagedMemoryStream/4.3.0": {"sha512": "tS89nK7pw8ebkkEfWujA05+ZReHKzz39W+bcX1okVR0GJCJuzPyfYfQZyiLSrjp121BB5J4uewZQiUTKri2pSQ==", "type": "package", "path": "system.io.unmanagedmemorystream/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.UnmanagedMemoryStream.dll", "lib/netstandard1.3/System.IO.UnmanagedMemoryStream.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.UnmanagedMemoryStream.dll", "ref/netstandard1.3/System.IO.UnmanagedMemoryStream.dll", "ref/netstandard1.3/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/de/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/es/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/fr/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/it/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/ja/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/ko/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/ru/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/zh-hans/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/zh-hant/System.IO.UnmanagedMemoryStream.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.unmanagedmemorystream.4.3.0.nupkg.sha512", "system.io.unmanagedmemorystream.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.3.7": {"sha512": "7J9oS95JCcyOtnKCr0tYUOMAj67Ee3WKal6li20vEeuQFdD7C4XuAABysp3H5dx+wmHkXuUNoxW3+U+OEsx+JQ==", "type": "package", "path": "system.linq.dynamic.core/1.3.7", "files": [".nupkg.metadata", ".signature.p7s", "PackageReadme.md", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net452/System.Linq.Dynamic.Core.dll", "lib/net452/System.Linq.Dynamic.Core.pdb", "lib/net452/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/net5.0/System.Linq.Dynamic.Core.dll", "lib/net5.0/System.Linq.Dynamic.Core.pdb", "lib/net5.0/System.Linq.Dynamic.Core.xml", "lib/net6.0/System.Linq.Dynamic.Core.dll", "lib/net6.0/System.Linq.Dynamic.Core.pdb", "lib/net6.0/System.Linq.Dynamic.Core.xml", "lib/net7.0/System.Linq.Dynamic.Core.dll", "lib/net7.0/System.Linq.Dynamic.Core.pdb", "lib/net7.0/System.Linq.Dynamic.Core.xml", "lib/net8.0/System.Linq.Dynamic.Core.dll", "lib/net8.0/System.Linq.Dynamic.Core.pdb", "lib/net8.0/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/netstandard2.1/System.Linq.Dynamic.Core.dll", "lib/netstandard2.1/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.1/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "logo.png", "system.linq.dynamic.core.1.3.7.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.7.0": {"sha512": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "type": "package", "path": "system.reflection.emit.lightweight/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Cng/5.0.0": {"sha512": "jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "type": "package", "path": "system.security.cryptography.cng/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.5.0.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/6.0.1": {"sha512": "ynmbW2GjIGg9K1wXmVIRs4IlyDolf0JXNpzFQ8JCVgwM+myUC2JeUggl2PwQig2PNVMegKmN1aAx7WPQ8tI3vA==", "type": "package", "path": "system.security.cryptography.pkcs/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.6.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/6.0.1": {"sha512": "5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "type": "package", "path": "system.security.cryptography.xml/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/net461/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.6.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Parallel/4.3.0": {"sha512": "cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "type": "package", "path": "system.threading.tasks.parallel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.Tasks.Parallel.dll", "lib/netstandard1.3/System.Threading.Tasks.Parallel.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.Parallel.dll", "ref/netcore50/System.Threading.Tasks.Parallel.xml", "ref/netcore50/de/System.Threading.Tasks.Parallel.xml", "ref/netcore50/es/System.Threading.Tasks.Parallel.xml", "ref/netcore50/fr/System.Threading.Tasks.Parallel.xml", "ref/netcore50/it/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ja/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ko/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ru/System.Threading.Tasks.Parallel.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.Parallel.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/System.Threading.Tasks.Parallel.dll", "ref/netstandard1.1/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/de/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/es/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/fr/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/it/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ja/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ko/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ru/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/zh-hans/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/zh-hant/System.Threading.Tasks.Parallel.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.parallel.4.3.0.nupkg.sha512", "system.threading.tasks.parallel.nuspec"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "UFU.CoreFX/5.0.3": {"sha512": "SjT5SqLrl9a3j47W7aEvhmQFEC9aCScMFC8RYpYzjamCXl93U3B/m3XWg/F0NBG8RUCIlLziJTnpKKE+Lm+lpA==", "type": "package", "path": "ufu.corefx/5.0.3", "files": [".nupkg.metadata", "lib/net6.0/UFU.CoreFX.dll", "lib/net6.0/UFU.CoreFX.runtimeconfig.json", "lib/net6.0/UFU.CoreFX.xml", "ufu.corefx.5.0.3.nupkg.sha512", "ufu.corefx.nuspec"]}, "UFU.IoT.UI/2.3.1": {"sha512": "UqwIr90jRE807BXryuArUcqJmO1BcIKU8Uw6G2Itf/pT8Eliizi3Y0vBqbMj2++xyAOjfN9WdnT+pz+rRSk/cQ==", "type": "package", "path": "ufu.iot.ui/2.3.1", "files": [".nupkg.metadata", "lib/netstandard2.0/UFU.IoT.UI.dll", "ufu.iot.ui.2.3.1.nupkg.sha512", "ufu.iot.ui.nuspec"]}, "UFU.IoT.Shared/1.0.0": {"type": "project", "path": "../UFU.IoT.Shared/UFU.IoT.Shared.csproj", "msbuildProject": "../UFU.IoT.Shared/UFU.IoT.Shared.csproj"}}, "projectFileDependencyGroups": {".NETCoreApp,Version=v2.2": ["Microsoft.NETCore.App >= 2.2.8", "NPOI >= 2.6.0", "RulesEngine >= 5.0.3", "UFU.CoreFX >= 5.0.3", "UFU.IoT.Shared >= 1.0.0", "UFU.IoT.UI >= 2.3.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "3.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj", "projectName": "UFU.IoT", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\publish\\linux-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj": {"projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.8, )", "autoReferenced": true}, "NPOI": {"target": "Package", "version": "[2.6.0, )"}, "RulesEngine": {"target": "Package", "version": "[5.0.3, )"}, "UFU.CoreFX": {"target": "Package", "version": "[5.0.3, )"}, "UFU.IoT.UI": {"target": "Package", "version": "[2.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "logs": [{"code": "NU1202", "level": "Error", "message": "包 UFU.CoreFX 5.0.3 与 netcoreapp2.2 (.NETCoreApp,Version=v2.2) 不兼容。 包 UFU.CoreFX 5.0.3 支持: net6.0 (.NETCoreApp,Version=v6.0)", "libraryId": "UFU.CoreFX", "targetGraphs": [".NETCoreApp,Version=v2.2"]}, {"code": "NU1201", "level": "Error", "message": "项目 UFU.IoT.Shared 与 netcoreapp2.2 (.NETCoreApp,Version=v2.2) 不兼容。 项目 UFU.IoT.Shared 支持: net6.0 (.NETCoreApp,Version=v6.0)", "libraryId": "UFU.IoT.Shared", "targetGraphs": [".NETCoreApp,Version=v2.2"]}, {"code": "NU1202", "level": "Error", "message": "包 UFU.CoreFX 5.0.3 与 netcoreapp2.2 (.NETCoreApp,Version=v2.2) / linux-x64 不兼容。 包 UFU.CoreFX 5.0.3 支持: net6.0 (.NETCoreApp,Version=v6.0)", "libraryId": "UFU.CoreFX", "targetGraphs": [".NETCoreApp,Version=v2.2/linux-x64"]}, {"code": "NU1201", "level": "Error", "message": "项目 UFU.IoT.Shared 与 netcoreapp2.2 (.NETCoreApp,Version=v2.2) / linux-x64 不兼容。 项目 UFU.IoT.Shared 支持: net6.0 (.NETCoreApp,Version=v6.0)", "libraryId": "UFU.IoT.Shared", "targetGraphs": [".NETCoreApp,Version=v2.2/linux-x64"]}]}