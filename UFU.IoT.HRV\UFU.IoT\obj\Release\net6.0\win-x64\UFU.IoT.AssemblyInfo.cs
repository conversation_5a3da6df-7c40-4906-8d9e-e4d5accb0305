//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("悠服科技(UFUTECH)")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("release")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright (C) 悠服科技(UFUTECH) 2023")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("悠服科技物联网系统")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("3.0.0")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("3.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("UFU.IoT")]
[assembly: System.Reflection.AssemblyTitleAttribute("UFU.IoT")]
[assembly: System.Reflection.AssemblyVersionAttribute("3.0.0")]

// 由 MSBuild WriteCodeFragment 类生成。

