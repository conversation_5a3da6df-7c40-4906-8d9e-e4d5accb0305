﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Concurrent;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Services;

namespace HX.HRV.Web.Services
{
    /// <summary>
    /// 虚拟设备连接
    /// </summary>
    public class VirtualDeviceConnect : IConnect
    {
        /// <summary>
        /// 虚拟设备
        /// </summary>
        public static Dictionary<string, DeviceModel> VirtualDevices { get; private set; } = new Dictionary<string, DeviceModel>();

        /// <summary>
        /// 真实设备-虚拟设备映射()
        /// </summary>
        public static Dictionary<string, List<string>> RealVirtualMapping { get; private set; } = new Dictionary<string, List<string>>();

        /// <summary>
        /// 虚拟设备连接
        /// </summary>
        public static Dictionary<string, VirtualDeviceConnect> VirtualDeviceConnects { get; private set; } = new Dictionary<string, VirtualDeviceConnect>();

        static VirtualDeviceConnect()
        {
            IoTService.OnSavedData += OnSavedData;

            //更新虚拟设备
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        await Task.Delay(10000);
                        using (DataRepository db = new DataRepository(UserInfo.System))
                        {
                            //更新虚拟设备
                            var virtualDevices = await db.Query<DeviceModel>().AsNoTracking().Where(m => m.Data.IsVirtual == true).ToListAsync();
                            VirtualDevices = virtualDevices.ToDictionary(k => k.Id, v => v.Data);

                            //更新映射
                            var deviceMappings = new Dictionary<string, List<string>>();
                            foreach (var virtualDevice in VirtualDevices.Values)
                            {
                                var realDeviceIds = virtualDevice.VirtualDeviceMappings.Select(m => m.RealDeviceId);
                                foreach (var realDeviceId in realDeviceIds)
                                {
                                    if (deviceMappings.ContainsKey(realDeviceId))
                                    {
                                        if (!deviceMappings[realDeviceId].Contains(virtualDevice.Id))
                                        {
                                            deviceMappings[realDeviceId].Add(virtualDevice.Id);
                                        }
                                    }
                                    else
                                    {
                                        deviceMappings[realDeviceId] = new List<string> { virtualDevice.Id };
                                    }
                                }
                            }
                            RealVirtualMapping = deviceMappings;
                        }

                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, "更新虚拟设备异常");
                    }
                }
            });
        }


        private static void OnSavedData(ConnectDevice connect, DataModel data)
        {
            try
            {
                if (!RealVirtualMapping.ContainsKey(connect.DeviceId))
                {
                    return;
                }

                var virtualDeviceIds = RealVirtualMapping[connect.DeviceId];
                foreach (var virtualDeviceId in virtualDeviceIds)
                {
                    var virtualDevice = VirtualDevices[virtualDeviceId];
                    var mappings = virtualDevice?.VirtualDeviceMappings?.Where(m => m.RealDeviceId == connect.DeviceId);
                    if (mappings == null || mappings.Count() == 0)
                    {
                        continue;
                    }

                    var virtualType = IoTService.DeviceTypes[virtualDevice!.TypeId];
                    var realType = connect.Device.DeviceType;

                    //判断是否存在设备连接
                    VirtualDeviceConnect virtualDeviceConnect = null;
                    if (!VirtualDeviceConnects.ContainsKey(virtualDeviceId))
                    {
                        virtualDeviceConnect = new VirtualDeviceConnect(virtualDeviceId, virtualDevice.TypeId, virtualType.Secret);
                        VirtualDeviceConnects.TryAdd(virtualDeviceId, virtualDeviceConnect);
                    }
                    else
                    {
                        virtualDeviceConnect = VirtualDeviceConnects[virtualDeviceId];
                    }


                    var dic = new Dictionary<string, dynamic>();
                    foreach (var mapping in mappings)
                    {
                        if (data.DataDic.ContainsKey(mapping.RealDataItem))
                        {
                            if (mapping.AutoMapping == true)
                            {
                                var dataItem = realType.DataItemList.FirstOrDefault(m => m.Key == mapping.RealDataItem);
                                if (dataItem?.DataType == DataItemType.Int || dataItem?.DataType == DataItemType.Float)
                                {
                                    dic[mapping.VirtualDataItem] = data.DataDic[mapping.RealDataItem] * mapping.Coefficient;
                                }
                                else
                                {
                                    dic[mapping.VirtualDataItem] = data.DataDic[mapping.RealDataItem];
                                }
                            }
                            else
                            {
                                LogTool.Logger.Error($"虚拟设备非自动映射未处理：[{connect.Device.Id}-{mapping.RealDataItem}]:[{virtualDevice.Id}-{mapping.VirtualDataItem}]");
                            }
                        }
                    }

                    //发送数据到设备
                    virtualDeviceConnect.SendDataToServer(data.MsgId, dic);

                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, "虚拟设备订阅OnSavedData异常");
            }
        }

        /// <summary>
        /// 开始服务
        /// </summary>
        public static void Start()
        {

        }

        /// <summary>
        /// 消息编号
        /// </summary>
        private int _msgId = 0;
        /// <summary>
        /// 数据缓存
        /// </summary>
        private ConcurrentDictionary<string, Msg> datas = new ConcurrentDictionary<string, Msg>();
        /// <summary>
        /// 连接编号
        /// </summary>
        public string ConnectId { get; } = Guid.NewGuid().ToString();
        /// <summary>
        /// 连接方式
        /// </summary>
        public ConnectMode ConnectMode { get; } = ConnectMode.Tcp;
        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceTypeId { get; private set; }
        /// <summary>
        /// 设备秘钥
        /// </summary>
        public string DeviceSecret { get; private set; }
        /// <summary>
        /// 设备Mac地址
        /// </summary>
        public string Mac { get; private set; }
        /// <summary>
        /// 芯片编号
        /// </summary>
        public string ChipSN { get; private set; }
        /// <summary>
        /// 设备编号
        /// </summary>
        public string? DeviceId { get; private set; }
        /// <summary>
        /// 登录Token
        /// </summary>
        public string? Token { get; private set; }
        /// <summary>
        /// 错误次数
        /// </summary>
        public int ExceptionTimes { get; private set; }
        /// <summary>
        /// 最后数据时间
        /// </summary>
        public DateTime LatestDataTime { get; private set; }

        /// <summary>
        /// 消息编号
        /// </summary>
        public int MsgId
        {
            get
            {
                _msgId++;
                if (_msgId >= 10000)
                {
                    _msgId = 1;
                }
                return _msgId;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public VirtualDeviceConnect(string virtualDeviceId, string deviceTypeId, string deviceSecret)
        {
            this.Mac = virtualDeviceId;
            this.ChipSN = virtualDeviceId;
            this.DeviceId = virtualDeviceId;
            this.DeviceTypeId = deviceTypeId;
            this.DeviceSecret = deviceSecret;
        }

        /// <summary>
        /// 发送二进制数据到设备（收到服务器消息）
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public bool SendBinary(byte[] data)
        {
            return false;
        }

        /// <summary>
        /// 发送文本数据到设备（收到服务器消息）
        /// </summary>
        /// <param name="data"></param>
        public bool SendText(string data)
        {
            try
            {
                if (data == null)
                {
                    return false;
                }

                Msg msg = JsonTool.Deserialize<Msg>(data);
                //发送数据到设备
                if (msg.CMD == CMD.Set)
                {
                    //返回设备Id表示注册成功
                    if (!string.IsNullOrWhiteSpace(msg?.Device?.Id))
                    {
                        DeviceId = msg.Device.Id;
                    }
                    //返回Token表示登录成功
                    if (!string.IsNullOrWhiteSpace(msg.Device?.Token))
                    {
                        Token = msg.Device.Token;
                        //登录成功后发送缓存数据到服务器
                        SendCacheDataToServer();
                    }

                    //发送数据到设备
                    if (msg.Data != null)
                    {
                        var datas = new Dictionary<string, Dictionary<string, object>>();
                        var virtualDevice = VirtualDevices[DeviceId!];
                        var deviceMappings = virtualDevice.VirtualDeviceMappings;
                        var virtualType = IoTService.DeviceTypes[virtualDevice!.TypeId];
                        foreach (var item in msg.Data)
                        {
                            var mappings = virtualDevice.VirtualDeviceMappings.Where(m => m.VirtualDataItem == item.Key).ToList();
                            foreach (var mapping in mappings)
                            {
                                //查找真实设备
                                var realDeviceId = mapping.RealDeviceId;
                                Dictionary<string, dynamic> realDeviceData;
                                if (!datas.ContainsKey(realDeviceId))
                                {
                                    realDeviceData = new Dictionary<string, dynamic>();
                                    datas[realDeviceId] = realDeviceData;
                                }
                                else
                                {
                                    realDeviceData = datas[realDeviceId];
                                }

                                //var realType = connect.Device.DeviceType;

                                //数据项转换
                                if (mapping.AutoMapping)
                                {
                                    realDeviceData[mapping.RealDataItem] = item.Value;
                                }
                                else
                                {
                                    var realValue = mapping.DataMappings.FirstOrDefault(m => m.VirtualValue == item.Value.ToString())?.RealValue;
                                    if (realValue != null)
                                    {
                                        realDeviceData[mapping.RealDataItem] = realValue;
                                    }
                                }
                            }
                        }

                        //发送数据
                        foreach (var item in datas)
                        {
                            var realDeviceConn = IoTService.Devices.Values.FirstOrDefault(m => m.DeviceId == item.Key);
                            if (realDeviceConn != null)
                            {
                                Msg realMsg = new Msg
                                {
                                    MsgId = msg.MsgId,
                                    CMD = CMD.Set,
                                    Device = new DeviceInfo { Id = item.Key },
                                    Data = item.Value
                                };
                                realDeviceConn.Connect.SendText(JsonTool.SerializeIgnoreNull(realMsg));
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
                }

                //错误检查
                if (msg.Error.HasValue && msg.Error != Error.Success)
                {
                    //设备未注册
                    if (msg.Error == Error.Unregistered)
                    {
                        DeviceId = null;
                        Token = null;
                    }
                    //设备未登录
                    else if (msg.Error == Error.Unauthorized)
                    {
                        Token = null;
                    }
                    //其他异常
                    else
                    {
                        Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss:fff")}Msg出现错误：{msg.Error},{data}");
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"发送文本消息到设备异常");
                return false;
            }
        }

        /// <summary>
        /// 设备注册
        /// </summary>
        private void Reg()
        {
            Msg msg = new Msg
            {
                MsgId = MsgId,
                CMD = CMD.Reg,
                Device = new DeviceInfo
                {
                    Type = DeviceTypeId,
                    Secret = DeviceSecret,
                    MAC = Mac,
                    ChipSN = ChipSN,
                    AppVer = "1.0"
                }
            };
            IoTService.ReceiveDeviceMsgV1(this, JsonTool.SerializeIgnoreNull(msg));
        }

        /// <summary>
        /// 设备登录
        /// </summary>
        private void Login()
        {
            Msg msg = new Msg
            {
                MsgId = MsgId,
                CMD = CMD.Login,
                Device = new DeviceInfo
                {
                    Id = DeviceId,
                    MAC = Mac,
                    Secret = DeviceSecret,
                    AppVer = "1.0"
                }
            };
            IoTService.ReceiveDeviceMsgV1(this, JsonTool.SerializeIgnoreNull(msg));
        }

        /// <summary>
        /// 发送缓存数据到服务器
        /// </summary>
        private void SendCacheDataToServer()
        {
            try
            {
                foreach (var item in datas.Keys)
                {
                    if (datas.TryRemove(item, out Msg deviceData))
                    {
                        deviceData.Device.Id = DeviceId;
                        deviceData.Device.Token = Token;
                        IoTService.ReceiveDeviceMsgV1(this, JsonTool.SerializeIgnoreNull(deviceData));
                    }
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"发送缓存数据异常");
            }
        }


        ///// <summary>
        ///// 收到设备数据
        ///// </summary>
        ///// <param name="data"></param>
        //public void ReceivedDeviceData(string data)
        //{
        //    //回复服务器
        //    Msg msg = new Msg();
        //    msg.MsgId = msg.MsgId;
        //    msg.CMD = CMD.Res;
        //    msg.Device = new DeviceInfo
        //    {
        //        Id = DeviceId,
        //        Token = Token
        //    };
        //    msg.Error = Error.Success;
        //    SendDataToServer(msg);

        //}

        /// <summary>
        /// 收到设备数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="time"></param>
        public void SendDataToServer(int? msgId, Dictionary<string, dynamic> data, DateTime? time = null)
        {
            //发送数据到设备
            Msg msg = new Msg
            {
                MsgId = msgId ?? MsgId,
                CMD = CMD.Set,
                Device = new DeviceInfo
                {
                    Id = DeviceId,
                    Token = Token
                },
                Data = data
            };

            //已登录，直接发送
            if (!string.IsNullOrWhiteSpace(Token))
            {
                IoTService.ReceiveDeviceMsgV1(this, JsonTool.SerializeIgnoreNull(msg));
            }
            //未登录
            else
            {
                datas.AddOrUpdate(Guid.NewGuid().ToString(), msg, (k, v) => msg);

                //检查设备注册
                if (string.IsNullOrWhiteSpace(DeviceId))
                {
                    Reg();
                }

                //检查设备登录
                else if (string.IsNullOrWhiteSpace(Token))
                {
                    Login();
                }
            }
        }

        /// <summary>
        /// 设备连接断开
        /// </summary>
        public void Disconnected()
        {
            var device = IoTService.Devices.Values.Where(m => m.Connect.ConnectId == this.ConnectId).FirstOrDefault();
            if (device != null)
            {
                IoTService.DeviceOffline(device);
            }
        }
    }
}
