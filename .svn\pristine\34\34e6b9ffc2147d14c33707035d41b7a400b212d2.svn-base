﻿using HX.HRV.Shared.Models.ViewModel;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Pages.Client.SystemConfig;
using Masa.Blazor;
using Microsoft.JSInterop;
using System.Text;
using System.Text.Json.Nodes;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;
using System.Net.WebSockets;
using System.Text.Json;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Shared.Services
{
    public class DeviceStateService : IDisposable
    {
        private readonly StateService _stateService;
        private readonly IJSRuntime _jsRuntime;
        private readonly CancellationTokenSource _cancellationToken;

        public IPopupService PopupService;
        public ClientWebSocket ClientSocket { get; private set; }


        private bool _disposedValue;

        public DeviceStateService(StateService stateService, IJSRuntime jsRuntime, IPopupService popupService)
        {
            _stateService = stateService;
            _jsRuntime = jsRuntime;
            PopupService = popupService;
            _cancellationToken = new CancellationTokenSource();
            Task.Run(async () =>
            {
                await InitHxSystemConfig();
                await InitDevicesAsync();
                InitWebsocket();
                await InitPatientRecordModelListAsync();
                UpdateDeviceTaskTimer();
                return Task.CompletedTask;
            }).Wait();
        }

        public List<DeviceStatusViewModel> SerialPortDeviceDataList =>
            DeviceDataList.Where(m => SerialPortDevices.Any(x => x.DeviceSN == m.Device.DeviceSN))?.ToList() ??
            new List<DeviceStatusViewModel>();

        public List<SerialPortDevice> SerialPortDevices { get; private set; } = new List<SerialPortDevice>();


        public HxSystemConfigModel SystemConfig { get; private set; } = new();

        // 示例状态
        public List<DeviceStatusViewModel> DeviceDataList { get; private set; } = new List<DeviceStatusViewModel>();

        // 状态变更事件
        public event Action OnAfterBeginCheck;

        //设备变更
        public event Action OnDeviceNumberChange;
        public event Action<JsonNode> OnMessageReceived;
        private const string DeviceTypeId = "2407160100000001";

        public async Task InitDevicesAsync()
        {
            var queryParam = new Dictionary<string, string>
            {
                { "typeId", DeviceTypeId },
                { "page", "0 " },
                { "pageSize", "100" },
            };
            var deviceListUrl = "/api/v2.0/IoT/Devices/List";
            var result = await _stateService
                .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
            var deviceList = result?.Data
                ?.Select(m => m.Data)
                ?.OrderBy(m => int.TryParse(m.Name, out var num) ? num : 0)
                .ToList();
            if (SystemConfig.LayoutConfig is { DisplayNumber: > 0 })
            {
                DeviceDataList = deviceList?.Take(SystemConfig.LayoutConfig.DisplayNumber)?.Select(m =>
                {
                    var batteryStr = m?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");

                    var battery = batteryStr?.Value?.Value switch
                    {
                        JsonElement value => value.GetDouble(),
                        double d => d,
                        int i => i,
                        _ => 0d
                    };
                    return new DeviceStatusViewModel()
                    {
                        Device = m,
                        DeviceSN = m.DeviceSN,
                        Battery = battery,
                        DeviceStatus = m.IsOnline ? EnumDeviceStatus.空闲 : EnumDeviceStatus.离线,
                    };
                }).ToList();
            }
            else
            {
                DeviceDataList = deviceList?.Select(m =>
                {
                    var batteryStr = m?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");

                    var battery = batteryStr?.Value?.Value switch
                    {
                        JsonElement value => value.GetDouble(),
                        double d => d,
                        int i => i,
                        _ => 0d
                    };
                    return new DeviceStatusViewModel()
                    {
                        Device = m,
                        DeviceSN = m.DeviceSN,
                        Battery = battery,
                        DeviceStatus = m.IsOnline ? EnumDeviceStatus.空闲 : EnumDeviceStatus.离线,
                    };
                }).ToList();
            }
        }

        /// <summary>
        /// 初始化系统配置
        /// </summary>
        public async Task InitHxSystemConfig()
        {
            var systemConfig = await _stateService
                .GetAsJsonAsync<DataModel<HxSystemConfigModel>>(
                    "/api/v2.0/HRV_HX/HxSystemConfig/GetSystemConfigModelDetail");
            SystemConfig = systemConfig?.Data?.Data ?? new HxSystemConfigModel
            {
                IsAutoPrint = false,
                CheckTimeList = new(),
                PatientSource = new()
            };
            SystemConfig.SystemRateConfig ??= DefalultData.SystemRateConfig;
        }


        public void InitWebsocket()
        {
            // _ = UpdateDeviceTask();
            _ = Task.Run(async () =>
            {
                while (!_cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        if (ClientSocket == null ||
                            (ClientSocket.State != WebSocketState.Open
                             && ClientSocket.State != WebSocketState.Connecting))
                        {
                            var currentUri =
                                _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
                            var webSocketUrl =
                                $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
                            ClientSocket = new ClientWebSocket();
                            var serverUri = new Uri(webSocketUrl);
                            await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
                            await Task.Delay(50);
                        }

                        if (ClientSocket.State != WebSocketState.Open)
                        {
                            continue;
                        }

                        var buffer = new byte[1024 * 1024];
                        var result = await ClientSocket.ReceiveAsync(buffer, CancellationToken.None);
                        if (result.MessageType == WebSocketMessageType.Close)
                        {
                            var currentUri =
                                _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
                            var webSocketUrl =
                                $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
                            ClientSocket = new ClientWebSocket();
                            var serverUri = new Uri(webSocketUrl);
                            await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
                            await Task.Delay(50);
                        }

                        if (result.MessageType != WebSocketMessageType.Close && result.Count > 0)
                        {
                            var msg = Encoding.UTF8.GetString(buffer, 0, result.Count);
                            var jsonNode = JsonNode.Parse(msg);
                            await OnReceiveMessageAsync(jsonNode);
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(@" 
DefaultLayout,OnReceiveMessageAsync error：
" + e.StackTrace.ToString());
                        if (ClientSocket != null && ClientSocket.State != WebSocketState.Open)
                        {
                            var currentUri =
                                _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
                            var webSocketUrl =
                                $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
                            ClientSocket = new ClientWebSocket();
                            var serverUri = new Uri(webSocketUrl);
                            await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
                            await Task.Delay(50);
                        }
                    }
                }
            });
        }

        private async Task OnReceiveMessageAsync(JsonNode jsonNode)
        {
            try
            {
                var deviceSn = jsonNode?["SN"]?.ToString();
                OnMessageReceived?.Invoke(jsonNode);
                if (!string.IsNullOrEmpty(deviceSn))
                {
                    Console.WriteLine("【接收数据】：" + jsonNode.ToJsonString());
                    var deviceViewModel = DeviceDataList.FirstOrDefault(m =>
                        m.DeviceSN.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
                    if (deviceViewModel != null)
                    {
                        if (!string.IsNullOrEmpty(deviceSn))
                        {
                            var status = jsonNode["Status"]?.GetValue<int>();
                            if (status != null && Enum.IsDefined(typeof(EnumDeviceStatus), status))
                            {
                                if ((EnumDeviceStatus)status == EnumDeviceStatus.检测中)
                                {
                                    if (deviceViewModel.DeviceStatus == EnumDeviceStatus.空闲)
                                    {
                                        OnDeviceNumberChange?.Invoke();
                                        OnAfterBeginCheck?.Invoke();
                                    }

                                    deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
                                    deviceViewModel.IsBegining = false;
                                    deviceViewModel.IsPrint = false;
                                }
                                else if ((EnumDeviceStatus)status == EnumDeviceStatus.空闲 &&
                                         deviceViewModel.DeviceStatus == EnumDeviceStatus.检测中)
                                {
                                    deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
                                }
                                else
                                {
                                    deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
                                }
                            }
                            var battery = jsonNode["Battery"]?.GetValue<double>();
                            if (battery != null)
                            {
                                deviceViewModel.Battery = (double)battery;
                            }
                            var spo2 = jsonNode["SPO2"]?.GetValue<uint>();
                            if (spo2 != null)
                            {
                                deviceViewModel.OnSPO2Data?.Invoke(spo2 ?? 0);
                                deviceViewModel.SPO2Data.Add((uint)spo2);
                            }

                            var bmp = jsonNode["Bmp"]?.GetValue<uint>();
                            if (bmp != null)
                            {
                                deviceViewModel.OnReciveBMPDatas?.Invoke(bmp ?? 0);
                                deviceViewModel.BMPDatas.Add((uint)bmp);
                            }

                            if (jsonNode["PPG"] is JsonArray ppg)
                            {
                                var ppgData = ppg.GetValues<int>()?.ToList();
                                deviceViewModel.OnRecivePPGData?.Invoke(ppgData);
                                deviceViewModel.PPGData.AddRange(ppgData);
                            }

                            if (jsonNode["ORGPPG"] is JsonArray orgPpg)
                            {
                                var ppgData = orgPpg.GetValues<uint>()?.ToList();
                                deviceViewModel.OnOrgPPGData?.Invoke(ppgData);
                                deviceViewModel.OrgPPGData.AddRange(ppgData);
                            }
                            if (jsonNode["IMU"] is JsonArray imu)
                            {
                                var index = 0;
                                foreach (JsonArray imuJsonValue in imu)
                                {
                                    var  imuData = imuJsonValue.GetValues<short>();
                                    deviceViewModel.ImuDatas[index++].AddRange(imuData);
                                }
                            }
                            if (jsonNode[nameof(HXDataType.TEMPSKIN)] is JsonArray skinTemp)
                            {
                                var imuData = skinTemp.GetValues<float>();
                                deviceViewModel.SKINTEMPs.AddRange(imuData);
                            }
                            if (jsonNode["SCL"] is JsonArray scl)
                            {
                                var sclData = scl.GetValues<float>()?.ToList();
                                deviceViewModel.SCLDatas.AddRange(sclData);
                                deviceViewModel.OnReciveSCLDatasData?.Invoke(sclData);
                            }
                            if (jsonNode["SCR"] is JsonArray scr)
                            {
                                var scrData = scr.GetValues<float>()?.ToList();
                                deviceViewModel.SCRDatas.AddRange(scrData);
                                deviceViewModel.OnReciveSCRDatasData?.Invoke(scrData);
                            }
                            if (jsonNode["EDA"] is JsonArray eda)
                            {
                                var edaData = eda.GetValues<float>()?.ToList();
                                deviceViewModel.OnReciveEDAsData?.Invoke(edaData);
                                deviceViewModel.EDAs.AddRange(edaData);
                            }
                            var deviceRecord = jsonNode["DeviceRecord"]?.GetValue<PatientRecordModel>();
                            if (deviceRecord != null)
                            {
                                deviceViewModel.RecordModel = deviceRecord;
                            }
                            var progress = jsonNode["Progress"]?.GetValue<double>();
                            if (progress != null)
                            {
                                deviceViewModel.Progress = (double)progress;
                            }
                            if (jsonNode.AsObject().ContainsKey("CollectTime"))
                            {
                                var collectTime = jsonNode["CollectTime"]?.ToString();
                                if (collectTime != null && TimeSpan.TryParse(collectTime, out var timeSpan))
                                {
                                    deviceViewModel.CollectTime = timeSpan;
                                }
                            }
                            var isChecked = jsonNode["IsChecked"]?.GetValue<bool>();
                            if (isChecked ?? false)
                            {
                                deviceViewModel.ClearData();
                            }
                            var isPrint = jsonNode["IsPrint"]?.GetValue<bool>();
                            if (isPrint == true)
                            {
                                var printId = jsonNode["PrintId"]?.ToString();
                                var action = jsonNode["Action"]?.ToString();
                                await _jsRuntime.InvokeAsync<object>("SendPrintToWebView", printId,action);
                            }
                            deviceViewModel.NotifyPropertyChanged();
                            deviceViewModel.TriggerOnReceiveData(jsonNode);
                        }
                    }
                    else
                    {
                        var status = jsonNode["Status"]?.GetValue<int>();
                        if (status != null && Enum.IsDefined(typeof(EnumDeviceStatus), status))
                        {
                            if ((EnumDeviceStatus)status == EnumDeviceStatus.空闲)
                            {
                                var deviceListUrl = "/api/v2.0/IoT/Devices/List";
                                var queryParam = new Dictionary<string, string>
                                {
                                    { "deviceSN", deviceSn },
                                    { "page", "0 " },
                                    { "pageSize", "1" },
                                };
                                var result = await _stateService
                                    .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
                                var device = result?.Data.FirstOrDefault()?.Data;
                                if (device != null)
                                    DeviceDataList.Add(new DeviceStatusViewModel()
                                    {
                                        Device = device,
                                        DeviceSN = device.DeviceSN,
                                        Battery = 0,
                                        DeviceStatus = EnumDeviceStatus.空闲,
                                    });
                                OnDeviceNumberChange?.Invoke();
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($@"OnReceiveMessageAsync error：
{e}");
            }
        }

        private void UpdateDeviceTaskTimer()
        {
            _ = Task.Run(async () =>
            {
                await Task.Delay(2000);
                while (true)
                {
                    try
                    {
                        await Task.Delay(2000);
                        if (_cancellationToken.IsCancellationRequested)
                        {
                            break;
                        }

                        var deviceListUrl = "/api/v2.0/IoT/Devices/List";
                        var queryParam = new Dictionary<string, string>
                        {
                            { "typeId", DeviceTypeId },
                            { "page", "0 " },
                            { "pageSize", "100" },
                        };
                        var result = await _stateService
                            .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
                        var devices = result
                            ?.Data
                            ?.Select(m => m.Data)
                            ?.ToList();
                        if (devices != null && DeviceDataList.Exists(m => !devices.Exists(x => x.Id == m.Device.Id)))
                        {
                            DeviceDataList.RemoveAll(m => !devices.Exists(x => x.Id == m.Device.Id));
                            OnDeviceNumberChange?.Invoke();
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine("HXDefaultLayout  Error" + e.Message);
                    }
                }
            }, _cancellationToken.Token);
        }

        private async Task InitPatientRecordModelListAsync()
        {
            var deviceIds = DeviceDataList.Select(m => m.Device.Id).ToArray();
            var res = await _stateService
                .GetAsJsonAsync<List<PatientRecordModel>>(
                    "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelList", new Dictionary<string, string>()
                    {
                        { "deviceIds", string.Join(",", deviceIds) }
                    });
            var recordModels = res?.Data?.ToList();
            if (recordModels != null)
            {
                foreach (var record in recordModels)
                {
                    var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == record.DeviceId);
                    if (deviceData != null && deviceData.RecordModel == null) deviceData.RecordModel = record;
                }
            }
        }

        public void Dispose()
        {
            _cancellationToken.Cancel();
        }

        public PatientRecordModel GetPatientRecordModel(string deviceId)
        {
            var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
            return deviceData?.RecordModel;
        }

        public DeviceStatusViewModel GetDeviceStatusViewModel(string deviceId)
        {
            var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
            return deviceData;
        }

        public event Action OnSummaryChange;
    }
}