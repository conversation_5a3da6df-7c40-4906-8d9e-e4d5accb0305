﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Pages.Client.DeviceStatus
@inject  RealTimeMonitoringChartRow InjectedComponent
    @if (DeviceStatusData != null)
    {
        <MCard Style="height: 100%;" Class="rounded-lg">
            <MCardTitle Class="pa-0 d-flex  align-center justify-space-between"
                        Style="background-color: #bbd4f0; height:2rem;border-radius: 16px 16px 0px 0px !important;">
                <DeviceStatusCardHead 
                                      DeviceStatusData="DeviceStatusData"
                                      LinkType="1">
                </DeviceStatusCardHead>
            </MCardTitle>
            <MCardText Style="height:90%;" Class="pa-1">
                @if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.检测中)
                {
                    <MCardText Style="background-color: #e0ecfa;height:90%;" Class="pa-1">
                        <DynamicComponent Type="InjectedComponent.GetType()" Parameters="@(new Dictionary<string, object>
                                                                                         {
                                                                                             { "DeviceStatusData", DeviceStatusData },
                                                                                         })">
                        </DynamicComponent>
                    </MCardText>
                }
                else if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.空闲)
                {
                    <div Style="height: 100%;" class="d-flex align-center justify-center">
                        <img src="/images/icon/free_fill.png" style="width:38px; " alt=""/>
                        <MLabel Style="font-size:2rem">空闲</MLabel>
                    </div>
                }
                else
                {
                    <div style="height: 100%;" class="d-flex align-center justify-center">
                        <img src="/images/icon/<EMAIL>" style="width:38px" alt=""/>
                        <MLabel Style="font-size: 2rem"> 离线</MLabel>
                    </div>
                }
            </MCardText>
        </MCard>
    }
@code {
    [Parameter] public DeviceStatusViewModel DeviceStatusData { get; set; }
}