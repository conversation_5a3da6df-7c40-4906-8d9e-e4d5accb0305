﻿@page "/Client/SCI/DataAnalysis/{RecordId}"
@attribute [Permission("数据分析", Icon = "shuju", MenuOrder = "5")]
@using System.Globalization
@using System.Net.WebSockets
@using System.Text
@using System.Text.Json
@using System.Text.Json.Nodes
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Services
@using HX.Base.Shared.Components
@using HX.HRV.SCI.Shared.Models
@using UFU.CoreFX.Utils
@using UFU.IoT.Shared.Models
@using Masa.Blazor.Presets
@inject IJSRuntime JS
@implements IDisposable
<Style>

    .hrv-report-fields {
        display: flex;
        flex-wrap: wrap; /* 支持换行布局 */
        border: 1px solid #ddd; /* 可选：边框 */
        background-color: #f9f9f9; /* 背景色 */
        font-size: 1.125em;
        padding: 1em 2em 1em 2em;
        flex-flow: row;
        width: 100%;
        justify-content: space-between;
    }

    .field-item {
        display: flex;
        justify-content: center;
        align-items: center;

        flex-direction: row;
        align-content: stretch;
    }

    .hrv-report-label {
        font-weight: bold;
        color: #333;
        text-align: end;
    }

    .hrv-report-value {
        color: #555;
        text-align: left;
    }

    .data-analysis-table {
    }

    .data-analysis-table th,
    .data-analysis-table td {
        border-bottom: 1px solid #ddd;
        padding: 0.125em;
        width: 10em;
    }

    .data-analysis-table tr:first-child {
        border-top: 1px solid #ddd;
    }

    .data-analysis-table th {
        background-color: #e1ebf6;
        color: #333;
    }

    .section-title {
        font-weight: bold;
        color: #333;
        text-align: left;
        font-size: 1.125em;
    }

</Style>
<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<MCard Flat Id="data-analysis" Style="font-size: 4mm">

    <div style="display: flex;   ">
        <div class="hrv-report-fields">
            @if (_fields != null)
            {
                foreach (var field in _fields)
                {
                    <div class="field-item">
                        <div class="hrv-report-label">@(field.Label)：</div>
                        <div class="hrv-report-value">@field.Value</div>
                    </div>
                }
            }
        </div>
    </div>
    <div>
        <div>
            <MContainer Class="d-flex justify-center align-center">
                <div class="d-flex justify-center align-center" id="minMaxValues">
                </div>
                <div class="d-flex justify-center align-center">
                    <MLabel>时间:</MLabel>
                    <PDateDigitalClockPicker @bind-Value="minDate"
                                             Step=@TimeSpan.FromMinutes(1)
                                             TimeFormat="TimeFormat.Hr24">
                        <PDefaultDateTimePickerActivator Filled SoloInverted
                                                         Dense
                                                         Clearable/>
                    </PDateDigitalClockPicker>
                </div>
                <div class="d-flex justify-center align-center">
                    <MLabel>时长:</MLabel>
                    <MTextField HideDetails="true" @bind-Value="_spaceTime" SoloInverted
                                Dense>
                    </MTextField>
                </div>
                <div class="d-flex justify-center align-center">
                    <MButton Type="primary" Style="margin-left: 10px" OnClick="OnDateClick">确定</MButton>
                    <MButton Type="primary" Style="margin-left: 10px" OnClick="()=>isShowName=true">分析</MButton>
                    <MButton Type="primary" Style="margin-left: 10px" Color="blue" OnClick="OnExportClick">导出</MButton>
                </div>
            </MContainer>
        </div>
    </div>
    
    <div style="display: flex">
        <div style="flex:5">
            <div style="display: flex;padding:  0em 2em;   ">
                <div style="width: 100%">
                    @if (!string.IsNullOrEmpty(FileContent))
                    {
                        <HXEchartComponent MinDate="minDate" @ref="childComponentRef" OnDataZoom="HandleDataZoom" FileContent="@FileContent"
                                           Value="1"/>
                    }
                </div>
            </div>
            <div style="display: flex;padding:  0em 2em;   ">
                <div style="width: 100%">
                    @if (!string.IsNullOrEmpty(SCRFileContent))
                    {
                        <HXEchartComponent OnDataZoom="HandleDataZoom" FileContent="@SCRFileContent" Value="1"/>
                    }
                </div>
            </div>
        </div>
        <div style=" flex: 2;display: flex; padding: 2em;">
            <div style="display: flex;padding: 2em; flex-flow: column;">
                <div style="font-size: 1em">
                    <div class="section-title">Time Domain</div>
                    <table class="data-analysis-table">
                        <tr>
                            @foreach (var reportData in ReportDataModel.timeDomain)
                            {
                                <td style="font-weight: bold">@reportData.Key </td>
                            }
                        </tr>
                        <tr>
                            @foreach (var reportData in ReportDataModel.timeDomain)
                            {
                                <td>@Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key) @reportData.Unit.Replace("-", "")</td>
                            }
                        </tr>
                    </table>
                </div>
                <div style="font-size: 1em">
                    <div class="section-title">Frequency domain</div>
                    <table class="data-analysis-table">
                        @{
                            var size = 7;
                            var totalPage = (ReportDataModel.frequencyDomain.Length + size - 1) / size;

                            for (var i = 0; i < totalPage; i++)
                            {
                                <tr>
                                    @for (var j = 0; j < size; j++)
                                    {
                                        var index = i * size + j;
                                        if (index < ReportDataModel.frequencyDomain.Length)
                                        {
                                            <td style="font-weight: bold">@ReportDataModel.frequencyDomain[index].Key </td>
                                        }
                                    }
                                </tr>
                                <tr>
                                    @for (var j = 0; j < size; j++)
                                    {
                                        var index = i * size + j;
                                        if (index < ReportDataModel.frequencyDomain.Length)
                                        {
                                            <td style="font-size: 0.85rem">@(Statistics?.StatisticsDictionary.GetValueOrDefault(ReportDataModel.frequencyDomain[index].Key)+ReportDataModel.frequencyDomain[index].Unit.Replace("-", ""))</td>
                                        }
                                    }
                                </tr>
                            }
                        }

                    </table>
                </div>
                <div style="font-size: 1em">
                    <div class="section-title">Nonlinear</div>
                    <table class="data-analysis-table">
                        <tr>
                            @foreach (var reportData in ReportDataModel.nonlinearAnalysis)
                            {
                                <td style="font-weight: bold">@reportData.Key </td>
                            }
                        </tr>
                        <tr>
                            @foreach (var reportData in ReportDataModel.nonlinearAnalysis)
                            {
                                <td>@(Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key) +reportData.Unit.Replace("-", ""))</td>
                            }
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</MCard>
<PModal 
    @bind-Value="isShowName"
    Persistent
        Title="请输入分析名称"
        Width="500"
        OnSave="HandleOnSave"
        OnCancel="HandleOnCancel">
    <MRow>
        <MCol Cols="12">
            <MTextField @bind-Value="Name"
                        Label="分析名称"
                        Placeholder="请输入分析名称"
                        Dense
                        Outlined />
        </MCol>
    </MRow>
</PModal>
@code {

    private class Field
    {
        public string Label { get; set; }
        public string Value { get; set; }
    }
    public bool isShowName { get; set; }
    private string   Name { get; set; }
    [CascadingParameter(Name = "ClientWebSocket")]
    ClientWebSocket ClientWebSocket { get; set; }
    [Parameter] public string RecordId { get; set; }

    [CascadingParameter(Name = "ALGDataSubjectService")]
    private ALGDataSubjectService ALGDataSubjectService { get; set; }
    private PatientModel patientModel = new PatientModel();
    private ReportDataStatistics Statistics { get; set; }

    private PatientRecordModel patientRecordModel = new PatientRecordModel();
    private UserInfo userModel = new UserInfo();
    private object _option { get; set; }
    [Inject] StateService StateService { get; set; }
    private string FileContent { get; set; }
    private string SCRFileContent { get; set; }
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }
    private async Task HandleOnSave(ModalActionEventArgs args)
    {
        isShowName = true;
        await OnAnalysisButtonClick();
    }

    private void HandleOnCancel()
    {
        Name = string.Empty;
        isShowName = false;
    }
    protected override async Task OnInitializedAsync()
    {
        await InitRecordDataAsync();
        _fields = new Field[]
        {
            new Field { Label = "被试Id", Value = patientModel.Id },
            new Field { Label = "数据名称", Value = patientModel.Name },
            new Field { Label = "开始采集时间", Value = patientRecordModel.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss") },
            new Field { Label = "结束采集时间", Value = patientRecordModel.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss") },
            new Field { Label = "采样率", Value = patientRecordModel.PpgRate.ToString() },
            new Field { Label = "数据长度", Value = patientRecordModel.CollectEndTime - patientRecordModel.CollectStartTime > TimeSpan.Zero ? (patientRecordModel.CollectEndTime - patientRecordModel.CollectStartTime).ToString(@"hh\:mm\:ss", CultureInfo.InvariantCulture) : "" }
        };
        var res = await InternalHttpClientService.GetPatientRecordModelDetailById(RecordId);
        patientRecordModel = res.Data;
        var filePath = Path.Combine(patientRecordModel.GetRecordDirectoryPath(), $"{HXDataType.PPG}.csv");
        if (File.Exists(filePath))
        {
            FileContent = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
        }
        var scrFilePath = Path.Combine(patientRecordModel.GetRecordDirectoryPath(), $"EDA.csv");
        if (File.Exists(scrFilePath))
        {
            SCRFileContent = await File.ReadAllTextAsync(scrFilePath, Encoding.UTF8);
        }
        Statistics = patientRecordModel.Statistics;
        minDate = patientRecordModel.CollectStartTime;
        ALGDataSubjectService.OnMessageReceived += han;
        await base.OnInitializedAsync();
    }

    private void han(JsonNode algJsonNode)
    {
        if (algJsonNode["Function"]?.ToString() == "FromALG")
        {
             record = algJsonNode["Record"].Deserialize<DataModel<DataAnalysisRecordModel>>();
             Statistics = record.Data.Statistics;
            PopupService.HideProgressCircular();
            isShowName=false;
            PopupService.EnqueueSnackbarAsync("分析完成",AlertTypes.Success);
            InvokeAsync(StateHasChanged);
        }
    }

    private async Task InitRecordDataAsync()
    {
        var res = await InternalHttpClientService.GetPatientRecordModelDetailById(RecordId);
        patientRecordModel = res.Data;
        userModel = res.User;
        patientModel = patientRecordModel.Patient;
    }

    private IEnumerable<Field> _fields;

    private void HandleDataZoom((int startIndex, int endIndex) indices)
    {
        StartIndex = indices.startIndex;
        EndIndex = indices.endIndex;
    }

    private int _chartType = 1;
    private int _spaceTime = 1;
    private DateTime minDate = DateTime.MaxValue;
    private HXEchartComponent childComponentRef;
    private DataModel<DataAnalysisRecordModel> record;

    private void UpdateValue(int value)
    {
        _chartType = value;
    }

    private async Task OnDateClick()
    {
        await childComponentRef?.UpdateDataChange(_spaceTime, _chartType);
    }

    private async Task OnAnalysisButtonClick()
    {
        var data = new
        {
            Function = "ToALG",
            Data = new
            {
                RecordId = RecordId,
                StartIndex = StartIndex,
                EndIndex = EndIndex,
                Name
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(data);
        await this.ClientWebSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
            CancellationToken.None);
        PopupService.ShowProgressCircular();
    }

    [Inject] IPopupService PopupService { get; set; }
    private int EndIndex { get; set; }
    private int StartIndex { get; set; }
    public void Dispose()
    {
        if (ALGDataSubjectService is { OnMessageReceived: not null })
        {
            ALGDataSubjectService.OnMessageReceived -= han;
        }
    }
    
    [Inject] HttpClient HttpClient { get; set; }
    
    private async Task OnExportClick()
    {
        if (record==null)
        {
          await  PopupService.EnqueueSnackbarAsync("请先进行数据分析", AlertTypes.Warning);
            return;
        }
        _=Task.Run(async () =>
        {
           PopupService.ShowProgressCircular();
           await JS.InvokeVoidAsync("AutoPrintPdf",$"/client/DataAnalysis/ReportDetail/{record.Id}");
           await Task.Delay(3000);
           PopupService.HideProgressCircular();
        });
        await JS.InvokeVoidAsync("downloadFileFromUrl", $"api/v2.0/SCI/DataAnalysisRecord/DownloadStatic/{record.Id}");
    }
    
    

}
