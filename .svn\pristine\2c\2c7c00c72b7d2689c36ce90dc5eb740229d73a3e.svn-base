﻿@using HX.HRV.Shared.Models.ViewModel
@{
    var style = GetHeadStyle();
}

@if (DeviceStatusData != null)
{
    <div Style="@style"
         class="d-flex justify-space-between ">
        <div class="d-flex align-center justify-center">
            <MTooltip Left>
                <ActivatorContent>
                    <MCard @attributes="@context.Attrs"
                           style="width:1rem; 
                           height: 1rem; 
                           background-color:rgba(0,0,0,0); 
                           border-radius: 50%; 
                           border: 2px solid #FFFFFF;"></MCard>
                </ActivatorContent>
            </MTooltip>
            <span class="ml5" style="color: #fff;">@DeviceStatusData.Device.Name</span>
        </div>

        @if (HeaderContent != null&&(DeviceStatusData?.Device?.IsOnline??false))
        {
            <div class="d-flex align-center justify-center">
            @HeaderContent
            </div>
        }
        
        @if (DeviceStatusData?.IsUsbLinked??false)
        {
            <div class="d-flex align-center justify-center">
                <span class="mdi mdi-usb" style="font-size: 1.4rem;color: #fff;"> </span>
            </div>
        }
       
        @if (DeviceStatusData?.Device?.IsOnline ?? false)
        {
            <div class="d-flex align-center justify-center">
                @if (IsCharge)
                {
                    var spanClass = "mdi mdi-battery";
                    if (DeviceStatusData?.Battery < 10)
                    {
                        spanClass = "mdi mdi-battery-outline";
                    }
                    else if (DeviceStatusData?.Battery < 100)
                    {
                        spanClass = $"mdi mdi-battery-{(int)(DeviceStatusData?.Battery / 10) * 10}";
                    }

                    <span class="@spanClass"
                          style="font-size: 1.4rem;color: #fff;">
                    </span>
                }
                else
                {
                    <img style="width:28px" src="/images/icon/electricity-full.png" alt=""/>
                }
                <img style="width:28px" src="/images/icon/<EMAIL>" alt=""/>

            </div>
        }
    </div>
}

@code {

    /// <summary>
    /// 是否充电中
    /// </summary>
    [Parameter]
    public bool IsCharge { get; set; } = false;
    [Parameter] public RenderFragment? HeaderContent { get; set; }

    /// <summary>
    /// 连接类型
    /// </summary>
    [Parameter]
    public int LinkType { get; set; } = 1;

    public string DeviceId { get; set; }

    [CascadingParameter(Name = "DeviceDataList")]
    private List<DeviceStatusViewModel> DeviceDataList { get; set; } = new();

    // private DeviceStatusViewModel DeviceStatusData => DeviceDataList.FirstOrDefault(m => m.Device.Id == DeviceId);

    [Parameter] public DeviceStatusViewModel DeviceStatusData { get; set; }

    protected override void OnInitialized()
    {
        base.OnInitialized();
    }

    private string GetHeadStyle()
    {
       
        var style = "height: 20%;padding: 1rem;width: 100%;border-radius: 8px 8px 0 0;";
        if (this.DeviceStatusData?.IsUsbLinked == true)
        {
            style += "background: linear-gradient(90deg, rgb(0, 96, 204),rgb(86, 151, 222));";
        }
        else
        {
            if (this.DeviceStatusData?.DeviceStatus != Models.EnumDeviceStatus.离线)
            {
                style += this.DeviceStatusData?.ColorLinearString;

            }
            else
            {
                style += "background: linear-gradient(90deg, #a6b8cd, #BBD2EC);";
            }
        }
      

        return style;
    }

}
