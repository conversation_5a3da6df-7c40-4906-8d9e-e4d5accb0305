﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace  HX.HRV.Shared.Models
{
	public class LicenseData
	{
		public string UserName { get; set; }

		/// <summary>
		/// 过期时间
		/// </summary>
		public DateTime ExpiryDate { get; set; }

		/// <summary>
		/// 机器码
		/// </summary>
		public string UniqueIdentifier { get; set; }

		public LicenseVersion LicenseVersion { get; set; }
	}

	public enum LicenseVersion
	{
		//普通
		Comm = 1,
		//
		SCI

	}
}
