﻿using Microsoft.Extensions.DependencyInjection;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared;

namespace HX.HRV.Shared
{
    /// <summary>
    /// 模块配置
    /// </summary>
    public class Module : BaseModule
    {
        /// <summary>
        /// 模块名
        /// </summary>
        public override string Name { get; set; } = "心率变异性系统共享";
        public override string Icon { get; set; } = "iconfont icon-admin";

        /// <summary>
        /// 服务配置
        /// </summary>
        /// <param name="services"></param>
        public override void Configure(IServiceCollection services)
        {
            //ConfigureItems.Add(new ConfigureItem
            //{
            //    Name = "HX.HRV.Shared Blazor初始化",
            //    Order = 201,
            //    Services = (services) =>
            //    {
            //        BlazorApp.DefaultLayout = typeof(HX.HRV.Shared.Pages.LingLongTuLayout);
            //        BlazorApp.SetModuleLayout("基础管理系统共享", typeof(UFU.CoreFX.Shared.Pages.DefaultLayout));
            //    }
            //});
        }
    }
}
