﻿using System.Globalization;
using NWaves.Operations;
using NWaves.Signals;

namespace HX.HRV.Web.Units;

public static class SignalResampler
{
    /// <summary>
    /// 使用 NWaves 对连续信号进行重采样（内置抗混叠滤波器），保持波形质量。
    /// </summary>
    /// <param name="original">原始数据数组</param>
    /// <param name="originalRate">原始采样率（如100Hz）</param>
    /// <param name="targetRate">目标采样率（如25Hz）</param>
    /// <returns>降采样后的 float 数组</returns>
    public static float[] Resample(float[] original, int originalRate, int targetRate)
    {
        if (originalRate == targetRate)
            return original.ToArray();

        var signal = new DiscreteSignal(originalRate, original);
        var resampler = new Resampler();

        // 内部自动设计滤波器并进行抗混叠处理
        var resampled = resampler.Resample(signal, targetRate);

        return resampled.Samples;
    }
    
  
    
    
    public enum SignalRateMinuteSecond
    {
         _1HZ = 1,
        _25HZ = 25,
        _50HZ = 50,
        _100HZ = 100,
        _200HZ = 200,
        _500HZ = 500
    }
    
  


    public static List<string> DownsampleLines(List<string> lines, SignalRateMinuteSecond targetRate)
    {
        const string dateFormat = "yyyy-MM-dd HH:mm:ss.fff";

        var samples = new List<(DateTime Time, float Value)>();

        foreach (var line in lines)
        {
            var parts = line.Split(',');
            if (parts.Length < 2) continue;

            if (!DateTime.TryParseExact(parts[0], dateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out var time))
                continue;

            if (!float.TryParse(parts[1], out var value))
                continue;

            samples.Add((time, value));
        }

        double intervalSec = 1.0 / (int)targetRate;
        var interval = TimeSpan.FromSeconds(intervalSec);

        var groups = samples
            .GroupBy(s => s.Time.Ticks / interval.Ticks)
            .OrderBy(g => g.Key);

        var result = new List<string>();

        foreach (var group in groups)
        {
            var avg = group.Average(g => g.Value);
            var time = new DateTime(group.Key * interval.Ticks);
            result.Add($"{time:yyyy-MM-dd HH:mm:ss.fff},{avg:F3}");
        }

        return result;
    }


    
   
}