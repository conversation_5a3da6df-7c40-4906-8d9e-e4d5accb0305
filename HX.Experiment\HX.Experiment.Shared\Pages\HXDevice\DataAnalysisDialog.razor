@using HX.Experiment.Shared.Model
@using HX.Experiment.Shared.Services
@using UFU.IoT.Models
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services

<style>
    .analysis-stepper {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }

    .analysis-card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .time-picker-container {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .analysis-result-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
    }

    .progress-container {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>

<div class="analysis-stepper">
    <MStepper @bind-Value="_currentStep" Class="analysis-card">
        <MStepperHeader>
            <MStepperStep Step="1" Complete="_currentStep > 1">
                <MIcon>mdi-clock-outline</MIcon>
                选择时间段
            </MStepperStep>
            <MDivider></MDivider>
            <MStepperStep Step="2" Complete="_currentStep > 2">
                <MIcon>mdi-chart-line</MIcon>
                数据分析
            </MStepperStep>
            <MDivider></MDivider>
            <MStepperStep Step="3" Complete="_currentStep > 3">
                <MIcon>mdi-file-chart</MIcon>
                分析结果
            </MStepperStep>
        </MStepperHeader>

        <MStepperItems>
            <!-- 步骤1: 选择时间段 -->
            <MStepperContent Step="1">
                <MCard Class="mb-4">
                    <MCardTitle Class="primary white--text">
                        <MIcon Class="mr-2">mdi-calendar-range</MIcon>
                        选择分析时间段
                    </MCardTitle>
                    <MCardText Class="pa-6">
                        <MRow>
                            <MCol Cols="12" Class="mb-4">
                                <MAlert Type="AlertTypes.Info" Dense>
                                    <MIcon Class="mr-2">mdi-information</MIcon>
                                    请选择要分析的时间段，建议分析时长不超过1小时以获得最佳性能
                                </MAlert>
                            </MCol>
                            
                            <MCol Cols="12" Md="6">
                                <div class="time-picker-container">
                                    <MLabel Class="text-h6 mb-2">开始时间</MLabel>
                                    <MTextField @bind-Value="StartTimeString"
                                               Label="开始时间"
                                               Type="datetime-local"
                                               Dense
                                               Outlined
                                               PrependIcon="mdi-clock-start">
                                    </MTextField>
                                </div>
                            </MCol>
                            
                            <MCol Cols="12" Md="6">
                                <div class="time-picker-container">
                                    <MLabel Class="text-h6 mb-2">结束时间</MLabel>
                                    <MTextField @bind-Value="EndTimeString"
                                               Label="结束时间"
                                               Type="datetime-local"
                                               Dense
                                               Outlined
                                               PrependIcon="mdi-clock-end">
                                    </MTextField>
                                </div>
                            </MCol>
                            
                            <MCol Cols="12">
                                <MTextField @bind-Value="AnalysisName"
                                           Label="分析名称"
                                           Placeholder="请输入分析名称"
                                           Dense
                                           Outlined
                                           PrependIcon="mdi-tag">
                                </MTextField>
                            </MCol>
                        </MRow>
                    </MCardText>
                    <MCardActions Class="pa-6 pt-0">
                        <MButton Color="grey" OnClick="CloseDialog">
                            <MIcon Left>mdi-close</MIcon>
                            取消
                        </MButton>
                        <MSpacer />
                        <MButton Color="primary" OnClick="NextStep" Disabled="@(!IsStep1Valid)">
                            <MIcon Right>mdi-arrow-right</MIcon>
                            下一步
                        </MButton>
                    </MCardActions>
                </MCard>
            </MStepperContent>

            <!-- 步骤2: 数据分析 -->
            <MStepperContent Step="2">
                <MCard Class="mb-4">
                    <MCardTitle Class="success white--text">
                        <MIcon Class="mr-2">mdi-cog</MIcon>
                        正在进行数据分析
                    </MCardTitle>
                    <MCardText Class="pa-6">
                        @if (_isAnalyzing)
                        {
                            <div class="progress-container">
                                <div class="text-center mb-4">
                                    <MProgressCircular Indeterminate Size="64" Color="primary" Class="mb-4"></MProgressCircular>
                                    <div class="text-h6">@_analysisStage</div>
                                    <div class="text-body-1 grey--text">@_analysisMessage</div>
                                </div>
                                
                                <MProgressLinear Value="@_analysisProgress" 
                                               Color="primary" 
                                               Height="8" 
                                               Rounded>
                                </MProgressLinear>
                                
                                <div class="text-center mt-2">
                                    <span class="text-body-2">@_analysisProgress%</span>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="text-center">
                                <MIcon Size="64" Color="primary" Class="mb-4">mdi-play-circle</MIcon>
                                <div class="text-h6 mb-2">准备开始分析</div>
                                <div class="text-body-1 grey--text mb-4">
                                    分析时间段: @StartTime.ToString("yyyy-MM-dd HH:mm:ss") - @EndTime.ToString("yyyy-MM-dd HH:mm:ss")
                                </div>
                                <div class="text-body-1 grey--text">
                                    分析名称: @AnalysisName
                                </div>
                            </div>
                        }
                    </MCardText>
                    <MCardActions Class="pa-6 pt-0">
                        <MButton Color="grey" OnClick="PreviousStep" Disabled="@_isAnalyzing">
                            <MIcon Left>mdi-arrow-left</MIcon>
                            上一步
                        </MButton>
                        <MSpacer />
                        @if (!_isAnalyzing)
                        {
                            <MButton Color="success" OnClick="StartAnalysis">
                                <MIcon Left>mdi-play</MIcon>
                                开始分析
                            </MButton>
                        }
                        else
                        {
                            <MButton Color="warning" OnClick="CancelAnalysis">
                                <MIcon Left>mdi-stop</MIcon>
                                取消分析
                            </MButton>
                        }
                    </MCardActions>
                </MCard>
            </MStepperContent>

            <!-- 步骤3: 分析结果 -->
            <MStepperContent Step="3">
                <MCard Class="mb-4">
                    <MCardTitle Class="analysis-result-card white--text">
                        <MIcon Class="mr-2">mdi-chart-box</MIcon>
                        分析结果
                    </MCardTitle>
                    <MCardText Class="pa-6">
                        @if (_analysisResult != null)
                        {
                            <MRow>
                                <MCol Cols="12">
                                    <MAlert Type="AlertTypes.Success" Dense>
                                        <MIcon Class="mr-2">mdi-check-circle</MIcon>
                                        分析完成！分析名称: @_analysisResult.Name
                                    </MAlert>
                                </MCol>
                                
                                <MCol Cols="12" Md="6">
                                    <MCard Outlined>
                                        <MCardTitle>心率变异性分析</MCardTitle>
                                        <MCardText>
                                            @if (_analysisResult.HrvStatistics != null)
                                            {
                                                <div>统计数据已生成</div>
                                                @if (!string.IsNullOrEmpty(_analysisResult.HrImageUrl))
                                                {
                                                    <img src="@_analysisResult.HrImageUrl" alt="心率图表" style="width: 100%; max-width: 400px;" />
                                                }
                                            }
                                            else
                                            {
                                                <div class="text-body-2 grey--text">暂无心率变异性数据</div>
                                            }
                                        </MCardText>
                                    </MCard>
                                </MCol>
                                
                                <MCol Cols="12" Md="6">
                                    <MCard Outlined>
                                        <MCardTitle>皮肤电分析</MCardTitle>
                                        <MCardText>
                                            @if (_analysisResult.EdaTimeDomain != null)
                                            {
                                                <div>EDA数据已分析</div>
                                                @if (!string.IsNullOrEmpty(_analysisResult.EdaImageUrl))
                                                {
                                                    <img src="@_analysisResult.EdaImageUrl" alt="EDA图表" style="width: 100%; max-width: 400px;" />
                                                }
                                            }
                                            else
                                            {
                                                <div class="text-body-2 grey--text">暂无皮肤电数据</div>
                                            }
                                        </MCardText>
                                    </MCard>
                                </MCol>
                            </MRow>
                        }
                        else
                        {
                            <div class="text-center">
                                <MIcon Size="64" Color="grey" Class="mb-4">mdi-file-question</MIcon>
                                <div class="text-h6">暂无分析结果</div>
                            </div>
                        }
                    </MCardText>
                    <MCardActions Class="pa-6 pt-0">
                        <MButton Color="grey" OnClick="PreviousStep">
                            <MIcon Left>mdi-arrow-left</MIcon>
                            上一步
                        </MButton>
                        <MSpacer />
                        @if (_analysisResult != null)
                        {
                            <MButton Color="primary" OnClick="ExportResult">
                                <MIcon Left>mdi-download</MIcon>
                                导出结果
                            </MButton>
                        }
                        <MButton Color="success" OnClick="CloseDialog">
                            <MIcon Left>mdi-check</MIcon>
                            完成
                        </MButton>
                    </MCardActions>
                </MCard>
            </MStepperContent>
        </MStepperItems>
    </MStepper>
</div>

@code {
    [Parameter] public DeviceModel? DeviceModel { get; set; }
    [Parameter] public EventCallback CloseDialog { get; set; }

    [Inject] private IDataAnalysisService DataAnalysisService { get; set; } = null!;
    [Inject] private IPopupService PopupService { get; set; } = null!;

    private int _currentStep = 1;
    private DateTime StartTime = DateTime.Now.AddHours(-1);
    private DateTime EndTime = DateTime.Now;
    private string AnalysisName = string.Empty;
    
    private bool _isAnalyzing = false;
    private int _analysisProgress = 0;
    private string _analysisStage = string.Empty;
    private string _analysisMessage = string.Empty;
    private string? _currentAnalysisId;
    private ExperimentAnalysisResult? _analysisResult;

    private string StartTimeString
    {
        get => StartTime.ToString("yyyy-MM-ddTHH:mm");
        set => DateTime.TryParse(value, out StartTime);
    }

    private string EndTimeString
    {
        get => EndTime.ToString("yyyy-MM-ddTHH:mm");
        set => DateTime.TryParse(value, out EndTime);
    }

    private bool IsStep1Valid => 
        !string.IsNullOrWhiteSpace(AnalysisName) && 
        StartTime < EndTime && 
        (EndTime - StartTime).TotalHours <= 24;

    protected override void OnInitialized()
    {
        AnalysisName = $"分析_{DateTime.Now:yyyyMMdd_HHmmss}";
        
        // 订阅分析事件
        DataAnalysisService.AnalysisProgressChanged += OnAnalysisProgressChanged;
        DataAnalysisService.AnalysisCompleted += OnAnalysisCompleted;
    }

    private void NextStep()
    {
        if (_currentStep < 3)
            _currentStep++;
    }

    private void PreviousStep()
    {
        if (_currentStep > 1)
            _currentStep--;
    }

    private async Task StartAnalysis()
    {
        try
        {
            _isAnalyzing = true;
            _analysisProgress = 0;
            _analysisStage = "初始化";
            _analysisMessage = "准备开始分析...";
            StateHasChanged();

            // 这里需要一个检测记录ID，实际应用中应该从用户选择或当前检测中获取
            var recordId = Guid.NewGuid().ToString("N"); // 临时生成

            _currentAnalysisId = await DataAnalysisService.StartAnalysisAsync(
                recordId, StartTime, EndTime, AnalysisName);

            if (string.IsNullOrEmpty(_currentAnalysisId))
            {
                await PopupService.EnqueueSnackbarAsync("启动分析失败", AlertTypes.Error);
                _isAnalyzing = false;
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"启动分析失败: {ex.Message}", AlertTypes.Error);
            _isAnalyzing = false;
            StateHasChanged();
        }
    }

    private async Task CancelAnalysis()
    {
        _isAnalyzing = false;
        _analysisProgress = 0;
        await PopupService.EnqueueSnackbarAsync("分析已取消", AlertTypes.Info);
        StateHasChanged();
    }

    private async Task ExportResult()
    {
        if (_analysisResult == null) return;

        try
        {
            var exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                $"分析结果_{_analysisResult.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.zip");

            var success = await DataAnalysisService.ExportAnalysisResultAsync(_analysisResult.Id, exportPath);
            
            if (success)
            {
                await PopupService.EnqueueSnackbarAsync($"结果已导出到: {exportPath}", AlertTypes.Success);
            }
            else
            {
                await PopupService.EnqueueSnackbarAsync("导出失败", AlertTypes.Error);
            }
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"导出失败: {ex.Message}", AlertTypes.Error);
        }
    }

    private async void OnAnalysisProgressChanged(object? sender, DataAnalysisProgressEventArgs e)
    {
        if (e.AnalysisId == _currentAnalysisId)
        {
            _analysisProgress = e.Progress;
            _analysisStage = e.Stage;
            _analysisMessage = e.Message;
            await InvokeAsync(StateHasChanged);
        }
    }

    private async void OnAnalysisCompleted(object? sender, DataAnalysisCompletedEventArgs e)
    {
        if (e.AnalysisId == _currentAnalysisId)
        {
            _isAnalyzing = false;
            
            if (e.IsSuccess && e.Result != null)
            {
                _analysisResult = e.Result;
                _currentStep = 3;
                await InvokeAsync(async () =>
                {
                    await PopupService.EnqueueSnackbarAsync("分析完成！", AlertTypes.Success);
                    StateHasChanged();
                });
            }
            else
            {
                await InvokeAsync(async () =>
                {
                    await PopupService.EnqueueSnackbarAsync(e.ErrorMessage ?? "分析失败", AlertTypes.Error);
                    StateHasChanged();
                });
            }
        }
    }

    public void Dispose()
    {
        DataAnalysisService.AnalysisProgressChanged -= OnAnalysisProgressChanged;
        DataAnalysisService.AnalysisCompleted -= OnAnalysisCompleted;
    }
}
