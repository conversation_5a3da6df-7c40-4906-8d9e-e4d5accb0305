<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="8" />
    <option name="CUSTOM_SCOPE_NAME" value="当前文件" />
  </component>
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="UFU.CoreFX.Shared">../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj</projectFile>
    <projectFile profileName="UFU.IoT.Shared">../../NetCore/UFU.IoT.V3/UFU.IoT.Shared/UFU.IoT.Shared.csproj</projectFile>
    <projectFile profileName="IIS Express">../../NetCore/UFU.IoT.V3/UFU.IoT/UFU.IoT.csproj</projectFile>
    <projectFile profileName="UFU.IoT">../../NetCore/UFU.IoT.V3/UFU.IoT/UFU.IoT.csproj</projectFile>
    <projectFile pubXmlPath="../../NetCore/UFU.IoT.V3/UFU.IoT/Properties/PublishProfiles/FolderProfile.pubxml">../../NetCore/UFU.IoT.V3/UFU.IoT/UFU.IoT.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared.Base">../HX.Base/HX.Base.Shared/HX.Base.Shared.csproj</projectFile>
    <projectFile pubXmlPath="../HX.Encrypt/Properties/PublishProfiles/FolderProfile.pubxml">../HX.Encrypt/HX.Encrypt.csproj</projectFile>
    <projectFile pubXmlPath="../UFU.IoT.HRV/UFU.IoT/Properties/PublishProfiles/FolderProfile.pubxml">../UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.Experiment.Client/HX.Experiment.Client.csproj</projectFile>
    <projectFile profileName="http">HX.Experiment.Client/HX.Experiment.Client.csproj</projectFile>
    <projectFile profileName="https">HX.Experiment.Client/HX.Experiment.Client.csproj</projectFile>
    <projectFile kind="Uwp">HX.Experiment.Client/Masa.Maui.Windows.csproj</projectFile>
    <projectFile kind="XamarinIOS">HX.Experiment.Client/Masa.Maui.Windows.csproj</projectFile>
    <projectFile kind="XamarinMacOS">HX.Experiment.Client/Masa.Maui.Windows.csproj</projectFile>
    <projectFile profileName="配置文件 1">HX.Experiment.Client/Masa.Maui.Windows.csproj</projectFile>
    <projectFile pubXmlPath="HX.Experiment.Client/Properties/PublishProfiles/MSIX-win10-x64.pubxml">HX.Experiment.Client/Masa.Maui.Windows.csproj</projectFile>
    <projectFile pubXmlPath="HX.Experiment.Client/Properties/PublishProfiles/MSIX-win10-x641.pubxml">HX.Experiment.Client/Masa.Maui.Windows.csproj</projectFile>
    <projectFile>HX.Experiment.MAUI/HX.Experiment.MAUI.Web.Client/HX.Experiment.MAUI.Web.Client.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj</projectFile>
    <projectFile profileName="http">HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj</projectFile>
    <projectFile profileName="https">HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj</projectFile>
    <projectFile kind="Uwp">HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj</projectFile>
    <projectFile kind="XamarinIOS">HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj</projectFile>
    <projectFile kind="XamarinMacOS">HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj</projectFile>
    <projectFile>HX.Experiment/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj</projectFile>
    <projectFile>HX.Experiment/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj</projectFile>
    <projectFile>HX.Experiment/HX.Experiment.Web.Client/HX.Experiment.Web.Client.csproj</projectFile>
    <projectFile profileName="BlazorApp.Server">HX.Experiment/HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj</projectFile>
    <projectFile kind="Docker">HX.Experiment/HX.Experiment.Web.Service/HX.HRV.Web.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.Experiment/HX.Experiment.Web.Service/HX.HRV.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.Experiment/HX.Experiment.Web.Service/Properties/PublishProfiles/FolderProfile.pubxml">HX.Experiment/HX.Experiment.Web.Service/HX.HRV.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.Experiment/HX.Experiment.Web.Service/Properties/PublishProfiles/HX.HRV.Web.pubxml">HX.Experiment/HX.Experiment.Web.Service/HX.HRV.Web.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj</projectFile>
    <projectFile profileName="http">HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj</projectFile>
    <projectFile profileName="https">HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj</projectFile>
    <projectFile>HX.Experiment/HX.Experiment.Window/HX.Experiment.Window.csproj</projectFile>
    <projectFile kind="Uwp">HX.Experiment/HX.Experiment.csproj</projectFile>
    <projectFile kind="XamarinIOS">HX.Experiment/HX.Experiment.csproj</projectFile>
    <projectFile kind="XamarinMacOS">HX.Experiment/HX.Experiment.csproj</projectFile>
    <projectFile profileName="Windows Machine">HX.Experiment/HX.Experiment.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared.Base">HX.HRV.Base.Shared/HX.HRV.Base.Shared.csproj</projectFile>
    <projectFile profileName="HX.HRV.Base.Web">HX.HRV.Base.Web/HX.HRV.Base.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.Base.Web/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.Base.Web/HX.HRV.Base.Web.csproj</projectFile>
    <projectFile kind="Uwp">HX.HRV.Client/HX.HRV.Client.csproj</projectFile>
    <projectFile kind="XamarinIOS">HX.HRV.Client/HX.HRV.Client.csproj</projectFile>
    <projectFile kind="XamarinMacOS">HX.HRV.Client/HX.HRV.Client.csproj</projectFile>
    <projectFile profileName="HX.HRV.Client">HX.HRV.Client/HX.HRV.Client.csproj</projectFile>
    <projectFile profileName="Windows Machine">HX.HRV.Client/HX.HRV.Client.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.Keygen/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.Keygen/HX.HRV.Keygen.csproj</projectFile>
    <projectFile kind="Uwp">HX.HRV.MAUI/HX.HRV.MAUI.csproj</projectFile>
    <projectFile kind="XamarinIOS">HX.HRV.MAUI/HX.HRV.MAUI.csproj</projectFile>
    <projectFile kind="XamarinMacOS">HX.HRV.MAUI/HX.HRV.MAUI.csproj</projectFile>
    <projectFile profileName="Windows Machine">HX.HRV.MAUI/HX.HRV.MAUI.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.MDns/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.MDns/HX.HRV.MDns.csproj</projectFile>
    <projectFile kind="Uwp">HX.HRV.Maui/HX.HRV.Maui.csproj</projectFile>
    <projectFile kind="XamarinIOS">HX.HRV.Maui/HX.HRV.Maui.csproj</projectFile>
    <projectFile kind="XamarinMacOS">HX.HRV.Maui/HX.HRV.Maui.csproj</projectFile>
    <projectFile profileName="Windows Machine">HX.HRV.Maui/HX.HRV.Maui.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared">HX.HRV.SCI.Shared/HX.HRV.SCI.Shared.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.SCI.Shared/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.SCI.Shared/HX.HRV.SCI.Shared.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.SCI.Shared/Properties/PublishProfiles/FolderProfile1.pubxml">HX.HRV.SCI.Shared/HX.HRV.SCI.Shared.csproj</projectFile>
    <projectFile profileName="HX.HRV.SCI.Server">HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj</projectFile>
    <projectFile profileName="HX.HRV.SCI.Web">HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile1.pubxml">HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.SCI.Web/Properties/PublishProfiles/HX.HRV.SCI.pubxml">HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared.Base">HX.HRV.Shared.Base/HX.HRV.Shared.Base.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared.WASM">HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared">HX.HRV.Shared/HX.HRV.Shared.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared.Base">HX.HRV.Shared/HX.HRV.Shared.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.Shared/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.Shared/HX.HRV.Shared.csproj</projectFile>
    <projectFile profileName="HX.HRV.Shared">HX.HRV.Shared_Back/HX.HRV.Shared_Back.csproj</projectFile>
    <projectFile profileName="HX.HRV.Web.Base">HX.HRV.Web.Base/HX.HRV.Web.Base.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.HRV.Web.Base/HX.HRV.Web.Base.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.Web.Base/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.Web.Base/HX.HRV.Web.Base.csproj</projectFile>
    <projectFile kind="Docker">HX.HRV.Web/HX.HRV.Web.csproj</projectFile>
    <projectFile profileName="BlazorApp.Server">HX.HRV.Web/HX.HRV.Web.csproj</projectFile>
    <projectFile profileName="HX.HRV.Web">HX.HRV.Web/HX.HRV.Web.csproj</projectFile>
    <projectFile profileName="IIS Express">HX.HRV.Web/HX.HRV.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.Web/Properties/PublishProfiles/FolderProfile.pubxml">HX.HRV.Web/HX.HRV.Web.csproj</projectFile>
    <projectFile pubXmlPath="HX.HRV.Web/Properties/PublishProfiles/HX.HRV.Web.pubxml">HX.HRV.Web/HX.HRV.Web.csproj</projectFile>
    <projectFile profileName="UFU.IoT.Shared">UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj</projectFile>
    <projectFile profileName="IIS Express">UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj</projectFile>
    <projectFile profileName="UFU.IoT">UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj</projectFile>
    <projectFile pubXmlPath="UFU.IoT.HRV/UFU.IoT/Properties/PublishProfiles/FolderProfile.pubxml">UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="649c87f6-1614-49b6-b525-4a867a068d1a" name="更改" comment="基本稳定">
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/.dockerignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/NavMenu.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Models/AlgorithmShowModel.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/AlgorithmOverview.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/AlgorithmOverviewDetail.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Routes.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Controllers/ProductRegisterController.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/img" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/img/JSSR" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/img/sandip.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/css" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/css/materialdesignicons.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/css/materialdesignicons.min.css.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.eot" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Layout" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Layout/MainLayout.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Layout/MainLayout.razor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Model" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Model/DiskInfo.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Model/ExperimentTask.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Model/Student.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Model/TaskTypeEnum.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Pages" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Pages/TaskList.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Routes.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Services" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Services/ICameraFrameService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Services/IClassService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Services/IDeviceService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Services/IImportStudentService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Services/IUserService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/_Imports.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Client" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Client/Module.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/App.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/HX.Experiment.Window.csproj" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/MainForm.Designer.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/MainForm.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/MainForm.resx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/MainFrom.resx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Program.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Services" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Services/CameraFrameService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Services/DeviceService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Services/ImportStudentService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Services/UDiskService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/Services/UserService.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/_Imports.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot/css" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot/css/app.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot/favicon.ico" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot/sample-data" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/wwwroot/sample-data/weather.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/Module.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/Program.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/Properties" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/Properties/launchSettings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/_Imports.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/appsettings.Development.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/appsettings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/app.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/bootstrap" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/bootstrap/bootstrap.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/bootstrap/bootstrap.min.css.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/FONT-LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/ICON-LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/css" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/css/open-iconic-bootstrap.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/fonts" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/fonts/open-iconic.eot" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/fonts/open-iconic.otf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/fonts/open-iconic.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/fonts/open-iconic.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/css/open-iconic/font/fonts/open-iconic.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/favicon.ico" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/icon-192.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/icon-512.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/images" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/js" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/manifest.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/sample-data" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/sample-data/weather.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/service-worker.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HX.HRV.Shared.WASM/wwwroot/service-worker.published.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/.vs" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/.vs/HX.PythonService" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/.vs/HX.PythonService/FileContentIndex" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/dist" afterDir="true" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway.sln" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway.sln" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/HX.ALGGateway.Client.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/HX.ALGGateway.Client.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor.css" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/ALGHome.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/ALGHome.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/_Imports.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/_Imports.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/ALGWebSocketStaticManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/ALGWebSocketStaticManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/App.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/App.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Layout" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Pages/Home.razor" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Pages/Weather.razor" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Routes.razor" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/_Imports.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/_Imports.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Controllers/AlgorithmsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Controllers/AlgorithmsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Properties/launchSettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Properties/launchSettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/app.css" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/app.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Base/HX.Base.Shared/HX.Base.Shared.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Base/HX.Base.Shared/HX.Base.Shared.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Database/HX.Database.Shared/Pages/DocumentDetail.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Database/HX.Database.Shared/Pages/DocumentDetail.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Database/HX.Database.Shared/wwwroot/js/echart/echartsInterop.js" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Database/HX.Database.Shared/wwwroot/js/echart/echartsInterop.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Database/HX.Database.Web/HX.Database.Web.csproj.user" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Database/HX.Database.Web/HX.Database.Web.csproj.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Encrypt/HX.Encrypt.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Encrypt/HX.Encrypt.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.FileExport/HX.FileExport/FileExport.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.FileExport/HX.FileExport/FileExport.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.MAUI/MainPage.xaml.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.MAUI/MainPage.xaml.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.MAUI/MauiProgram.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.MAUI/MauiProgram.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.MAUI/Services/UDiskService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.MAUI/Services/UDiskService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Shared/HX.HRV.SCI.Shared.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Shared/HX.HRV.SCI.Shared.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Shared/Module.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Shared/Module.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Shared/Pages/Client/DataAnalysis/DataAnalysis.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Shared/Pages/Client/DataAnalysis/DataAnalysis.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Shared/Pages/Client/Dialog/DeviceStatusDialog.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Shared/Pages/Client/Dialog/DeviceStatusDialog.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Web/Areas/SCI/Controllers/DataAnalysisRecordController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Web/Areas/SCI/Controllers/DataAnalysisRecordController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml.user" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/HX.HRV.Shared.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/HX.HRV.Shared.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Models/PatientRecordModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Models/PatientRecordModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Models/ViewModel/DeviceStatusViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Models/ViewModel/DeviceStatusViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Account/List.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Account/List.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Components/HRVHeader.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Components/HRVHeader.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Components/HXDeviceLayout.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Components/HXDeviceLayout.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/DeviceStatus/DeviceStatusCardContent.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/DeviceStatus/DeviceStatusCardContent.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/DeviceStatusDialogChartComponent.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/DeviceStatusDialogChartComponent.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/ExportRecordDialog.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/ExportRecordDialog.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/SelectedDeviceDialog.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/SelectedDeviceDialog.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/HxSystemConfig.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/HxSystemConfig.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Pages/HRVDefaultLayout.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Pages/HRVDefaultLayout.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Shared/Services/DeviceStateService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Shared/Services/DeviceStateService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Areas/HRV_HX/Controllers/PatientController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Areas/HRV_HX/Controllers/PatientController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Areas/HRV_HX/Controllers/PatientRecordController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Areas/HRV_HX/Controllers/PatientRecordController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/HX.HRV.Web.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/HX.HRV.Web.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Pages/_HRVLayout.cshtml" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Pages/_HRVLayout.cshtml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Properties/PublishProfiles/FolderProfile.pubxml.user" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Properties/PublishProfiles/FolderProfile.pubxml.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Properties/launchSettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Properties/launchSettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Services/ByteAnalysisServices.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Services/ByteAnalysisServices.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Services/FileExportService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Services/FileExportService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/Services/ImportFileDataAnalysisService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/Services/ImportFileDataAnalysisService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.Web/appsettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.Web/appsettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.HRV.sln" beforeDir="false" afterPath="$PROJECT_DIR$/HX.HRV.sln" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Services/BinaryDeviceWebSocketConnect.cs" beforeDir="false" afterPath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Services/BinaryDeviceWebSocketConnect.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Services/BinaryIoTService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Services/BinaryIoTService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/appsettings.Development.json" beforeDir="false" afterPath="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/appsettings.Development.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.Designer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.Designer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml.user" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.WebsocketTest/HX.WebsocketTest/HX.WebsocketTest/Controllers/HomeController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.WebsocketTest/HX.WebsocketTest/HX.WebsocketTest/Controllers/HomeController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/BlazorApp.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/BlazorApp.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Components/AuthView.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Components/AuthView.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/CoreModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/CoreModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DataModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DataModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Pages/Core/Form/Components/TableDataBase.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Pages/Core/Form/Components/TableDataBase.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/AuthService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/AuthService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/StateService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/StateService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Utils/JsonTool.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Utils/JsonTool.cs" afterDir="false" />
    </list>
    <list id="01318f70-2a1f-42a7-b37d-78f49243d43d" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/.dockerignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/.vs/ProjectSettings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/dist/main.exe" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/main.exe" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/main_test.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5_1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5_2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_6.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_tes.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_gsr_algorithm/ppg_gsr_algorithm_2.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/requirements.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/block.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/block.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/dataloader.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/dataloader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/dataset.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/dataset.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/emotion_calculator.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/emotion_calculator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/model_trans.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/model_trans.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/predict.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/predict.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/roc.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/roc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/sublayer.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/sublayer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/try.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/emotion/try___.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_gsr_algorithm/ppg_gsr_algorithm.py" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.PythonService/src/algorithms/ppg_gsr_algorithm/ppg_gsr_algorithm.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="EndpointsView">
    <option name="showSidePanel" value="false" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="CSS File" />
        <option value="JavaScript File" />
        <option value="JSON File" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/.nuget/packages/microsoft.windowsappsdk/1.6.250205002/include/DeploymentManagerAutoInitializer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/.nuget/packages/microsoft.windowsappsdk/1.6.250205002/include/WindowsAppSDK-VersionInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/f0f9de5692adf1c0576de062f93c6ab7b176433f/src/Components/Components/src/ComponentBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/f0f9de5692adf1c0576de062f93c6ab7b176433f/src/Components/Components/src/LayoutView.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/f0f9de5692adf1c0576de062f93c6ab7b176433f/src/Components/Components/src/Routing/RouteData.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/f0f9de5692adf1c0576de062f93c6ab7b176433f/src/Components/Components/src/Routing/Router.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/f0f9de5692adf1c0576de062f93c6ab7b176433f/src/Mvc/Mvc.Core/src/Infrastructure/ActionMethodExecutor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/ComponentBase.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/LayoutComponentBase.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/RenderTree/RenderTreeDiffBuilder.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/RenderTree/Renderer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/Rendering/ComponentState.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/Rendering/RenderTreeBuilder.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/RouteView.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/aspnetcore/fedc545ce86467b7d3413d906f1ab02fb3db12ff/src/Components/Components/src/Routing/Router.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/2aade6beb02ea367fd97c4070a4198802fe61c03/src/libraries/System.Text.Json/Common/ThrowHelper.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/2aade6beb02ea367fd97c4070a4198802fe61c03/src/libraries/System.Text.Json/src/System/Text/Json/Nodes/JsonNode.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/2aade6beb02ea367fd97c4070a4198802fe61c03/src/libraries/System.Text.Json/src/System/Text/Json/ThrowHelper.Node.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/2aade6beb02ea367fd97c4070a4198802fe61c03/src/libraries/System.Text.Json/src/System/Text/Json/ThrowHelper.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Linq/src/System/Linq/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/ArgumentNullException.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Collections/Generic/List.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/DateTime.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Nullable.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Runtime/CompilerServices/AsyncTaskMethodBuilderT.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Runtime/CompilerServices/AsyncValueTaskMethodBuilder.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Runtime/ExceptionServices/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/RuntimeType.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/String.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Threading/ExecutionContext.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/Threading/Tasks/TaskContinuation.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/6c636980f730a30c3f5352cff80ce035ae53f016/src/libraries/System.Private.CoreLib/src/System/TimeOnly.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/coreclr/System.Private.CoreLib/src/System/Runtime/CompilerServices/CastHelpers.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/coreclr/System.Private.CoreLib/src/System/Threading/Monitor.CoreCLR.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Collections/Generic/Dictionary.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Collections/Generic/KeyValuePair.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/IO/TextReader.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Int64.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Number.Parsing.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Runtime/CompilerServices/AsyncTaskMethodBuilderT.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Runtime/ExceptionServices/ExceptionDispatchInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/Threading/ExecutionContext.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Private.CoreLib/src/System/ThrowHelper.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e2ca2f8a1c5b643df628c85842a8f2b362b2c441/src/libraries/System.Text.Json/src/System/Text/Json/Nodes/JsonValueOfT.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/e77011b31a3e5c47d931248a64b47f9b2d47853d/src/libraries/System.Linq/src/System/Linq/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/58795FA46DA7D5895371CF1A/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/HX.HRV.Keygen.ViewModel.MainViewModel.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/58795FA46DA7D5895371CF1A/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator/HX.HRV.Keygen.ViewModel.MainViewModel.GenerateLicenseKey.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/AD13ADA465FA25B95537AF13/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/HX.HRV.Keygen.ViewModel.MainViewModel.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/AD13ADA465FA25B95537AF13/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/__KnownINotifyPropertyChangingArgs.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/AD13ADA465FA25B95537AF13/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator/HX.HRV.Keygen.ViewModel.MainViewModel.CopyToClipboard.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/AD13ADA465FA25B95537AF13/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator/HX.HRV.Keygen.ViewModel.MainViewModel.GenerateLicenseKey.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/BB3AA6574AA0684115677B0A/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/HX.HRV.Keygen.ViewModel.MainViewModel.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/D722C7D885DDC00091E2B323/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/HX.Experiment.PageModels.MainPageModel.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/2b06e36481c94e9782f68d54d902582dd000/c2/ef3fb482/0200000Apdb4.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/55/82218f31/020001B4.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/92/caef3d89/020001E0pdb599.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/9e/c148a500/020001B5.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/b7/8150392b/020001E0pdb602.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/770de106e9574880a7e071e259a51db9ec00/83/445eb1a2/02000009pdb4.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/770de106e9574880a7e071e259a51db9ec00/cc/d6127e4b/02000008pdb26.il" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/02bfa03b73f94f5b8947ec21da8cdcba74518/60/e291f76a/Environment.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/02bfa03b73f94f5b8947ec21da8cdcba74518/e2/6531c6ba/AppDomain.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/03fcdaf8b307481a8b1ba74b072e7c711cbb20/10/ba95e838/ActionMethodExecutor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/03fcdaf8b307481a8b1ba74b072e7c711cbb20/36/869fed1f/NotFoundResult.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/03fcdaf8b307481a8b1ba74b072e7c711cbb20/45/6b19f36e/ControllerBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/0680874775134ca8a27a3d45b52d0547200b98/71/64b90645/DbContext.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/0cc70438d1f24c659c4a696738d5746291c40/32/66317659/ICoreWebView2Methods.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/0cc70438d1f24c659c4a696738d5746291c40/3e/66e4b687/CoreWebView2.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/14e53b20c3604839a00012aa07bda4ef7d20/9e/17f2f4bc/RuntimeInformation.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/1761044f838d44a78810021c7ae7bcb1a1fe00/78/140f1262/UI.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/1834814c814c4aecb57971718461b2d635280/b7/f43b06df/RenderTreeDiffBuilder.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/1897246582c04ef8a8d50e9399c9f3a9148250/3c/1f5a35ea/CurrentThreadScheduler.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/20dc5b68e1804421af35ca19aa7e8b6134d708/2f/7a18949c/Dictionary`2.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/20dc5b68e1804421af35ca19aa7e8b6134d708/3c/459996d9/List`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/20dc5b68e1804421af35ca19aa7e8b6134d708/81/f51c5513/TaskAwaiter`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/20dc5b68e1804421af35ca19aa7e8b6134d708/f6/65187530/Task.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/26c9728fb1f84b25afaa7f3078591d4022a00/0d/0e56dcd7/ILogger.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/0c/5d16fc9d/ModuleLoader.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/1e/5550d4e1/DataRepository.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/21/94536bb8/CoreApp.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/25/59712cb1/DataRepository.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/25/6b1b349d/PermissionTable.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/38/e9d436b5/Email.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/3d/b1c5e846/UId.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/48/56792c93/CacheService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/4e/31a3f304/CoreModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/4f/8feaaafd/LogTool.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/59/c9068c96/FormDataController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/6e/2d341f74/PageExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/80/b986b89b/LogTool.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/81/b289553c/IWebSocket.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/90/fdbf7d6a/CoreModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/92/a8fecec0/Env.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/a3/5607d542/UId.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/ba/f3620f0f/HostModel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/d0/4efd455f/IWebSocket.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/ed/e426c4b5/Env.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/fb/f843669d/ILock.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/fd/2ac1885e/CoreApp.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/387123f38c65422f8f56e9a2db64571921908/8e/e69f4d07/EndpointMetadataApiExplorerServiceCollectionExtens.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/09/85b05fc2/HybridWebViewServiceCollectionExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/b4/26dc3fd9/HybridWebViewDeveloperTools.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3a94cc79548f4abab3ede69c758e35303db10/0f/51d6bc16/Uri.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4147c69c491144778e0ce7a35bee380835490/f3/27a4faa9/LayoutView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/45735c24d3a3437f924e3c35957156861b4908/1e/26224222/HttpResponseMessage.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/45735c24d3a3437f924e3c35957156861b4908/51/521f10a6/HttpClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4734f75c4fff49698fc2a330fd8314a815d28/27/6f1ffc75/SerialStream.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4734f75c4fff49698fc2a330fd8314a815d28/3d/4849ba8e/SerialPort.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f7dc6a66ce64068bee60c2aba27e7608dd10/0d/e3c545c2/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f7dc6a66ce64068bee60c2aba27e7608dd10/6b/71c7c5be/JsonNode.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f7dc6a66ce64068bee60c2aba27e7608dd10/af/04e70085/JsonNode.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f7dc6a66ce64068bee60c2aba27e7608dd10/f1/c45973a6/JsonConverter`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f8979ec275b4e82888312c896092fb61cb620/12/e719eba2/VisualElement.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f8979ec275b4e82888312c896092fb61cb620/2d/db8068ae/HybridWebView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4f8979ec275b4e82888312c896092fb61cb620/93/96b11fd6/Element.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/505a76e4c68e465685fdb8e931a943d236d28/24/e498a5d0/MapExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5418ddd702a64c39834cc1227ff927e15e00/09/9c902a0a/ICookieStoreService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5418ddd702a64c39834cc1227ff927e15e00/6a/35c7a1d6/ILocalStorageService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5418ddd702a64c39834cc1227ff927e15e00/d1/584d9503/IStorageService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/562134ee629b40379f8862725579ac7920908/e8/8a7cf500/HttpClientJsonExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/57fdd832f3ef4c78a3d467967d41772322908/60/8dc53633/IServiceCollection.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/608729900997469182c81444a25a75021c7a38/a5/99e645c4/Page.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/620b388e587141b29cde2a5c8a01ecc43c538/09/2357747e/ConcurrentDictionary`2.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/620b388e587141b29cde2a5c8a01ecc43c538/79/20dbaf17/BlockingCollection`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/620f6e7eba834c42a2f2db6b22950df6531838/55/b03f0097/Convert.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/630acdeb1565430d9328a5bf4673c26415928/7e/48b7d4ab/WebAssemblyHost.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/63ee384769844530bc73c5391d8c6ee0aa70/4d/d003008d/ServiceCollectionServiceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6a121952883a48049cdcbef7508936ae1755b0/aa/77368b29/AppWindowPresenterKind.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6b4b7e2aa37a4eb2b6eeb2a284f132f483518/dc/7850c04a/Utilities.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6ba4aaa2780646e5ada7d20877c43f4f1b6930/6e/43fa277f/JsonDocument.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/76803a04e31b4fee99d90bcbfc5a6bdde8e930/5c/682921b0/String.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/76803a04e31b4fee99d90bcbfc5a6bdde8e930/af/c99890ea/Exception.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/7969ace55d0e44e09678441138acdc8fbf20/dd/0d8f89b6/RSA.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/8c3203a96114427dac3d2afec88e8e539b08/e6/ff3b232b/ZipFileExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/8d78ff2d2a3b4ed19cd61cd0a0baf8351f528/7a/76b0c392/Enumerable.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/90a5b0623e9d431481966d1f6bba2caf1ff20/b2/40010dd2/JSRuntimeExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/90a5b0623e9d431481966d1f6bba2caf1ff20/f6/eb7576d4/JSRuntime.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/914c58d93cbb4a21bb4b23c2828e2a20354a0/8d/e12fe3c7/ComponentBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/92edc974701e40a589093347c8b789e1ad10/da/9e23a763/ServiceCollectionHostedServiceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/99c8cebad3cc4a0595b5d68a42cf27c986f28/4e/92ded860/EventCallbackWorkItem.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/99c8cebad3cc4a0595b5d68a42cf27c986f28/75/6248c755/NavigationManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/99c8cebad3cc4a0595b5d68a42cf27c986f28/79/ee2c63c5/RenderTreeBuilder.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/9c77e3a3ff4347ec8533a4cc7e8e4cb4347200/39/bf1b558f/ServiceCollectionExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/9c77e3a3ff4347ec8533a4cc7e8e4cb4347200/ae/5a00e76e/Layout.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a6b7f037ba7b44df80b8d3aa7e58eeb2e8e938/29/150b58b5/EventHandler.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a6b7f037ba7b44df80b8d3aa7e58eeb2e8e938/4d/54a7c9b1/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a8f4e63a18974030955d623c4952e1bcf270/2b/e7248882/JSObjectReferenceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a8f4e63a18974030955d623c4952e1bcf270/e1/988bb198/DotNetObjectReference.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a9cdf275e73a4952afed7a4fa4299a483ad38/67/b3d3612d/RazorPage.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/aca93a7aa47544b490dc5fb41509b60f1b6928/cd/3ddc4b95/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/09/5ccf6dfc/MDataTable`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/15/42101fa3/DataOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/17/ecc7d30d/MSelect`3.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/29/63fb9e3d/MDataIterator`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/67/249aa82a/MButton.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/95/e95de2a2/IPopupService.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/b0/6bda3f5c/EnqueuedSnackbars.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/b8/36253c47/MInput`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/c9/6c64169a/MChip.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b176fe01c2a14f46802bedc7b3738d9b301400/ed/696ce8a5/MButton.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b95edcc1c50e44ebbf7efbbca83c493ee8e928/66/dea80ce9/Enum.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b95edcc1c50e44ebbf7efbbca83c493ee8e928/b6/e092650e/SafeFileHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/b9d570839e8c4e54baf2dcdc73be61b412b08/a7/312824fe/ClientWebSocket.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/c5265e644e22482194754977faf84d7a7200/75/d6008671/Extensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cbe9bd9f42d94858941ef74ba805384c1caa18/a1/47739fc2/HybridWebViewRawMessageReceivedEventArgs.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cf09892f71c24deab0bf5a1ab8c66fdc6ef028/8d/7db99370/FrameworkElement.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d36f5542a36946fd848d7e2cc5ad4b9695328/db/52e2cbfb/Image.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d36f5542a36946fd848d7e2cc5ad4b9695328/f6/45971dbe/ImageFormat.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d5eabc389e724859bea40cb8ff10ea1c4fc10/8d/b606d660/ReactiveCommand.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d5eabc389e724859bea40cb8ff10ea1c4fc10/b2/ce1136a0/DependencyResolverMixins.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d5eabc389e724859bea40cb8ff10ea1c4fc10/b8/81317075/ReactiveCommand`2.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d96e02df17164c71bb8089546b51ff521f4930/52/d2372a3d/RSA.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/db739f6fe25040a09df5eeeffa20204fb6938/19/41974f77/Timer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/df877abe81fc4e6c92246ae7334a05ab2f4800/7c/bd55d6f2/MSelect`3.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/df877abe81fc4e6c92246ae7334a05ab2f4800/cf/ea71c761/MCard.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/06/4cf7ff87/String.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/09/70f2c4de/EventTags.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/65/43947cf5/Decimal.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/73/c3f93d60/SafeFileHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/7d/81a5ad5f/Environment.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/81/658f024c/File.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/a1/8eb60c2c/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/a3/75d3c9c2/StreamReader.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/bc/65124c9c/Number.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/c1/138ff15e/CancellationTokenSource.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/c7/745961c4/IDisposable.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/cb/a6f7e27a/ExecutionContext.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/e0/d1b233d0/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/e4/abecbf67/Exception.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/ea/00ba90d5/Dictionary`2.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/f6/4fd04cf4/DateTime.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/f9/86313f49/WaitHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e3157c720a4245958cf8a1b12c4b4a27e90928/06/152d0697/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e3157c720a4245958cf8a1b12c4b4a27e90928/4e/2dc0df5b/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e340358da82c45f3a364000ea4fd007e3cd28/9f/372bc559/QueryHelpers.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e3e81322313e4161a1db166ef9e45f169a10/5b/89fb0bb3/ReactiveComponentBase`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e3e81322313e4161a1db166ef9e45f169a10/d6/44f12039/ReactiveInjectableComponentBase`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e703623710cc408eaac0f2c6feb0a00c81230/e9/78bac74a/ExceptionHelpers.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e7f6c511b1e94f50abe34bd1f2c2c1002c708/07/06061316/WebSocketState.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e7f6c511b1e94f50abe34bd1f2c2c1002c708/7f/cc5c2d19/WebSocket.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e8e5a49a6cf645398f70e67356a1113019518/20/95554a91/WebApplication.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e8e5a49a6cf645398f70e67356a1113019518/2e/7d719be3/WebApplicationBuilder.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ec741cbe9485415fbe03923d2327c08f1b9318/3a/461643cc/HttpClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ec741cbe9485415fbe03923d2327c08f1b9318/3a/b81bdd72/HttpClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ed9594fbe0b7417bb96c34eaf2bfc1503f938/63/e1404ee8/Uri.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/metadata/363a9cb0787b4b5f8334385bba011c905f0c00/2b/c5835327/UFU.CoreFX.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/metadata/3a81c6c0c6e94dc28923e000cb2c2c19b7c30/80/853f6687/Microsoft.Maui.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/metadata/eb8824ebf9c84f80b5b0a13f6f303a741b7e00/cc/0ed37775/SixLabors.ImageSharp.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/108afe858165a89d9c44a73767f5520cca5eb89fd357279e999a7196377/ValueTuple.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/14143cf3acc15ece9f8e83e4f9c594bb813abc89dd97cc546346d3de5c8e/MDataFooter.razor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1879de5c8e7156049c355a9faf1a33454d63ff02b6849419412f4ee6c608d96/ValueTask.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/198a6261cebc324728dcc4c178edc5f2894123f711722c80e8557cbfd27651/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1af51483c9bf18871cce649b9f966dd7be686536f998c33bba3c5fc4adea4a0/Task.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1e6253f938894ce5e58c9d13271790eda41424ed2e9ce2743fb6d94535d562/EventCallbackOfT.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1f267e2aad3c08bb1d0cbf513920b3ac81f3c4bda7fff2e946da18113428b3/MauiApp.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/242bcc6c257c9bb019b0d56a7076f52a6dc6f11420c48298e157edde842bebac/BlazorWebView.Windows.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/269bb7795fa521417f46e34bc83b19e7f3690b3188495860ee939a746ab9cc/EChartsJSModule.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/269fba27841844b2d16e91aea2e58894468cb17d132c9350fd6cafc0aa6afc7/ComponentBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/277022deb5485dfae11942b020d7562a41dd8c64a3acd797a666844b24585d1/IPopupService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/27b91272f348469b8fccc471c1ccd767f57a64f55a44975790bfae805b4ebb/Future.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2854ce6d56c18d0d837d3a3ef9c4f2c7c77691fa3528c8394986ac7ce7719/StackFrameIterator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/296ae41f6460fbe8f01ab3b8143999598784c68e7e365342e24e32f4256614/TabRule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2beaec4c56a883a46a8c14bfc2b92fe9c61d51ef3d597ed85b8fd1b0191774/DynamicQueryableExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2dd8adc97c57bd9719b58d97eca822d918c/HybridWebViewHandlerJsonContext.GetJsonTypeInfo.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/3b60a568ed22f283255f6f8d6b4a6dbd11eaec8c61ba7d524333fdff6e418cff/ClientWebSocket.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/3ea95343b9387b73062a9b61b7538f826c54ce3529632b4472ba4494044d23/AsyncTaskMethodBuilderT.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/40f11f1ca53a8f2247c42993aaa5e8715c43bd74334e2db142e3527ff7df488/ThrowHelper.Node.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/437949a1581226d19dd5c12fb9c863bd3a7b4fe91d94de6993adca186d046/ServiceCollectionExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/49248b96372ef8ebdfee01642bcb21c433254ddb2e233f7560695f7de3d8b/MChip.razor.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/4b845e1ab72687a184ca3650a037fab73bcbce5035e31e3ff860df2494a2/ReactiveCommand.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/4d2d7438fbaaaa3b663b255d5e29fc95cf61aa3652a6cb7edacdbf1b7440/MasaComponentBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/4e2ed42a9b4f27d92e81f9a99bb394f6c9bb27/HybridWebViewHandlerJsonContext.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/553c91c7516c62767c8e482f456f10cb7a7c89b87bb22fba815d37ecf118a9e/Path.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/558c1d46e1e21d2e78ee2ab67a674f6927bf95355b2f245f35d74bb5ec0f92/CancellationTokenSource.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/56c665453e61b7a241e4db6c184a5c6e713678f06c873acdeffaff638f9486/CallSiteValidator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/575c31ecc35a636b6f962cb71d53992552941a18982aae1c8e6c16305cc3761b/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/5b92574433232d5c5ed533393d2853774e0233c2af2d910367761c50592d/KeyValuePair.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/5c8ac4e2b5f6b8368b6e4244b2fc480b859fd87578fb32344fd7170152159/HybridWebViewRawMessageReceivedEventArgs.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/6354a7b35d7821629924d3676acd7e67a6f7f94343e0e66ec439aa2bd6ed5/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/65f557c97610f2f659fa6eb9697e0b4f51cbb2c64ba1191fbb2157daee62133/MToggleable.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/6d45c530695c3351da41c6e52924b56231b0116868a1ffbb8daaaa11711d3320/ServiceProviderKeyedServiceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/732a3ff679450a837fc08eaccb7fabe4f5602b49befb51bc8369197e06bb6/Buffer.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/762d5267ee9f8e905fb9da6f825cee9990ac36f9c740c440363682524b4967/EChartsJSObjectReferenceProxy.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/79b4b5d07e8c5ac3de172b75667c6bded8e1fa6f42f36d8f6dc2f9e3d568/ArgumentNullException.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/7a1ad32abaee44480b66c2c1fcee9628cca92a4cd272eced5781564201180/Number.Parsing.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/7fae937e13ed52cf7b7079061a6b947b946c41c4a86a47a162b519b3763447/WebApplication.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/80d5c8631dbbc6d157fa47e19e9ffc89725507666cbd36d2916a9a431f05fa8/FlyoutBehavior.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/80ffc2efb289afc4d3c7e957db9a02d823af214222ec89e274bbdc632c07e15/ObjectDefaultConverter.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/8533613521963b4321f6bfee56b253c5a8d7b3d19c5f8f9fbaf6b914c61ebc/NativeWindow.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/8a06b314b31fa2ec54ffc18e9d057e91891cf72182dd6ce1985a0a43c/MemoryStream.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/8de19b695332190499bf4ae4bb2298a75a66e9c40b8ef6615cae95a57e584/MECharts.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/8e74803b88261fc723cfb0c2383759429dc5ef4fddf2ec9ade5f02b983c81e3/EventCallbackWorkItem.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/907a3e4cef18a5958f645ed8e3dc9ed72d97a5d4dcf616cc1f539feb74f48d/PPageStackTab.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/9446111159ad35e383efe39dce611854048c9ede2b5f8dc21d728b2ce37f8/Container.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/95c2bb4545e2464fac5182e0ca4181eb5fac8f3/Components_DataIterator_MDataFooter_razor.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/95cd8cf2ccd2bb650b63829170ed7bc1282533b0e83fa5cacd7bc0626dc6/RelayCommand.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/969c1feb109cc8493fe43fc9969c28df70d448c9/HybridWebViewHandlerJsonContext.String.g.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/99bdf9c4711cfca240583b945bad450a17a3a33/HybridWebViewHandlerJsonContext.PropertyNames.g.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/9f3a4c1645a90e1d6a54347a377e58973c7e4825f6f34716f7f4e017e4ed5/IDataFooterParameters.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/9ff4a377ddd8b8376a34e93b525519e26e3989a7f57783aa958abc10f5ec87/AsyncValueTaskMethodBuilder.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/a2486b5e044b68e08e1cf9c5b9aa7af81ef4da37de0a0551f713f5374ace/Exception.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/a86e1612711dc640abd3acd49217d406d4e1fec4c82edc89f3a4d38d68cd0/RxApp.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/ac8eb857d780126ca383394720efc6566564e0cb189ca84769b246aef686ba/HybridWebView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/af5e8f58e0cd43a56f652468b1080bbdbf4276e50cddad25571e6f22556715/EntityFrameworkQueryableExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/af819871a45c889a99278adfe82a4a8df6f56d12876e52dccf9dac0b053161f/ArrayConverter.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b248864d65a854e6532bf12c97c95038d498c0b2dffa76a017cd13a87721e77c/IEChartsJSObjectReferenceProxy.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b42ed0df5c4f7cc762378d6088a7654fe3bfb924898886f2c1b3529ca41fa081/ViewForMixins.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b56a992da534ff2f7a8e226182a4862f1dc19c49fb9305485051deb348cc23/StreamReader.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b5727fa4f0693ee0abec9a167af4cf429be1574e5837bd0fc1b4e80884adc58/VideoCapture.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bd1d5c50194fea68ff3559c160230b0ab50f5acf4ce3061bffd6d62958e2182/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bf35e833c131204afa71fce1c83ab39cedec6a66566fed14f61ab1f19791b9f/EChartsInitOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c5a9519890f6ba84826a4606f6c3b71b031c861ffb0916e7f3e382fa3cd41e/PopupServiceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c6ad457575d35dcbff26c9a5e3fb1d92f1d3c55f25ef6ebf475367b5d8/List.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c7102cd0ffb8973777e61b1942c3fffac7e14016a511d055c3adf73ff91748/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c78f60511b7bc96a2a4f3f6565377b9b37f955a5e73e17ff9567e2a2a2cf021/RenderTreeBuilder.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c8aaa787d4b9f06013317d155940521fd0694e7378c5f44bf7cf606e8b3c4bc7/DataOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c8e03614d261f91ee36c5379b2121d98f8d535fdee7177c847d3f2eaf8b67/Image.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c97a53f2b92513f73d7e40f2dd8e6a3159279e68a2be13637b494733d6142499/PPageStack.razor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/cb84b0c6366bb07e1e6831d31e321b5f2dee508d7317ddd6e8e62b6ce82b5d/Enum.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/cd27da1c88eb987fd83f3aea4956e1572111916f0603b19c22f62918cfbbe/MasaComponentBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/ce97cce1c8751784d3c11a71438382de60495a5294862c55163d6db935b2ca/Utilities.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d1bf7e54af961c6e1eb9412504ddaef30b4a2164819574f507de69b1aaf586/JsonValueOfT.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d1f68ef86315497c7b39ddb155d5cf3ace964abca69857f9943cd58479799/ServiceProvider.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d8e190e35d13d28aee285245b0e92c885b737074cebbf0ec67284d0a99710/TapGestureRecognizer.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d9d9faea5e6ed6438a777ef8c6eed41261f81d8e86488ed923ad92564f54e/ThrowHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/da76a636bea797b7867a4fdb219fa9cacf0ac938b33efff6868fc3881154493/BlazorWebViewServiceCollectionExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/df71358f7b7337a1eee3a65abe6d678dd9a065e256b8cc3fd44a2949754/Object.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/e5d623ea960f2c3c9fda144954d339f8d4cd3dad6dd8bd4ab96093a010ab/Dictionary.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/e9275f7faedee57da8f93beaed46220f4ebe5ed13629032f56996549c42c3e0/MTabs.razor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/f295b4ae92233b176e2919cee0d6dab82dca2210d27162bdfe8744f5aa0/ProgressCircularOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/f324c62785677b53e916aa9084ae54a42014c79d132908ae191cf8e3af4cfb/File.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/f4708639776589f5ac2739f6fcbfc67343b1f9dac51cc1dbfdb003eef77cc1c/Monitor.CoreCLR.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/f5b3dab4ea5027d02fe4e4af1e73512f31dc6a9/HybridWebViewHandlerJsonContext.JSInvokeMethodData.g.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fa1dd9ebea84d93752acce07f99818f9cbf64510d4d79e5753eb7fc596a261/View.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fa4df877efa5d154ca3ebd4386f960f4eef5ccc034f7da1d1bcb981cb1c79/MauiAppBuilder.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fb6787529f6450c528c1dca7dae4fde6541cc5d26cdff222a6e24775a8ba6/AppDomain.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fbca76ff27de297a9cf87a2a37eb36225555cdef2d5b2e34765ad81d6b6e22/PopupService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fd3613e79f4141b726a234dc6b12c9e2fa50a7be28b045e19275c69bbf1166/CancellationToken.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/Services/AdminSocketConnect.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/Services/HXDeviceSocketService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.MAUI/MainPage.xaml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.MAUI/MainPage.xaml.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.MAUI/obj/Debug/net9.0-windows10.0.18362.0/win-x64/Platforms/Windows/App.g.i.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.SCI.Shared/Module.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.SCI.Shared/Program.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/launchSettings.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.SCI.Web/Services/ALGDataAnalysisService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.SCI.Web/appsettings.Development.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.SCI.Web/appsettings.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Models/PatientModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Models/PatientRecordModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Models/SystemRateConfig.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Report/EmotionReport.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Report/ReportDetailEmpty.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Report/ShowReportDetail.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/DeviceConfigEdit.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/HxSystemConfig.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Web/Module.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Web/Services/AlgWebSocketClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Web/Units/HrvVariable.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Web/appsettings.Development.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/HX.HRV.Web/appsettings.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Areas/IoT/Controllers/V2/DevicesController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Services/BinaryDeviceWebSocketConnect.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Services/BinaryIoTService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/AdminWebSocket.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/BinaryDeviceWebSocketConnect.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/CommandConfigWebSocketService.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/IoTService.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/BaseModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DataEntityAttribute.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DataModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DataPermission.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DistrictModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/OrganModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/Result.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/UploadFileViewModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/UserModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/UserOrganModel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/VerifyCodeModel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Pages/Core/Form/GenerateFormItem.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Pages/DefaultLayout.razor" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/AuthService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/StateService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Utils/JsonTool.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Utils/UValidator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.TestWeb.Web/Module.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.TestWeb.Web/Pages/_ViewStart.cshtml" root0="FORCE_HIGHLIGHTING" root1="SKIP_HIGHLIGHTING" root2="FORCE_HIGHLIGHTING" />
  </component>
  <component name="HttpClientEndpointsTabState">
    <option name="cachedRequestData" value="&lt;CachedHttpClientTabRequests&gt;&#10;  &lt;entry key=&quot;e2c46a3c19d50b66b8731df97e4c385a5ca20cb3&quot; value=&quot;###&amp;#10;GET http://localhost:55051/api/IoT/PlaceDevices/Get/{{id}}&quot; /&gt;&#10;&lt;/CachedHttpClientTabRequests&gt;" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2iwiESYDERIHQV3LbRoKZEatRWD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET 启动设置配置文件.HX.Experiment.Web.Service: BlazorApp.Server.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.Experiment.Web: http.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Base.Shared: HX.HRV.Shared.Base.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Base.Web.executor&quot;: &quot;Run&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Client: Windows Machine.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.MAUI.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.MAUI: Windows Machine.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Maui: Windows Machine.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.SCI.Shared: HX.HRV.Shared.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.SCI.Web.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.SCI.Web: HX.HRV.SCI.Server.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Web.Base.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Web.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HX.HRV.Web: BlazorApp.Server.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.UFU.IoT.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.未命名.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 项目.HX.Experiment.Client.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 项目.HX.Experiment.Window.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 项目.HX.HRV.Keygen.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 项目.HX.HRV.MDns.executor&quot;: &quot;Run&quot;,
    &quot;.NET 项目.未命名 (1).executor&quot;: &quot;Debug&quot;,
    &quot;.NET 项目.未命名.executor&quot;: &quot;Debug&quot;,
    &quot;Docker.HX.HRV.SCI.Web/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder0&quot;: &quot;0&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder1&quot;: &quot;1&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder2&quot;: &quot;2&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder3&quot;: &quot;3&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder4&quot;: &quot;4&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder5&quot;: &quot;5&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth0&quot;: &quot;283&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth1&quot;: &quot;283&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth2&quot;: &quot;283&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth3&quot;: &quot;282&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth4&quot;: &quot;282&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth5&quot;: &quot;282&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder0&quot;: &quot;0&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder1&quot;: &quot;1&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder2&quot;: &quot;2&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder3&quot;: &quot;3&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder4&quot;: &quot;4&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder5&quot;: &quot;5&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth0&quot;: &quot;283&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth1&quot;: &quot;283&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth2&quot;: &quot;283&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth3&quot;: &quot;282&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth4&quot;: &quot;282&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth5&quot;: &quot;282&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;ShowUsagesActions.previewPropertyKey&quot;: &quot;true&quot;,
    &quot;UWP.HX.Experiment.MAUI.executor&quot;: &quot;Debug&quot;,
    &quot;UWP.HX.Experiment.executor&quot;: &quot;Debug&quot;,
    &quot;UWP.HX.HRV.Client.executor&quot;: &quot;Debug&quot;,
    &quot;UWP.HX.HRV.MAUI.executor&quot;: &quot;Debug&quot;,
    &quot;XThreadsFramesViewSplitterKey&quot;: &quot;0.9515707&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;do_not_show_Android_plugin_suggestion&quot;: &quot;true&quot;,
    &quot;iOS.HX.HRV.Client.executor&quot;: &quot;Debug&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;list.type.of.created.stylesheet&quot;: &quot;CSS&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propDebugger&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;发布到 IIS.HX.HRV.SCI.Web: FolderProfile.executor&quot;: &quot;Run&quot;,
    &quot;发布到 IIS.HX.HRV.Web: FolderProfile.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.HX.HRV.Mdns.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HRV Client.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HRVClient HX.HRV.MAUI .executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Client to folder.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Keygen to folder (1).executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Keygen to folder.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Keygen.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.MAUI to folder (1).executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.MAUI to folder.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.SCI.Web to folder.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.SCI.Web.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Web to folder (1).executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Web to folder.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish HX.HRV.Web.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.未命名.executor&quot;: &quot;Run&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;
    ]
  }
}</component>
  <component name="RiderProblemsViewState">
    <option name="autoscrollToSource" value="true" />
    <hideBySeverity>
      <option value="14" />
      <option value="15" />
      <option value="20" />
      <option value="300" />
    </hideBySeverity>
  </component>
  <component name="RunManager" selected=".NET 项目.HX.Experiment.Window">
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Mdns" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" ready_to_run="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/MDns" target_framework="net9.0" uuid_high="1481166463794105324" uuid_low="-6291931268936538839" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Mdns" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" ready_to_run="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/MDns" target_framework="net9.0" uuid_high="1481166463794105324" uuid_low="-6291931268936538839" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HRV Client" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Debug" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/HRVClient" target_framework="net9.0-windows10.0.26100.0" uuid_high="-167367698336955260" uuid_low="-5757340877832139143" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HRV Client" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Debug" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/HRVClient" target_framework="net9.0-windows10.0.26100.0" uuid_high="-167367698336955260" uuid_low="-5757340877832139143" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.Keygen" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/HX.HRV.Keygen/bin/Release/net8.0-windows10.0.20348.0/win-x64/publish" target_framework="net8.0-windows7.0" uuid_high="8120363119044939642" uuid_low="-5739297016225907748" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.Keygen" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/HX.HRV.Keygen/bin/Release/net8.0-windows10.0.20348.0/win-x64/publish" target_framework="net8.0-windows7.0" uuid_high="8120363119044939642" uuid_low="-5739297016225907748" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.MAUI to folder (1)" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/HX.HRV.MAUI/bin/Release/net9.0-windows10.0.26100.0/win-x64/publish" target_framework="net9.0-windows10.0.26100.0" trim_unused_assemblies="true" uuid_high="-167367698336955260" uuid_low="-5757340877832139143" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.MAUI to folder (1)" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/HX.HRV.MAUI/bin/Release/net9.0-windows10.0.26100.0/win-x64/publish" target_framework="net9.0-windows10.0.26100.0" trim_unused_assemblies="true" uuid_high="-167367698336955260" uuid_low="-5757340877832139143" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.MAUI to folder" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Debug" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/Client" target_framework="net9.0-windows10.0.22000.0" uuid_high="-167367698336955260" uuid_low="-5757340877832139143" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.MAUI to folder" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Debug" delete_existing_files="true" include_all_content_for_self_extract="true" include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true" runtime="win-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/Client" target_framework="net9.0-windows10.0.22000.0" uuid_high="-167367698336955260" uuid_low="-5757340877832139143" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.SCI.Web" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" runtime="linux-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/HX.HRV.SCI.Web" target_framework="net6.0" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.SCI.Web" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" runtime="linux-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/HX.HRV.SCI.Web" target_framework="net6.0" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.Web" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" runtime="linux-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/HX.HRV.Web" target_framework="net6.0" uuid_high="-1927604995763519052" uuid_low="-4704142784711891444" />
      <method v="2" />
    </configuration>
    <configuration name="Publish HX.HRV.Web" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" runtime="linux-x64" self_contained="true" target_folder="$PROJECT_DIR$/../../../HuiXin/Publish/HX.HRV.Web" target_framework="net6.0" uuid_high="-1927604995763519052" uuid_low="-4704142784711891444" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Encrypt: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/../HX.Encrypt/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-7062336802614656135" uuid_low="-5373831055123197727" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Encrypt: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/../HX.Encrypt/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-7062336802614656135" uuid_low="-5373831055123197727" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Base.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Base.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3563809249854574438" uuid_low="-7784319787018017057" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Base.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Base.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3563809249854574438" uuid_low="-7784319787018017057" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Base.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Base.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-5006675940730713067" uuid_low="-8652767265851332768" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Keygen: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Keygen/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="8120363119044939642" uuid_low="-5739297016225907748" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Keygen: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Keygen/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="8120363119044939642" uuid_low="-5739297016225907748" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.MDns: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.MDns/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="1481166463794105324" uuid_low="-6291931268936538839" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.MDns: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.MDns/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="1481166463794105324" uuid_low="-6291931268936538839" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Shared: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Shared/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="6858689051749600294" uuid_low="-7482742840737672776" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Shared: FolderProfile1" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile1.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Shared/Properties/PublishProfiles/FolderProfile1.pubxml" uuid_high="6858689051749600294" uuid_low="-7482742840737672776" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Shared: FolderProfile1" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile1.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Shared/Properties/PublishProfiles/FolderProfile1.pubxml" uuid_high="6858689051749600294" uuid_low="-7482742840737672776" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Shared: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Shared/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="6858689051749600294" uuid_low="-7482742840737672776" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: FolderProfile1" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile1.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile1.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: FolderProfile1" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile1.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile1.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: HX.HRV.SCI" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="HX.HRV.SCI.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/HX.HRV.SCI.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web: HX.HRV.SCI" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="HX.HRV.SCI.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.SCI.Web/Properties/PublishProfiles/HX.HRV.SCI.pubxml" uuid_high="3884818055423082627" uuid_low="-8045845793505733482" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Shared: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Shared/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3354174440883309047" uuid_low="-7168233236755198533" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Shared: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Shared/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3354174440883309047" uuid_low="-7168233236755198533" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-4310757413260934194" uuid_low="-8539190980448530337" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-4310757413260934194" uuid_low="-8539190980448530337" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Web/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="6127951369125381920" uuid_low="-8223586243030101465" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web: HX.HRV.Web" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="HX.HRV.Web.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/Properties/PublishProfiles/HX.HRV.Web.pubxml" uuid_high="-4310757413260934194" uuid_low="-8539190980448530337" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web: HX.HRV.Web" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="HX.HRV.Web.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/Properties/PublishProfiles/HX.HRV.Web.pubxml" uuid_high="-4310757413260934194" uuid_low="-8539190980448530337" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web.Base: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Web.Base/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-5006675940730713067" uuid_low="-8652767265851332768" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web.Base: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Web.Base/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-5006675940730713067" uuid_low="-8652767265851332768" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web.Base: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.HRV.Web.Base/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-5006675940730713067" uuid_low="-8652767265851332768" />
      <method v="2" />
    </configuration>
    <configuration name="Masa.Maui.Windows: MSIX-win10-x64" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="MSIX-win10-x64.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment.Client/Properties/PublishProfiles/MSIX-win10-x64.pubxml" uuid_high="2616411690779430897" uuid_low="-4653600069148038317" />
      <method v="2" />
    </configuration>
    <configuration name="Masa.Maui.Windows: MSIX-win10-x641" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="MSIX-win10-x641.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment.Client/Properties/PublishProfiles/MSIX-win10-x641.pubxml" uuid_high="2616411690779430897" uuid_low="-4653600069148038317" />
      <method v="2" />
    </configuration>
    <configuration name="Masa.Maui.Windows: MSIX-win10-x641" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="MSIX-win10-x641.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment.Client/Properties/PublishProfiles/MSIX-win10-x641.pubxml" uuid_high="2616411690779430897" uuid_low="-4653600069148038317" />
      <method v="2" />
    </configuration>
    <configuration name="Masa.Maui.Windows: MSIX-win10-x64" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="MSIX-win10-x64.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment.Client/Properties/PublishProfiles/MSIX-win10-x64.pubxml" uuid_high="2616411690779430897" uuid_low="-4653600069148038317" />
      <method v="2" />
    </configuration>
    <configuration name="UFU.IoT: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3563809249854574438" uuid_low="-7784319787018017057" />
      <method v="2" />
    </configuration>
    <configuration name="UFU.IoT: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3563809249854574438" uuid_low="-7784319787018017057" />
      <method v="2" />
    </configuration>
    <configuration name="UFU.IoT: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-1749448684546603468" uuid_low="-6216525510155494184" />
      <method v="2" />
    </configuration>
    <configuration name="未命名" type="DotNetProject" factoryName=".NET Project" nameIsGenerated="true">
      <option name="EXE_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/bin/Debug/net6.0/HX.HRV.SCI.Web.exe" />
      <option name="PROGRAM_PARAMETERS" value="--applicationpath D:\Project\HuiXin\HX.HRV\HX.HRV.SCI.Shared\bin\Debug\net6.0\HX.HRV.SCI.Shared.dll" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.HRV.SCI.Web" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <envs>
        <env name="ASPNETCORE_URLS" value="http://0.0.0.0:5222/" />
      </envs>
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net6.0" />
      <browser url="http://0.0.0.0:5222/" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client" type="DotNetProject" factoryName=".NET Project" temporary="true">
      <option name="EXE_PATH" value="$USER_HOME$/.nuget/packages/microsoft.aspnetcore.components.webassembly.devserver/9.0.7/tools/blazor-devserver.dll" />
      <option name="PROGRAM_PARAMETERS" value="--applicationpath D:\Project\HuiXin\HX.HRV\HX.Experiment.Client\bin\Debug\net9.0\HX.Experiment.Client.dll" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.Experiment.Client" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net9.0" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client" type="DotNetProject" factoryName=".NET Project" temporary="true">
      <option name="EXE_PATH" value="$USER_HOME$/.nuget/packages/microsoft.aspnetcore.components.webassembly.devserver/9.0.7/tools/blazor-devserver.dll" />
      <option name="PROGRAM_PARAMETERS" value="--applicationpath D:\Project\HuiXin\HX.HRV\HX.Experiment.Client\bin\Debug\net9.0\HX.Experiment.Client.dll" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.Experiment.Client" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net9.0" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web.Client/HX.Experiment.MAUI.Web.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web.Client/HX.Experiment.MAUI.Web.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Client/HX.Experiment.Web.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Client/HX.Experiment.Web.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Window" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/HX.Experiment.Window.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Window" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Window/HX.Experiment.Window.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Keygen" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="$PROJECT_DIR$/HX.HRV.Keygen/bin/Debug/net8.0-windows7.0/HX.HRV.Keygen.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.HRV.Keygen/bin/Debug/net8.0-windows7.0" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.HRV.Keygen/HX.HRV.Keygen.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net8.0-windows7.0" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Keygen" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="$PROJECT_DIR$/HX.HRV.Keygen/bin/Debug/net8.0-windows7.0/HX.HRV.Keygen.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.HRV.Keygen/bin/Debug/net8.0-windows7.0" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.HRV.Keygen/HX.HRV.Keygen.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net8.0-windows7.0" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.MDns" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="$PROJECT_DIR$/HX.HRV.MDns/bin/Debug/net9.0/HX.HRV.MDns.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.HRV.MDns/bin/Debug/net9.0" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.HRV.MDns/HX.HRV.MDns.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net9.0" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.MDns" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="$PROJECT_DIR$/HX.HRV.MDns/bin/Debug/net9.0/HX.HRV.MDns.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.HRV.MDns/bin/Debug/net9.0" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.HRV.MDns/HX.HRV.MDns.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net9.0" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="未命名" type="DotNetProject" factoryName=".NET Project" nameIsGenerated="true">
      <option name="EXE_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/bin/Debug/net6.0/HX.HRV.SCI.Web.exe" />
      <option name="PROGRAM_PARAMETERS" value="--applicationpath D:\Project\HuiXin\HX.HRV\HX.HRV.SCI.Shared\bin\Debug\net6.0\HX.HRV.SCI.Shared.dll" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/HX.HRV.SCI.Web" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <envs>
        <env name="ASPNETCORE_URLS" value="http://0.0.0.0:5222/" />
      </envs>
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net6.0" />
      <browser url="http://0.0.0.0:5222/" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment: Windows Machine" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.19041.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Windows Machine" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment: Windows Machine" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.19041.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Windows Machine" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Client: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/HX.Experiment.Client.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI.Web: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.Web/HX.Experiment.MAUI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web/HX.Experiment.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web.Service: BlazorApp.Server" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="BlazorApp.Server" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web.Service: BlazorApp.Server" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="BlazorApp.Server" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.MAUI" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.19041.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Windows Machine" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.MAUI: Windows Machine" type="LaunchSettings" factoryName=".NET Launch Settings Profile" singleton="false">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.26100.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Windows Machine" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <option name="RUNTIME_TYPE" value="coreclr" />
      <method v="2">
        <option name="Build" default="false" projectName="HX.HRV.MAUI" projectPath="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" />
      </method>
    </configuration>
    <configuration name="HX.HRV.MAUI: Windows Machine" type="LaunchSettings" factoryName=".NET Launch Settings Profile" singleton="false">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.26100.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Windows Machine" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <option name="RUNTIME_TYPE" value="coreclr" />
      <method v="2">
        <option name="Build" default="false" projectName="HX.HRV.MAUI" projectPath="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" />
      </method>
    </configuration>
    <configuration name="HX.HRV.MAUI" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.MAUI/HX.HRV.MAUI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.19041.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Windows Machine" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.SCI.Web" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.SCI.Web" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.SCI.Web" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.SCI.Web" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Shared/HX.HRV.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.Shared.Base" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Shared.WASM" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.Shared.WASM" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Shared.WASM: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Shared.WASM: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Shared.WASM" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Shared.WASM/HX.HRV.Shared.WASM.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.Shared.WASM" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Shared/HX.HRV.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.Shared.Base" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Web/HX.HRV.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="BlazorApp.Server" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web: BlazorApp.Server" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.SCI.Web" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" with-js-debugger="true" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web: BlazorApp.Server" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.SCI.Web/HX.HRV.SCI.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.SCI.Web" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" with-js-debugger="true" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Web/HX.HRV.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="BlazorApp.Server" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.HRV.Web" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.HRV.Web/HX.HRV.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HX.HRV.Web" />
      <option name="USE_EXTERNAL_CONSOLE" value="1" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Masa.Maui.Windows: 配置文件 1" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0-windows10.0.19041.0" />
      <option name="LAUNCH_PROFILE_NAME" value="配置文件 1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Masa.Maui.Windows: 配置文件 1" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0-windows10.0.19041.0" />
      <option name="LAUNCH_PROFILE_NAME" value="配置文件 1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.CoreFX.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.CoreFX.Shared" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.CoreFX.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.CoreFX.Shared" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.IoT" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT/UFU.IoT.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT/UFU.IoT.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.IoT.Shared" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.IoT.Shared" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT.Shared" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT.Shared/UFU.IoT.Shared.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.IoT.Shared" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.IoT" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UFU.IoT" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT/UFU.IoT.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="UFU.IoT" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Masa.Maui.Windows" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Masa.Maui.Windows" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="Masa.Maui.Windows" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="Masa.Maui.Windows" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment/HX.Experiment.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Masa.Maui.Windows" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Masa.Maui.Windows" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Client/Masa.Maui.Windows.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.Web.Service/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="hx.hrv.web" />
          <option name="contextFolderPath" value="D:\Project\HuiXin\HX.HRV" />
          <option name="sourceFilePath" value="HX.Experiment/HX.Experiment.Web.Service/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.Web.Service/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="hx.hrv.web" />
          <option name="contextFolderPath" value="D:\Project\HuiXin\HX.HRV" />
          <option name="sourceFilePath" value="HX.Experiment/HX.Experiment.Web.Service/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="hx.hrv.sci.web" />
          <option name="containerName" value="hx.hrv.sci.web" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="80" />
                <option name="hostIp" value="127.0.0.1" />
                <option name="hostPort" value="5220" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="showCommandPreview" value="true" />
          <option name="sourceFilePath" value="HX.HRV.SCI.Web/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.SCI.Web/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="hx.hrv.sci.web" />
          <option name="containerName" value="hx.hrv.sci.web" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="80" />
                <option name="hostIp" value="127.0.0.1" />
                <option name="hostPort" value="5220" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="showCommandPreview" value="true" />
          <option name="sourceFilePath" value="HX.HRV.SCI.Web/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="hx.hrv.web" />
          <option name="contextFolderPath" value="D:\Project\HuiXin\HX.HRV" />
          <option name="sourceFilePath" value="HX.HRV.Web/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.HRV.Web/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="hx.hrv.web" />
          <option name="contextFolderPath" value="D:\Project\HuiXin\HX.HRV" />
          <option name="sourceFilePath" value="HX.HRV.Web/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Web.Service: BlazorApp.Server" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.Web: IIS Express" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Web: IIS Express" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Web: http" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Web: https" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Client: https" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.MAUI.Web: IIS Express" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.MAUI.Web: http" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.MAUI.Web: https" />
      <item itemvalue=".NET 启动设置配置文件.Masa.Maui.Windows: 配置文件 1" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Client: IIS Express" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Client: http" />
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment: Windows Machine" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.Shared.WASM" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.Shared.WASM: IIS Express" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.SCI.Web" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.Shared" />
      <item itemvalue=".NET 启动设置配置文件.UFU.IoT: IIS Express" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.Web: BlazorApp.Server" />
      <item itemvalue=".NET 启动设置配置文件.UFU.CoreFX.Shared" />
      <item itemvalue=".NET 启动设置配置文件.UFU.IoT" />
      <item itemvalue=".NET 启动设置配置文件.UFU.IoT.Shared" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.MAUI" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.Web" />
      <item itemvalue=".NET 启动设置配置文件.HX.HRV.MAUI: Windows Machine" />
      <item itemvalue=".NET 项目.HX.Experiment.Window" />
      <item itemvalue=".NET 项目.HX.Experiment.Web.Client" />
      <item itemvalue=".NET 项目.HX.Experiment.MAUI" />
      <item itemvalue=".NET 项目.HX.Experiment.MAUI.Web" />
      <item itemvalue=".NET 项目.HX.Experiment.MAUI.Web.Client" />
      <item itemvalue=".NET 项目.未命名" />
      <item itemvalue=".NET 项目.HX.HRV.Keygen" />
      <item itemvalue=".NET 项目.HX.HRV.MDns" />
      <item itemvalue=".NET 项目.HX.Experiment.Client" />
      <item itemvalue="Docker.HX.Experiment.Web.Service/Dockerfile" />
      <item itemvalue="Docker.HX.HRV.Web/Dockerfile" />
      <item itemvalue="Docker.HX.HRV.SCI.Web/Dockerfile" />
      <item itemvalue="iOS.HX.Experiment.MAUI" />
      <item itemvalue="iOS.Masa.Maui.Windows" />
      <item itemvalue="iOS.HX.Experiment" />
      <item itemvalue="macOS.HX.Experiment.MAUI" />
      <item itemvalue="macOS.Masa.Maui.Windows" />
      <item itemvalue="macOS.HX.Experiment" />
      <item itemvalue="UWP.HX.Experiment.MAUI" />
      <item itemvalue="UWP.Masa.Maui.Windows" />
      <item itemvalue="UWP.HX.Experiment" />
      <item itemvalue="发布到 IIS.Masa.Maui.Windows: MSIX-win10-x64" />
      <item itemvalue="发布到 IIS.Masa.Maui.Windows: MSIX-win10-x641" />
      <item itemvalue="发布到 IIS.HX.Encrypt: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.Shared: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.SCI.Shared: FolderProfile1" />
      <item itemvalue="发布到 IIS.HX.HRV.SCI.Shared: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.Keygen: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.SCI.Web: HX.HRV.SCI" />
      <item itemvalue="发布到 IIS.HX.HRV.SCI.Web: FolderProfile1" />
      <item itemvalue="发布到 IIS.HX.HRV.Web: HX.HRV.Web" />
      <item itemvalue="发布到 IIS.HX.HRV.MDns: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.Base.Web: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.SCI.Web: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.Web.Base: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.HRV.Web: FolderProfile" />
      <item itemvalue="发布到 IIS.UFU.IoT: FolderProfile" />
      <item itemvalue="发布到文件夹.Publish HX.HRV.MAUI to folder (1)" />
      <item itemvalue="发布到文件夹.Publish HX.HRV.Keygen" />
      <item itemvalue="发布到文件夹.HX.HRV.Mdns" />
      <item itemvalue="发布到文件夹.Publish HX.HRV.MAUI to folder" />
      <item itemvalue="发布到文件夹.Publish HRV Client" />
      <item itemvalue="发布到文件夹.Publish HX.HRV.SCI.Web" />
      <item itemvalue="发布到文件夹.Publish HX.HRV.Web" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue=".NET 项目.HX.Experiment.Client" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.ALGGateway" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Base" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin\HX.Base\HX.Base.Shared" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Base\HX.Base.Shared" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Database" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Encrypt" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.FileExport" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.HRV" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.PythonService" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Screenshot" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.WebsocketTest" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared" />
          <option name="myCopyRoot" value="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.ALGGateway" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Base" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Database" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin\HX.Encrypt" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Encrypt" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.FileExport" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin\HX.HRV" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.HRV" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.PythonService" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Screenshot" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.WebsocketTest" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared" />
          <option name="myCopyRoot" value="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="649c87f6-1614-49b6-b525-4a867a068d1a" name="更改" comment="" />
      <created>1720407929791</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1720407929791</updated>
      <workItem from="1720407930683" duration="46000" />
      <workItem from="1720409941433" duration="16190000" />
      <workItem from="1720491694234" duration="17170000" />
      <workItem from="1720515071823" duration="8690000" />
      <workItem from="1720577006139" duration="5706000" />
      <workItem from="1720582817904" duration="13041000" />
      <workItem from="1720661742524" duration="18088000" />
      <workItem from="1720687995309" duration="3923000" />
      <workItem from="1720691971281" duration="52654000" />
      <workItem from="1721112907954" duration="1807000" />
      <workItem from="1721115243293" duration="53214000" />
      <workItem from="1721354601296" duration="5610000" />
      <workItem from="1721379378970" duration="162887000" />
      <workItem from="1722822369558" duration="1311000" />
      <workItem from="1723008743804" duration="1628000" />
      <workItem from="1723013181159" duration="27602000" />
      <workItem from="1723108786367" duration="42295000" />
      <workItem from="1723445828384" duration="24602000" />
      <workItem from="1723599263982" duration="17340000" />
      <workItem from="1723686001629" duration="683000" />
      <workItem from="1723686700340" duration="64000" />
      <workItem from="1723691837427" duration="8671000" />
      <workItem from="1723705212065" duration="8703000" />
      <workItem from="1723715095183" duration="7444000" />
      <workItem from="1723797039553" duration="4505000" />
      <workItem from="1723945290065" duration="63654000" />
      <workItem from="1724135652962" duration="19571000" />
      <workItem from="1724206998455" duration="202000" />
      <workItem from="1724207766759" duration="39000" />
      <workItem from="1724207815499" duration="867000" />
      <workItem from="1724208708860" duration="1779000" />
      <workItem from="1724210507181" duration="353000" />
      <workItem from="1724210871621" duration="31572000" />
      <workItem from="1724297350080" duration="33689000" />
      <workItem from="1724397430694" duration="63097000" />
      <workItem from="1724739510882" duration="66000" />
      <workItem from="1724739595095" duration="652000" />
      <workItem from="1724740272991" duration="6034000" />
      <workItem from="1724746403542" duration="61206000" />
      <workItem from="1724983333362" duration="10670000" />
      <workItem from="1725242675242" duration="381000" />
      <workItem from="1725243068421" duration="36790000" />
      <workItem from="1725344604270" duration="14949000" />
      <workItem from="1726637611085" duration="28718000" />
      <workItem from="1726735331860" duration="5536000" />
      <workItem from="1726797567677" duration="11004000" />
      <workItem from="1727059775227" duration="9743000" />
      <workItem from="1727072056741" duration="32580000" />
      <workItem from="1727172009018" duration="2540000" />
      <workItem from="1727343894291" duration="31201000" />
      <workItem from="1727602413148" duration="267000" />
      <workItem from="1727602796276" duration="146000" />
      <workItem from="1727683105921" duration="83000" />
      <workItem from="1727688170681" duration="490000" />
      <workItem from="1727689922925" duration="2000" />
      <workItem from="1728354130442" duration="35442000" />
      <workItem from="1728447122861" duration="563000" />
      <workItem from="1728447697508" duration="6544000" />
      <workItem from="1728459307732" duration="346000" />
      <workItem from="1728460094894" duration="1105000" />
      <workItem from="1728462024288" duration="34984000" />
      <workItem from="1728627378305" duration="498000" />
      <workItem from="1728627972872" duration="123000" />
      <workItem from="1728629952221" duration="7497000" />
      <workItem from="1728639287335" duration="1480000" />
      <workItem from="1728700595992" duration="147000" />
      <workItem from="1728700750817" duration="140170000" />
      <workItem from="1729159735977" duration="35928000" />
      <workItem from="1729736783704" duration="17814000" />
      <workItem from="1730093887855" duration="33489000" />
      <workItem from="1730372604311" duration="3790000" />
      <workItem from="1730377641806" duration="91000" />
      <workItem from="1730775337891" duration="2351000" />
      <workItem from="1730945673019" duration="21371000" />
      <workItem from="1730974034569" duration="2550000" />
      <workItem from="1731045420563" duration="17203000" />
      <workItem from="1731289227280" duration="17559000" />
      <workItem from="1731390904102" duration="28500000" />
      <workItem from="1731480599075" duration="232000" />
      <workItem from="1731482549106" duration="5303000" />
      <workItem from="1731487963940" duration="7434000" />
      <workItem from="1731549288340" duration="29030000" />
      <workItem from="1731579211826" duration="18936000" />
      <workItem from="1731652087452" duration="2719000" />
      <workItem from="1731655124247" duration="1729000" />
      <workItem from="1731894629204" duration="222000" />
      <workItem from="1731907600531" duration="9397000" />
      <workItem from="1732082764171" duration="363000" />
      <workItem from="1732083152954" duration="6322000" />
      <workItem from="1732089971220" duration="5925000" />
      <workItem from="1732096556375" duration="15722000" />
      <workItem from="1732169714468" duration="81807000" />
      <workItem from="1732587702891" duration="95006000" />
      <workItem from="1732879149025" duration="1490000" />
      <workItem from="1732891035067" duration="5318000" />
      <workItem from="1733117641640" duration="15995000" />
      <workItem from="1733215766005" duration="3662000" />
      <workItem from="1733282759710" duration="4165000" />
      <workItem from="1733374821074" duration="60931000" />
      <workItem from="1733727886029" duration="39000" />
      <workItem from="1733727935482" duration="40209000" />
      <workItem from="1733967192443" duration="30751000" />
      <workItem from="1734068160927" duration="821000" />
      <workItem from="1734069004685" duration="5408000" />
      <workItem from="1734078091261" duration="3227000" />
      <workItem from="1734310158843" duration="1904000" />
      <workItem from="1734312376851" duration="3502000" />
      <workItem from="1734324978189" duration="2687000" />
      <workItem from="1734327682816" duration="3716000" />
      <workItem from="1734332955590" duration="41706000" />
      <workItem from="1734503746499" duration="5537000" />
      <workItem from="1734510589479" duration="13789000" />
      <workItem from="1734592340763" duration="41000" />
      <workItem from="1734592410817" duration="7191000" />
      <workItem from="1734921855389" duration="11877000" />
      <workItem from="1735018048968" duration="31145000" />
      <workItem from="1735278347453" duration="40532000" />
      <workItem from="1735608740018" duration="3777000" />
      <workItem from="1735625633058" duration="347000" />
      <workItem from="1735625997943" duration="17020000" />
      <workItem from="1735797785796" duration="18353000" />
      <workItem from="1735869993716" duration="22269000" />
      <workItem from="1735992984589" duration="10008000" />
      <workItem from="1736003928948" duration="9032000" />
      <workItem from="1736313754459" duration="37000" />
      <workItem from="1736313802700" duration="20517000" />
      <workItem from="1736334556713" duration="13549000" />
      <workItem from="1736493523109" duration="16644000" />
      <workItem from="1736730754371" duration="11352000" />
      <workItem from="1736908348562" duration="15609000" />
      <workItem from="1737014736553" duration="3541000" />
      <workItem from="1737078512799" duration="1227000" />
      <workItem from="1737094175779" duration="272000" />
      <workItem from="1737201284260" duration="2598000" />
      <workItem from="1737352803515" duration="1587000" />
      <workItem from="1737354902794" duration="287000" />
      <workItem from="1737355269442" duration="5968000" />
      <workItem from="1737420951745" duration="614000" />
      <workItem from="1737421584060" duration="26647000" />
      <workItem from="1737531268338" duration="572000" />
      <workItem from="1737531881261" duration="3088000" />
      <workItem from="1737534991348" duration="22122000" />
      <workItem from="1737610906833" duration="1100000" />
      <workItem from="1737626252205" duration="257000" />
      <workItem from="1737685179771" duration="3997000" />
      <workItem from="1737689391453" duration="5344000" />
      <workItem from="1737704032556" duration="1678000" />
      <workItem from="1737711747933" duration="81000" />
      <workItem from="1737713510912" duration="902000" />
      <workItem from="1737714705291" duration="2173000" />
      <workItem from="1738892182252" duration="1449000" />
      <workItem from="1738906145149" duration="6802000" />
      <workItem from="1738914876689" duration="1331000" />
      <workItem from="1738922531670" duration="1211000" />
      <workItem from="1739165252372" duration="602000" />
      <workItem from="1739166052914" duration="30324000" />
      <workItem from="1739269182388" duration="9637000" />
      <workItem from="1739281309415" duration="19434000" />
      <workItem from="1739410794509" duration="379000" />
      <workItem from="1739411753167" duration="4288000" />
      <workItem from="1739514541466" duration="789000" />
      <workItem from="1739516067693" duration="1330000" />
      <workItem from="1739518290605" duration="160000" />
      <workItem from="1739518462110" duration="29085000" />
      <workItem from="1739758896146" duration="6931000" />
      <workItem from="1739786556726" duration="118000" />
      <workItem from="1739846487859" duration="1254000" />
      <workItem from="1739862523054" duration="33924000" />
      <workItem from="1740057178863" duration="4342000" />
      <workItem from="1740102924450" duration="2894000" />
      <workItem from="1740117516973" duration="1973000" />
      <workItem from="1740120660957" duration="276000" />
      <workItem from="1740121114601" duration="12131000" />
      <workItem from="1740382125554" duration="16752000" />
      <workItem from="1740534995761" duration="3094000" />
      <workItem from="1740559139100" duration="16367000" />
      <workItem from="1740640827046" duration="10021000" />
      <workItem from="1740652785214" duration="12111000" />
      <workItem from="1740667938774" duration="19693000" />
      <workItem from="1740721520860" duration="23878000" />
      <workItem from="1740979201697" duration="2766000" />
      <workItem from="1740982448338" duration="19146000" />
      <workItem from="1741058984494" duration="15479000" />
      <workItem from="1741078616145" duration="3460000" />
      <workItem from="1741086636771" duration="9256000" />
      <workItem from="1741153484447" duration="173000" />
      <workItem from="1741154476717" duration="49791000" />
      <workItem from="1741317348135" duration="3263000" />
      <workItem from="1741327947978" duration="14197000" />
      <workItem from="1741588400316" duration="14059000" />
      <workItem from="1741670706142" duration="1221000" />
      <workItem from="1741761868182" duration="3039000" />
      <workItem from="1741767299057" duration="84000" />
      <workItem from="1741773382606" duration="389000" />
      <workItem from="1741774154610" duration="1549000" />
      <workItem from="1741779753761" duration="124000" />
      <workItem from="1741779892490" duration="247000" />
      <workItem from="1741780188808" duration="700000" />
      <workItem from="1741780943917" duration="1838000" />
      <workItem from="1741857813091" duration="2867000" />
      <workItem from="1741863298405" duration="372000" />
      <workItem from="1741934721038" duration="869000" />
      <workItem from="1741936536980" duration="3367000" />
      <workItem from="1742196152028" duration="4236000" />
      <workItem from="1742202202439" duration="1253000" />
      <workItem from="1742204616348" duration="1650000" />
      <workItem from="1742271479831" duration="1970000" />
      <workItem from="1742362133303" duration="1839000" />
      <workItem from="1742366166848" duration="5816000" />
      <workItem from="1742378196245" duration="30307000" />
      <workItem from="1742521987427" duration="4420000" />
      <workItem from="1742538063552" duration="2211000" />
      <workItem from="1742543982081" duration="7121000" />
      <workItem from="1742783871891" duration="1190000" />
      <workItem from="1742785375588" duration="9927000" />
      <workItem from="1742807313699" duration="43000" />
      <workItem from="1742807629415" duration="4110000" />
      <workItem from="1742812539197" duration="1652000" />
      <workItem from="1742880040615" duration="38023000" />
      <workItem from="1743069586862" duration="383000" />
      <workItem from="1743070024498" duration="281000" />
      <workItem from="1743141491001" duration="8246000" />
      <workItem from="1743406158535" duration="23568000" />
      <workItem from="1743590046395" duration="42532000" />
      <workItem from="1744033931753" duration="2245000" />
      <workItem from="1744075171816" duration="9582000" />
      <workItem from="1744093892678" duration="6257000" />
      <workItem from="1744102811901" duration="7368000" />
      <workItem from="1744118028584" duration="7203000" />
      <workItem from="1744155266796" duration="10350000" />
      <workItem from="1744169301100" duration="4261000" />
      <workItem from="1744181178178" duration="448000" />
      <workItem from="1744186224693" duration="1358000" />
      <workItem from="1744197498162" duration="81000" />
      <workItem from="1744197597735" duration="11288000" />
      <workItem from="1744266259178" duration="57834000" />
      <workItem from="1744610739838" duration="1055000" />
      <workItem from="1744611833791" duration="14000" />
      <workItem from="1744614245273" duration="21466000" />
      <workItem from="1744694472900" duration="1504000" />
      <workItem from="1744701906426" duration="24162000" />
      <workItem from="1744792964743" duration="1690000" />
      <workItem from="1744794682239" duration="483000" />
      <workItem from="1744795462721" duration="247000" />
      <workItem from="1744801798962" duration="1269000" />
      <workItem from="1744880565924" duration="155000" />
      <workItem from="1744880784323" duration="74000" />
      <workItem from="1744880896088" duration="100000" />
      <workItem from="1744881009555" duration="197000" />
      <workItem from="1744881224247" duration="408000" />
      <workItem from="1744885030106" duration="148000" />
      <workItem from="1744885191750" duration="4844000" />
      <workItem from="1744944807288" duration="3756000" />
      <workItem from="1744956508368" duration="8320000" />
      <workItem from="1744974984376" duration="940000" />
      <workItem from="1745214412845" duration="7741000" />
      <workItem from="1745291486924" duration="166000" />
      <workItem from="1745291671551" duration="4177000" />
      <workItem from="1745304617823" duration="2317000" />
      <workItem from="1745320117475" duration="8662000" />
      <workItem from="1745395093098" duration="17208000" />
      <workItem from="1745463317426" duration="5990000" />
      <workItem from="1745474786590" duration="28350000" />
      <workItem from="1745571812459" duration="1320000" />
      <workItem from="1745573534013" duration="697000" />
      <workItem from="1745576217815" duration="49226000" />
      <workItem from="1745992929087" duration="673000" />
      <workItem from="1746501956105" duration="62350000" />
      <workItem from="1746775172106" duration="11474000" />
      <workItem from="1747016546652" duration="3006000" />
      <workItem from="1747035473729" duration="6799000" />
      <workItem from="1747103040510" duration="4009000" />
      <workItem from="1747114381028" duration="15363000" />
      <workItem from="1747274412230" duration="10030000" />
      <workItem from="1747287854330" duration="11705000" />
      <workItem from="1747301741802" duration="5328000" />
      <workItem from="1747307289264" duration="13650000" />
      <workItem from="1747384613121" duration="2099000" />
      <workItem from="1747388891275" duration="1830000" />
      <workItem from="1747391249332" duration="4499000" />
      <workItem from="1747398984156" duration="8178000" />
      <workItem from="1747649194576" duration="1660000" />
      <workItem from="1747704798062" duration="357000" />
      <workItem from="1747707221086" duration="24471000" />
      <workItem from="1747790051907" duration="242000" />
      <workItem from="1747790367235" duration="6753000" />
      <workItem from="1747818942819" duration="10817000" />
      <workItem from="1747878772138" duration="8433000" />
      <workItem from="1747894309557" duration="278000" />
      <workItem from="1747902169367" duration="28507000" />
      <workItem from="1747982482070" duration="22576000" />
      <workItem from="1748248266380" duration="64344000" />
      <workItem from="1748489273499" duration="16766000" />
      <workItem from="1748584839585" duration="5477000" />
      <workItem from="1748596821334" duration="41727000" />
      <workItem from="1749029863277" duration="25816000" />
      <workItem from="1749124018856" duration="2506000" />
      <workItem from="1749175609173" duration="26938000" />
      <workItem from="1749518830198" duration="12446000" />
      <workItem from="1749637130591" duration="49000" />
      <workItem from="1750038875406" duration="55218000" />
      <workItem from="1750142515384" duration="8021000" />
      <workItem from="1750151024575" duration="15006000" />
      <workItem from="1750229679527" duration="1445000" />
      <workItem from="1750232195769" duration="41196000" />
      <workItem from="1750337735212" duration="610000" />
      <workItem from="1750348061493" duration="1387000" />
      <workItem from="1750350361939" duration="2126000" />
      <workItem from="1750383181409" duration="1942000" />
      <workItem from="1750386397165" duration="299000" />
      <workItem from="1750387499699" duration="10372000" />
      <workItem from="1750398837695" duration="1250000" />
      <workItem from="1750400679292" duration="6000" />
      <workItem from="1750400810653" duration="760000" />
      <workItem from="1750401797586" duration="19126000" />
      <workItem from="1750585042897" duration="14142000" />
      <workItem from="1750661940761" duration="117000" />
      <workItem from="1750662113646" duration="307000" />
      <workItem from="1750662436996" duration="64000" />
      <workItem from="1750662667128" duration="344000" />
      <workItem from="1750669039055" duration="533000" />
      <workItem from="1750682890179" duration="3441000" />
      <workItem from="1750729526195" duration="8182000" />
      <workItem from="1750740984650" duration="6427000" />
      <workItem from="1750752706134" duration="2040000" />
      <workItem from="1750755829333" duration="34000" />
      <workItem from="1750758248985" duration="726000" />
      <workItem from="1750763283686" duration="4996000" />
      <workItem from="1750829043468" duration="1061000" />
      <workItem from="1750833863222" duration="21458000" />
      <workItem from="1750857826856" duration="14374000" />
      <workItem from="1750917207318" duration="11609000" />
      <workItem from="1750929977039" duration="15776000" />
      <workItem from="1750992370351" duration="18156000" />
      <workItem from="1751026376214" duration="10671000" />
      <workItem from="1751178383495" duration="7017000" />
      <workItem from="1751206639602" duration="59665000" />
      <workItem from="1751348813270" duration="29564000" />
      <workItem from="1751431937114" duration="73069000" />
      <workItem from="1751615355686" duration="1416000" />
      <workItem from="1751623825644" duration="34778000" />
      <workItem from="1751869135856" duration="7049000" />
      <workItem from="1751877311552" duration="261000" />
      <workItem from="1751877579195" duration="43097000" />
      <workItem from="1752142852723" duration="1118000" />
      <workItem from="1752200978235" duration="1937000" />
      <workItem from="1752208142237" duration="8778000" />
      <workItem from="1752457285283" duration="16428000" />
      <workItem from="1752553995899" duration="207000" />
      <workItem from="1752554210076" duration="1540000" />
      <workItem from="1752562159179" duration="7944000" />
      <workItem from="1752578481642" duration="10434000" />
      <workItem from="1752646535745" duration="7732000" />
      <workItem from="1752720985746" duration="13453000" />
      <workItem from="1752815938165" duration="1789000" />
      <workItem from="1752818094839" duration="1173000" />
      <workItem from="1752820347181" duration="14158000" />
      <workItem from="1752835464041" duration="6161000" />
      <workItem from="1753068383576" duration="656000" />
      <workItem from="1753091343433" duration="14201000" />
      <workItem from="1753171968402" duration="2089000" />
      <workItem from="1753176058582" duration="321000" />
      <workItem from="1753177277348" duration="809000" />
      <workItem from="1753178910852" duration="1661000" />
      <workItem from="1753235185749" duration="19708000" />
      <workItem from="1753406972994" duration="8598000" />
      <workItem from="1753423682034" duration="1108000" />
      <workItem from="1753425011017" duration="311000" />
      <workItem from="1753425411889" duration="464000" />
      <workItem from="1753426249075" duration="230000" />
      <workItem from="1753426566988" duration="4996000" />
      <workItem from="1753432492131" duration="11703000" />
      <workItem from="1753667546356" duration="6421000" />
      <workItem from="1753674526166" duration="10225000" />
      <workItem from="1753688956025" duration="11809000" />
      <workItem from="1753756836961" duration="692000" />
      <workItem from="1753760107696" duration="3118000" />
      <workItem from="1753781518021" duration="6809000" />
      <workItem from="1753857535041" duration="794000" />
      <workItem from="1753942214228" duration="350000" />
      <workItem from="1753943115398" duration="3527000" />
      <workItem from="1754274943282" duration="40000" />
    </task>
    <task id="LOCAL-00001" summary="GetAllReferencedAssemblies">
      <option name="closed" value="true" />
      <created>1729671941027</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1729671941027</updated>
    </task>
    <task id="LOCAL-00002" summary="提交解决方案">
      <option name="closed" value="true" />
      <created>1737355944721</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1737355944721</updated>
    </task>
    <task id="LOCAL-00003" summary="添加Module">
      <option name="closed" value="true" />
      <created>1737356297208</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1737356297208</updated>
    </task>
    <task id="LOCAL-00004" summary="添加客户端和MDns">
      <option name="closed" value="true" />
      <created>1743496083278</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1743496083278</updated>
    </task>
    <task id="LOCAL-00005" summary="add logo">
      <option name="closed" value="true" />
      <created>1743496277126</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1743496277126</updated>
    </task>
    <task id="LOCAL-00006" summary="add">
      <option name="closed" value="true" />
      <created>1744161119789</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1744161119789</updated>
    </task>
    <task id="LOCAL-00007" summary="修改设备状态服务&#10;修改数据分析">
      <option name="closed" value="true" />
      <created>1744595681473</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1744595681473</updated>
    </task>
    <task id="LOCAL-00008" summary="连接时  发送消息">
      <option name="closed" value="true" />
      <created>1744965350365</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744965350365</updated>
    </task>
    <task id="LOCAL-00009" summary="添加接收客户端消息订阅">
      <option name="closed" value="true" />
      <created>1745214646201</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1745214646201</updated>
    </task>
    <task id="LOCAL-00010" summary="添加接收客户端消息订阅">
      <option name="closed" value="true" />
      <created>1745214668559</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1745214668559</updated>
    </task>
    <task id="LOCAL-00011" summary="添加接收客户端消息订阅">
      <option name="closed" value="true" />
      <created>1745289456614</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1745289456614</updated>
    </task>
    <task id="LOCAL-00012" summary="添加接收客户端消息订阅">
      <option name="closed" value="true" />
      <created>1745289465707</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1745289465707</updated>
    </task>
    <task id="LOCAL-00013" summary="修复bug">
      <option name="closed" value="true" />
      <created>1745572095053</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1745572095053</updated>
    </task>
    <task id="LOCAL-00014" summary="修复bug">
      <option name="closed" value="true" />
      <created>1745806382631</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1745806382631</updated>
    </task>
    <task id="LOCAL-00015" summary="修复bug">
      <option name="closed" value="true" />
      <created>1745806413949</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1745806413949</updated>
    </task>
    <task id="LOCAL-00016" summary="添加注册机认证">
      <option name="closed" value="true" />
      <created>1747386181992</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747386181992</updated>
    </task>
    <task id="LOCAL-00017" summary="添加注册机认证">
      <option name="closed" value="true" />
      <created>1749026640578</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1749026640578</updated>
    </task>
    <task id="LOCAL-00018" summary="添加ViewModel">
      <option name="closed" value="true" />
      <created>1750078600759</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750078600759</updated>
    </task>
    <task id="LOCAL-00019" summary="修改显示">
      <option name="closed" value="true" />
      <created>1750078628476</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1750078628476</updated>
    </task>
    <task id="LOCAL-00020" summary="基本稳定">
      <option name="closed" value="true" />
      <created>1751955978524</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1751955978524</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="UnrealLogPanelSettings">
    <option name="clearOnStart" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="GetAllReferencedAssemblies" />
    <MESSAGE value="提交解决方案" />
    <MESSAGE value="添加Module" />
    <MESSAGE value="添加客户端和MDns" />
    <MESSAGE value="add logo" />
    <MESSAGE value="add" />
    <MESSAGE value="修改设备状态服务&#10;修改数据分析" />
    <MESSAGE value="连接时  发送消息" />
    <MESSAGE value="添加接收客户端消息订阅" />
    <MESSAGE value="修复bug" />
    <MESSAGE value="添加注册机认证" />
    <MESSAGE value="添加ViewModel" />
    <MESSAGE value="修改显示" />
    <MESSAGE value="基本稳定" />
    <option name="LAST_COMMIT_MESSAGE" value="基本稳定" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Web/Services/DeviceStatusDataService.cs</url>
          <line>172</line>
          <properties documentPath="D:\Project\HuiXin\HX.HRV\HX.HRV.Web\Services\DeviceStatusDataService.cs" containingFunctionPresentation="方法 'AddDeviceStatusData'">
            <startOffsets>
              <option value="5380" />
            </startOffsets>
            <endOffsets>
              <option value="5432" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="1448" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/HxSystemConfig.razor</url>
          <line>261</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1475" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/HxSystemConfig.razor</url>
          <line>244</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1476" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/RealTimeMonitoring/RealTimeMonitoring.razor</url>
          <line>21</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1517" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Web/Units/FileDataHelper.cs</url>
          <line>549</line>
          <properties documentPath="D:\Project\HuiXin\HX.HRV\HX.HRV.Web\Units\FileDataHelper.cs" containingFunctionPresentation="方法 'ProcessFilesAsync'">
            <startOffsets>
              <option value="19435" />
            </startOffsets>
            <endOffsets>
              <option value="19487" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="1606" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/0c/5d16fc9d/ModuleLoader.cs</url>
          <line>141</line>
          <properties documentPath="C:\Users\<USER>\AppData\Roaming\JetBrains\Rider2025.1\resharper-host\DecompilerCache\decompiler\363a9cb0787b4b5f8334385bba011c905f0c00\0c\5d16fc9d\ModuleLoader.cs" containingFunctionPresentation="方法 'LoadDLL'">
            <startOffsets>
              <option value="5713" />
            </startOffsets>
            <endOffsets>
              <option value="5781" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="1687" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Units/HrvVariable.cs</url>
          <line>20</line>
          <properties documentPath="D:\Project\HuiXin\HX.HRV\HX.HRV.Shared\Units\HrvVariable.cs" containingFunctionPresentation="构造函数 'HrvVariable'">
            <startOffsets>
              <option value="518" />
            </startOffsets>
            <endOffsets>
              <option value="569" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="1699" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Web/Areas/HRV_HX/Controllers/PatientRecordController.cs</url>
          <line>86</line>
          <properties documentPath="D:\Project\HuiXin\HX.HRV\HX.HRV.Web\Areas\HRV_HX\Controllers\PatientRecordController.cs" containingFunctionPresentation="方法 'GetPatientRecordModelDetailById'">
            <startOffsets>
              <option value="2801" />
            </startOffsets>
            <endOffsets>
              <option value="2913" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="1722" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/DeviceStatus/DeviceStatusCardContent.razor</url>
          <line>306</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1724" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/Dialog/PatientDialog.razor</url>
          <line>69</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1725" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/DeviceConfigEdit.razor</url>
          <line>239</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1727" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/SystemConfig/DeviceConfigEdit.razor</url>
          <line>320</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1731" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/DeviceStatus/DeviceStatusCardHead.razor</url>
          <line>42</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1732" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.HRV.Shared/Services/DeviceStateService.cs</url>
          <line>36</line>
          <properties documentPath="D:\Project\HuiXin\HX.HRV\HX.HRV.Shared\Services\DeviceStateService.cs" containingFunctionPresentation="方法 'InitAsync'">
            <startOffsets>
              <option value="1229" />
            </startOffsets>
            <endOffsets>
              <option value="1256" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="1737" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.Experiment/HX.Experiment.Shared/Pages/Eye/Calibration.razor</url>
          <line>41</line>
          <properties isMultiMethodBreakpoint="true">
            <option name="multiMethodBreakpoint" value="true" />
          </properties>
          <option name="timeStamp" value="1741" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="Type#System.Text.Json.Nodes.JsonValue`1+DebugView" memberName="Value" />
        <PinnedItemInfo parentTag="Frame#UFU.CoreFX.Module.ModuleLoader.LoadDLL" memberName="dll" />
        <PinnedItemInfo parentTag="Type#HX.HRV.Web.Pages.Client.RealTimeMonitoring.RealTimeMonitoring+DeviceData" memberName="Device" />
        <PinnedItemInfo parentTag="Type#UFU.CoreFX.Shared.Models.AuthRouteData" memberName="Layout" />
        <PinnedItemInfo parentTag="Type#UFU.CoreFX.Models.Menu" memberName="Children" />
        <PinnedItemInfo parentTag="Type#UFU.IoT.Models.DataModel" memberName="DataDic" />
        <PinnedItemInfo parentTag="Type#System.Net.Http.HttpResponseMessage" memberName="Content" />
        <PinnedItemInfo parentTag="Type#UFU.IoT.Services.ConnectDevice" memberName="ConnectTime" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="LaunchSettings">
        <watch expression="fileNameMessage.timestamp &gt;= startTimeSpan" />
        <watch expression="type.AsType()" />
        <watch expression="assembly" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>