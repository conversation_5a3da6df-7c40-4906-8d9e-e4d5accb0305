﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Areas.IoT.Controllers.V2
{
    /// <summary>
    /// 配置项管理
    /// </summary>
    [Area("IoT")]
    [ApiVersion("2.0")]
    [Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
    public class ConfigItemsController : Controller
    {
        private readonly DataRepository _context;

        /// <summary>
        /// 配置项管理
        /// </summary>
        /// <param name="context"></param>
        public ConfigItemsController(DataRepository context)
        {
            _context = context;
        }

        #region 接口

        /// <summary>
        /// 配置项管理
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <returns></returns>
        [HttpGet()]
        [Permission("配置项管理接口/列表", IsAPI = true)]
        public async Task<Result<List<DataItem>>> List(string typeId)
        {
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            var result = deviceType?.Data?.ConfigItemList ?? new List<DataItem>();
            return new Result<List<DataItem>>(result);
        }

        /// <summary>
        /// 配置项管理接口/存在
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">变量名</param>
        /// <returns></returns>
        [HttpGet()]
        [Permission("配置项管理接口/存在", IsAPI = true)]
        public async Task<Result<bool>> Exists(string typeId, string key)
        {
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            var res = deviceType?.Data?.ConfigItemList?.Any(m => m.Key == key);
            return new Result<bool>(res??false);
        }

        /// <summary>
        /// 配置项管理接口/详情
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">变量名</param>
        /// <returns></returns>
        [HttpGet()]
        [Permission("配置项管理接口/详情", IsAPI = true)]
        public async Task<Result<DataItem>> Get(string typeId, string key)
        {
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            var result = deviceType?.Data?.ConfigItemList?.FirstOrDefault(m => m.Key == key);
            return new Result<DataItem>(result);
        }

        /// <summary>
        /// 配置项管理接口/添加
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="dataItem">配置项</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("配置项管理接口/添加", IsAPI = true)]
        public async Task<Result<DataItem>> Add(string typeId, [FromBody] DataItem dataItem)
        {
            var result = new Result<DataItem>();
            if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(dataItem, result))
            {
                return result;
            }

            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }

            if (deviceType.Data?.ConfigItemList == null)
            {
                deviceType.Data.ConfigItemList = new List<DataItem>();
            }

            if (!deviceType.Data.ConfigItemList.Any(m => m.Key == dataItem.Key))
            {
                deviceType.Data.ConfigItemList.Add(dataItem);
                deviceType.Version = Guid.NewGuid().ToString();
                deviceType.UpdateTime = DateTime.Now;

                _context.Update(deviceType);
                await _context.SaveChangesAsync();
            }
            else
            {
               result.AddError( "变量名已存在");
               return result;
            }
            return new Result<DataItem>(dataItem);
        }

        /// <summary>
        /// 配置项管理接口/编辑
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">原变量名</param>
        /// <param name="dataItem">配置项</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("配置项管理接口/编辑", IsAPI = true)]
        public async Task< Result<DataItem>> Edit(string typeId, string key, [FromBody] DataItem dataItem)
        {
            var result = new Result<DataItem>();
            if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(dataItem, result))
            {
                return result;
            }

            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }

            var index = deviceType.Data?.ConfigItemList?.FindIndex(m => m.Key == key);
            if (index.HasValue && index != -1)
            {
                deviceType.Data.ConfigItemList[index.Value] = dataItem;
                deviceType.Version = Guid.NewGuid().ToString();
                deviceType.UpdateTime = DateTime.Now;
                _context.Update(deviceType);
                await _context.SaveChangesAsync();
                return new Result<DataItem>(dataItem);
            }

            result.AddError("变量名不存在"); 
            return result;
        }

        /// <summary>
        /// 配置项管理接口/删除
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">变量名</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("配置项管理接口/删除", IsAPI = true)]
        public async Task<Result<bool>> Delete(string typeId, string key)
        {
            var result = new Result<bool>(true);
            if (string.IsNullOrEmpty(typeId))
            {
                result.AddError("参数错误");
                return result;
               
            }

            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }

            var index = deviceType.Data?.ConfigItemList?.FindIndex(m => m.Key == key);
            if (index.HasValue && index != -1)
            {
                deviceType.Data.ConfigItemList.RemoveAt(index.Value);
                deviceType.Version = Guid.NewGuid().ToString();
                deviceType.UpdateTime = DateTime.Now;
                _context.Update(deviceType);
                await _context.SaveChangesAsync();
                return result;
            }
            else
            {
                    result.AddError("变量名不存在");
                    return result;
            }
        }

        /// <summary>
        /// 配置项管理接口/批量删除
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="keys">变量名</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("配置项管理接口/批量删除", IsAPI = true)]
        public async Task<Result<bool>> BatchDelete(string typeId, [FromBody] string[] keys)
        {
            var result = new Result<bool>(true);
            if (keys == null || !keys.Any())
            {
                result.AddError("keys 参数错误");
                return result;
            }
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }
            var count = deviceType.Data?.ConfigItemList?.RemoveAll(m => keys.Contains(m.Key));
            if (count > 0)
            {
                deviceType.Version = Guid.NewGuid().ToString();
                deviceType.UpdateTime = DateTime.Now;
                _context.Update(deviceType);
                await _context.SaveChangesAsync();
            }
            else
            {
                result.AddError("未找到要删除的变量");
            }
            return result;
        }

        #endregion
    }
}
