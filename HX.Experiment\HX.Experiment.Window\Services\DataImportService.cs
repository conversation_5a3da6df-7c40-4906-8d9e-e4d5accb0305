using System.Net.Http.Headers;
using System.Text.Json;
using HX.Experiment.Shared.Model;
using HX.Experiment.Shared.Services;
using UFU.IoT.Models;
using UFU.CoreFX.Shared.Services;

namespace HX.Experiment.Window.Services;

/// <summary>
/// 数据导入服务实现
/// </summary>
public class DataImportService : IDataImportService
{
    private readonly IDeviceService _deviceService;
    private readonly StateService _stateService;
    private readonly UDiskService _udiskService;
    private readonly HttpClient _httpClient;

    public event EventHandler<DataImportProgressEventArgs>? ProgressChanged;

    public DataImportService(
        IDeviceService deviceService, 
        StateService stateService,
        UDiskService udiskService,
        HttpClient httpClient)
    {
        _deviceService = deviceService;
        _stateService = stateService;
        _udiskService = udiskService;
        _httpClient = httpClient;
    }

    public async Task<bool> ImportDataAsync(string deviceSn, string recordId)
    {
        try
        {
            var diskInfo = _udiskService.DiskList.FirstOrDefault(m =>
                m.ShowName.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
            
            if (diskInfo == null)
            {
                OnProgressChanged(deviceSn, 0, "设备未找到", true, true, "设备未找到");
                return false;
            }

            diskInfo.IsParsing = true;
            OnProgressChanged(deviceSn, 0, "开始导入数据...", false, false);

            // 创建临时目录
            string dir = Path.GetDirectoryName(Environment.ProcessPath) ?? "";
            var targetPath = Path.Combine(dir, "bin", "data", diskInfo.ShowName, Guid.NewGuid().ToString("N"));
            
            if (!Directory.Exists(targetPath))
            {
                Directory.CreateDirectory(targetPath);
            }

            // 复制文件
            int copyIndex = 0;
            string[] copyFiles = Directory.GetFiles(Path.Combine(diskInfo.LogicalName, "data"));
            
            foreach (string file in copyFiles)
            {
                copyIndex++;
                string name = Path.GetFileName(file);
                string dest = Path.Combine(targetPath, name);
                await Task.Delay(10);
                File.Copy(file, dest);
                
                int progress = copyIndex * 50 / copyFiles.Length;
                OnProgressChanged(deviceSn, progress, $"复制文件 {copyIndex}/{copyFiles.Length}", false, false);
            }

            diskInfo.IsParsing = false;

            // 异步删除原文件
            _ = Task.Run(() => DeleteFiles(diskInfo.LogicalName));

            // 上传文件到服务器
            var files = Directory.GetFiles(targetPath, "*.bin");
            var index = 0;
            var filesCount = files.Length;
            var guid = Guid.NewGuid();
            var url = "/api/v2/hx_experiment/TestRecord/UploadFile";

            var sortedFiles = files.OrderBy(file =>
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                string[] parts = fileName.Split('-');
                var timestampString = parts.ElementAt(parts.Length - 1);
                return long.Parse(timestampString);
            }).ToArray();

            foreach (var fileName in sortedFiles)
            {
                try
                {
                    var formData = new MultipartFormDataContent();
                    index++;

                    var fileContent = new StreamContent(new FileStream(fileName, FileMode.Open, FileAccess.Read));
                    fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    
                    formData.Add(fileContent, "file", Path.GetFileName(fileName));
                    formData.Add(new StringContent(Path.GetFileName(fileName)), "name");
                    formData.Add(new StringContent(guid.ToString("N")), "guid");
                    formData.Add(new StringContent(recordId), "recordId");
                    formData.Add(new StringContent((index == filesCount).ToString()), "isEnd");
                    
                    var progress = (index * 50 / filesCount) + 50;
                    formData.Add(new StringContent(progress.ToString()), "progress");
                    formData.Add(new StringContent(diskInfo.ShowName), "deviceSn");

                    var response = await _httpClient.PostAsync(url, formData);
                    var responseContent = await response.Content.ReadAsStringAsync();
                    
                    OnProgressChanged(deviceSn, progress, $"上传文件 {index}/{filesCount}", false, false);
                    await Task.Delay(10);
                }
                catch (Exception ex)
                {
                    OnProgressChanged(deviceSn, 0, "上传失败", true, true, ex.Message);
                    return false;
                }
            }

            // 清理临时文件
            try
            {
                Directory.Delete(targetPath, true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理临时文件失败: {ex.Message}");
            }

            OnProgressChanged(deviceSn, 100, "数据导入完成", true, false);
            return true;
        }
        catch (Exception ex)
        {
            OnProgressChanged(deviceSn, 0, "导入失败", true, true, ex.Message);
            return false;
        }
    }

    public async Task<int> GetDeviceFileCountAsync(string deviceSn)
    {
        try
        {
            var diskInfo = _udiskService.DiskList.FirstOrDefault(m =>
                m.ShowName.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
            
            if (diskInfo == null) return 0;

            var dataPath = Path.Combine(diskInfo.LogicalName, "data");
            if (!Directory.Exists(dataPath)) return 0;

            return Directory.GetFiles(dataPath, "*.bin").Length;
        }
        catch
        {
            return 0;
        }
    }

    public async Task<bool> HasDataAsync(string deviceSn)
    {
        var fileCount = await GetDeviceFileCountAsync(deviceSn);
        return fileCount > 0;
    }

    public async Task<bool> ClearDeviceDataAsync(string deviceSn)
    {
        try
        {
            var diskInfo = _udiskService.DiskList.FirstOrDefault(m =>
                m.ShowName.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
            
            if (diskInfo == null) return false;

            await Task.Run(() => DeleteFiles(diskInfo.LogicalName));
            diskInfo.FileCount = 0;
            return true;
        }
        catch
        {
            return false;
        }
    }

    private void DeleteFiles(string logicalName)
    {
        try
        {
            var dataPath = Path.Combine(logicalName, "data");
            if (Directory.Exists(dataPath))
            {
                var files = Directory.GetFiles(dataPath);
                foreach (var file in files)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"删除文件失败: {file}, {ex.Message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除文件失败: {ex.Message}");
        }
    }

    private void OnProgressChanged(string deviceSn, int progress, string message, bool isCompleted, bool hasError, string? errorMessage = null)
    {
        ProgressChanged?.Invoke(this, new DataImportProgressEventArgs
        {
            DeviceSn = deviceSn,
            Progress = progress,
            Message = message,
            IsCompleted = isCompleted,
            HasError = hasError,
            ErrorMessage = errorMessage
        });
    }
}
