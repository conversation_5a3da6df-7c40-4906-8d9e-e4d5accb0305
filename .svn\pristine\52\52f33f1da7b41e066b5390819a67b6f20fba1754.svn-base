﻿using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.AlgDataModel;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Utilities;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services
{
    /// <summary>
    ///  算法Socket通信服务
    /// </summary>
    public static class AlgWebSocketClient
    {
        private static ClientWebSocket _clientWebSocket;
        private static string _proxyHost;
        private static int _proxyPort;

        static AlgWebSocketClient()
        {
            _proxyHost = Env.Configuration["AlgorithmProxy:Host"];
            _proxyPort = int.Parse(Env.Configuration["AlgorithmProxy:Port"]);
     
            LogTool.Logger.Information("算法网关地址:" + _proxyHost + ":" + _proxyPort);

			_ = Task.Run(async () =>
			{
				_clientWebSocket = new ClientWebSocket();
				await _clientWebSocket.ConnectAsync(
					new Uri($"ws://{_proxyHost}:{_proxyPort}/api/Algorithms?ClientType=2"),
					CancellationToken.None);
				await GetAlgClientWebSocketAsync();
				await Task.Delay(2000);
				while (true)
				{
					try
					{
						var clientWebSocket = await GetAlgClientWebSocketAsync();
						var buffer = new ArraySegment<byte>(new byte[1024 * 1024 * 10]);
						var result = await clientWebSocket.ReceiveAsync(buffer, CancellationToken.None);
						if (clientWebSocket.State == WebSocketState.Connecting)
						{
							await Task.Delay(300);
							continue;
						}
						if (result.MessageType == WebSocketMessageType.Close)
						{
							await clientWebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "算法网关断开连接",
								CancellationToken.None);
							await Task.Delay(300);
							continue;
						}
						if (result.Count > 0)
						{
							var data = ParseCommandPacket(buffer.Array);
							OnDataReceived?.Invoke(data);
							InvokeCallbacks(data);
						}
					}
					catch (Exception ex)
					{
						if (ex is WebSocketException { WebSocketErrorCode: WebSocketError.ConnectionClosedPrematurely })
						{
						}
						else
						{
							LogTool.Logger.Error("接收算法网关数据错误：" + ex);
							await Task.Delay(300);
						}
					}
				}
			});
		}

        public static Action<ParsedPacket> OnDataReceived { get; set; }

        /// <summary>
        ///
        /// </summary>
        public static async Task InitAsync()
        {
           
          
        }


        public static ParsedPacket ParseCommandPacket(byte[] packet)
        {
            if (packet.Length < 2)
                throw new ArgumentException("Invalid packet length");
            var commandContentLength = BitConverter.ToUInt16(packet[..2]);
            byte[] commandContent = new byte[commandContentLength];
            Array.Copy(packet, 2, commandContent, 0, commandContentLength);
            string jsonString = Encoding.UTF8.GetString(commandContent);
            byte[] data = new byte[packet.Length - 2 - commandContentLength];
            Array.Copy(packet, 2 + commandContentLength, data, 0, data.Length);
            return new ParsedPacket
            {
                JsonContent = jsonString,
                BinaryData = data
            };
        }

        /// <summary>
        /// 发送二进制数据到算法网关
        /// </summary>
        /// <param name="jsonBytes"></param>
        /// <param name="bytes"></param>
        private static async Task SendToAlgAsync(byte[] jsonBytes, byte[] bytes)
        {
            var dataPacket = CreateCommandPacket(jsonBytes, bytes);
            var buffer = new ArraySegment<byte>(dataPacket);
            var clientWebSocket = await GetAlgClientWebSocketAsync();
            await clientWebSocket.SendAsync(buffer, WebSocketMessageType.Binary, true,
                CancellationToken.None);
            LogTool.Logger.Information($"发送数据到算法网关成功,{_proxyHost}:{_proxyPort}");
        }


        /// <summary>
        /// 发送二进制数据到算法网关 并且注册默认的回调
        /// </summary>
        /// <param name="jsonBytes"></param>
        /// <param name="bytes"></param>
        public static async Task SendToAlgAsyncAndRegister(byte[] jsonBytes, byte[] bytes, string key,
            Action<ParsedPacket> callback = null)
        {
            await SendToAlgAsync(jsonBytes, bytes);
            if (callback != null)
            {
                RegisterCallback(key, callback);
            }
            else
            {
                RegisterCallback(key, OnReceiveCalculatorData);
            }

            LogTool.Logger.Information($"发送数据到算法网关成功,{_proxyHost}:{_proxyPort}");
        }

        public static async Task SendMsgToAlg(string recordId, Dictionary<string, List<uint>> ppgData, int ppgRate,
            long startTime, bool isEnd, string algorithm = "PPGCalculator", Action<ParsedPacket> callback = null)
        {
            var dataBytes = new Dictionary<string, byte[]>();
            var totalLength = 0;
            foreach (var kvp in ppgData)
            {
                var byteArray = new byte[kvp.Value.Count * sizeof(uint)];
                Buffer.BlockCopy(kvp.Value?.ToArray() ?? Array.Empty<uint>(), 0, byteArray, 0, byteArray.Length);
                dataBytes[kvp.Key] = byteArray;
                totalLength += byteArray.Length;
            }

            // Prepare JSON data
            var jsonData = new
            {
                MessageType = AlgSocketMessageType.Call,
                ClientId = recordId,
                Algorithm = algorithm,
                IsEnd = isEnd,
                PPGRate = ppgRate,
                StartTime = startTime,
                Data = dataBytes.ToDictionary(kvp => kvp.Key + "_Length", kvp => kvp.Value.Length)
            };
            // Combine all byte arrays
            var combinedBytes = new byte[totalLength];
            int offset = 0;
            foreach (var byteArray in dataBytes.Values)
            {
                Buffer.BlockCopy(byteArray, 0, combinedBytes, offset, byteArray.Length);
                offset += byteArray.Length;
            }
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            await SendToAlgAsyncAndRegister(jsonBytes, combinedBytes, recordId, callback: callback);
        }
        public static async Task SendTickMsgToAlg(
            string recordId, 
            long timeSpan,
            string algorithm = "PPGCalculator",
            string tickTag = "tick")
        {
            var jsonData = new
            {
                MessageType = AlgSocketMessageType.Call,
                ClientId = recordId,
                Algorithm = algorithm,
                AlgorithmAction = "tick",
                TickTime =timeSpan,
                TickTag = tickTag
            };
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            await SendToAlgAsync(jsonBytes, Array.Empty<byte>());
        }


		/// <summary>
		/// 创建二进制数据
		/// </summary>
		/// <param name="jsonBytes"></param>
		/// <param name="binaryData"></param>
		/// <returns></returns>
		public static byte[] CreateCommandPacket(byte[] jsonBytes, byte[] binaryData)
		{
			byte[] commandContent = new byte[2 + jsonBytes.Length + binaryData.Length];
			ushort binaryDataLength = (ushort)jsonBytes.Length;
			Array.Copy(BitConverter.GetBytes(binaryDataLength), 0, commandContent, 0, 2);
			Array.Copy(jsonBytes, 0, commandContent, 2, jsonBytes.Length);
			if (binaryData.Length > 0)
			{
				Array.Copy(binaryData, 0, commandContent, 2 + jsonBytes.Length, binaryData.Length);
			}
			return commandContent;
		}

		private static object lockObj = new object();

        /// <summary>
        /// 获取和算法网关的连接
        /// </summary>
        /// <returns></returns>
        private static async Task<ClientWebSocket> GetAlgClientWebSocketAsync()
        {
            if (_clientWebSocket is { State: WebSocketState.Open })
            {
                return _clientWebSocket;
            }
            try
            {
                _clientWebSocket = new ClientWebSocket();
                //获取当前的URL和Host
                var webSocketUrl = $"ws://{_proxyHost}:{_proxyPort}/api/Algorithms?ClientType=2";
                var serverUri = new Uri(webSocketUrl);
                await _clientWebSocket.ConnectAsync(serverUri, CancellationToken.None);
            }
            catch (Exception e)
            {
                LogTool.Logger.Error("获取算法网关连接失败：" + e);
            }
            return _clientWebSocket;
        }


        /// <summary>
        /// PPG的算法
        /// </summary>
        /// <param name="data"></param>
        private static void OnReceiveCalculatorData(ParsedPacket data)
        {
            var jsonData = JsonNode.Parse(data.JsonContent);

            var algorithm = jsonData?["Algorithm"]?.GetValue<string>();
            if (algorithm == "PPGCalculator")
            {
                LogTool.Logger.Information($"OnReceiveCalculatorData：收到PPG数据{data.JsonContent}");
                ParsePpgData(data);
            }
            else if (algorithm == "GSRSeparated")
            {
                ParseGsrData(data);
            }
        }

        /// <summary>
        /// 解析Gsr数据
        /// </summary>
        /// <param name="data"></param>
        private static void ParseGsrData(ParsedPacket data)
        {
            var jsonData = JsonNode.Parse(data.JsonContent);
            var clientId = jsonData?["ClientId"]?.ToString();
            if (string.IsNullOrEmpty(clientId))
            {
                LogTool.Logger.Information($"ParseGSRData：收到未知消息{data.JsonContent}");
                return;
            }

            var devices = DeviceStatusDataService.GetDeviceDataList();
            var device = devices.FirstOrDefault(m => m.RecordModel?.Id == clientId);
            var recordId = jsonData?["ClientId"]?.ToString();
            var scrLength = jsonData?["Data"]?["bytes_scr_list_len"]?.GetValue<int>() ?? 0;
            var sclLength = jsonData?["Data"]?["bytes_scl_list_len"]?.GetValue<int>() ?? 0;
            var scrData = MemoryMarshal.Cast<byte, float>(data.BinaryData.Take(scrLength).ToArray())
                .ToArray();
            var sclData = MemoryMarshal.Cast<byte, float>(data.BinaryData.Take(sclLength).ToArray())
                .ToArray();

            Task.Run(async () =>
            {
                var path = "";
                var record = device?.RecordModel;
                if (device?.RecordModel == null)
                {
                    await using var db = new DataRepository(UserInfo.System);
                    var patientRecordModel =
                        await db.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == recordId);
                    record = patientRecordModel.Data;
                    path = record.GetRecordDirectoryPath();
                }

                path = record.GetRecordDirectoryPath();
                FileDataHelper.SaveDataToCsv(
                    path,
                    record.CollectStartTime.ToUnix(),
                    "SCL&SCR",
                    new Dictionary<string, List<float>>
                    {
                        { "SCL", sclData.ToList() },
                        { "SCR", scrData.ToList() }
                    }
                );
            });
            if (device is not { DeviceStatus: EnumDeviceStatus.检测中 }) return;
            var deviceData = new
            {
                SN = device.Device.DeviceSN,
                SCL = sclData,
                SCR = scrData,
                From_ParseGSRData = 1
            };
            _ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(deviceData));
        }


        /// <summary>
        ///  解析算法传回的PPG数据
        /// </summary>
        /// <param name="data"></param>
        private static void ParsePpgData(ParsedPacket data)
        {
            var jsonData = JsonNode.Parse(data.JsonContent);
            var isDenoisedSignal = jsonData?["Data"]?.AsObject()?.ContainsKey("denoised_signal_len");
            var clientId = jsonData?["ClientId"]?.ToString();
            if (string.IsNullOrEmpty(clientId))
            {
                LogTool.Logger.Information($"OnReceivePigCalculator：收到未知消息{data.JsonContent}");
                return;
            }

            var devices = DeviceStatusDataService.GetDeviceDataList();
            var device = devices.FirstOrDefault(m => m.RecordModel?.Id == clientId);
            if ((isDenoisedSignal ?? false))
            {
                if (device?.DeviceStatus == EnumDeviceStatus.检测中)
                {
                    try
                    {
                        var bytes_denoised_signal_len =
                            jsonData?["Data"]?["denoised_signal_len"]?.GetValue<int>() ?? 0;
                        var ppgData = MemoryMarshal
                            .Cast<byte, int>(data.BinaryData.Take(bytes_denoised_signal_len).ToArray())
                            .ToArray();
                        DeviceStatusDataService.OnReceivePPGData(device, ppgData);
                        var toPageJson = new JsonObject();
                        toPageJson.Add("SN", device.Device.DeviceSN);
                        toPageJson.Add("PPG", JsonValue.Create(ppgData));
                        _ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        try
                        {
                            var heartJson = new JsonObject();
                            var bmp = jsonData?["Data"]?["heart_rate"]?.GetValue<int>() ?? 0;
                            DeviceStatusDataService.OnReceiveSPO2Data(bmp>0?(uint)bmp:0, device.Device.DeviceSN);
                            heartJson.Add("Bmp", bmp > 0 ? (uint)bmp : 0);
                            heartJson.Add("From", "ParsePpgData");
                            heartJson.Add("SN", device.Device.DeviceSN);
                            _ = HXAdminWebSocket.SendMsgToAdmin(heartJson.ToJsonString());
                        }
                        catch (Exception e)
                        {
                            LogTool.Logger.Information(" OnReceivePigCalculator_____heart_rate错误", e);
                        }
                    }
                    catch (Exception e)
                    {
                        LogTool.Logger.Information(e, $"OnReceivePigCalculator_____isDenoisedSignal错误");
                    }
                }
            }
            else
            {
                try
                {
                    
                    
                    
                    // var hr_len = jsonData?["Data"]?["hr_len"]?.GetValue<int>() ?? 0;
                    // var hr_data = MemoryMarshal.Cast<byte, int>(data.BinaryData.Take(hr_len).ToArray())
                    //     .ToArray();
                    // var nni_len = jsonData?["Data"]?["nni_len"]?.GetValue<int>() ?? 0;
                    // var nni_data = MemoryMarshal.Cast<byte, int>(data.BinaryData[hr_len..(hr_len + nni_len)].ToArray())
                    //     .ToArray();
                    //
                    // var frequency_len = jsonData?["Data"]?["frequency_len"]?.GetValue<int>() ?? 0;
                    // var frequency_data = MemoryMarshal
                    //     .Cast<byte, int>(data.BinaryData[(hr_len + nni_len)..(hr_len + nni_len + frequency_len)]
                    //         .ToArray())
                    //     .ToArray();
                    // var psd_len = jsonData?["Data"]?["psd_len"]?.GetValue<int>() ?? 0;
                    // var psd_data = MemoryMarshal
                    //     .Cast<byte, int>(data
                    //         .BinaryData[
                    //             (hr_len + nni_len + frequency_len)..(hr_len + nni_len + frequency_len + psd_len)]
                    //         .ToArray())
                    //     .ToArray();
                    
                    var offset = 0;
                    var lengths = jsonData?["Data"]?.AsObject().Where(m => m.Key.EndsWith("_len"));
                    var  hr_data =new List<uint>();
                    var  nni_data = new List<uint>();
                    var frequency_data = new List<uint>();
                    var  psd_data =new List<uint>();
                    var  hr_original_len =new List<uint>();
                    foreach (var (key, value) in lengths)
                    {
                        var length = value.GetValue<int>() ;

                        var skip = length * sizeof(uint);
                        var dataArray = MemoryMarshal.Cast<byte, uint>(data.BinaryData[offset..(offset +skip )].ToArray()).ToArray().ToList();
                        offset += skip;
                        switch (key)
                        {
                            case "hr_len":
                                hr_data = dataArray;
                                break;
                            case "nni_len":
                                nni_data = dataArray;
                                break;
                            case "frequency_len":
                                frequency_data = dataArray;
                                break;
                            case "psd_len":
                                psd_data = dataArray;
                                break;
                            case "hr_original_len":
                                hr_original_len = dataArray;
                                break;
                        }
                       
                    }
                    
                    
                    using var db = new DataRepository();
                    var recordData = db
                        .Query<PatientRecordModel>()
                        .FirstOrDefault(m => m.Id == clientId);
                    if (recordData?.Data == null)
                    {
                        LogTool.Logger.Error($"不存在检测记录{clientId}");
                        return;
                    }

                    if (recordData.Data.BuildReportStatus == BuildReportStatus.Completed)
                    {
                        LogTool.Logger.Error($"{clientId}检测记录已经生成报告");
                        return;
                    }

                    var record = recordData.Data;
                    record.Statistics = new ReportDataStatistics();
                    var dataIndex = jsonData["Data"]?["data_index"];
                   
                    var resultDictionary = new Dictionary<string, string>();
                    foreach (var (key, value) in dataIndex.AsObject())
                    {
                        string formattedValue;

                        if (value.GetValueKind() == JsonValueKind.Number)
                        {
                            formattedValue = key.Contains("hr")
                                ? value.GetValue<float>().ToString("F0")
                                : value.GetValue<float>().ToString("F2");
                        }
                        else
                        {
                            formattedValue = value.ToString();
                        }

                        resultDictionary.Add(key, formattedValue);
                    }
                    record.Statistics.StatisticsDictionary = resultDictionary;
                    record.Statistics.HRList = hr_data.Select( m => (int)m).ToList();
                    record.Statistics.PsdList = psd_data.Select( m => (int)m).ToList();
                    record.Statistics.NniList = nni_data.Select( m => (int)m).ToList();
                    record.Statistics.FrequencyList = frequency_data.Select( m => (int)m).ToList();
                    record.BuildReportStatus = BuildReportStatus.Completed;
                    recordData.Data = record;
                    recordData.UpdateTime = DateTime.Now;
                    db.Update(recordData);
                    db.SaveChanges();
                    if (device != null && clientId == device.RecordModel?.Id &&
                        device.DeviceStatus == EnumDeviceStatus.检测中)
                    {
                        var deviceData = new
                        {
                            SN = device.Device.DeviceSN,
                            Status = device.DeviceStatus,
                            IsAutoPrint = true,
                            recordId = record.Id,
                            From_ParsePPGData = 1
                        };
                        _ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(deviceData));
                        _ = DeviceStatusDataService.SetPatientRecordEnd(device);
                    }
                    //发送打印的消息
                    _ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(new
                    {
                        IsPrint = true,
                        SN= DeviceStatusDataService.GetDeviceData(recordData.Data.DeviceId)?.Device?.DeviceSN,
                        PrintId = record.Id
                    }));
                }
                catch (Exception e)
                {
                    LogTool.Logger.Error("解析算法回传数据失败" + e);
                }
            }
        }


        /// <summary>
        ///  回调
        /// </summary>
        private static readonly ConcurrentDictionary<string, Action<ParsedPacket>> Callbacks = new();

        /// <summary>
        ///  注册回调
        /// </summary>
        /// <param name="key"></param>
        /// <param name="callback"></param>
        public static void RegisterCallback(string key, Action<ParsedPacket> callback)
        {
            if (Callbacks.ContainsKey(key)) return;
            Callbacks.TryAdd(key, callback);
        }

        /// <summary>
        ///  取消回调
        /// </summary>
        /// <param name="key"></param>
        private static void UnregisterCallback(string key)
        {
            Callbacks.TryRemove(key, out _);
        }

        private static void InvokeCallbacks(ParsedPacket data)
        {
            var webSocketData = JsonTool.Deserialize<AlgWebSocketData>(data.JsonContent);
            var key = webSocketData.ClientId;
            if (key == null)
            {
                LogTool.Logger.Warning("AlgWebSocketClient,InvokeCallbacks,未知消息:" + data.JsonContent);
            }

            if (Callbacks.TryGetValue(key, out var callback))
            {
                callback(data);
                // UnregisterCallback(key);
            }
        }

        public static async Task SendEndMsgToAlg(
            string recordId,
            long StartTime,
            long time,
            bool IsEnd = false
        )
        {
            var jsonData = new
            {
                MessageType = 3,
                ClientId = recordId,
                Algorithm = "PPGCalculator",
                IsEnd = IsEnd,
                PPGRate = 200,
                StartTime = StartTime,
                Time = time,
                Data = new
                {
                    PPG_G_Length = 0,
                    PPG_R_Length = 0,
                    PPG_I_Length = 0
                }
            };
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            await AlgWebSocketClient.SendToAlgAsyncAndRegister(
                jsonBytes,
                Array.Empty<byte>(),
                recordId
            );
        }
    }
}