@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@inject DeviceStateService DeviceStateService

@implements IDisposable
<DeviceStatusCardHead IsShowDeviceSN="true"
    DeviceStatusData="DeviceStatusData"
    LinkType="1">
</DeviceStatusCardHead>
@if (DeviceStatusData?.DeviceStatus != EnumDeviceStatus.离线||DeviceStatusData?.IsUsbLinked==true)
{
    <DeviceStatusCardContent DeviceId=@DeviceId/>
}
else
{
    <div style="height: 80%;" class="d-flex align-center justify-center">
        <img src="/images/icon/<EMAIL>" style="width:2rem"/>
        <MLabel Style="font-size: 2rem">离线</MLabel>
    </div>
}

@code {

    protected override Task OnInitializedAsync() {
        DeviceStatusData.OnDeviceStatusChange+=OnChange;
        return base.OnInitializedAsync();
    }
    private void OnChange() {
        InvokeAsync(StateHasChanged);
    }
    public DeviceStatusViewModel DeviceStatusData => DeviceDataList.FirstOrDefault(x => x.Device.Id == DeviceId);
    private List<DeviceStatusViewModel> DeviceDataList => DeviceStateService.DeviceDataList;
    [Parameter] public string DeviceId { get; set; }
    public void Dispose() {
        DeviceStatusData.OnDeviceStatusChange-=OnChange;
    }

}