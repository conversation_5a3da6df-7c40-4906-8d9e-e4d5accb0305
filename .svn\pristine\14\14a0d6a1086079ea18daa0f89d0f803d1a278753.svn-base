﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="logo.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="WPF-UI" Version="4.0.2" />
  </ItemGroup>

  <ItemGroup>
    <None Update="hx_gen_pub_key.pem">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\HX.Encrypt\HX.Encrypt.csproj" />
  </ItemGroup>

</Project>
