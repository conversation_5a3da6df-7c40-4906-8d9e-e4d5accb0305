﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;
using HX.HRV.Shared.Models;
using UFU.CoreFX.Models;

namespace HX.HRV.SCI.Shared.Models;



/// <summary>
/// 
/// </summary>
[DataEntity("2501030100000004")]
public class DataAnalysisRecordModel
{
    public string Id { get; set; }
    public string RecordId { get; set; }
    public string Name { get; set; }
    public long  StartIndex { get; set; }
    public long EndIndex { get; set; }

    public DateTime StartTime { get; set; }
    
    public DateTime EndTime { get; set; }
    public ReportDataStatistics Statistics { get; set; }
    [NotMapped]
    public PatientModel Patient { get; set; }
    [NotMapped]
    public PatientRecordModel PatientRecord { get; set; }

    [JsonPropertyName("eda_time_domain")]
    public List<EdaTimeDomain> EdaTimeDomain { get; set; }
    
    [JsonPropertyName("eda_scr_results")]
    public List<EdaSCRResult> EdaSCRResults { get; set; }
    
    [JsonPropertyName("eda_event_results")]
    public List<EdaEventResult> EdaEventResults { get; set; }

	public EmotionDataModel Emotion { get; set; }


	public string GetPath(string id)
    {
        var binPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin","analysis");
        if (!Directory.Exists(binPath))
        {
            Directory.CreateDirectory(binPath);
        }
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;
        var day = DateTime.Now.Day;
        var dir = Path.Combine(binPath, $"{year}", $"{month}-{day}",id);
        return dir;
    }
}

public class EdaTimeDomain
{
    public string Name { get; set; }
    public double Mean { get; set; }
    public double Max { get; set; }
    public double Min { get; set; }
    public double SD { get; set; }
    public double Var { get; set; }
    [JsonPropertyName("Polar Distance")]
    public double Polar_Distance { get; set; }
}

public class EdaSCRResult
{
    public int SCR_Onsets { get; set; }
    public string SCR_OnsetsTime { get; set; }
    public int SCR_Peaks { get; set; }
    public string SCR_PeaksTime { get; set; }
    public double SCR_RiseTime { get; set; }
    public double SCR_Amplitude { get; set; }
    public double SCR_Recovery { get; set; }
    public double SCR_RecoveryTime { get; set; }
}

    
    
public class EdaEventResult
{
    public string event_id { get; set; }
    public string event_label { get; set; }
    public int event_Onset { get; set; }
    public string event_OnsetTime { get; set; }
    public int event_duration { get; set; }
    public double event_durationTime { get; set; }
    public int nSCR { get; set; }
    public float Latency { get; set; }
    public float Amplitude_Summary { get; set; }
    public float Average_SCL { get; set; }
    public float Average_RiseTime { get; set; }
    public float Average_Amplitude { get; set; }
    public float Average_RecoveryTime { get; set; }
}