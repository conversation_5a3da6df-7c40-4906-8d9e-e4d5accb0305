﻿<Project Sdk="Microsoft.NET.Sdk.Razor">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<!--<Nullable>enable</Nullable>-->
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>
	<ItemGroup>
	  <Content Remove="Models\WebSocketReceiveModel.razor" />
	</ItemGroup>
	<ItemGroup>
		<SupportedPlatform Include="browser" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Net.Codecrete.QrCodeGenerator" Version="2.0.4" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.0.2" />
		<PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.0.0" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="wwwroot\" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\HX.HRV.Shared\HX.HRV.Shared.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <UpToDateCheckInput Remove="Pages\Client\DeviceStatus\DeviceStatus.razor" />
	</ItemGroup>
</Project>

