﻿using System.Management;
using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.MAUI.Services;
using Makaretu.Dns;
using Microsoft.Extensions.Configuration;
using Microsoft.UI.Xaml.Controls;
using Microsoft.Web.WebView2.Core;

namespace HX.HRV.MAUI;

public partial class MainPage : ContentPage
{
    public class DiskInfo
    {
        public string LogicalName { get; set; }
        public string Caption { get; set; }
        public string ShowName { get; set; }

        /// <summary>
        /// 解析中
        /// </summary>
        public bool IsParsing { get; set; }

        /// <summary>
        /// 解析进度
        /// </summary>
        public int Progress { get; set; }
    }

    private List<DiskInfo> _diskList;
    int count = 0;
    private readonly UDiskService _udiskService;

    private static string Host;

    //private static string Host = "hx.linglongtu.cn:5222";
    IConfiguration configuration;

    public MainPage(IConfiguration config)
    {
        
  //       if (!File.Exists("host.txt"))
  //       {
  //           var allips = new List<IPAddress>();
  //           var query = new ManagementObjectSearcher(
  //               "SELECT * FROM Win32_NetworkAdapter WHERE PhysicalAdapter = True");
  //           foreach (ManagementObject adapter in query.Get())
  //           {
  //       
  //               var adapterName = adapter["Name"].ToString();
  //       
  //               // Skip WSL, vEthernet and other virtual adapters
  //               if (adapterName.Contains("WSL") ||
  //                   adapterName.Contains("vEthernet") ||
  //                   adapterName.Contains("Virtual") ||
  //                   adapterName.Contains("VMware") ||
  //                   adapterName.Contains("VirtualBox"))
  //               {
  //                   continue;
  //               }
  //       
  //               Console.WriteLine(adapter["Name"]);
  //               int index = Convert.ToInt32(adapter["Index"]); // 关键字段：用于关联 IP 配置
  //               // 查询关联的 IP 配置
  //               var configQuery = new ManagementObjectSearcher(
  //                   $"SELECT * FROM Win32_NetworkAdapterConfiguration WHERE Index = {index}");
  //       
  //               foreach (ManagementObject ipconfig in configQuery.Get())
  //               {
  //                   string[] ips = (string[])ipconfig["IPAddress"] ?? new string[0];
  //                   foreach (var ip in ips)
  //                   {
  //                       if (ip.Contains(":"))
  //                       {
  //                           continue;
  //                       }
  //                       Console.WriteLine(ip);
  //                       allips.Add(IPAddress.Parse(ip));
  //                   }
  //               }
  //           }
		// 	Host =  allips[0].ToString();
		// }
		// else
        
        var ipAddresses = MulticastService.GetIPAddresses();

        foreach (var ipAddress in ipAddresses)
        {
            //假如是IPv4地址
            if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
            {
                Host = ipAddress+":5219";
            }
        }
		Host ??= File.ReadAllText("host.txt").Trim() is { Length: > 0 } host ? host : "127.0.0.1:5001";

        UpdateDeviceList();
        InitializeComponent();
        _udiskService = new UDiskService();
        // 订阅 U 盘插入和拔出事件
        _udiskService.UDiskInserted += (sender, e) => { UpdateDeviceList(); };
        _udiskService.UDiskRemoved += (sender, e) => { RemoveDeviceList(sender, e); };
        _udiskService.StartMonitoring();
        hybridWebView.SetInvokeJavaScriptTarget(this);
        hybridWebView.Loaded += (s, e) =>
        {
            ((HybridWebView)s).EvaluateJavaScriptAsync($"window.location = 'http://{Host}'");
        };
		

	}
	// 	protected override void OnHandlerChanged()
	// 	{
	// 		base.OnHandlerChanged();
	// 		//设置 windows 下自动全屏显示 https://www.saoniuhuo.com/question/detail-2593977.html
	// #if WINDOWS
	// 		var window = App.Current.Windows.FirstOrDefault().Handler.PlatformView as Microsoft.UI.Xaml.Window;
	// 		IntPtr windowHandle = WinRT.Interop.WindowNative.GetWindowHandle(window);
	// 		Microsoft.UI.WindowId windowId = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(windowHandle);
	// 		Microsoft.UI.Windowing.AppWindow appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(windowId);
	// 		//全屏显示，无最大化、最小化、关闭栏。无窗体外壳。
	// 		appWindow.SetPresenter(Microsoft.UI.Windowing.AppWindowPresenterKind.FullScreen);
	// 		//最大化，有窗体外壳。
	// 		(appWindow.Presenter as Microsoft.UI.Windowing.OverlappedPresenter).Maximize();
	// 		// this line can maximize the window
	// #endif
	// 	}
	protected override void OnDisappearing()
	{
		base.OnDisappearing();
        // 获取底层的 WebView2 对象

        if (hiddenWebView.Handler.PlatformView is WebView2 webView2
            && webView2.CoreWebView2 != null
            && webView2.CoreWebView2.CookieManager != null)
        {
            // 清除所有 cookie
            webView2.CoreWebView2.CookieManager.DeleteAllCookies();
        }
        
        if (hybridWebView.Handler.PlatformView is WebView2 webView)
        {
            webView.CoreWebView2.CookieManager.DeleteAllCookies();
        }
    }

	private void UpdateDeviceList()
    {
        try
        {
            _diskList = new List<DiskInfo>();
            // 获取 Win32_DiskDrive 信息
            var diskClass = new ManagementClass("Win32_DiskDrive");
            var disks = diskClass.GetInstances();
            foreach (ManagementObject disk in disks)
            {
                // 获取磁盘 Caption 信息
                var diskCaption = disk["Caption"]?.ToString() ?? "Unknown";
                if (!diskCaption.Contains("HuiXin", StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // 关联查询 Win32_DiskDriveToDiskPartition
                var partitionSearcher = new ManagementObjectSearcher(
                    "ASSOCIATORS OF {Win32_DiskDrive.DeviceID='" + disk["DeviceID"] + "'} " +
                    "WHERE AssocClass = Win32_DiskDriveToDiskPartition");

                foreach (ManagementObject partition in partitionSearcher.Get())
                {
                    // 关联查询 Win32_LogicalDisk
                    var logicalSearcher = new ManagementObjectSearcher(
                        "ASSOCIATORS OF {Win32_DiskPartition.DeviceID='" + partition["DeviceID"] + "'} " +
                        "WHERE AssocClass = Win32_LogicalDiskToPartition");
                    foreach (ManagementObject logicalDisk in logicalSearcher.Get())
                    {
                        // 获取逻辑磁盘盘符
                        var logicalName = logicalDisk["Name"]?.ToString() ?? "Unknown";
                        // 将结果添加到列表
                        _diskList.Add(new DiskInfo
                        {
                            LogicalName = logicalName,
                            Caption = diskCaption,
                            ShowName = diskCaption.Split(' ')[1],
                            IsParsing = false
                        });
                    }
                }
            }

            disks.Dispose();
            diskClass.Dispose();
        }
        catch (Exception ex)
        {
            Console.WriteLine("更新设备列表时出错：" + ex.Message);
        }
    }

    private void RemoveDeviceList(object sender, EventArgs e)
    {
        try
        {
            var newDisks = new List<DiskInfo>();
            // 获取 Win32_DiskDrive 信息
            var diskClass = new ManagementClass("Win32_DiskDrive");
            var disks = diskClass.GetInstances();
            foreach (ManagementObject disk in disks)
            {
                // 获取磁盘 Caption 信息
                var diskCaption = disk["Caption"]?.ToString() ?? "Unknown";
                if (!diskCaption.Contains("HuiXin", StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // 关联查询 Win32_DiskDriveToDiskPartition
                var partitionSearcher = new ManagementObjectSearcher(
                    "ASSOCIATORS OF {Win32_DiskDrive.DeviceID='" + disk["DeviceID"] + "'} " +
                    "WHERE AssocClass = Win32_DiskDriveToDiskPartition");

                foreach (ManagementObject partition in partitionSearcher.Get())
                {
                    // 关联查询 Win32_LogicalDisk
                    var logicalSearcher = new ManagementObjectSearcher(
                        "ASSOCIATORS OF {Win32_DiskPartition.DeviceID='" + partition["DeviceID"] + "'} " +
                        "WHERE AssocClass = Win32_LogicalDiskToPartition");
                    foreach (ManagementObject logicalDisk in logicalSearcher.Get())
                    {
                        // 获取逻辑磁盘盘符
                        var logicalName = logicalDisk["Name"]?.ToString() ?? "Unknown";
                        // 将结果添加到列表
                        newDisks.Add(new DiskInfo
                        {
                            LogicalName = logicalName,
                            Caption = diskCaption,
                            ShowName = diskCaption.Split(' ')[1],
                            IsParsing = false
                        });
                    }
                }
            }

            if (newDisks != null && newDisks.Count > 0)
            {
                _diskList = _diskList.Where(m => newDisks.Any(n => n.LogicalName == m.LogicalName)).ToList();
            }

            disks.Dispose();
            diskClass.Dispose();
        }
        catch (Exception ex)
        {
            Console.WriteLine("更新设备列表时出错：" + ex.Message);
        }
    }

    private void hybridWebView_RawMessageReceived(object sender, HybridWebViewRawMessageReceivedEventArgs e)
    {
        var jsonNode = JsonNode.Parse(e.Message);
        var action = jsonNode["action"].GetValue<string>();
        switch (action)
        {
            case "ExportToClient":
                Task.Run(async () => await UploadFileAsync(e.Message));
                break;
            case "getDeviceInfo":
                hybridWebView.SendRawMessage(JsonSerializer.Serialize(new
                {
                    action = jsonNode["action"],
                    data = _diskList
                }));
                break;
            case "printPdf":
                // 获取底层的 WebView2 对象
                if (hiddenWebView.Handler.PlatformView is WebView2 webView2)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        try
                        {
                            CoreWebView2PrintSettings printSettings = null;
                            printSettings = webView2.CoreWebView2.Environment.CreatePrintSettings();
                            printSettings.MarginBottom = 0;
                            printSettings.MarginLeft = 0;
                            printSettings.MarginRight = 0;
                            printSettings.MarginTop = 0.1;
                            var id = jsonNode["printId"].GetValue<string>();
                            webView2.CoreWebView2.Navigate($"http://{Host}/client/report/report-detail/empty/{id}");
                            await Task.Delay(5000);
                            await webView2.CoreWebView2.PrintAsync(printSettings);
                        }
                        catch (Exception)
                        {
                            await File.AppendAllTextAsync($"error_{DateTime.Now:yyyy-M-d}.txt", e.Message);
                        }
                    });
                }

                break;
        }
    }

  


    /// <summary>
    /// 复制文件夹及文件
    /// </summary>
    /// <param name="sourceFolder">原文件路径</param>
    /// <param name="destFolder">目标文件路径</param>
    /// <returns></returns>
    public bool CopyFolder(string sourceFolder, string destFolder)
    {
        try
        {
            //如果目标路径不存在,则创建目标路径
            if (!System.IO.Directory.Exists(destFolder))
            {
                System.IO.Directory.CreateDirectory(destFolder);
            }

            int index = 0;
            //得到原文件根目录下的所有文件
            string[] files = System.IO.Directory.GetFiles(sourceFolder);
            foreach (string file in files)
            {
                index++;
                string name = System.IO.Path.GetFileName(file);
                string dest = System.IO.Path.Combine(destFolder, name);
                System.IO.File.Copy(file, dest); //复制文件
            }

            //得到原文件根目录下的所有文件夹
            string[] folders = System.IO.Directory.GetDirectories(sourceFolder);

            foreach (string folder in folders)
            {
                index++;
                string name = System.IO.Path.GetFileName(folder);
                string dest = System.IO.Path.Combine(destFolder, name);
                CopyFolder(folder, dest); //构建目标路径,递归复制文件
            }

            return true;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    private HttpClient _httpClient;

    public async Task UploadFileAsync(string json)
    {
        var jsonNode = JsonNode.Parse(json);
        var deviceSn = jsonNode["deviceSn"].GetValue<string>();
        var diskInfo =
            _diskList.FirstOrDefault(m => m.ShowName.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
        if (diskInfo == null)
        {
            return;
        }

        diskInfo.IsParsing = true;
        var targretPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "data", diskInfo.ShowName,
            Guid.NewGuid().ToString("N"));
        // if (!CopyFolder(Path.Combine(diskInfo.LogicalName, "data"), targretPath))
        // {
        // 	return;
        // }
        //如果目标路径不存在,则创建目标路径
        if (!System.IO.Directory.Exists(targretPath))
        {
            System.IO.Directory.CreateDirectory(targretPath);
        }

        int copyIndex = 0;
        //得到原文件根目录下的所有文件
        string[] copyfiles = System.IO.Directory.GetFiles(Path.Combine(diskInfo.LogicalName, "data"));
        foreach (string file in copyfiles)
        {
            copyIndex++;
            string name = System.IO.Path.GetFileName(file);
            string dest = System.IO.Path.Combine(targretPath, name);
            await Task.Delay(10);
            System.IO.File.Copy(file, dest); //复制文件
            diskInfo.Progress = copyIndex * 50 / copyfiles.Length;
        }

        diskInfo.IsParsing = false;

        _ = Task.Run(() => DeleteFiles(diskInfo.LogicalName));
        var files = Directory.GetFiles(targretPath, "*.bin");
        _httpClient = new HttpClient();
        var recordId = jsonNode["recordId"].GetValue<string>();
        var index = 0;
        var filesCount = files.Length;
        var guid = Guid.NewGuid();
        var url = $"http://{Host}/api/v2.0/HRV_HX/PatientRecord/UploadFile";


        var sortedFiles = files.OrderBy(file =>
        {
            // 提取时间戳
            string fileName = Path.GetFileNameWithoutExtension(file);
            string[] parts = fileName.Split('-');
            // 提取时间戳 最后一个
            var timestampString = parts.ElementAt(parts.Length - 1);
            return long.Parse(timestampString);
            //return long.MaxValue; // 如果无法解析时间戳，放到最后
        }).ToArray();
        foreach (var fileName in sortedFiles)
        {
            var formData = new MultipartFormDataContent();
            index++;
            // 创建 MultipartFormDataContent 来处理文件上传
            // 读取文件内容
            var fileContent = new StreamContent(new FileStream(Path.Combine(fileName), FileMode.Open, FileAccess.Read));
            fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            // 将文件添加到表单
            formData.Add(fileContent, "file", fileName);
            var name = Path.GetFileName(fileName);
            // 其他字段
            formData.Add(new StringContent(name), "name");
            formData.Add(new StringContent(guid.ToString("N")), "guid");
            formData.Add(new StringContent(recordId), "recordId");
            formData.Add(new StringContent((index == filesCount).ToString()), "isEnd");
            var progress = (index * 50 / filesCount) + 50;
            formData.Add(new StringContent(progress.ToString()), "progress");
            formData.Add(new StringContent(diskInfo.ShowName), "deviceSn");
            try
            {
                // 发送POST请求
                var response = await _httpClient.PostAsync(url, formData);
                var responseContent = await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"上传过程中发生错误: {ex.Message}");
            }
        }
    }

    private void DeleteFiles(string logicalName)
    {
        var files = Directory.GetFiles(Path.Combine(logicalName, $"data"), "*.bin");
        var index = 0;
        foreach (var file in files)
        {
            index++;
            File.Delete(file);
        }
    }
}