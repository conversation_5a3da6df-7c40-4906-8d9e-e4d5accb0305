﻿using System.Globalization;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Unicode;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.ViewModel;
using HX.HRV.Web.Services;
using HX.HRV.Web.Units;
using Masa.Blazor;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Shared.Models;


namespace HX.HRV.Web.Areas.HRV_HX.Controllers;

/// <summary>
/// 检测记录
/// </summary>
[Area("HRV_HX")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class PatientRecordController : Controller
{
    private readonly DataRepository _context;
    private readonly CoreDbContext _coreContext;

    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// 心率变异性数据管理接口
    /// </summary>
    /// <param name="context"></param>
    /// <param name="coreContext"></param>
    public PatientRecordController(DataRepository context, CoreDbContext coreContext)
    {
        _context = context;
        _coreContext = coreContext;

        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.IgnoreNullValues = true;
    }


    /// <summary>
    /// 心率变异性数据管理接口/详情
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性数据管理接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> GetPatientRecordModelDetail(string deviceId)
    {
        var patientRecordModel = await _context.Query<PatientRecordModel>()
            .OrderByDescending(m => m.Id)
            .FirstOrDefaultAsync(m => m.Data.DeviceId == deviceId);
        return new Result<DataModel<PatientRecordModel>>(patientRecordModel);
    }

    /// <summary>
    /// 心率变异性数据管理接口/详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性数据管理接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> GetPatientRecordModelDetailById(string id)
    {
        var patientRecordModel = await _context.Query<PatientRecordModel>()
            .OrderByDescending(m => m.Id)
            .FirstOrDefaultAsync(m => m.Data.Id == id);
        var user = await new UserService().GetUserInfoByIdAsync(_coreContext, patientRecordModel.UserId);
        patientRecordModel.User = user.Data;
        var hrShowFrom = HrvVariable.HrvVariableInstance.HRShowFrom;
        if (patientRecordModel.Data.Statistics != null && !"Alg".Equals(hrShowFrom, StringComparison.OrdinalIgnoreCase))
        {
            patientRecordModel.Data.Statistics.HRList = CSVDataHelper
                .GetBmpData(patientRecordModel.Data.GetRecordDirectoryPath());
        }
        var device = await _context.Query<DeviceModel>()
            .FirstOrDefaultAsync(m => m.Id == patientRecordModel.Data.DeviceId);
        patientRecordModel.Data.Device = device.Data;
        return new Result<DataModel<PatientRecordModel>>(patientRecordModel);
    }

    /// <summary>
    /// 检测记录/详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性数据管理接口/详情", IsAPI = true)]
    public async Task<Result<List<PatientRecordModel>>> GetPatientRecordModelList([FromQuery] string deviceIds)
    {
        if (deviceIds == null)
        {
            return new Result<List<PatientRecordModel>>(new List<PatientRecordModel>());
        }
        var idlist = deviceIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var patientRecordModel = await _context.Query<PatientRecordModel>()
            .Where(m => idlist.Contains(m.Data.DeviceId)
                        && m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Checking)
            .OrderByDescending(m => m.Id)
            .ToListAsync();
        var list = patientRecordModel?.Select(m => m.Data)?.ToList() ?? new List<PatientRecordModel>();
        return new Result<List<PatientRecordModel>>(list);
    }

    /// <summary>
    /// 心率变异性数据管理接口/添加
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Permission("心率变异性数据管理接口/添加", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> Add([FromBody] PatientRecordModel dataModel)
    {




        UserInfo userInfo = ViewBag.User;
        var result = new Result<DataModel<PatientRecordModel>>();
        dataModel.Id = UId.GetNewId();

        var maxRecordRecordId = await _context
            .Query<PatientRecordModel>()
            .Where(m => m.AddTime >= DateTime.Now.Date)
            .OrderByDescending(m => m.Id)
            .FirstOrDefaultAsync();
        if (maxRecordRecordId != null)
        {
            dataModel.RecordId = maxRecordRecordId.Data.RecordId + 1;
        }
        else
        {
            dataModel.RecordId = 1;
        }
        dataModel.RecordCode = DateTime.Now.ToString("yyyyMMdd") + dataModel.RecordId.ToString("0000");
        dataModel.IsNotEmotion = true;
        var model = new DataModel<PatientRecordModel>
        {
            Data = dataModel,
            Id = dataModel.Id,
            OrganId = userInfo?.Organ?.GetTopOrganId(),
            UserId = userInfo?.Id,
            AddTime = DateTime.Now,
            UpdateTime = DateTime.Now,
        };
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }
        _context.Add(model);
        await _context.SaveChangesAsync();
        if (dataModel.CheckTime > 0)
        {
            await DeviceStatusDataService.SetPatientRecordData(dataModel.DeviceId, dataModel.Id);
        }

        result.Data = model;
        return result;
    }

    /// <summary>
    /// 心率变异性数据管理接口/编辑
    /// </summary>
    /// <param name="id">编号</param>
    /// <param name="PatientRecordModel">产品信息</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性数据管理接口/编辑", IsAPI = true)]
    public async Task<Result<PatientRecordModel>> Edit(string id, [FromBody] PatientRecordModel PatientRecordModel)
    {
        var result = new Result<PatientRecordModel>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(PatientRecordModel, result))
        {
            return result;
        }

        var model = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (model == null)
        {
            result.AddError("设备类型不存在");
            return result;
        }

        _context.Update(model);
        await _context.SaveChangesAsync();
        result.Data = model.Data;
        return result;
    }


    [HttpGet()]
    [Permission("心率变异性数据管理接口/列表", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<PageList<DataModel<PatientRecordModel>>>> GetPatientRecordModelListByParams(
        [FromQuery] PatientModel patientModel, [FromQuery] PatientRecordModel recordModel,
        [FromQuery] string recordCodeOrId,
        [FromQuery] int? page = 1,
        [FromQuery] int? pageSize = 100)
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Query<PatientRecordModel>();
        if (!string.IsNullOrEmpty(patientModel.CardId?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.CardId == patientModel.CardId);
        }

        if (!string.IsNullOrEmpty(patientModel.Name?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.Name == patientModel.Name);
        }

        if (patientModel.Age != default)
        {
            query = query.Where(m => patientModel.Age == m.Data.Patient.Age);
        }

        if (!string.IsNullOrEmpty(patientModel.OutpatientNumberString?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.OutpatientNumberString == patientModel.OutpatientNumberString);
        }

        if (patientModel.Sex != default)
        {
            query = query.Where(m => m.Data.Patient.Sex == patientModel.Sex);
        }

        if (!string.IsNullOrEmpty(patientModel.Source?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.Source == patientModel.Source);
        }

        if (recordModel.CollectStartTime != default)
        {
            query = query.Where(m => m.Data.CollectStartTime >= recordModel.CollectStartTime);
        }

        if (recordModel.CollectEndTime != default)
        {
            var endDate = recordModel.CollectEndTime.AddDays(1);
            query = query.Where(m => m.Data.CollectEndTime < endDate);
        }

        if (!string.IsNullOrEmpty(recordModel.PatientId))
        {
            query = query.Where(m => m.Data.PatientId == recordModel.PatientId);
        }

        if (!string.IsNullOrEmpty(recordModel.DeviceId))
        {
            query = query.Where(m => m.Data.DeviceId == recordModel.DeviceId);
        }

        if (!string.IsNullOrEmpty(recordModel.RecordCode))
        {
            query = query.Where(m => m.Data.RecordCode == recordModel.RecordCode);
        }

        if (!string.IsNullOrEmpty(recordModel.Id))
        {
            query = query.Where(m => m.Id == recordModel.Id);
        }

        if (!string.IsNullOrEmpty(recordCodeOrId))
        {
            query = query.Where(m => m.Data.RecordCode == recordCodeOrId || m.Id == recordCodeOrId);
        }

        if (!userInfo.Roles.Any(m=>m.Name.Equals("管理员", StringComparison.OrdinalIgnoreCase)))
        {
            query = query.Where(m => m.UserId == userInfo.Id);
        }

        var dataModels = await query
            .OrderByDescending(m => m.Id)
            .ToPageListAsync(pageIndex: page.Value, pageSize: pageSize.Value <= 0 ? 100 : pageSize.Value);
        return new Result<PageList<DataModel<PatientRecordModel>>>()
        { Success = true, Data = dataModels, Page = dataModels.PageInfo };
    }

    /// <summary>
    /// 设备管理接口/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性数据管理接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string id)
    {
        var result = new Result<bool>();
        if (id == null)
        {
            return result;
        }
        var patient = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }
        var path = patient.Data.GetRecordDirectoryPath();
        if (Directory.Exists(path))
        {
            Directory.Delete(path, true);
        }
        _context.Remove(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        return result;
    }

    /// <summary>
    /// 停止检测
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性数据管理接口/更新状态", IsAPI = true)]
    public async Task<Result<bool>> Finished([FromQuery] string id)
    {
        var result = new Result<bool>()
        {
            Success = false
        };
        if (id == null)
        {
            return result;
        }
        var patient = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Data.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }
        patient.UpdateTime = DateTime.Now;
        patient.Data.EnumPatientCheckStatus = EnumPatientCheckStatus.Checked;
        patient.Data.CollectEndTime = DateTime.Now;
        _context.Update(patient);
        _context.SaveChanges();
        result.Data = true;
        result.Success = true;
        await DeviceStatusDataService.SetPatientRecordToStop(id);
        return result;
    }

    [HttpPost()]
    [Permission("心率变异性数据管理接口/更新状态", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> Began(string id)
    {
        var result = new Result<DataModel<PatientRecordModel>>()
        {
            Success = false
        };
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Data.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }

        patient.UpdateTime = DateTime.Now;
        patient.Data.CollectStartTime = DateTime.Now;
        patient.Data.CollectEndTime = DateTime.Now.AddMinutes(patient.Data.CheckTime);
        patient.Data.EnumPatientCheckStatus = EnumPatientCheckStatus.Checking;
        _context.Update(patient);
        await _context.SaveChangesAsync();
        result.Data = patient;
        result.Success = true;
        return result;
    }

    /// <summary>
    ///  患者检测记录/统计
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [Permission("心率变异性数据管理接口/统计", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<PatientRecordStatistics>> GetPatientRecordCount()
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Query<PatientRecordModel>();
        var allCount = await _context.Query<PatientRecordModel>().CountAsync();
        var today = DateOnly.FromDateTime(DateTime.Now).ToDateTime(TimeOnly.MinValue);
        var count = await
            query
                .Where(o => o.Data.CollectStartTime > today)
                .CountAsync();
        return new Result<PatientRecordStatistics>()
        {
            Success = true,
            Data = new PatientRecordStatistics()
            {
                AllCount = allCount,
                TodayCount = count
            }
        };
    }

    static Dictionary<string, string> fileTypeMap = new Dictionary<string, string>();

    [HttpPost]
    [Permission("心率变异性数据管理接口/上传文件", IsAPI = true, AllowAnonymous = true)]
    public async Task<Result<string>> UploadFile()
    {
        var result = new Result<string>();
        try
        {
            var file = Request.Form.Files["file"];
            var recordId = this.Request.Form["recordId"].ToString();
            var isEnd = this.Request.Form["isEnd"].ToString();
            var deviceSn = Request.Form["deviceSn"].ToString();
            var fileName = this.Request.Form["name"].ToString();
            var progress = this.Request.Form["progress"].ToString();
            var model = await _context.Query<PatientRecordModel>()
                .FirstOrDefaultAsync(m => m.Id == recordId);
            if (model == null)
            {
                result.AddError(" 检测记录不存在");
                return result;
            }

            ImportFileDataAnalysisService.GetFileNameMessage(fileName.TrimEnd(".bin"), 
                out var timestampAndFrequencyFromFileName);

            if (model.Data.DeviceStartCheckTime == default)
            {
                model.Data.DeviceStartCheckTime = timestampAndFrequencyFromFileName.time;
                model.Data.CollectStartTime = timestampAndFrequencyFromFileName.time;
                _context.Update(model);
                await _context.SaveChangesAsync();
            }

            var dir = model.Data.GetRecordDirectoryPath();
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }

            var filePath = Path.Combine(dir, fileName);
            if (file == null)
            {
                result.AddError("请选择文件");
                return result;
            }

            byte[] numArray = new byte[file.Length];
            await file.OpenReadStream().ReadAsync(numArray, 0, (int)file.Length);
            await System.IO.File.WriteAllBytesAsync(filePath, numArray);
            float.TryParse(progress, out var floatProgress);

            var toPageJson = new JsonObject
            {
                { "SN", deviceSn },
                { "Status", (int)EnumDeviceStatus.USB传输中 },
                { "Progress", floatProgress }
            };

            if (isEnd == true.ToString())
            {
                model.Data.CollectEndTime = timestampAndFrequencyFromFileName.time.AddSeconds(timestampAndFrequencyFromFileName.seconds);
                model.Data.BuildReportStatus = BuildReportStatus.UnKnown;
                model.Data.CheckTime = (model.Data.CollectEndTime - model.Data.CollectStartTime).Minutes;
                _context.Update(model);
                await _context.SaveChangesAsync();
                _ = ImportFileDataAnalysisService.AnalysisBinDirectory(deviceSn, dir);
            }
            else
            {
                _ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToString());
            }

        }
        catch (Exception ex)
        {
            result.AddError("文件上传出错");
            return result;
        }

        return result;
    }


    [HttpGet]
    public async Task<Result<List<string>>> GetFileContent(string recordId, HXDataType dataType)
    {
        var result = new Result<List<string>>();
        var patientRecordModel = _context.Query<PatientRecordModel>().FirstOrDefault(m => m.Id == recordId);
        if (patientRecordModel == null)
        {
            result.Message = "检测记录不存在";
            return null;
        }
        var datas = await FileDataHelper.ProcessFilesAsync(
            patientRecordModel.Data.GetRecordDirectoryPath(), $"*{dataType.ToString()}*.csv",
            default, default);
        //假如超过10W行数据就需要进行降采样
        if (datas.Count > 100_000)
        {
            var rate = EstimateFrequencyFromLines(datas);
            var multiply = datas.Count / 100_000.0;
            var targetRateValue = rate / multiply;
            // 获取所有枚举值并排序（降序，越高优先）
            var allRates = Enum.GetValues(typeof(SignalResampler.SignalRateMinuteSecond))
                .Cast<SignalResampler.SignalRateMinuteSecond>()
                .Select(e => (int)e)
                .OrderByDescending(v => v)
                .ToList();
            // 找到 <= targetRateValue 的第一个匹配项 并且返回的数值少于10_000
            var bestMatch = allRates
                .FirstOrDefault(v => v <= targetRateValue && datas.Count / v < 100_000);
            // 如果找不到（极端情况），默认用最低值
            var selectedRate = (SignalResampler.SignalRateMinuteSecond)(bestMatch == 0 ? allRates.Last() : bestMatch);
            LogTool.Logger.Information("原始频率: {Rate} Hz，数据量: {DatasCount}，降采样目标: {SignalRateMinuteSecond}", rate, datas.Count, selectedRate);
            // 执行降采样
            datas = SignalResampler.DownsampleLines(datas, selectedRate);
        }


        var headerColumns = "";
        switch (dataType)
        {
            case HXDataType.PPG:
                headerColumns = "Time,PPG_R,PPG_I,PPG_G,Event";
                break;
            case HXDataType.HRSPO2:
                headerColumns = "Time,HR,SPO2,Battery,Event";
                break;
            case HXDataType.IMU:
                headerColumns = "Time,GYRO-X,GYRO-Y,GYRO-Z,ACC-X,ACC-Y,ACC-Z,GEO-X,GEO-Y,GEO-Z,Event";
                break;
            case HXDataType.EDA:
            case HXDataType.TEMPSKIN:
            case HXDataType.TEMPAIR:
            case HXDataType.AP:
            case HXDataType.LIGHT:
                headerColumns = $"Time,{dataType},Event";
                break;
        }
        datas.Insert(0, headerColumns);
        result.Data = datas;
        result.Success = true;
        return result;
    }

    public static int EstimateFrequencyFromLines(List<string> lines)
    {
        const string dateFormat = "yyyy-MM-dd HH:mm:ss.fff";
        DateTime startTime = default;
        var count = 0;

        foreach (var line in lines)
        {
            var parts = line.Split(',');
            if (parts.Length < 2) continue;
            if (!DateTime.TryParseExact(parts[0], dateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out var time))
                continue;
            // 找到第一个 .000 作为开始时间
            if (startTime == default && parts[0].EndsWith(".000"))
            {
                startTime = time;
            }
            // 如果已经有开始时间，并且时间在 [startTime, startTime + 1秒) 区间内，计数
            if (startTime != default && time >= startTime && time < startTime.AddSeconds(1))
            {
                count++;
            }
            // 超过 1 秒就可以停止
            if (startTime != default && time >= startTime.AddSeconds(1))
            {
                break;
            }
        }
        return count;
    }







}