﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Services
@using UFU.IoT.Shared.Utils
@inject NavigationManager NavigationManager


<MDataTable OnOptionsUpdate="@HandleOnOptionsUpdate"
            TItem="DataModel<HxExportTaskModel>"
            Headers="_headers"
            ItemKey="r => r.Id"
            Items="HxExportTaskModels"
            Page="@_options.Page"
            Stripe
            DisableSort="true"
            ItemsPerPage="_options.ItemsPerPage"
            Loading="_loading"
            FooterProps="@(props => { props.ShowFirstLastPage = false; props.ShowCurrentPage = false; props.PageText = @"共{2}条数据"; props.DisableItemsPerPage = true; })"
            ServerItemsLength="@_total">
    <HeaderColContent Context="header">
        <MLabel Style="font-weight: 400;
font-size: 18px;
height: 63px;
color: #28333E;">@header.Text</MLabel>
    </HeaderColContent>
    <ItemColContent Context="item">
        @if (item.Header.Value == "actions")
        {
        <div class="d-flex align-center justify-center">
            <a href="@($"api/v2.0/HRV_HX/HxExportTask/DownloadFile/{item.Item.Id}")" target="_blank" Link Color="blue" Class="text-decoration-underline" Plain>
                下载
            </a>
        </div>
        }
        else
        {
            if (item.Value is RenderFragment)
            {
                @((RenderFragment)item.Value)
            }
            else
            {
                @item.Value
            }
        }
    </ItemColContent>
</MDataTable>


@code {
    private bool _loading;
    private DataOptions _options = new(1, 10);
    private int _total;

    private List<DataTableHeader<DataModel<HxExportTaskModel>>> _headers => new()
    {
        new()
        {
            Text = "导出时间",
            Align = DataTableHeaderAlign.Start,
            Sortable = false,
            CellRender = C => C.AddTime.ToString("yyyy-M-d HH:mm:ss")
        },
        new()
        {
            Text = "数量",
            CellRender = C => C.Data.ExportCount.ToString(),
        },
        new()
        {
            Text = "状态",
            CellRender = C => C.Data.ExportStatus.GetDisplayName(),
        },
        new()
        {
            Text = "进度",
            CellRender = c =>
            {
                RenderFragment line = @<MProgressLinear Value="@(c.Data.Progress)" BufferValue="100" BackgroundOpacity="0.2" Height="25"><strong style="color: white">@($"{context}%")</strong></MProgressLinear>;
                return line;
            },
        },
        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Width = "100px",
            Align = DataTableHeaderAlign.Center,
        }
    };

    /// <summary>
    ///  内部HttpClient
    /// </summary>
    [Inject]
    public InternalHttpClientService InternalHttpClientService { get; set; }

    [Inject] IPopupService PopupService { get; set; }

    private List<DataModel<HxExportTaskModel>> HxExportTaskModels { get; set; } = new();

    [Parameter] public EventCallback CloseDialog { get; set; }

    [Parameter] public string StartTaskId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await InitDataList();
        // hubConnection = new HubConnectionBuilder()
        //     .WithUrl(NavigationManager.ToAbsoluteUri("/hrv/exportHub"))
        //     .Build();
        //
        // hubConnection.On<(string, double)>("ReceiveProgress", item =>
        // {
        //     // 接收进度
        //     var task = HxExportTaskModels.FirstOrDefault(x => x.Id == item.Item1);
        //     if (task == null)
        //     {
        //         return;
        //     }
        //     task.Data.Progress = item.Item2;
        //     InvokeAsync(StateHasChanged);
        // });
        // hubConnection.On("ReceiveExportComplete", () => { InvokeAsync(StateHasChanged); });
        // await hubConnection.StartAsync();
        // if (!string.IsNullOrEmpty(StartTaskId))
        // {
        //     await StartExport();
        // }
        _timer = new Timer(InitTimerDataList, null, 500, 300);
        await base.OnInitializedAsync();
    }

    private async void InitTimerDataList(object _)
    {
        await InitDataList();
        await InvokeAsync(StateHasChanged);
    }

    private async Task InitDataList()
    {
       
        var queryParam = new Dictionary<string, string>
        {
            { "page", _options.Page.ToString() },
            { "pageSize", _options.ItemsPerPage.ToString() }
        };
        var result = await InternalHttpClientService.GetExportTaskList(queryParam);
        HxExportTaskModels = result?.Data;
        _total = result?.Page?.TotalCount ?? 0;
       
    }
    private async Task HandleOnOptionsUpdate(DataOptions obj)
    {
        _options = obj;
        await InitDataList();
    }
    private Timer _timer;
    
    // private HubConnection hubConnection;
    // private async Task StartExport()
    // {
    //     await hubConnection.SendAsync("StartExport", StartTaskId);
    // }
    // /// <summary>
    // /// 
    // /// </summary>
    // public async ValueTask DisposeAsync()
    // {
    //     await hubConnection.DisposeAsync();
    // }

}