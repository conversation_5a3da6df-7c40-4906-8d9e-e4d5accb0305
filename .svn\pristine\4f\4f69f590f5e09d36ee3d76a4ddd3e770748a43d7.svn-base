﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using UFU.IoT.Models
@using HX.HRV.Shared.Pages.Client.Components
<style>
    .dialog-button.confirm {
        width: 300px;
        background: linear-gradient(0deg, #008ef7, #1e9fff);
        box-shadow: 0px 8px 16px 0px rgb(48 138 239 / 83%);
        border-radius: 28px;
        color: white;
        font-size: 2rem;
    }

    .dialog-button.cancel {
        width: 300px;
        border-radius: 28px;
        box-shadow: 0px 8px 16px 0px rgb(177 213 253 / 83%);
        font-size: 2rem;
    }

    .customer-input .m-input__prepend-outer .m-label {
        width: 140px;
        font-size: 1.25rem;
    }

    .patient-dialog .m-text-field__slot {
        font-size: 1.625rem;
    }
    .patient-dialog .customer-text {
          font-size: 1.625rem;
        font-weight: 400;
        position: unset !important;
        color: black;
      }
</style>

<MForm Model="PatientModel" Context="SelectedDialogContext">
    <MRow>
        <MCardText Class="d-flex justify-center flex-column align-center">
            <MForm Model="PatientModel">
                <MRow Class="mt-2">

                    @if (IsOnlyAdd)
                    {
                        <MCol Cols="6">
                            <MTextField Class="customer-input" Dense Solo
                                        @bind-Value="PatientModel.CardId">
                                <PrependContent>
                                    <MLabel Class="mr-2">身份证号:<label style="color:red">*</label></MLabel>
                                </PrependContent>
                            </MTextField>
                        </MCol>
                    }
                    else
                    {
                        <MCol Cols="6">
                            <MAutocomplete
                                Class="customer-input"
                                TItem="PatientModel"
                                TItemValue="string"
                                TValue="string"
                                OnChange="s => PatientModel.CardId = s"
                                Items="@_entries"
                                Dense
                                Solo
                                Chips
                                Clearable
                                Loading="_isIdCardLoading"
                                OnSelect="(item) => { PatientModel = item.Item; }"
                                OnSearchInputUpdate="UpdateSearchInputAsync"
                                @bind-Value="PatientModel.CardId"
                                ItemText="r => { return r.Name + ' ' + r.CardId; }"
                                ItemValue="r => r.CardId">
                                <PrependContent>
                                    <MLabel Class="mr-2">身份证号:<label style="color:red">*</label></MLabel>
                                </PrependContent>
                                <SelectionContent Context="ItemContext">
                                    @ItemContext.Item?.CardId
                                </SelectionContent>
                                <LabelContent>
                                    <MLabel Class="customer-text"> @PatientModel.CardId</MLabel>
                                </LabelContent>
                            </MAutocomplete>
                        </MCol>
                    }


                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Name">
                            <PrependContent>
                                <MLabel Class="mr-2">姓名:<label style="color:red">*</label></MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo

                                    @bind-Value="PatientModel.OutpatientNumberString">
                            <PrependContent>
                                <MLabel Class="mr-2">门诊/住院号:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="6">
                        <MSelect Disabled="IsDisable" Class="customer-input" Id="sex" Items="Sexes"
                                 @bind-Value="@PatientModel.Sex"
                                 ItemText="u => u.ToString()"
                                 SingleLine
                                 Clearable="true"
                                 Dense
                                 ItemValue="u => u" Solo>
                            <PrependContent>
                                <MLabel Class="mr-2">性别:</MLabel>
                            </PrependContent>
                        </MSelect>
                    </MCol>
                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Age">
                            <PrependContent>
                                <MLabel Class="mr-2">年龄:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Area">
                            <PrependContent>
                                <MLabel Class="mr-2">病区:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Height">
                            <PrependContent>
                                <MLabel Class="mr-2">身高(CM):</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Weight">
                            <PrependContent>
                                <MLabel Class="mr-2">体重(KG):</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="12" Class="d-flex  justify-start align-center">
                        <MLabel Style="width: 120px;text-align: end;font-size: 1.25rem;" Class="mr-2">患者来源:</MLabel>
                        <HRVSourceGroupButton Disabled="IsDisable" ConfigModel="SystemConfig"
                                              @bind-SourceText="@PatientModel.Source">
                        </HRVSourceGroupButton>
                    </MCol>
                    @if (!IsOnlyAdd)
                    {
                        <MCol Cols="12" Class="d-flex  justify-start align-center">
                            <MLabel Class="mr-2" Style="width: 120px;text-align: end;font-size: 1.25rem;">检测时长:</MLabel>
                            <HRVCheckTimeButton @bind-CheckTimeValue="CheckTimeValue"
                                                ConfigModel="SystemConfig">
                            </HRVCheckTimeButton>

                        </MCol>
                    }
                    @*  <MCol Cols="12" Class="d-flex  justify-start align-start"> *@
                    @*     <MLabel Class="mr-2" Style="width: 120px;text-align: end;font-size: 1.25rem;">医嘱:</MLabel> *@
                    @*     <MTextarea Disabled="IsDisable" *@
                    @*                Dense *@
                    @*                Solo *@
                    @*                NoResize *@
                    @*                Placeholder="输入医嘱" *@
                    @*                @bind-Value="@PatientModel.DoctorsAdvice" *@
                    @*                Rows="7"> *@
                    @*     </MTextarea> *@
                    @* </MCol> *@
                </MRow>
            </MForm>
        </MCardText>
    </MRow>
    <MRow Justify="JustifyTypes.Center">
        <MCol Cols="4">
            <MButton Height="56" Class="dialog-button cancel" Color="#fff" OnClick="CancelAsync">取消</MButton>
        </MCol>
        <MCol Cols="4">
            <MButton Height="56" Class="dialog-button confirm" OnClick="SaveAsync">
                确认
            </MButton>
        </MCol>
    </MRow>
</MForm>
@if (!IsOnlyAdd&&SelectedDialog)
{
    <MDialog Width="500" @bind-Value="SelectedDialog"    Persistent="true">
        <ChildContent>
            <SelectedDeviceDialog DeviceId="@DeviceStatusData.Device.Id"
                                  PatientModel="PatientModel"
                                  CheckTimeValue="@CheckTimeValue"
                                  CloseDialogEvent="() => { 
                                      SelectedDialog = false;
                                      CloseDialog.InvokeAsync();  
                                      PatientModel=new PatientModel();
                                      CheckTimeValue=0;}">
            </SelectedDeviceDialog>
        </ChildContent>
    </MDialog>
}

@code {

    // protected bool IsDisable => !IsOnlyAdd&& !string.IsNullOrEmpty(PatientModel.Id);
    protected bool IsDisable => false;
    [Parameter] public PatientModel PatientModel { get; set; } = new();
    protected bool _isIdCardLoading;
    protected PatientRecordModel RecordModel { get; set; } = new();
    protected DeviceModel Device => DeviceStatusData.Device;
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }
    protected Sex[] Sexes { get; } = Enum.GetValues<Sex>();
    [Parameter] public DeviceStatusViewModel DeviceStatusData { get; set; }

    /// <summary>
    /// 是否只是新增
    /// </summary>
    [Parameter]
    public bool IsOnlyAdd { get; set; } = false;

    [Parameter] public string DialogTitle { get; set; }


    protected bool SelectedDialog { get; set; } = false;

    protected async Task SaveAsync()
    {
        await HandleSubmit();
    }
    [CascadingParameter(Name = "DeviceDataList")]
    protected List<DeviceStatusViewModel> DeviceDataList { get; set; }

    [Inject] IPopupService PopupService { get; set; }

    protected List<PatientModel> _entries = new();

    public async Task UpdateSearchInputAsync(string val)
    {
        _isIdCardLoading = true;
        var result = await InternalHttpClientService.GetPatientModelList(
            new()
            {
                { nameof(PatientModel.CardId), val }
            });
        _entries = result.Data?.Select(m => m.Data)?.ToList();
        _isIdCardLoading = false;
    }

    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    protected async Task HandleSubmit()
    {
        if (string.IsNullOrEmpty(PatientModel.CardId))
        {
            await PopupService.EnqueueSnackbarAsync("身份证号不能为空", AlertTypes.Error); return;
        }
        if (string.IsNullOrEmpty(PatientModel.Name))
        {
            await PopupService.EnqueueSnackbarAsync("姓名不能为空", AlertTypes.Error); return;
        }
        if (!IsOnlyAdd&&CheckTimeValue <= 0)
        {
            await PopupService.EnqueueSnackbarAsync("检测时长不能为空", AlertTypes.Error); return;
        }
        // if(string.IsNullOrEmpty(PatientModel.Source)){
        //     await PopupService.EnqueueSnackbarAsync("患者来源不能为空", AlertTypes.Error); return;
        // }


        if (!IsOnlyAdd&&PatientModel.Id!=null)
        {
            var isExist = DeviceDataList?.Any(m => m.RecordModel?.Id == PatientModel.Id)??false;
            if (isExist)
            {
                await PopupService.EnqueueSnackbarAsync("该患者已经在检测中", AlertTypes.Error); 
                return;
            }
        }
        
        var result = await InternalHttpClientService.AddOrEditPatientAsync(PatientModel);
        if (IsOnlyAdd)
        {
            if (result.Success)
            {
                await PopupService.EnqueueSnackbarAsync("保存成功！", AlertTypes.Success );
                await CloseDialog.InvokeAsync();
            }
             if (!result.Success&&!string.IsNullOrEmpty(result.Message))
            {
                await PopupService.EnqueueSnackbarAsync(result.Message,  AlertTypes.Error);
                return;
            }
        }

        if (result.Success)
        {
            PatientModel = result.Data?.Data;
            SelectedDialog = true;
        }
    }

    [Parameter] public EventCallback CloseDialog { get; set; }

    protected async Task CancelAsync()
    {
        if (!IsOnlyAdd)
        {
            this.PatientModel = new PatientModel();
        }

        await CloseDialog.InvokeAsync();
    }

    protected async Task CheckCardId()
    {
        if (!IsOnlyAdd && !string.IsNullOrEmpty(PatientModel.CardId))
        {
            var res = await InternalHttpClientService.GetPatientByCardAsync(PatientModel.CardId);
            if (res.Success)
            {
                if (res.Data is { Data: not null })
                {
                    PatientModel = res.Data.Data;
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        RecordModel ??= new();
        // PatientModel=PatientModel ?? new PatientModel();
        await InitHxSystemConfig();
        await base.OnInitializedAsync();
    }


    /// <summary>
    /// 初始化系统配置
    /// </summary>
    protected async Task InitHxSystemConfig()
    {
        var systemConfig = await InternalHttpClientService.GetSystemConfigAsync();
        this.SystemConfig = systemConfig?.Data?.Data ?? new HxSystemConfigModel
        {
            IsAutoPrint = false,
            CheckTimeList = new(),
            PatientSource = new()
        };
    }

    protected HxSystemConfigModel SystemConfig { get; set; } = new HxSystemConfigModel
    {
        IsAutoPrint = false,
        CheckTimeList = new(),
        PatientSource = new()
    };

    [Parameter] public HxSystemConfigModel ConfigModel { get; set; }
    protected bool dialog_Time;
    public int CheckTimeValue = 0;
}