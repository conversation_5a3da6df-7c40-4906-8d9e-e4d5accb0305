﻿using System.Collections.Concurrent;
using System.Text;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using PdfSharp;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
/// 
/// </summary>
public class GenerateReportService : BackgroundService
{
    private static List<DataModel<PatientRecordModel>> _pendingDatas;

    private static ConcurrentDictionary<string, IsSendingRecord> _isSending = new();

    private class IsSendingRecord
    {
        public IsSendingRecord(string id, DateTime createTime)
        {
            Id = id;
            CreateTime = createTime;
        }

        public string Id { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public GenerateReportService()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        LogTool.GetLogger("GenerateReport", "GenerateReport").Information("开始执行生成报告任务");
        _= Task.Run(async () =>
        {
            await using var db = new DataRepository(UserInfo.System);
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
                    _pendingDatas = await GetPendingGenerateAsync(db);
                    if (_pendingDatas is not { Count: > 0 })
                    {
                        continue;
                    }

                    for (int i = 0; i < _pendingDatas.Count; i++)
                    {
                        if (stoppingToken.IsCancellationRequested)
                        {
                            break;
                        }
                        var record = _pendingDatas[i];
                        if (!_isSending.ContainsKey(record.Id))
                        {
                            // 执行任务
                            await SendMsgToAlgAsync(record, db);
                        }
                        _pendingDatas.Remove(record);
                        var isSending =
                            _isSending.GetOrAdd(record.Id, new IsSendingRecord(record.Id, DateTime.Now));
                        _isSending.AddOrUpdate(record.Id, isSending, (k, v) => isSending);
                    }
                }
                catch (Exception ex)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(ex, "处理导出报告任务发生错误");
                }
            }
        }, stoppingToken);
        _= Task.Run(async () =>
        {
           
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                    for (int i = 0; i < _isSending.Count; i++)
                    {
                        var record = _isSending.Values.ToList()[i];
                        if (!(DateTime.Now.Subtract(record.CreateTime).Seconds >10)) continue;
                        _isSending.TryRemove(record.Id, out _);
                        LogTool.Logger.Information($"移除超时任务:{record.Id}");
                    }
                }
                catch (Exception e)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(e, "处理导出报告任务发生错误");
                }
            }
        }, stoppingToken);
    }


    /// <summary>
    /// 获取待生成报告的记录
    /// </summary>
    /// <param name="_dbContext"></param>
    /// <returns></returns>
    private async Task<List<DataModel<PatientRecordModel>>> GetPendingGenerateAsync(DataRepository _dbContext)
    {
        // 获取未生成报告的记录
        var endTime = DateTime.Now.AddSeconds(-20);
        var isSendingIds = _isSending.Keys.ToList();
        var records = await _dbContext.Query<PatientRecordModel>(PermissionSwitch.Off)
            .Where(t =>
                t.Data.CollectEndTime < endTime
                &&t.Data.CollectEndTime>DateTime.MinValue
                && !isSendingIds.Contains(t.Id)
                && t.Data.BuildReportStatus != BuildReportStatus.Completed 
                && t.Data.BuildReportStatus != BuildReportStatus.IsLinked 
                && t.Data.BuildReportStatus != BuildReportStatus.NoCondition)
            .OrderByDescending(m => m.Id)
            .Take(20)
            .ToListAsync();
        return records;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="record"></param>
    private static async Task SendMsgToAlgAsync(DataModel<PatientRecordModel> record, DataRepository _dbContext)
    {
        var path = record.Data.GetRecordDirectoryPath();
        if (!Directory.Exists(path) || !File.Exists(Path.Combine(path, HXDataType.PPG.ToString() + ".csv")))
        {
            record.Data.BuildReportStatus = BuildReportStatus.NoCondition;
            _dbContext.Update(record);
            await _dbContext.SaveChangesAsync();
            return;
        }
        var ppgRate = record.Data.PpgRate;
        var ppgDic = FileDataHelper.ReadDataFromCsv<uint>(path, HXDataType.PPG.ToString());
        var ppgGBytes = ppgDic["PPG_G"];
        var ppgRBytes = ppgDic["PPG_R"];
        var ppgIBytes = ppgDic["PPG_I"];
        //var pageSize = 500 * 60;
        var pageSize = 500;
		if (ppgGBytes.Count <= pageSize)
        {
			var dic = new Dictionary<string, List<uint>>
			{
				["PPG_G"] = ppgGBytes,
				["PPG_R"] = ppgRBytes,
				["PPG_I"] = ppgIBytes
			};
			await AlgWebSocketClient.SendMsgToAlg(record.Id, dic, ppgRate == 0 ? 500 : ppgRate,
                record.Data.DeviceStartCheckTime!=DateTime.MinValue? record.Data.DeviceStartCheckTime.ToUnixMs():record.Data.CollectStartTime.ToUnixMs(),
				true);
        }
        else
        {
            var page = 0;
			var totalPage = (ppgGBytes.Count + pageSize - 1) / pageSize;
			while (page * pageSize < ppgGBytes.Count)
			{
				page++;
				var dic = new Dictionary<string, List<uint>>
				{
					["PPG_G"] = ppgGBytes.Skip(page * pageSize).Take(pageSize).ToList(),
					["PPG_R"] = ppgRBytes.Skip(page * pageSize).Take(pageSize).ToList(),
					["PPG_I"] = ppgIBytes.Skip(page * pageSize).Take(pageSize).ToList()
				};
				await AlgWebSocketClient.SendMsgToAlg(record.Id, dic, ppgRate == 0 ? 500 : ppgRate,
					record.Data.DeviceStartCheckTime!=DateTime.MinValue? record.Data.DeviceStartCheckTime.ToUnixMs():record.Data.CollectStartTime.ToUnixMs(),
					page== totalPage);
                await Task.Delay(10);
			}
        } 
        try
        {
            if (!Directory.Exists(path) || !File.Exists(Path.Combine(path, HXDataType.EDA.ToString() + ".csv")))
            {
                record.Data.BuildReportStatus = BuildReportStatus.NoCondition;
                _dbContext.Update(record);
                await _dbContext.SaveChangesAsync();
                return;
            }
            var edaDatas = FileDataHelper.ReadDataFromCsv<float>(path, HXDataType.EDA.ToString());
            var edaBytes = edaDatas["Value"];
            var edaPageSize = 50 * 60;
            var edapage = 0;
            while (edapage * edaPageSize < edaBytes.Count)
            {
                edapage++;


                var startTime = record.Data.DeviceStartCheckTime != DateTime.MinValue
                        ? record.Data.DeviceStartCheckTime.ToUnixMs()
                        : record.Data.CollectStartTime.ToUnixMs();
                var data = edaBytes.Skip(edapage * edaPageSize).Take(edaPageSize).ToList();
                await SendGsrMsgToAlg(record.Id, data.ToArray(), record.Data.DeviceStartCheckTime.ToUnixMs(),
                    startTime);
            }
        }
        catch (Exception e)
        {
            LogTool.Logger.Error("GenerateReportService SendGsrMsgToAlg:" + e.Message);
        }
    }
    private static async Task SendGsrMsgToAlg(
        string recordId,
        float[] gsr_g_data,
        long timeSpan,
        long StartTime
    )
    {
        byte[] databytes = new byte[gsr_g_data.Count() * sizeof(float)];
        Buffer.BlockCopy(gsr_g_data.ToArray(), 0, databytes, 0, databytes.Length);
        var jsonData = new
        {
            MessageType = 3,
            ClientId = recordId,
            Algorithm = "GSRSeparated",
            IsEnd = false,
            StartTime,
            Time = timeSpan * 1000,
            Data = new { gsr_Length = databytes.Length, }
        };
        var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
        //LogTool.Logger.Information("GenerateReportService SendGsrMsgToAlg:" + JsonTool.Serialize(new { gsr_g_data }));
        await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, databytes, recordId);
    }
    public override async Task StopAsync(CancellationToken stoppingToken)
    {
        LogTool.Logger.Information("生成报告服务正在停止");
        await base.StopAsync(stoppingToken);
    }
}