﻿using System.Net;
using HX.Experiment.Shared.Services;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;

namespace HX.Experiment.MAUI.Services;

public class UserService(StateService stateService) : IUserService
{
    public string GetUserId()
    {
        throw new NotImplementedException();
    }

    public string GetUserName()
    {
        throw new NotImplementedException();
    }

    public async Task<LoginTokenModel> LoginAsync(string userName, string password)
    {
        var result = await 
            stateService.PostAsJsonAsync<LoginTokenModel>("/api/v2/Core/Auth/Login",new { userName, password });
        return !result.Success ? null : result.Data;
    }

    public void Logout()
    {
        throw new NotImplementedException();
    }

    public bool IsAuthenticated()
    {
        throw new NotImplementedException();
    }

    public void Register(string userName, string password)
    {
        throw new NotImplementedException();
    }
}