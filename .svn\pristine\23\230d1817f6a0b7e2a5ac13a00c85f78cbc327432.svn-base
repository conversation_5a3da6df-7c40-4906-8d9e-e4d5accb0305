﻿using System.Collections.Concurrent;
using System.Text.Json.Nodes;
using UFU.IoT.Models;

namespace HX.HRV.Shared.Models;

/// <summary>
/// 
/// </summary>
public class DeviceStatusData
{
	
	
    public DeviceModel Device { get; set; }

    public bool IsOffine { get; set; }
    public bool IsStoping { get; set; }
    public bool IsReading { get; set; }

    /// <summary>
    ///    是否标记
    /// </summary>
    public bool IsMarked { get; set; }
    /// <summary>
    ///  当前检测记录
    /// </summary>
    public PatientRecordModel RecordModel { get; set; }

    /// <summary>
    /// 设备数据
    /// </summary>
    public ConcurrentQueue<JsonNode> DataItems { get; set; }

    /// <summary>
    ///  采集数据
    /// </summary>
    public List<uint> BMPDatas { get; set; } = new List<uint>();

    public List<uint> SPO2Data { get; set; } = new List<uint>();
    public List<int> PPGData { get; set; } = new List<int>();
    public List<uint> OrgPPGData { get; set; } = new List<uint>();
    
    public List<uint> PPG_RData { get; set; } = new List<uint>();
    
    public List<uint> PPG_IData { get; set; } = new List<uint>();
    public List<short> ImuDatas { get; set; } = new List<short>();
    public List<float> SKINTEMPs { get; set; } = new List<float>();
    public List<float> Edas { get; set; } = new List<float>();
    
    public TimeSpan CollectTime { get; set; }

    /// <summary>
    /// 开始检测的MsgId
    /// </summary>
    public uint MsgId { get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public EnumDeviceStatus DeviceStatus=>GetDeviceStatus();


    private object _StatusLock = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; }

    private EnumDeviceStatus GetDeviceStatus()
    {
        lock (_StatusLock)
        {
			var enumDeviceStatus = EnumDeviceStatus.空闲;
			if (!Device.IsOnline) return EnumDeviceStatus.离线;
			if (RecordModel != null)
			{
				if (RecordModel?.EnumPatientCheckStatus == EnumPatientCheckStatus.Checking &&
					RecordModel?.CollectEndTime > DateTime.Now)
				{
					return EnumDeviceStatus.检测中;
				}
				if ((RecordModel?.EnumPatientCheckStatus == EnumPatientCheckStatus.Checking
					 && RecordModel?.CollectEndTime < DateTime.Now)
					|| RecordModel?.EnumPatientCheckStatus == EnumPatientCheckStatus.Checked)
				{
					return EnumDeviceStatus.完成;
				}
			}
			if (!Device.IsOnline)
			{
				return EnumDeviceStatus.离线;
			}
			return enumDeviceStatus;
		}
    }

    /// <summary>
    /// 
    /// </summary>
    public double Battery { get; set; }

    /// <summary>
    ///   进度
    /// </summary>
    public double Progress { get; set; }

    public int OffSet { get; set; }
    public long? StartTime { get; set; }
    public bool IsSendEnd { get; set; }


    private static Object _lock = new object();
    
    public int GetOffSet( )
    {
	    lock (_lock)
	    {
		    OffSet += 1;
		    return OffSet;
	    }
    }
    
    

    /// <summary>
    /// 清空数据
    /// </summary>
    public void ClearData()
    {
        BMPDatas.Clear();
        SPO2Data.Clear();
        PPGData.Clear();
        ImuDatas.Clear();
        Edas.Clear();
        SKINTEMPs.Clear();
        OrgPPGData.Clear();
        PPG_RData.Clear();
        PPG_IData.Clear();
        Errors.Clear();
    }
}