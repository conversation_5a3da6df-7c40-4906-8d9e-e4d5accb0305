﻿function exportHtml2PdfForPreview(isAutoPrint,pdfName) {
    let contentElement = document.getElementById("report-detail");
    const button = document.getElementById("btnPrint");
    const btnBack = document.getElementById("btnBack");
    const btnDownload = document.getElementById("btnDownload");
    btnBack.hidden = true;
    button.hidden = true;
    btnDownload.hidden = true;
    const originalHeight = contentElement.style.height;
    contentElement.style.height = "290mm";
    let opt = {
        filename: (pdfName===undefined?"report.pdf" : pdfName),
        html2canvas: {scale: 1},
        pagebreak: {mode: 'avoid-all',},
        jsPDF: {unit: "mm", format: "a4", orientation: "portrait"},
    };
    // 获取当前页面的 URL
    const url = new URL(window.location.href);

// 获取查询参数对象
    const params = new URLSearchParams(url.search);

// 获取指定参数的值
    if (isAutoPrint == 1) {
        html2pdf()
            .set(opt)
            .from(contentElement)
            .output('blob')  // 获取 Blob 格式的 PDF
            .then(function (pdfBlob) {
                // 将 Blob 对象转换为 URL
                const pdfUrl = URL.createObjectURL(pdfBlob);
                // 创建一个 iframe 元素并设置其 src 为 Blob URL
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = pdfUrl;
                // 将 iframe 插入到 DOM 中
                document.body.appendChild(iframe);
                // 当 iframe 加载完成后，自动打印
                iframe.onload = function () {
                    setTimeout(function () {
                        iframe.contentWindow.print();
                    }, 1500);
                };
                return true;
            })
            .then(() => {
                button.hidden = false;
                btnBack.hidden = false;
                btnDownload.hidden = false;
                contentElement.style.height = originalHeight;
            });
    } else {
        html2pdf()
            .set(opt)
            .from(contentElement)
            .toPdf()
            .get('pdf')
            .then(function (pdf) {
                //设置pdf页眉、页脚
                var totalPages = pdf.internal.getNumberOfPages();
                for (let i = 1; i <= totalPages; i++) {
                    pdf.setPage(i);
                    pdf.setFontSize(12);
                    pdf.setTextColor(50);
                    pdf.text(i + ' / ' + totalPages, (pdf.internal.pageSize.getWidth() - 30), (pdf.internal.pageSize.getHeight() - 8));//页脚
                }
            })
            .save()
            .then(() => {
                contentElement.style.height = originalHeight;
                button.hidden = false;
                btnBack.hidden = false;
                btnDownload.hidden = false;
                return true;
            });
    }
}

function ExportHtml2PdfForAutoPrint() {
    const button = document.getElementById('hiddeniframe').contentDocument.getElementById("btnPrint")
    button.click();
}

function AutoPrintPdf(url) {
    const iframe = document.getElementById('hiddeniframe');
    iframe.src = url;
    iframe.onload = function () {
         setTimeout(function () {
             iframe.contentDocument.getElementById("btnDownload").click();
        }, 4000);
    }
}

// wwwroot/js/fileDownload.js
function downloadFileFromUrl(url) {
    const link = document.createElement('a');
    link.href = url;
    link.target="_blank"; 
    link.click();
}