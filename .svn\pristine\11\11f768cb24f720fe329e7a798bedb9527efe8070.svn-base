﻿using System.Management;
using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Nodes;
using Windows.UI.Popups;
using HX.HRV.MAUI.Services;
using Makaretu.Dns;
using Microsoft.Extensions.Configuration;
using Microsoft.UI.Xaml.Controls;
using Microsoft.Web.WebView2.Core;

namespace HX.HRV.MAUI;

public partial class MainPage : ContentPage
{
    private readonly UDiskService _udiskService;
    private readonly SerialPortService _comPortService;
    private static string Host;

    public MainPage(IConfiguration config, SerialPortService _serialPortService, UDiskService uDiskService)
    {

        InitializeComponent();
		hybridWebView.SetInvokeJavaScriptTarget(this);
     
        
        var file = Path.GetDirectoryName(Environment.ProcessPath);
        var path = Path.Combine(file, "Host.txt");
        if (File.Exists(path))
        { 
            Host = File.ReadAllText(path);
        }
        else
        {
            var mdns = new MulticastService();
            mdns.AnswerReceived += (s, e) =>
            {
                // Is this an answer to host addresses?
                var addresses = e.Message.Answers.OfType<AddressRecord>();
                foreach (var address in addresses)
                {
                    Host = address.Address.ToString() + ":5222";
                }
            };
            try
            {
                mdns.Start();
                while (string.IsNullOrEmpty(Host))
                {
                    mdns.SendQuery("ms.hrv.huixin.local", type: DnsType.A);
                    Thread.Sleep(100);
                }
            }
            finally
            {
                mdns.Stop();
            }
        }
		hybridWebView.Loaded += (s, e) =>hybridWebView.EvaluateJavaScriptAsync($"window.location = 'http://{Host}'");
		_udiskService = uDiskService;
        _comPortService = _serialPortService;
        _comPortService.OnSerialPortReceived += (data,deviceSn) =>
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    var message = new
                    {
                        action = "onSerialPortReceived",
                        DeviceSN = deviceSn,
                        data = data
                    };
                    hybridWebView.SendRawMessage(JsonSerializer.Serialize(message));
                }
                catch (Exception e)
                {
                    Unit.Log("BeginInvokeOnMainThread "+e.Message);
                }
         
            });
        };
        _udiskService.StartMonitoring();
        _comPortService.StartMonitoring();
        
    }




	protected override void OnDisappearing()
    {
        base.OnDisappearing();
        // 获取底层的 WebView2 对象

        if (hiddenWebView.Handler.PlatformView is WebView2 webView2
            && webView2.CoreWebView2 != null
            && webView2.CoreWebView2.CookieManager != null)
        {
            // 清除所有 cookie
            webView2.CoreWebView2.CookieManager.DeleteAllCookies();
        }

        if (hybridWebView.Handler.PlatformView is WebView2 webView)
        {
            webView.CoreWebView2.CookieManager.DeleteAllCookies();
        }
    }

    private void hybridWebView_RawMessageReceived(object sender, HybridWebViewRawMessageReceivedEventArgs e)
    {
        var jsonNode = JsonNode.Parse(e.Message);
        var action = jsonNode["action"].GetValue<string>();
        var deviceSN = string.Empty;
        switch (action)
        {
            case "ExportToClient":
                Task.Run(async () => await UploadFileAsync(e.Message));
                break;
            case "getDeviceInfo":
                hybridWebView.SendRawMessage(JsonSerializer.Serialize(new
                {
                    action = jsonNode["action"],
                    data = _udiskService.DiskList
                }));
                break;
            case "printPdf":
            case  "printEmotionPdf":
                // 获取底层的 WebView2 对象
                if (hiddenWebView.Handler.PlatformView is WebView2 webView2)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        try
                        {
                            CoreWebView2PrintSettings printSettings = null;
                            printSettings = webView2.CoreWebView2.Environment.CreatePrintSettings();
                            printSettings.MarginBottom = 0;
                            printSettings.MarginLeft = 0;
                            printSettings.MarginRight = 0;
                            printSettings.MarginTop = 0.1;
                            var id = jsonNode["printId"].GetValue<string>();
                            if (action == "printPdf")
                            {
                                webView2.CoreWebView2.Navigate($"http://{Host}/client/report/report-detail/empty/{id}");
                            }
                            else
                            {
                                webView2.CoreWebView2.Navigate($"http://{Host}/client/report/EmotionDetailEmpty/{id}");
                            }
                            await Task.Delay(5000);
                            await webView2.CoreWebView2.PrintAsync(printSettings);
                        }
                        catch (Exception)
                        {
                            await File.AppendAllTextAsync($"error_{DateTime.Now:yyyy-M-d}.txt", e.Message);
                        }
                    });
                }
                break;
            case "toSerialPort":
                deviceSN = jsonNode["Device"]["SN"].GetValue<string>();
                var data = jsonNode.ToJsonString();
                _comPortService.SendMessageToComPort(data, deviceSN);
				hybridWebView.SendRawMessage(jsonNode.ToJsonString());
				break;
            case "openSerialPort":
                try
                {
                    Unit.Log("openSerialPort Start "+jsonNode.ToJsonString());
                    deviceSN = jsonNode["Device"]["SN"].GetValue<string>();
                    _comPortService.OpenSerialPort(deviceSN);
                    hybridWebView.SendRawMessage(jsonNode.ToJsonString());
                }
                catch (Exception exception)
                {
                    Unit.Log("openSerialPort ERROR "+exception.Message);
                }
                break;
            case "closeSerialPort":
                deviceSN = jsonNode["Device"]["SN"].GetValue<string>();
                _comPortService.CloseSerialPort(deviceSN);
                break;
        }
    }


    /// <summary>
    /// 复制文件夹及文件
    /// </summary>
    /// <param name="sourceFolder">原文件路径</param>
    /// <param name="destFolder">目标文件路径</param>
    /// <returns></returns>
    public bool CopyFolder(string sourceFolder, string destFolder)
    {
        try
        {
            //如果目标路径不存在,则创建目标路径
            if (!Directory.Exists(destFolder))
            {
                Directory.CreateDirectory(destFolder);
            }

            int index = 0;
            //得到原文件根目录下的所有文件
            string[] files = Directory.GetFiles(sourceFolder);
            foreach (string file in files)
            {
                index++;
                string name = Path.GetFileName(file);
                string dest = Path.Combine(destFolder, name);
                File.Copy(file, dest); //复制文件
            }

            //得到原文件根目录下的所有文件夹
            string[] folders = Directory.GetDirectories(sourceFolder);

            foreach (string folder in folders)
            {
                index++;
                string name = Path.GetFileName(folder);
                string dest = Path.Combine(destFolder, name);
                CopyFolder(folder, dest); //构建目标路径,递归复制文件
            }

            return true;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    private HttpClient _httpClient;

    public async Task UploadFileAsync(string json)
    {
        var jsonNode = JsonNode.Parse(json);
        var deviceSn = jsonNode["deviceSn"].GetValue<string>();
        var diskInfo =
            _udiskService.DiskList.FirstOrDefault(m =>
                m.ShowName.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
        if (diskInfo == null)
        {
            return;
        }

        diskInfo.IsParsing = true;
		string dir = Path.GetDirectoryName(Environment.ProcessPath);
		var targretPath = Path.Combine(dir, "bin", "data", diskInfo.ShowName,
            Guid.NewGuid().ToString("N"));
        //如果目标路径不存在,则创建目标路径
        if (!Directory.Exists(targretPath))
        {
            Directory.CreateDirectory(targretPath);
        }

        int copyIndex = 0;
        //得到原文件根目录下的所有文件
        string[] copyfiles = Directory.GetFiles(Path.Combine(diskInfo.LogicalName, "data"));
        foreach (string file in copyfiles)
        {
            copyIndex++;
            string name = Path.GetFileName(file);
            string dest = Path.Combine(targretPath, name);
            await Task.Delay(10);
            File.Copy(file, dest); //复制文件
            diskInfo.Progress = copyIndex * 50 / copyfiles.Length;
        }

        diskInfo.IsParsing = false;

        _ = Task.Run(() => DeleteFiles(diskInfo.LogicalName));
        var files = Directory.GetFiles(targretPath, "*.bin");
        _httpClient = new HttpClient();
        var recordId = jsonNode["recordId"].GetValue<string>();
        var index = 0;
        var filesCount = files.Length;
        var guid = Guid.NewGuid();
        var url = $"http://{Host}/api/v2.0/HRV_HX/PatientRecord/UploadFile";

        var sortedFiles = files.OrderBy(file =>
        {
            // 提取时间戳
            string fileName = Path.GetFileNameWithoutExtension(file);
            string[] parts = fileName.Split('-');
            // 提取时间戳 最后一个
            var timestampString = parts.ElementAt(parts.Length - 1);
            return long.Parse(timestampString);
            //return long.MaxValue; // 如果无法解析时间戳，放到最后
        }).ToArray();
        foreach (var fileName in sortedFiles)
        {
            try
            {
				var formData = new MultipartFormDataContent();
				index++;
				// 创建 MultipartFormDataContent 来处理文件上传
				// 读取文件内容
				var fileContent = new StreamContent(new FileStream(Path.Combine(fileName), FileMode.Open, FileAccess.Read));
				fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
				// 将文件添加到表单
				formData.Add(fileContent, "file", fileName);
				var name = Path.GetFileName(fileName);
				// 其他字段
				formData.Add(new StringContent(name), "name");
				formData.Add(new StringContent(guid.ToString("N")), "guid");
				formData.Add(new StringContent(recordId), "recordId");
				formData.Add(new StringContent((index == filesCount).ToString()), "isEnd");
				var progress = (index * 50 / filesCount) + 50;
				formData.Add(new StringContent(progress.ToString()), "progress");
				formData.Add(new StringContent(diskInfo.ShowName), "deviceSn");
				// 发送POST请求
				var response = await _httpClient.PostAsync(url, formData);
				var responseContent = await response.Content.ReadAsStringAsync();
                await Task.Delay(100);
			}
            catch (Exception ex)
            {

                continue;
            }
        }
    }

    private void DeleteFiles(string logicalName)
    {
        var files = Directory.GetFiles(Path.Combine(logicalName, $"data"), "*.bin");
        var index = 0;
        foreach (var file in files)
        {
            index++;
            File.Delete(file);
        }
    }
}