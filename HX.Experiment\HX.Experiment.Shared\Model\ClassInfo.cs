using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UFU.CoreFX.Models;

namespace HX.Experiment.Shared.Model;


[DataEntity("2507280300000002")]
public class ClassInfo
{
    /// <summary>
    /// 班级ID
    /// </summary>
    public string Id { get; set; }

    [Required]
    public string Name { get; set; }

    /// <summary>
    /// 第几届
    /// </summary>
    public string Year { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    /// <returns></returns>
    public string Major   { get; set; }

    /// <summary>
    ///  描述或者备注
    /// </summary>
    public string Description { get; set; }
    
    [NotMapped]
    public List<DataModel<StudentInfo>> Students { get; set; } = new();
}