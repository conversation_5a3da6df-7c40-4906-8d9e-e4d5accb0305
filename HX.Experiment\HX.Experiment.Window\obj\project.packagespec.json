﻿"restore":{"projectUniqueName":"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\HX.Experiment.Window.csproj","projectName":"HX.Experiment.Window","projectPath":"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\HX.Experiment.Window.csproj","outputPath":"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0-windows"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0-windows7.0":{"targetAlias":"net9.0-windows","projectReferences":{"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj":{"projectPath":"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0-windows7.0":{"targetAlias":"net9.0-windows","dependencies":{"DeviceId":{"target":"Package","version":"[6.9.0, )"},"EPPlus":{"target":"Package","version":"[7.5.1, )"},"Microsoft.AspNetCore.Components.WebView.WindowsForms":{"target":"Package","version":"[9.0.90, )"},"Microsoft.Extensions.Http":{"target":"Package","version":"[9.0.*, )"},"OpenCvSharp4.Extensions":{"target":"Package","version":"[4.11.0.20250507, )"},"OpenCvSharp4.Windows":{"target":"Package","version":"[4.11.0.20250507, )"},"System.Management":{"target":"Package","version":"[9.0.7, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"},"Microsoft.WindowsDesktop.App.WindowsForms":{"privateAssets":"none"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}