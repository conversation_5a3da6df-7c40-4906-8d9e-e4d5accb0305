{"GlobalPropertiesHash": "4mWAzIlC9wdr9HscxB5fdeim4YNXzEr0UHDQ8pKI5m0=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["nuQFhe1YMh7N84cIeXMfLXShJNtaTrhlfKC/OIV8xDg=", "xR9cy7oHbE+JjSphrvqR2gdSlpfpYaZyeB8xqGVRANA=", "Y497/N+any2kxaOHuLy8f+AXySOPn87az9nbhz2eqQ8=", "631tM3QhCB6BTxpfLtDVBxKhV8AMrv0DVnTmhxfb+9g=", "GDsMsJrRARWNJxB1kXXwYS8cql4GwrTxIbfX8Zr2eB0="], "CachedAssets": {"nuQFhe1YMh7N84cIeXMfLXShJNtaTrhlfKC/OIV8xDg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\appsettings.Development.json", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tswichycbp", "Integrity": "c/lfngzrIF/BxNxQwHaX/Pop1wh4aMKu8dUEyzjHcew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 127, "LastWriteTime": "2025-07-18T06:07:11.8993094+00:00"}, "xR9cy7oHbE+JjSphrvqR2gdSlpfpYaZyeB8xqGVRANA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\appsettings.json", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tswichycbp", "Integrity": "c/lfngzrIF/BxNxQwHaX/Pop1wh4aMKu8dUEyzjHcew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 127, "LastWriteTime": "2025-07-18T06:07:11.8983084+00:00"}, "Y497/N+any2kxaOHuLy8f+AXySOPn87az9nbhz2eqQ8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\service-worker.js", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "service-worker#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p5osetst1n", "Integrity": "TctPZVfdh8ggpX4P91CPLt37uuLQnm09TrEfmVKDZu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\service-worker.js", "FileLength": 269, "LastWriteTime": "2025-06-19T12:13:03.7141952+00:00"}, "631tM3QhCB6BTxpfLtDVBxKhV8AMrv0DVnTmhxfb+9g=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\service-worker.published.js", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "service-worker.published#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "elnr3bjcxu", "Integrity": "zjA4CYzC6Ai3fWKr4SDmSI/FecKSDjrZnDGkCsR5VPg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\service-worker.published.js", "FileLength": 2292, "LastWriteTime": "2025-06-19T12:13:03.6831686+00:00"}}, "CachedCopyCandidates": {}}