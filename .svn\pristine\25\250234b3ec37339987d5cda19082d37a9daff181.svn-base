﻿using System.Collections.Concurrent;
using System.Text.Json.Nodes;
using AntDesign;
using UFU.IoT.Models;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Shared.Models.ViewModel;

/// <summary>
/// 
/// </summary>
public class DeviceStatusViewModel
{

	/// <summary>
	///   USB是否连接
	/// </summary>
	public bool IsUsbLinked { get; set; }

    /// <summary>
    /// 
    /// </summary>
	public bool IsUsbPort { get; set; }
	public DeviceModel Device { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string DeviceSN { get; set; }
    /// <summary>
    ///  当前检测记录
    /// </summary>
    public PatientRecordModel RecordModel { get; set; }

    /// <summary>
    /// 设备数据
    /// </summary>
    public ConcurrentQueue<JsonNode> DataItems { get; set; }

    /// <summary>
    ///  采集数据
    /// </summary>
    public List<uint> BMPDatas { get; set; } = new List<uint>();
    public List<uint> SPO2Data { get; set; } = new List<uint>();
    public List<int> PPGData { get; set; } = new List<int>();
    public List<short>[] ImuDatas { get; set; } = {
	    new(),
	    new(),
	    new()
    };
    public List<float> SKINTEMPs { get; set; } = new List<float>();
    public List<float> SCRDatas { get; set; } = new List<float>();
    public List<float> SCLDatas { get; set; } = new List<float>();
	public List<uint> OrgPPGData { get; set; } = new List<uint>();
	/// <summary>
	/// 皮肤电阻
	/// </summary>
	public List<float> EDAs { get; set; }= new List<float>();
	
	public List<Dictionary<string,List<float>>> ImuList { get; set; }= new();

	

	public Action<List<int>> OnRecivePPGData { get; set; }
	public Action<uint> OnSPO2Data { get; set; }
	public Action<uint> OnReciveBMPDatas { get; set; }
	public Action<List<uint>> OnOrgPPGData { get; set; }
	public Action<List<float>> OnReciveEDAsData { get; set; }
	public Action<List<float>> OnReciveSCRDatasData { get; set; }
	public Action<List<float>> OnReciveSCLDatasData { get; set; }


	public DeviceStatusViewModel()
	{
		
	}

	public event Action<JsonNode> OnReceiveData;
	
    public TimeSpan CollectTime { get; set; }
    /// <summary>
    /// 开始检测的MsgId
    /// </summary>
    public uint MsgId { get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public EnumDeviceStatus DeviceStatus { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; }

    public string ColorString => CollectStatusToColorMap[DeviceStatus];

    private static Dictionary<EnumDeviceStatus, string> CollectStatusToColorMap = new()
    {
        { default, "#fff" },
        { EnumDeviceStatus.空闲, "#1f88ff" },
        { EnumDeviceStatus.检测中, "#00b057" },
        { EnumDeviceStatus.USB传输中, "#00b057" },
        { EnumDeviceStatus.USB传输完成, "#1f88ff" },
		{ EnumDeviceStatus.完成, "#ffbd3e" },
        { EnumDeviceStatus.报警, "#ff3e3e" },
        { EnumDeviceStatus.离线, "#fff" },
    };

    /// <summary>
    ///   颜色
    /// </summary>
    public string ColorLinearString => CollectStatusToHeadColorMap[DeviceStatus];

    private static Dictionary<EnumDeviceStatus, string> CollectStatusToHeadColorMap => new()
    {
        { default, "background: linear-gradient(90deg, #a6b8cd, #BBD2EC);" },
        { EnumDeviceStatus.空闲, "background: linear-gradient(90deg, rgb(0, 96, 204),rgb(86, 151, 222));" },
        { EnumDeviceStatus.检测中, "background: linear-gradient(90deg, #05b55c, #7cddb0)" },
        { EnumDeviceStatus.离线, "background: linear-gradient(90deg, #C7DDF7, #BBD2EC);" },
        { EnumDeviceStatus.完成, "background: linear-gradient(90deg, #ffbd3e, #ffbd3e);" },
        { EnumDeviceStatus.报警, "background: linear-gradient(90deg, #FFD991, #FEB11D);" },
        { EnumDeviceStatus.USB传输中, "background: linear-gradient(90deg, rgb(0, 96, 204),rgb(86, 151, 222));" },
        { EnumDeviceStatus.USB传输完成, "background: linear-gradient(90deg, #C7DDF7, #BBD2EC);" },
    };

    /// <summary>
    /// 
    /// </summary>
    public double Battery { get; set; }
    public string ConnectionType { get; set; } = "WIFI";
    /// <summary>
    ///   进度
    /// </summary>
    public double Progress { get; set; }

    public bool IsBegining { get; set; }
    public bool IsPrint { get; set; }
    public bool IsCharging { get; set; } = true;
    public event Action OnDeviceStatusChange;

    /// <summary>
    /// 清空数据
    /// </summary>
    public void ClearData()
    {
	    BMPDatas.Clear();
	    SPO2Data.Clear();
	    PPGData.Clear();
	    ImuDatas.ForEach(i=>i.Clear());
	    EDAs.Clear();
	    SKINTEMPs.Clear();
	    RecordModel=null;
    }

	public void NotifyPropertyChanged()
    {
	    OnDeviceStatusChange?.Invoke();
    }


	public event Action<string> OnReceiveClientMessage;

    public void NotifyRecievedClientMessage(string message)
	{
		OnReceiveClientMessage?.Invoke(message);
	}


	public virtual void TriggerOnReceiveData(JsonNode obj)
	{
		OnReceiveData?.Invoke(obj);
	}
}