﻿@page "/client/system/device-config-edit"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Utils
@using UFU.IoT.Shared.Models
@layout HRVDefaultLayout
@attribute [Permission("系统配置/设备串口设置列表/设备串口设置", IsMenu = true)]
@inject IJSRuntime js
@implements IDisposable
<MCol Cols="12" Class="pa-4">
	<MCard Class="pa-4" Title="频率设置">
		<MRow>
			<MCol Cols="12" Md="3">
				<MTextField @bind-Value="SystemConfig.SystemRateConfig.HRRate" Dense
							Outlined>
				</MTextField>
			</MCol>
			<MCol Cols="12" Md="3">
				<MTextField @bind-Value="SystemConfig.SystemRateConfig.EDARate" Dense
				            Outlined
				            Label="皮肤电阻">
				</MTextField>
			</MCol>
			<MCol Cols="12" Md="3">
				<MTextField @bind-Value="SystemConfig.SystemRateConfig.ENVRate" Dense
				            Label="气压 环境温度 光照" Outlined>
				</MTextField>
			</MCol>
			<MCol Cols="12" Md="3">
				<MTextField @bind-Value="SystemConfig.SystemRateConfig.IMURate" Dense

				            Label="九轴(IMU)" Outlined>
				</MTextField>
			</MCol>
			<MCol Cols="12" Md="3">
				<MTextField @bind-Value="SystemConfig.SystemRateConfig.PPGRate" Dense
				            Label="PPGRate" Outlined>
				</MTextField>
			</MCol>
			<MCol Cols="12" Md="3">
				<MTextField @bind-Value="SystemConfig.SystemRateConfig.SPO2Rate"
				            Dense
				            Label="血氧" Outlined>
				</MTextField>
			</MCol>
		</MRow>
	</MCard>
</MCol>
<MCol Cols="12">
	<MCard Class="pa-4" Title="设备版本设置">
		<MRow>
			<MCol Cols="2">
				<MSwitch @bind-Value="ServerLocation"
				         Flat
				         Label="@(ServerLocation ? "云端" : "本地")"></MSwitch>
			</MCol>
			<MCol Cols="2">
				<MSwitch @bind-Value="SoftwareVersion"
						 Flat Label="@(SoftwareVersion ? "医疗版" : "科研版")"></MSwitch>
			</MCol>
			<MCol Cols="2">
				<MSwitch @bind-Value="IsOnline"
				         Flat Label="@(IsOnline ? "在线" : "离线")"></MSwitch>
			</MCol>

		</MRow>
	</MCard>
</MCol>
<MCol Cols="12" Md="4">
	<MButton OnClick="SendSerialPortMsgToClient">设置</MButton>
</MCol>

@code {

	protected override async Task OnInitializedAsync() {
		await base.OnInitializedAsync();
	}

	protected override void OnInitialized() {
		SystemConfig = DeviceStateService.SystemConfig.TryDeepClone();
		SendOpenSerialPortMsgToClient();
		base.OnInitialized();
	}
	[SupplyParameterFromQuery]
	[Parameter] public string id { get; set; }
	[Inject] IPopupService PopupService { get; set; }
	[Inject] IConfiguration Configuration { get; set; }
	[Inject] public StateService stateService { get; set; }
	[Inject] public DeviceStateService DeviceStateService { get; set; }

	private HxSystemConfigModel SystemConfig;
	private string DeviceSN = "";

	private List<DeviceStatusViewModel> DeviceDataList => DeviceStateService.DeviceDataList;


	private bool ServerLocation { get; set; }

	private bool SoftwareVersion { get; set; }

	private bool IsOnline { get; set; }


	/// <summary>
	/// 发送配置消息到客户端
	/// </summary>
	private async Task SendSerialPortMsgToClient() {
		var device = DeviceDataList.FirstOrDefault(m => m.Device?.Id == id);
		var msgJson = new {
			MsgId = 555555,
			action = "toSerialPort",
			//时间戳
			Time = DateTime.Now.ToUnixMs(),
			Device = new {
				SN = device?.Device.DeviceSN,
				Type = device?.Device.TypeId,
			},
			CMD = (int)BinaryCMD.Write,
			Data = new {
				ServerLocation = ServerLocation ? 1 : 0,
				SoftwareVersion = SoftwareVersion ? 1 : 0,
				IsOnline = IsOnline ? 1 : 0,
				//PPG
				PPGRate = SystemConfig.SystemRateConfig.PPGRate,
				//九轴
				IMURate = SystemConfig.SystemRateConfig.IMURate,
				//SKT采样率 皮肤温度
				SKTRate = SystemConfig.SystemRateConfig.SKTRate,
				//血氧
				SPO2Rate = SystemConfig.SystemRateConfig.SPO2Rate,
				//心率
				HRRate = SystemConfig.SystemRateConfig.HRRate,
				//气压 环境温度 光照
				ENVRate = SystemConfig.SystemRateConfig.ENVRate,
				//皮肤电阻
				EDARate = SystemConfig.SystemRateConfig.EDARate,
			}
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeAsync<object>("SendMessageToClientByMsg", msgStr);
		await PopupService.EnqueueSnackbarAsync("设置成功",AlertTypes.Success);
	}

	/// <summary>
	///  发送打开串口的消息到客户端
	/// </summary>
	private async Task SendOpenSerialPortMsgToClient() {
		var device = DeviceDataList.FirstOrDefault(m => m.Device?.Id == id);
		var msgJson = new {
			action = "openSerialPort",
			Time = DateTime.Now.ToUnixMs(),
			Device = new {
				SN = device?.Device.DeviceSN,
				Type = device?.Device.TypeId,	
			}
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeAsync<object>("SendMessageToClientByMsg", msgStr);
		await	PopupService.EnqueueSnackbarAsync("串口已打开", AlertTypes.Success);
	}

	private async Task SendCloseSerialPortMsgToClient() {
		var device = DeviceDataList.FirstOrDefault(m => m.Device?.Id == id);
		var msgJson = new {
			action = "closeSerialPort",
			Time = DateTime.Now.ToUnixMs(),
			Device = new {
				SN = device?.Device.DeviceSN,
				Type = device?.Device.TypeId,
			}
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeVoidAsync("SendMessageToClientByMsg", msgStr);
	}
	public void Dispose() {
		SendCloseSerialPortMsgToClient();
	}

}