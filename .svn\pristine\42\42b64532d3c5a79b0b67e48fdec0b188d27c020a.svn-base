﻿using System.Collections.Concurrent;
using System.Text.Json;

namespace HX.HRV.MAUI.Services;

using System;
using System.Management;
using System.IO.Ports;
using Microsoft.Win32;
using System.Text.Json.Nodes;
using Tmds.Linux;
using Makaretu.Dns;
using Windows.Storage.Streams;

public class SerialPortService:IDisposable
{
    // 串口设备的 ClassGuid
    const string SerialPortClassGuid = "{4d36e978-e325-11ce-bfc1-08002be10318}";

    public class SerialPortDevice
    {
        public string BusReportedDeviceDesc { get; set; }

        public string FriendlyName { get; set; }

        public string Parent { get; set; }

        public string InstanceId { get; set; }
        public string? PortName { get; internal set; }

        /// <summary>
        /// 
        /// </summary>
        public string DeviceSN { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public SerialPortService()
    {
        LoadPorts();
    }

    public List<SerialPortDevice> SerialPortDevices { get; private set; } = [];
    private ManagementEventWatcher _insertWatcher;
    private ManagementEventWatcher _removeWatcher;
    /// <summary>
    ///  收到串口数据
    /// </summary>
    public event Action<string,string> OnSerialPortReceived;

	public void StartMonitoring()
    {
        //监视设备创建事件
        WqlEventQuery createQuery = new(
            "__InstanceCreationEvent",
            new TimeSpan(0, 0, 1),
            $"TargetInstance ISA 'Win32_PnPEntity' AND TargetInstance.ClassGuid = '{SerialPortClassGuid}'");
        _insertWatcher = new ManagementEventWatcher(createQuery);
        _insertWatcher.EventArrived += OnSerialPortInserted;
        _insertWatcher.Start();

        // 监视设备移除事件
        WqlEventQuery removeQuery = new(
            "__InstanceDeletionEvent",
            new TimeSpan(0, 0, 1),
            $"TargetInstance ISA 'Win32_PnPEntity' AND TargetInstance.ClassGuid = '{SerialPortClassGuid}'");
        _removeWatcher = new ManagementEventWatcher(removeQuery);
        _removeWatcher.EventArrived += OnSerialPortRemoved;
        _removeWatcher.Start();
    }

    public void StopMonitoring()
    {
        _insertWatcher?.Stop();
        _removeWatcher?.Stop();
    }

    /// <summary>
    ///  发送数据
    /// </summary>
    /// <param name="message"></param>
    /// <param name="deviceSN"></param>
    public void SendMessageToComPort(string message, string deviceSN)
    {
        var existsPort = _serialPorts.FirstOrDefault(m => m.Key== deviceSN)
            .Value;
        if (existsPort == null||!existsPort.IsOpen)
        {
            OpenSerialPort(deviceSN);
            existsPort = _serialPorts
                .FirstOrDefault(m => m.Key == deviceSN)
                .Value;
        }
        var bytes = JsonToBytes(message);
        var hex = JsonToBytes(message).ToArray().ToHex();
        try
        {
			existsPort.Write(bytes.ToArray(), 0, bytes.Count);
		}
        catch (Exception)
        {

           
        }
    }



    private List<byte> JsonToBytes(string json)
    {

        var bytes = new List<byte>();


        var jsonBytes = System.Text.Encoding.UTF8.GetBytes(json);

		ushort json_size = (ushort)jsonBytes.Length;
        var bin_size = 0;
		//2 包头 4 包长度 1 压缩 2 json长度   json_size-Json数据  bin数据 2 crc16
		var size = 2 + 4 + 1 + 2 + json_size + bin_size + 2;


        //头
        bytes.Add(0x55);
		bytes.Add(0x80);

        
        //整个数据长度
        var  byteSize = BitConverter.GetBytes(size);
		bytes.AddRange(byteSize);

        //压缩
        bytes.Add(0x00);

        //json数据的长度
        var jsonBytesSize = BitConverter.GetBytes(json_size);
        bytes.AddRange(jsonBytesSize);

        //Json的数据
        bytes.AddRange(jsonBytes);

        AppendCRC16(bytes);
        return bytes;

	}


	public static void  AppendCRC16(List<byte> data )
	{
		byte[] bytes = BitConverter.GetBytes(CRC16(data.ToArray()));
		data.AddRange(bytes);
	}

	public static ushort CRC16(byte[] data)
	{
		ushort num = ushort.MaxValue;
		for (int i = 0; i < data.Length; i++)
		{
			num ^= data[i];
			for (int j = 0; j < 8; j++)
			{
				num = ((((uint)num & (true ? 1u : 0u)) != 0) ? ((ushort)((uint)(num >> 1) ^ 0xA001u)) : ((ushort)(num >> 1)));
			}
		}
		return num;
	}


	/// <summary>
	///  串口 插入
	/// </summary>
	/// <param name="o"></param>
	/// <param name="eventArrivedEventArgs"></param>
	private void OnSerialPortInserted(object o, EventArrivedEventArgs eventArrivedEventArgs)
    {
        LoadPorts();
    }

    /// <summary>
    ///  串口 移除
    /// </summary>
    /// <param name="o"></param>
    /// <param name="eventArrivedEventArgs"></param>
    private void OnSerialPortRemoved(object o, EventArrivedEventArgs eventArrivedEventArgs)
    {
        LoadPorts();
    }


    /// <summary>
    /// 从注册表中加载端口
    /// </summary>
    private void LoadPorts()
    {
        ManagementObjectSearcher searcher = new ManagementObjectSearcher(
            $"SELECT * FROM Win32_PnPEntity WHERE ClassGuid='{SerialPortClassGuid}'");
        List<string> deviceSNs = new();
        foreach (ManagementObject propertyObj in searcher.Get())
        {
            var propertyObjargs = new object[]
            {
                new[]
                {
                    "DEVPKEY_Device_BusReportedDeviceDesc", 
                    "DEVPKEY_Device_FriendlyName", 
                    "DEVPKEY_Device_Parent",
                    "DEVPKEY_Device_InstanceId"
                },
                null
            };
            propertyObj.InvokeMethod("GetDeviceProperties", propertyObjargs);
            var mbos = (ManagementBaseObject[])propertyObjargs[1];
            var keys = (string[])propertyObjargs[0];
            if (mbos.Length > 0)
            {
                var data = mbos[0].Properties.OfType<PropertyData>().FirstOrDefault(p => p.Name == "Data");
                if (data != null && data.Value?.ToString()?.Contains("Huixin", StringComparison.OrdinalIgnoreCase) ==
                    true)
                {
                    var properties = keys.Zip(mbos,
                            (k, v) => new
                            {
                                Key = k,
                                Value = v.Properties.OfType<PropertyData>().FirstOrDefault(p => p.Name == "Data").Value
                                    ?.ToString()
                            })
                        .ToDictionary(x => x.Key, x => x.Value);
                    var parent = properties["DEVPKEY_Device_Parent"];
                    var deviceSn = parent.Split('\\').Last();
                    deviceSNs.Add(deviceSn);
                    if (SerialPortDevices.Any(x => x.DeviceSN == deviceSn))
                    {
                        return;
                    }

                    SerialPortDevice comport = new()
                    {
                        BusReportedDeviceDesc = properties["DEVPKEY_Device_BusReportedDeviceDesc"],
                        FriendlyName = properties["DEVPKEY_Device_FriendlyName"],
                        Parent = properties["DEVPKEY_Device_Parent"],
                        InstanceId = properties["DEVPKEY_Device_InstanceId"],
                        DeviceSN = deviceSn
                    };
                    var keyCom = Registry.LocalMachine.OpenSubKey(
                        $@"SYSTEM\CurrentControlSet\Enum\{properties["DEVPKEY_Device_InstanceId"]}\Device Parameters");
                    if (keyCom != null)
                    {
                        var port = (string)keyCom.GetValue("PortName");
                        comport.PortName = port;
                    }
                    SerialPortDevices.Add(comport);
                }
            }

            if (SerialPortDevices.Count > 0 && SerialPortDevices.Any(x => !deviceSNs.Contains(x.DeviceSN)))
            {
                //移除列表中的设备
                SerialPortDevices = SerialPortDevices.Where(m => deviceSNs.Contains(m.DeviceSN)).ToList();
            }
        }
    }
    private ConcurrentDictionary<string, SerialPort> _serialPorts = new();
    
    /// <summary>
    ///  打开串口
    /// </summary>
    /// <param name="portName"></param>
    public  void OpenSerialPort(string deviceSN)
    {
        var exists = _serialPorts
            .FirstOrDefault(m => m.Key == deviceSN).Value;
        if (exists is { IsOpen: true})
        {
            var msgJson = new {
                MsgId = 66666666,
                action = "toSerialPort",
                //时间戳
                Time = DateTime.Now.ToUnixMs(),
                Device = new {
                    SN = deviceSN,
                },
                CMD = 3,
                Data = new {
                }
            };
            SendMessageToComPort(JsonSerializer.Serialize(msgJson),deviceSN);
            return;
        }
        try
        {
			var portName = SerialPortDevices.FirstOrDefault(x => x.DeviceSN == deviceSN)?.PortName;
			var serialPort = new SerialPort(portName);
			serialPort.BaudRate = 9600;//波特率
			serialPort.Parity = Parity.None;//校验位
			serialPort.StopBits = StopBits.One;//停止位
			serialPort.DataBits = 8;//数据位
			serialPort.Handshake = Handshake.None;
			serialPort.ReadTimeout = 1500;
			serialPort.DtrEnable = true;//启用数据终端就绪信息
			serialPort.ReceivedBytesThreshold = 1;//DataReceived触发前内部输入缓冲器的字节数
			serialPort.DataReceived += SerialPort_DataReceived;
			serialPort.Open();
            
            
            //发送查询命令数据
            var msgJson = new {
                MsgId = 66666666,
                action = "toSerialPort",
                //时间戳
                Time = DateTime.Now.ToUnixMs(),
                Device = new {
                    SN = deviceSN,
                },
                CMD = 3,
                Data = new {
                }
            };
            _serialPorts.TryAdd(deviceSN, serialPort);
            SendMessageToComPort(JsonSerializer.Serialize(msgJson),deviceSN);
		}
        catch (Exception ex)
        { 
            ;
        }
    }
    /// <summary>
    ///  关闭串口
    /// </summary>
    /// <param name="portName"></param>
    public void CloseSerialPort(string deviceSN)
    {
        var exists = _serialPorts.FirstOrDefault(m => m.Key == deviceSN)
            .Value;
        if (exists != null)
        {
            exists.Close();
			_serialPorts.TryRemove(deviceSN, out exists);
		}
    }

    /// <summary>
    ///  串口数据接收
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            var dataBuffer = new List<byte>();
            var port = (SerialPort)sender;
            var length = port.BytesToRead;
            var buffer = new byte[length];
            port.Read(buffer, 0, length);
            dataBuffer.AddRange(buffer);
            var data = ParseDataToJson(dataBuffer.ToArray());
           var serialPortDevice = SerialPortDevices.FirstOrDefault(x => x.PortName == port.PortName);
			if (serialPortDevice == null) return;
           OnSerialPortReceived?.Invoke(data,serialPortDevice.DeviceSN);
		}
        catch (Exception ex)
        {
        }
		static string ParseDataToJson(byte[] datas)
		{
			var head = datas[0..2].ToHex();
			var size = datas[2..6].ToHex();
			var totalSize = BitConverter.ToUInt32(datas[2..6]);
			var zip = datas[6];
			var jsonSize = BitConverter.ToUInt16(datas[7..9]);
			var json = System.Text.Encoding.UTF8.GetString(datas, 9, jsonSize);
			var bin = datas[(9 + jsonSize)..^2].ToHex("");
			var binData = datas[(9 + jsonSize)..^2];
			var crc = BitConverter.ToUInt16(datas[^2..datas.Length]);
			var jsonObj = JsonNode.Parse(json);
			return jsonObj.ToJsonString();
		}
	}




    public void Dispose()
    {
        StopMonitoring();
    }
}