<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="RD-251.26927.67">
    <data-source name="HX.HRV@127.0.0.1" uuid="9ed9a8f4-4043-4dc7-ae62-74d696926b7e">
      <database-info product="PostgreSQL" version="17.3 (Debian 17.3-3.pgdg120+1)" jdbc-version="4.2" driver-name="PostgreSQL JDBC Driver" driver-version="42.7.3" dbms="POSTGRES" exact-version="17.3" exact-driver-version="42.7">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <auth-provider>no-auth</auth-provider>
      <schema-mapping>
        <introspection-scope>
          <node kind="database">
            <name qname="HX.HRV" />
            <name qname="HX.HRV.SCI" />
            <node kind="schema" qname="public" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>