﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="EPPlus" Version="7.5.1" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.WindowsForms" Version="9.0.90" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.*" />
        <PackageReference Include="OpenCvSharp4.Extensions" Version="4.11.0.20250507" />
        <PackageReference Include="OpenCvSharp4.Windows" Version="4.11.0.20250507" />
        <PackageReference Include="System.Management" Version="9.0.7" />
        <PackageReference Include="DeviceId" Version="6.9.0" />
    </ItemGroup>

    <ItemGroup>
        <Content Update="wwwroot\**">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\HX.Experiment.Shared\HX.Experiment.Shared.csproj" />
    </ItemGroup>

</Project>