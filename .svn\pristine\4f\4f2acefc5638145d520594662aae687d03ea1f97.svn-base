﻿@using System.Net.WebSockets
@using System.Text
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using UFU.CoreFX.Utils
@using UFU.IoT.Shared.Models
@implements  IDisposable
@inject  DeviceStateService DeviceStateService

<MCard Class="text-center" Style="background-color: #E8F5E9">
    <MCardTitle Style="background: linear-gradient(90deg, #7EE0AE, #04B55C);"
    Class="grey lighten-2  dialog-header text-center">
        <div class="hx-card-title" style="font-size: 2rem">
            佩戴设备
        </div>
        <MButton OnClick="@CloseDialogEvent" Class="ml-auto" Icon>
            <MIcon>mdi-close</MIcon>
        </MButton>
    </MCardTitle>
    <MCardText Class="d-flex justify-center flex-column align-center ">
        <MRow Style="width: 100%;" Class="mt-2">
            <MCol Cols="5">
                <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                    姓名:
                    <span>@PatientModel.Name</span>
                </MLabel>
            </MCol>
            <MCol Cols="4">
                <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                    年龄:
                    <span>@PatientModel.Age</span>
                </MLabel>
            </MCol>
            <MCol Cols="3">
                <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                    性别:
                    <span>@PatientModel.Sex</span>
                </MLabel>
            </MCol>
            <MCol Cols="12" Style="width: 100%;">
                <MDivider></MDivider>
            </MCol>
        </MRow>
        <MRow Style="width: 100%;" >
            <MCol >
                <MSelect Items="SelectedDeviceDataList"
                TValue="string"
                TItem="DeviceStatusViewModel"
                TItemValue="string"
                @bind-Value="_SelectedDeviceId"
                OnChange="(e)=>HandleSelectDevice(e)"
                ItemText="(DeviceStatusViewModel u) => string.Join('-', u.Device.Name, u.DeviceStatus)"
                ItemValue="( u)  => u.Device.Id"
                ItemDisabled="( u)  => u.DeviceStatus!=EnumDeviceStatus.空闲"
                Solo
                Dense
                SingleLine>
                    <PrependContent>
                        <MLabel Class="mr-2">选择设备:</MLabel>
                    </PrependContent>
                </MSelect>
            </MCol>
        </MRow>

        <MCard Class="text-center d-flex flex-column  justify-center "
        Style=" width: 100px; height: 100px;background-color: #4caf50;  border-radius: 10px;">
            <MLabel Class="text-h5" Style="color: white" Color="white"> @(_SelectedDevice.Device?.Name)</MLabel>
        </MCard>
        <MLabel Style="color: #0288d1; margin-top: 10px;font-size: 1.5rem">请找到显示以上内容的设备进行佩戴</MLabel>
    </MCardText>
    <MCardActions Class="text-center d-flex justify-center justify-center">
        <MButton OnClick="HandleSubmit"
        Color="#04B55C"
        Style=" width: 200px;color: white;  font-size:1.5rem; border-radius: 5px;">
            已佩戴,开始检测
        </MButton>
    </MCardActions>
</MCard>

@code {
    private const string BaseUrl = "/api/v2.0/IoT/";
    [Parameter] public EventCallback CloseDialogEvent { get; set; }
    [Inject] private InternalHttpClientService InternalHttpClientService { get; set; }
    IPopupService PopupService => DeviceStateService.PopupService;
    [Parameter] public string DeviceId { get; set; }
    [Parameter] public PatientModel PatientModel { get; set; }
    private DeviceStatusViewModel _SelectedDevice => DeviceDataList.FirstOrDefault(m => m.Device?.Id == _SelectedDeviceId);

    private ClientWebSocket SelectClientWebSocket => DeviceStateService?.ClientSocket;
    private List<DeviceStatusViewModel> DeviceDataList => DeviceStateService.DeviceDataList;

    private List<DeviceStatusViewModel> SelectedDeviceDataList => DeviceDataList.Where(m => m.DeviceStatus == EnumDeviceStatus.空闲)?.ToList() ?? new List<DeviceStatusViewModel>();

    private string _SelectedDeviceId { get; set; }

    protected override void OnInitialized()
    {
        _SelectedDeviceId = DeviceId;
        DeviceStateService.OnAfterBeginCheck+=OnAfterBeginCheck;
        base.OnInitialized();
    }

    private void OnAfterBeginCheck() {
        PopupService.HideProgressCircular();
    }
    
    

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }


    [Parameter] public int CheckTimeValue { get; set; }
    private PatientRecordModel RecordModel { get; set; } = new();

    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    private async Task HandleSubmit()
    {
        RecordModel.CheckTime = CheckTimeValue;
        RecordModel.CollectEndTime = DateTime.Now.AddMinutes(RecordModel.CheckTime);
        RecordModel.PatientId = PatientModel.Id;
        RecordModel.DeviceId = _SelectedDeviceId;
        RecordModel.CollectStartTime = DateTime.Now;
        RecordModel.Patient = PatientModel;
        RecordModel.BuildReportStatus =BuildReportStatus.UnKnown;
        RecordModel.EnumPatientCheckStatus = EnumPatientCheckStatus.Readying;
        RecordModel.DeviceId = _SelectedDevice.Device?.Id;
        var res = await InternalHttpClientService.AddRecordAsync(RecordModel);
        if (res.Success)
        {
            _SelectedDevice.RecordModel = res.Data.Data;
            var config = await InternalHttpClientService.GetSystemConfigAsync();
            var rateConfig = config?.Data?.Data?.SystemRateConfig;
            rateConfig ??= DefalultData.SystemRateConfig;
            var isSend = false;
            _startDeviceTimerCts?.Cancel();
            await SendBeganMsgToDevice(_SelectedDevice.Device.Id, rateConfig);
            PopupService.ShowProgressCircular();
            StartDeviceWaitTimer();
            await CloseDialogEvent.InvokeAsync();
            PopupService.HideProgressCircular();
        }else{
            PopupService.HideProgressCircular();
			await PopupService.EnqueueSnackbarAsync("保存失败"+res.Message, AlertTypes.Error);
        }
    }

    [Inject] StateService _stateService { get; set; }


    

    private async Task SendBeganMsgToDevice(string deviceId, SystemRateConfig config)
    {
        var device = _SelectedDevice.Device;
        var msgJson = new
        {
            MsgId = 11111,
            //时间戳
            Time = DateTime.Now.ToUnixMs(),
            Device = new
            {
                SN = device.DeviceSN,
                Type = device.TypeId,
            },
            CMD = (int)BinaryCMD.Write,
            Data = new
            {
                CollectMode = CollectMode.Continuous,
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
        
        if (SelectClientWebSocket.State != WebSocketState.Open)
        {
            await SelectClientWebSocket.ConnectAsync(serverUri, CancellationToken.None);
            await Task.Delay(100);
            Console.WriteLine("SelectDeviceDialog, SendBeganMsgToDevice:" + serverUri);
        }
        await SelectClientWebSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
            CancellationToken.None);
        _SelectedDevice.IsBegining = true;
    }


    protected override async Task  OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            _SelectedDeviceId=_SelectedDevice.Device.Id;
            _startDeviceTimerCts = new CancellationTokenSource();
           StartDeviceTimer();
        }
    }


    private async Task SendFindMsgToDevice(string deviceId,int color=0x4CAF50,int code = 16)
    {
        var device = DeviceDataList.FirstOrDefault(m => m.Device?.Id == deviceId);
        var msgJson = new
        {
            MsgId = 44444,
            //时间戳
            Time = DateTime.Now.ToUnixMs(),
            Device = new
            {
                SN = device?.Device.DeviceSN,
                Type = device?.Device.TypeId,
            },
            CMD = (int)BinaryCMD.Write,
            Data = new
            {
                FindCode=(byte)code,
                FindColor= color
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
        
        if (SelectClientWebSocket.State != WebSocketState.Open)
        {
            await SelectClientWebSocket.ConnectAsync(serverUri, CancellationToken.None);
            await Task.Delay(100);
            Console.WriteLine("SelectDeviceDialog, SendBeganMsgToDevice:" + serverUri);
        }
        await SelectClientWebSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
            CancellationToken.None);
        _SelectedDevice.IsBegining = true;
    }
    
    
    private async Task SendFindEndMsgToDevice(string deviceId)
    {
        var device = DeviceDataList.FirstOrDefault(m => m.Device?.Id == deviceId);
        var msgJson = new
        {
            MsgId = 44444,
            //时间戳
            Time = DateTime.Now.ToUnixMs(),
            Device = new
            {
                SN = device?.Device.DeviceSN,
                Type = device?.Device.TypeId,
            },
            CMD = (int)BinaryCMD.Write,
            Data = new
            {
                FindCode=(byte)0
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
        try
        {
            if (SelectClientWebSocket.State != WebSocketState.Open)
            {
                await SelectClientWebSocket.ConnectAsync(serverUri, CancellationToken.None);
                await Task.Delay(100);
                await SelectClientWebSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
                    CancellationToken.None);
                _SelectedDevice.IsBegining = true;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine("SelectedDeviceDialog  Error:" + e.Message);
        }
       
    }
   private int[] Colors = new []{0xFF00000,0x00FF000};
    private void HandleSelectDevice(string deviceId)
    {
        _SelectedDeviceId= deviceId;
    }
    CancellationTokenSource _startDeviceTimerCts;
	private void StartDeviceTimer()
	{
		Task.Run(async () =>
		{
			while (!_startDeviceTimerCts.Token.IsCancellationRequested)
			{
				try
				{
					var code = _SelectedDevice?.Device?.Name;
					if (int.TryParse(code, out var intCode))
					{
						await SendFindMsgToDevice(_SelectedDeviceId, Colors[1], intCode);
						await Task.Delay(500, _startDeviceTimerCts.Token);
					}
					await SendFindMsgToDevice(_SelectedDeviceId, Colors[0], 0);
					await Task.Delay(500, _startDeviceTimerCts.Token);
				}
				catch (TaskCanceledException ex)
				{
					await SendFindEndMsgToDevice(_SelectedDeviceId);
				}
				catch (Exception ex)
				{
					Console.WriteLine($"设备 {_SelectedDeviceId} 页面定时器异常: " + ex);
                    await Task.Delay(100, _startDeviceTimerCts.Token);
				}
			}
			await SendFindEndMsgToDevice(_SelectedDeviceId);
		});
	}

    public void Dispose()
    {
        DeviceStateService.OnAfterBeginCheck-=OnAfterBeginCheck;
        _startDeviceTimerCts?.Cancel();
        deviceWaitCts?.Cancel();
        SelectClientWebSocket?.Dispose();
    }
    CancellationTokenSource deviceWaitCts;
    int waitCount = 0;
    private Uri serverUri;

    private void StartDeviceWaitTimer()
    {
        deviceWaitCts = new CancellationTokenSource();
        Task.Run(async () =>
        {
            try
            {
                while (!deviceWaitCts.Token.IsCancellationRequested)
                {
                    if (_SelectedDevice.DeviceStatus!=EnumDeviceStatus.检测中)
					{
                        await Task.Delay(100, deviceWaitCts.Token);
                        waitCount++;
                        if (waitCount > 20)
                        {
                            await PopupService.EnqueueSnackbarAsync($"设备{_SelectedDevice?.Device?.Name}未正确佩戴");
                            PopupService.HideProgressCircular();
                        }
                        continue;
					}
                    PopupService.HideProgressCircular();
                    await  CloseDialogEvent.InvokeAsync();
                }
            }
            catch (TaskCanceledException ex)
            {
                deviceWaitCts?.Cancel();
            }
            catch (Exception ex)
            {
                PopupService.HideProgressCircular();
                Console.WriteLine($"页面定时器异常: " + ex);
            }
        });
    }


}