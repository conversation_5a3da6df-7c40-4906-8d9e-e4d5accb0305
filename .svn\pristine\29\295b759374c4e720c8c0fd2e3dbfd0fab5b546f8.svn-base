﻿using System.Runtime.InteropServices;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Unicode;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.ViewModel;
using HX.HRV.Web.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;


namespace HX.HRV.Web.Areas.HRV_HX.Controllers;

/// <summary>
/// 检测记录
/// </summary>
[Area("HRV_HX")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class PatientRecordController : Controller
{
    private readonly DataRepository _context;
    private readonly CoreDbContext _coreContext;

    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// 心率变异性数据管理接口
    /// </summary>
    /// <param name="context"></param>
    /// <param name="coreContext"></param>
    public PatientRecordController(DataRepository context, CoreDbContext coreContext)
    {
        _context = context;
        _coreContext = coreContext;

        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.IgnoreNullValues = true;
    }


    /// <summary>
    /// 心率变异性数据管理接口/详情
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性数据管理接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> GetPatientRecordModelDetail(string deviceId)
    {
        var patientRecordModel = await _context.Query<PatientRecordModel>()
            .OrderByDescending(m => m.Id)
            .FirstOrDefaultAsync(m => m.Data.DeviceId == deviceId);
        return new Result<DataModel<PatientRecordModel>>(patientRecordModel);
    }

    /// <summary>
    /// 心率变异性数据管理接口/详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性数据管理接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> GetPatientRecordModelDetailById(string id)
    {
        var patientRecordModel = await _context.Query<PatientRecordModel>()
            .OrderByDescending(m => m.Id)
            .FirstOrDefaultAsync(m => m.Data.Id == id);
        var user = await new UserService().GetUserInfoByIdAsync(_coreContext, patientRecordModel.UserId);
        patientRecordModel.User = user.Data;
        return new Result<DataModel<PatientRecordModel>>(patientRecordModel);
    }

    /// <summary>
    /// 检测记录/详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性数据管理接口/详情", IsAPI = true)]
    public async Task<Result<List<PatientRecordModel>>> GetPatientRecordModelList([FromQuery] string deviceIds)
    {
        var idlist = deviceIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var patientRecordModel = await _context.Query<PatientRecordModel>()
            .Where(m => idlist.Contains(m.Data.DeviceId)
                        && m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Checking)
            .OrderByDescending(m => m.Id)
            .ToListAsync();
        var list = patientRecordModel?.Select(m => m.Data)?.ToList() ?? new List<PatientRecordModel>();
        return new Result<List<PatientRecordModel>>(list);
    }

    /// <summary>
    /// 心率变异性数据管理接口/添加
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Permission("心率变异性数据管理接口/添加", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> Add([FromBody] PatientRecordModel dataModel)
    {
        UserInfo userInfo = ViewBag.User;
        var result = new Result<DataModel<PatientRecordModel>>();
        dataModel.Id = UId.GetNewId();
        dataModel.RecordCode = "R" + dataModel.Id;
        dataModel.IsNotEmotion = true;
        var model = new DataModel<PatientRecordModel>
        {
            Data = dataModel,
            Id = dataModel.Id,
            OrganId = userInfo?.Organ?.GetTopOrganId(),
            UserId = userInfo?.Id,
            AddTime = DateTime.Now,
            UpdateTime = DateTime.Now,
        };
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }

        _context.Add(model);
        await _context.SaveChangesAsync();
        if (dataModel.CheckTime > 0)
        {
            await DeviceStatusDataService.SetPatientRecordData(dataModel.DeviceId, dataModel.Id);
        }

        result.Data = model;
        return result;
    }

    /// <summary>
    /// 心率变异性数据管理接口/编辑
    /// </summary>
    /// <param name="id">编号</param>
    /// <param name="PatientRecordModel">产品信息</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性数据管理接口/编辑", IsAPI = true)]
    public async Task<Result<PatientRecordModel>> Edit(string id, [FromBody] PatientRecordModel PatientRecordModel)
    {
        var result = new Result<PatientRecordModel>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(PatientRecordModel, result))
        {
            return result;
        }

        var model = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (model == null)
        {
            result.AddError("设备类型不存在");
            return result;
        }

        _context.Update(model);
        await _context.SaveChangesAsync();
        result.Data = model.Data;
        return result;
    }


    [HttpGet()]
    [Permission("心率变异性数据管理接口/列表", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<PageList<DataModel<PatientRecordModel>>>> GetPatientRecordModelListByParams(
        [FromQuery] PatientModel patientModel, [FromQuery] PatientRecordModel recordModel,
        [FromQuery] string recordCodeOrId,
        [FromQuery] int? page = 1,
        [FromQuery] int? pageSize = 100)
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Query<PatientRecordModel>();
        if (!string.IsNullOrEmpty(patientModel.CardId?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.CardId == patientModel.CardId);
        }

        if (!string.IsNullOrEmpty(patientModel.Name?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.Name == patientModel.Name);
        }

        if (patientModel.Age != default)
        {
            query = query.Where(m => patientModel.Age == m.Data.Patient.Age);
        }

        if (!string.IsNullOrEmpty(patientModel.OutpatientNumberString?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.OutpatientNumberString == patientModel.OutpatientNumberString);
        }

        if (patientModel.Sex != default)
        {
            query = query.Where(m => m.Data.Patient.Sex == patientModel.Sex);
        }

        if (!string.IsNullOrEmpty(patientModel.Source?.Trim()))
        {
            query = query.Where(m => m.Data.Patient.Source == patientModel.Source);
        }

        if (recordModel.CollectStartTime != default)
        {
            query = query.Where(m => m.Data.CollectStartTime >= recordModel.CollectStartTime);
        }

        if (recordModel.CollectEndTime != default)
        {
            var endDate = recordModel.CollectEndTime.AddDays(1);
            query = query.Where(m => m.Data.CollectEndTime < endDate);
        }

        if (!string.IsNullOrEmpty(recordModel.PatientId))
        {
            query = query.Where(m => m.Data.PatientId == recordModel.PatientId);
        }

        if (!string.IsNullOrEmpty(recordModel.DeviceId))
        {
            query = query.Where(m => m.Data.DeviceId == recordModel.DeviceId);
        }

        if (!string.IsNullOrEmpty(recordModel.RecordCode))
        {
            query = query.Where(m => m.Data.RecordCode == recordModel.RecordCode);
        }

        if (!string.IsNullOrEmpty(recordModel.Id))
        {
            query = query.Where(m => m.Id == recordModel.Id);
        }

        if (!string.IsNullOrEmpty(recordCodeOrId))
        {
            query = query.Where(m => m.Data.RecordCode == recordCodeOrId || m.Id == recordCodeOrId);
        }

        var dataModels = await query
            .OrderByDescending(m => m.AddTime)
            .ToPageListAsync(pageIndex: page.Value, pageSize: pageSize.Value <= 0 ? 100 : pageSize.Value);
        return new Result<PageList<DataModel<PatientRecordModel>>>()
            { Success = true, Data = dataModels, Page = dataModels.PageInfo };
    }

    /// <summary>
    /// 设备管理接口/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性数据管理接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string id)
    {
        var result = new Result<bool>();
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }

        _context.Remove(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        return result;
    }

    /// <summary>
    /// 设备管理接口/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性数据管理接口/更新状态", IsAPI = true)]
    public async Task<Result<bool>> Finished([FromQuery] string id)
    {
        var result = new Result<bool>()
        {
            Success = false
        };
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Data.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }

        patient.UpdateTime = DateTime.Now;
        patient.Data.EnumPatientCheckStatus = EnumPatientCheckStatus.Checked;
        _context.Update(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        result.Success = true;
        await DeviceStatusDataService.SetPatientRecordToStop(id);
        return result;
    }

    [HttpPost()]
    [Permission("心率变异性数据管理接口/更新状态", IsAPI = true)]
    public async Task<Result<DataModel<PatientRecordModel>>> Began(string id)
    {
        var result = new Result<DataModel<PatientRecordModel>>()
        {
            Success = false
        };
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Data.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }

        patient.UpdateTime = DateTime.Now;
        patient.Data.CollectStartTime = DateTime.Now;
        patient.Data.CollectEndTime = DateTime.Now.AddMinutes(patient.Data.CheckTime);
        patient.Data.EnumPatientCheckStatus = EnumPatientCheckStatus.Checking;
        _context.Update(patient);
        await _context.SaveChangesAsync();
        result.Data = patient;
        result.Success = true;
        return result;
    }

    /// <summary>
    ///  患者检测记录/统计
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [Permission("心率变异性数据管理接口/统计", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<PatientRecordStatistics>> GetPatientRecordCount()
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Query<PatientRecordModel>();
        var allCount = await _context.Query<PatientRecordModel>().CountAsync();
        var today = DateOnly.FromDateTime(DateTime.Now).ToDateTime(TimeOnly.MinValue);
        var count = await
            query
                .Where(o => o.Data.CollectStartTime > today)
                .CountAsync();
        return new Result<PatientRecordStatistics>()
        {
            Success = true,
            Data = new PatientRecordStatistics()
            {
                AllCount = allCount,
                TodayCount = count
            }
        };
    }

    static Dictionary<string, string> fileTypeMap = new Dictionary<string, string>();

    [HttpPost]
    [Permission("心率变异性数据管理接口/上传文件", IsAPI = true, AllowAnonymous = true)]
    public async Task<Result<string>> UploadFile()
    {
        var result = new Result<string>();
        try
        {
            var file = Request.Form.Files["file"];
            var recordId = this.Request.Form["recordId"].ToString();
            var isEnd = this.Request.Form["isEnd"].ToString();
            var deviceSn = Request.Form["deviceSn"].ToString();
            var fileName = this.Request.Form["name"].ToString();
            var progress = this.Request.Form["progress"].ToString();
            var model = await _context.Query<PatientRecordModel>()
                .FirstOrDefaultAsync(m => m.Id == recordId);
            if (model == null)
            {
                result.AddError(" 检测记录不存在");
                return result;
            }

            GetFileNameMessage(fileName.TrimEnd(".bin"), out var timestampAndFrequencyFromFileName);

            if (model.Data.DeviceStartCheckTime == default)
            {
                model.Data.DeviceStartCheckTime = timestampAndFrequencyFromFileName.time;
                model.Data.CollectStartTime = timestampAndFrequencyFromFileName.time;
                _context.Update(model);
                await _context.SaveChangesAsync();
            }

            var dir = model.Data.GetRecordDirectoryPath();
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }

            var filePath = Path.Combine(dir, fileName);
            if (file == null)
            {
                result.AddError("请选择文件");
                return result;
            }

            byte[] numArray = new byte[file.Length];
            await file.OpenReadStream().ReadAsync(numArray, 0, (int)file.Length);
            await System.IO.File.WriteAllBytesAsync(filePath, numArray);
            float.TryParse(progress, out var floatProgress);

            var toPageJson = new JsonObject
            {
                { "SN", deviceSn },
                { "Status", (int)EnumDeviceStatus.USB传输中 },
                { "Progress", floatProgress }
            };

            if (isEnd == true.ToString())
            {
                model.Data.CollectEndTime = timestampAndFrequencyFromFileName.time;
                model.Data.BuildReportStatus = BuildReportStatus.UnKnown;
                model.Data.CheckTime = (model.Data.CollectEndTime - model.Data.CollectStartTime).Minutes;
                _context.Update(model);
                await _context.SaveChangesAsync();
                await ReceiveDeviceMsg(dir);
                var endtoPageJson = new JsonObject
                {
                    { "SN", deviceSn },
                    { "Status", (int)EnumDeviceStatus.USB传输完成 }
                };
                _ = HXAdminWebSocket.SendMsgToAdmin(endtoPageJson.ToString());
            }
            else
            {
                _ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToString());
            }

        }
        catch (Exception ex)
        {
            result.AddError("文件上传出错");
            return result;
        }

        return result;
    }


    private async Task ReceiveDeviceMsg(string directory)
    {
        var files = Directory.GetFiles(directory, "*.bin");
        var sortedFiles = files.OrderBy(file =>
        {
            // 提取时间戳
            string fileName = Path.GetFileNameWithoutExtension(file);
            string[] parts = fileName.Split('-');
            // 提取时间戳 最后一个
            var timestampString = parts.ElementAt(parts.Length - 1);
            return long.Parse(timestampString);
            //return long.MaxValue; // 如果无法解析时间戳，放到最后
        }).ToArray();
        var tickFiles = Directory.GetFiles(directory, "*tick*.bin");

        var ticks = new List<string>();
        foreach (var tickFile in tickFiles)
        {
            var fileContent = await System.IO.File.ReadAllBytesAsync(tickFile);
            var ticksTimes = ParseTickData(fileContent);
            ticks.AddRange(ticksTimes);
        }

        foreach (var file in sortedFiles)
        {
            var fileContent = await System.IO.File.ReadAllBytesAsync(file);
            var (startTime, frequency, fileType, timestamp) = ExtractTimestampAndFrequencyFromFileName(file);
            List<string> parsedData = ParseFileByType(fileType, fileContent, startTime, frequency);
            fileType = fileType.Replace("-", "").Replace("_", "");
            var outputFilePath = Path.Combine(directory, $"{fileType}_{timestamp}.csv");
            if (Directory.GetFiles(directory, $"*{fileType}*.csv").Length <= 0)
            {
                switch (fileType)
                {
                    case "PPG":
                        await System.IO.File.WriteAllLinesAsync(outputFilePath,
                            new List<string>() { "Time,PPG_R,PPG_I,PPG_G,Marked" });
                        break;
                    case "HRSPO2":
                        await System.IO.File.WriteAllLinesAsync(outputFilePath, new List<string>() { "Time,HR,SPO2,Battery,Marked" });
                        break;
                    case "IMU":
                        await System.IO.File.WriteAllLinesAsync(outputFilePath,
                            new List<string>() { "Time,GYRO-X,GYRO-Y,GYRO-Z,ACC-X,ACC-Y,ACC-Z,GEO-X,GEO-Y,GEO-Z,Marked" });
                        break;
                    case "EDA":
                    case "TEMPSKIN":
                    case "TEMPAIR":
                    case "AP":
                    case "LIGHT":
                        await System.IO.File.WriteAllLinesAsync(outputFilePath,
                            new List<string>() { $"Time,{fileType},Marked" });
                        break;
                    case "TICK":
                        //await System.IO.File.WriteAllLinesAsync(outputFilePath, new List<string>() { "Time" });
                        break;
                }
            }

            var isTicksData = parsedData.Select((str, index) =>
                new
                {
                    str,
                    index,
                    isTicks = ticks.Any(m => str.StartsWith(
                        m))
                }
            ).Where(m => m.isTicks)?.ToList();
            if (isTicksData != null)
            {
                foreach (var data in isTicksData)
                {
                    parsedData[data.index] += $",0";
                }
            }

            await System.IO.File.AppendAllLinesAsync(outputFilePath, parsedData);
        }
    }

    private static (DateTime time, int frequency, string fileType, long timestamp)
        ExtractTimestampAndFrequencyFromFileName(
            string fileName)
    {
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        if (GetFileNameMessage(fileNameWithoutExtension, out var extractTimestampAndFrequencyFromFileName))
            return extractTimestampAndFrequencyFromFileName;
        throw new Exception("文件名中未包含有效的时间戳或频率");
    }

    private static bool GetFileNameMessage(string fileNameWithoutExtension,
        out (DateTime time, int frequency, string fileType, long timestamp) extractTimestampAndFrequencyFromFileName)
    {
        var parts = fileNameWithoutExtension.Split('-').ToList();
        if (parts.Count >= 4)
        {
            // 提取时间戳 最后一个
            var timestampString = parts[parts.Count - 1];
            var timestamp = long.Parse(timestampString);
            var time = DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime;
            // 提取频率 倒数第三个
            var frequencyString = parts[parts.Count - 3].Replace("Hz", "");
            var fileTypes = parts.Skip(1).Take(parts.Count - 3 - 1);
            if (int.TryParse(frequencyString, out var frequency))
            {
                extractTimestampAndFrequencyFromFileName =
                    (time, frequency, string.Join("-", fileTypes).ToUpper(), timestamp);
                return true;
            }
        }

        throw new Exception("解析文件信息失败");
    }

    // 根据文件类型解析内容
    static List<string> ParseFileByType(string fileType, byte[] bytes, DateTime startTime, int frequency)
    {
        switch (fileType)
        {
            case "PPG":
                var ppgData = ParsePPGDataByDataType(bytes);
                return FormatPPGDataWithTimestamp(ppgData, startTime, frequency);
            case "HR_SPO2":
                return ParseSPO2DataByDataType(bytes, startTime, frequency);
            case "EDA":
                return ParseEDAData(bytes, startTime, frequency);
            case "IMU":
                return ParseIMUDataByDataType(bytes, startTime, frequency);
            case "TEMP-SKIN":
            case "TEMP-AIR":
            case "AP":
            case "LIGHT":
                return ParseByData(bytes, startTime, frequency);
            case "TICK":
                return ParseTickData(bytes, startTime, frequency);
            default:
                return new List<string>();
        }
    }

    private static List<string> ParseTickData(byte[] bytes, DateTime startTime, int frequency)
    {
        var data = MemoryMarshal.Cast<byte, uint>(bytes)
            .ToArray();
        var formatted = new List<string>();
        foreach (var item in data)
        {
            var dateTime = DateTimeExtensions.ParseUnix(item);
            formatted.Add($"{dateTime:yyyy-MM-dd HH:mm:ss}");
        }
        return formatted;
    }

    private static List<string> ParseTickData(byte[] bytes)
    {
        var data = MemoryMarshal.Cast<byte, uint>(bytes)
            .ToArray();
        var formatted = new List<string>();
        foreach (var item in data)
        {
            var dateTime = DateTimeExtensions.ParseUnix(item);
            formatted.Add(dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
        }
        return formatted;
    }


    private static List<string> FormatPPGDataWithTimestamp(Dictionary<string, List<uint>> ppgData,
        DateTime startTime, int frequency)
    {
        // 格式化PPG数据
        var formatted = new List<string>();
        var time = startTime;
        for (var i = 0; i < ppgData["PPG-R"].Count; i++)
        {
            if (i > 0)
            {
                // 根据频率计算时间
                time = time.AddSeconds(1.0 / frequency);
            }

            var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
            formatted.Add($"{timestamp},{ppgData["PPG-R"][i]},{ppgData["PPG-I"][i]},{ppgData["PPG-G"][i]}");
        }

        return formatted;
    }

    /// <summary>
    /// 9个字节一组, 一组三个数据 ，每个数据三个字节
    /// //红光;//红外光//绿光
    /// </summary>
    /// <param name="bytes"></param>
    /// <returns></returns>
    private static Dictionary<string, List<uint>> ParsePPGDataByDataType(byte[] bytes)
    {
        var data = new Dictionary<string, List<uint>>()
        {
            ["PPG-R"] = new List<uint>(),
            ["PPG-I"] = new List<uint>(),
            ["PPG-G"] = new List<uint>(),
        };
        // 遍历每9个字节，分别解析PPG-R, PPG-I, 和 PPG-G数据
        for (var i = 0; i < bytes.Length / 9; i++)
        {
            try
            {
                var rbytes = bytes[(i * 9 + 0)..(i * 9 + 3)];
                Array.Reverse(rbytes);
                data["PPG-R"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));

                rbytes = bytes[(i * 9 + 3)..(i * 9 + 6)];
                Array.Reverse(rbytes);
                data["PPG-I"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));

                rbytes = bytes[(i * 9 + 6)..(i * 9 + 9)];
                Array.Reverse(rbytes);
                data["PPG-G"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));
            }
            catch (System.Exception ex)
            {
                Console.WriteLine("解析数据异常：" + ex.Message);
            }
        }

        return data;
    }

    /// <summary>
    /// 解析数据类型中的心率、血氧和电量数据。
    /// </summary>
    /// <param name="bytes">要解析的字节数组。</param>
    /// <returns>包含心率、血氧和电量的元组。</returns>
    private static List<string> ParseSPO2DataByDataType(byte[] bytes, DateTime startTime, int frequency)
    {
        var result = new List<string>();
        var time = startTime;
        for (var i = 0; i < bytes.Length; i = i + 3)
        {
            // 根据频率计算时间
            time = time.AddSeconds(1.0 / frequency);
            try
            {
                result.Add($"{time.ToString("yyyy-MM-dd HH:mm:ss.fff")},{bytes[i]},{bytes[i + 1]},{bytes[i + 2]}");
            }
            catch (System.Exception ex)
            {
                Console.WriteLine("解析数据异常：" + ex.Message);
            }
        }

        return result;
    }

    private static List<string> ParseEDAData(byte[] bytes, DateTime startTime, int frequency)
    {
        var data = MemoryMarshal.Cast<byte, float>(bytes)
            .ToArray();
        var result = new List<string>();
        // 格式化数据
        var time = startTime;
        for (var i = 0; i < data.Length; i++)
        {
            if (i > 0)
            {
                // 根据频率计算时间
                time = time.AddSeconds(1.0 / frequency);
            }

            var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
            result.Add($"{timestamp},{(data[i] > 0 ? 1000f / data[i] : 0.0f)}");
        }

        return result;
    }


    private static List<string> ParseIMUDataByDataType(byte[] bytes, DateTime startTime, int frequency)
    {
        // 每次采样的数据长度为 18 个字节（9 方向，每方向 2 字节）
        int frameSize = 18;
        // 检查输入字节数组的长度是否为 18 的倍数
        if (bytes.Length % frameSize != 0)
        {
            throw new ArgumentException("数据长度不符合传感器格式！");
        }

        var time = startTime;
        var list = new List<string>();

        var count = bytes.Length / frameSize;
        // 遍历每一帧数据
        for (int i = 0; i < count; i++)
        {
            try
            {
                if (i > 0)
                {
                    // 根据频率计算时间
                    time = time.AddSeconds(1.0 / frequency);
                }
                var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
                // 解析陀螺仪数据（小端模式）
                var GYRO_X = BitConverter.ToInt16(bytes, i * frameSize + 0); // C1C2 陀螺仪 X 轴
                var GYRO_Y = BitConverter.ToInt16(bytes, i * frameSize + 2); // B1B2 陀螺仪 Y 轴
                var GYRO_Z = BitConverter.ToInt16(bytes, i * frameSize + 4); // A1A2 陀螺仪 Z 轴

                var ACC_X = BitConverter.ToInt16(bytes, i * frameSize + 6); // F1F2 加速度计 X 轴
                var ACC_Y = BitConverter.ToInt16(bytes, i * frameSize + 8); // E1E2 加速度计 Y 轴
                var ACC_Z = BitConverter.ToInt16(bytes, i * frameSize + 10); // D1D2 加速度计 Z 轴

                var GEO_X = BitConverter.ToInt16(bytes, i * frameSize + 12); // G1G2 地磁传感器 X 轴
                var GEO_Y = BitConverter.ToInt16(bytes, i * frameSize + 14); // H1H2 地磁传感器 Y 轴
                var GEO_Z = BitConverter.ToInt16(bytes, i * frameSize + 16); // I1I2 地磁传感器 Z 轴
                var str = $"{timestamp},{GYRO_X},{GYRO_Y},{GYRO_Z},{ACC_X},{ACC_Y},{ACC_Z},{GEO_X},{GEO_Y},{GEO_Z}";
                list.Add(str);
            }
            catch (Exception ex)
            {
                Console.WriteLine("解析数据异常：" + ex.Message);
            }
        }
        return list;
    }

    private static List<string> ParseByData(byte[] bytes, DateTime startTime, int frequency)
    {
        var data = MemoryMarshal.Cast<byte, float>(bytes)
            .ToArray();
        var result = new List<string>();
        // 格式化数据
        var time = startTime;
        for (var i = 0; i < data.Length; i++)
        {
            if (i > 0)
            {
                // 根据频率计算时间
                time = time.AddSeconds(1.0 / frequency);
            }

            var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
            result.Add($"{timestamp},{data[i]}");
        }
        return result;
    }
}