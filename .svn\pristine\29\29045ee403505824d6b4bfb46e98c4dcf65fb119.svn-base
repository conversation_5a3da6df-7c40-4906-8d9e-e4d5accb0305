﻿// DataAnalysisViewModel.cs
// 建议放在一个新的文件夹中，例如 "ViewModels"

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.WebSockets;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using HX.Base.Shared.Components;
using HX.HRV.SCI.Shared.Models;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.CSVDataModel;
using HX.HRV.Shared.Services;
using Masa.Blazor;
using Microsoft.JSInterop;
using ReactiveUI;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.CoreFX.Utils;

// 定义一个辅助类来传递 Snackbar 信息
public class SnackbarInfo
{
    public string Message { get; set; }
    public AlertTypes Type { get; set; }
}

public class DataAnalysisViewModel : ReactiveObject, IActivatableViewModel
{
    // 1. 依赖注入的服务
    private readonly StateService _stateService;
    private readonly DeviceStateService _deviceStateService;
    private readonly IJSRuntime _jsRuntime;
    private readonly IPopupService _popupService;
    private ClientWebSocket _clientSocket;
    private CancellationTokenSource _cts;

    #region 2. 响应式属性 (UI State)

    // 使用 this.RaiseAndSetIfChanged 来通知UI更新
    private int _step = 1;
    public int Step { get => _step; set => this.RaiseAndSetIfChanged(ref _step, value); }

    private string _recordId;
    public string RecordId { get => _recordId; set => this.RaiseAndSetIfChanged(ref _recordId, value); }

    private PatientRecordModel _patientRecordModel;
    public PatientRecordModel PatientRecordModel { get => _patientRecordModel; private set => this.RaiseAndSetIfChanged(ref _patientRecordModel, value); }

    private ReportDataStatistics _statistics;
    public ReportDataStatistics Statistics { get => _statistics; private set => this.RaiseAndSetIfChanged(ref _statistics, value); }

    private DataModel<DataAnalysisRecordModel> _analysisRecord;
    public DataModel<DataAnalysisRecordModel> AnalysisRecord { get => _analysisRecord; set => this.RaiseAndSetIfChanged(ref _analysisRecord, value); }
    
    private DateTime _segmentStartDate;
    public DateTime SegmentStartDate { get => _segmentStartDate; set => this.RaiseAndSetIfChanged(ref _segmentStartDate, value); }

    private DateTime _segmentEndDate;
    public DateTime SegmentEndDate { get => _segmentEndDate; set => this.RaiseAndSetIfChanged(ref _segmentEndDate, value); }
    
    // EDA/SCR 分析结果
    private string _hrImage;
    public string HRImage { get => _hrImage; private set => this.RaiseAndSetIfChanged(ref _hrImage, value); }

    private string _edaImage;
    public string EdaImage { get => _edaImage; private set => this.RaiseAndSetIfChanged(ref _edaImage, value); }
    
    private string _scrImage;
    public string ScrImage { get => _scrImage; private set => this.RaiseAndSetIfChanged(ref _scrImage, value); }

    private List<EdaTimeDomain> _edaTimeDomain = new();
    public List<EdaTimeDomain> EdaTimeDomain { get => _edaTimeDomain; private set => this.RaiseAndSetIfChanged(ref _edaTimeDomain, value); }
    
    private List<EdaSCRResult> _edaSCRResults;
    public List<EdaSCRResult> EdaSCRResults { get => _edaSCRResults; private set => this.RaiseAndSetIfChanged(ref _edaSCRResults, value); }

    private List<EdaEventResult> _edaEventResults;
    public List<EdaEventResult> EdaEventResults { get => _edaEventResults; private set => this.RaiseAndSetIfChanged(ref _edaEventResults, value); }
    
    private bool _isEdaAnalysisAvailable;
    public bool IsEdaAnalysisAvailable { get => _isEdaAnalysisAvailable; set => this.RaiseAndSetIfChanged(ref _isEdaAnalysisAvailable, value); }
    
    private StringNumber _selectedTab = "心率变异性分析";
    public StringNumber SelectedTab { get => _selectedTab; set => this.RaiseAndSetIfChanged(ref _selectedTab, value); }
    
    #endregion

    #region 3. 交互 (Interactions) - 用于ViewModel请求View执行UI操作

    public Interaction<Unit, string> GetAnalysisName { get; } = new();
    public Interaction<SnackbarInfo, Unit> ShowSnackbar { get; } = new();
    public Interaction<string, bool> SendToChartComponent { get; } = new(); // 请求View调用子组件方法
    
    #endregion
    
    #region 4. 响应式命令 (ReactiveCommands)

    public ReactiveCommand<Unit, Unit> InitializeCommand { get; }
    public ReactiveCommand<Unit, Unit> ConfirmDateRangeCommand { get; private set; }
    public ReactiveCommand<Unit, Unit> StartAnalysisCommand { get; private set; }
    public ReactiveCommand<Unit, Unit> ExportCommand { get; private set; }
    public ReactiveCommand<Unit, Unit> PreviousStepCommand { get; private set; }

    #endregion

    public ViewModelActivator Activator { get; } = new ViewModelActivator();

    public DataAnalysisViewModel(StateService stateService, DeviceStateService deviceStateService, IJSRuntime jsRuntime, IPopupService popupService)
    {
        // 依赖注入
        _stateService = stateService;
        _deviceStateService = deviceStateService;
        _jsRuntime = jsRuntime;
        _popupService = popupService;

        // 5. 使用 WhenActivated 管理生命周期
        this.WhenActivated(disposables =>
        {
            _cts = new CancellationTokenSource();
            
            // ViewModel激活时，执行初始化命令
            InitializeCommand.Execute().Subscribe().DisposeWith(disposables);

            // 订阅来自DeviceStateService的消息
            _deviceStateService.OnMessageReceived += HandleAlgMessage;
            
            // ViewModel停用时，执行清理逻辑
            Disposable.Create(() =>
            {
                Console.WriteLine("ViewModel deactivated. Cleaning up resources.");
                _deviceStateService.OnMessageReceived -= HandleAlgMessage;
                _clientSocket?.Dispose();
                _cts?.Cancel();
                _cts?.Dispose();
            }).DisposeWith(disposables);
        });
        
        // 定义命令
        InitializeCommand = ReactiveCommand.CreateFromTask(InitializeDataAsync);
        
        var canConfirmDateRange = this.WhenAnyValue(
            x => x.SegmentStartDate, x => x.SegmentEndDate, x => x.PatientRecordModel,
            (start, end, model) => model != null && start < end && start >= model.CollectStartTime && end <= model.CollectEndTime
        );
        ConfirmDateRangeCommand = ReactiveCommand.CreateFromTask(UpdateSegmentDataOnChart, canConfirmDateRange);

        var canAnalyze = this.WhenAnyValue(
            x => x.SegmentEndDate, x => x.SegmentStartDate,
            (end, start) => end - start <= TimeSpan.FromHours(1) && end > start
        );
        StartAnalysisCommand = ReactiveCommand.CreateFromTask(async () =>
        {
            // 通过Interaction从View获取分析名称
            var name = await GetAnalysisName.Handle(Unit.Default);
            if (!string.IsNullOrWhiteSpace(name))
            {
                await PerformAnalysis(name);
            }
        }, canAnalyze);

        ExportCommand = ReactiveCommand.CreateFromTask(ExportReport, 
            this.WhenAnyValue(x => x.AnalysisRecord,(DataModel<DataAnalysisRecordModel> r) => r != null));
        PreviousStepCommand = ReactiveCommand.Create(() => { Step = 1; });
    }
    
    #region 6. 业务逻辑方法

    private async Task InitializeDataAsync()
    {
        _popupService.ShowProgressCircular();
        var res = await _stateService.GetAsJsonAsync<DataModel<PatientRecordModel>>(
            "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string> { { "id", RecordId } });

        Console.WriteLine("InitializeDataAsync");
        
        if (res?.Data?.Data != null)
        {
            PatientRecordModel = res.Data.Data;
            SegmentStartDate = PatientRecordModel.CollectStartTime;
            SegmentEndDate = PatientRecordModel.CollectEndTime;
            // 连接WebSocket并加载初始图表数据
            await ConnectWebSocketAsync();
            await UpdateSegmentDataOnChart();
        }
        else
        {
            await ShowSnackbar.Handle(new SnackbarInfo { Message = "加载记录数据失败", Type = AlertTypes.Error });
            _popupService.HideProgressCircular();
        }
    }

    private async Task UpdateSegmentDataOnChart()
    {
        _popupService.ShowProgressCircular();
        var json = JsonSerializer.Serialize(new
        {
            recordId = RecordId,
            startTime = SegmentStartDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            endTime = SegmentEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff")
        });
        
        // 确保WebSocket已连接
        if (_clientSocket is not { State: WebSocketState.Open })
        {
            await ConnectWebSocketAsync();
        }
        
        // 发送消息
        await _clientSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(json)), WebSocketMessageType.Text, true, _cts.Token);
    }
    
    private async Task PerformAnalysis(string analysisName)
    {
        _popupService.ShowProgressCircular();
        var data = new
        {
            Function = "ToALG",
            Data = new
            {
                RecordId,
                StartTime = SegmentStartDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                EndTime = SegmentEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                Name = analysisName
            }
        };

        try
        {
            //TODO:
            // await _deviceStateService.ClientSocket.SendMessageAsync(JsonTool.SerializeIgnoreNull(data));
        }
        catch (Exception e)
        {
            await ShowSnackbar.Handle(new SnackbarInfo { Message = e.Message, Type = AlertTypes.Error });
            _popupService.HideProgressCircular();
        }
    }
    
    private async void HandleAlgMessage(JsonNode algJsonNode)
    {
        // 这是从DeviceStateService的回调，可能在后台线程上运行
        // ReactiveUI的属性变更会自动调度到UI线程
        try
        {
            if (algJsonNode["Function"]?.ToString() != "FromALG") return;

            var record = algJsonNode["Record"].Deserialize<DataModel<DataAnalysisRecordModel>>();
            if (record == null) return;
            
            AnalysisRecord = record;
            Statistics = record.Data.Statistics;
            HRImage = record.Data.Statistics.HRImage;
           
            if (algJsonNode["Category"]?.ToString() == "GSR")
            {
                EdaImage = record.Data.EdaImageUrl;
                ScrImage = record.Data.SCL_SCRImageUrl;
                EdaTimeDomain = record.Data.EdaTimeDomain;
                EdaSCRResults = record.Data.EdaSCRResults;
                EdaEventResults = record.Data.EdaEventResults;
                IsEdaAnalysisAvailable = true;
                ShowSnackbar.Handle(new SnackbarInfo { Message = "皮肤电分析完成", Type = AlertTypes.Success });
            }
            else
            {
                IsEdaAnalysisAvailable = false;
                ShowSnackbar.Handle(new SnackbarInfo { Message = "心率变异性分析完成", Type = AlertTypes.Success });
            }
            
            Step = 2;
        }
        catch (Exception e)
        {
            ShowSnackbar.Handle(new SnackbarInfo { Message = $"处理分析结果时出错: {e.Message}", Type = AlertTypes.Error });
        }
        finally
        {
            _popupService.HideProgressCircular();
        }
    }

    private async Task ExportReport()
    {
        _popupService.ShowProgressCircular();
        await _jsRuntime.InvokeVoidAsync("AutoPrintPdf", $"/client/DataAnalysis/ReportDetail/{AnalysisRecord.Id}");
        await Task.Delay(3000);
        await _jsRuntime.InvokeVoidAsync("downloadFileFromUrl", $"api/v2.0/SCI/DataAnalysisRecord/DownloadStatic/{AnalysisRecord.Id}");
        _popupService.HideProgressCircular();
    }
    
    public void OnChartDataZoom((float startIndex, float endIndex) arg)
    {
        if (PatientRecordModel == null) return;
        var totalTime = PatientRecordModel.CollectEndTime - PatientRecordModel.CollectStartTime;
        var startOffset = totalTime.TotalSeconds * (arg.startIndex / 100.0);
        var endOffset = totalTime.TotalSeconds * (arg.endIndex / 100.0);

        // 更新属性，UI会自动响应
        SegmentStartDate = PatientRecordModel.CollectStartTime.AddSeconds(startOffset);
        SegmentEndDate = PatientRecordModel.CollectStartTime.AddSeconds(endOffset);
    }

    private async Task ConnectWebSocketAsync()
    {
        if (_clientSocket is { State: WebSocketState.Open or WebSocketState.Connecting }) return;

        _clientSocket = new ClientWebSocket();
        var currentUri = _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
        var webSocketUrl = $"ws://{currentUri.Authority}/iot/hx_hrv_sci/v1?token={_stateService.Token}";
        
        try
        {
            await _clientSocket.ConnectAsync(new Uri(webSocketUrl), _cts.Token);
            // 连接成功后，启动一个长期任务来接收消息
            _ = ReceiveWebSocketDataAsync(_clientSocket);
        }
        catch (Exception ex)
        {
            await ShowSnackbar.Handle(new SnackbarInfo { Message = $"WebSocket连接失败: {ex.Message}", Type = AlertTypes.Error });
        }
    }

    private async Task ReceiveWebSocketDataAsync(ClientWebSocket socket)
    {
        var buffer = new byte[8192];
        while (socket.State == WebSocketState.Open && !_cts.IsCancellationRequested)
        {
            try
            {
                var result = await socket.ReceiveAsync(new ArraySegment<byte>(buffer), _cts.Token);
                if (result.MessageType == WebSocketMessageType.Close) break;

                var realData = Encoding.UTF8.GetString(buffer, 0, result.Count);
                // 通过Interaction请求View更新图表
                var isEnd = await SendToChartComponent.Handle(realData);
                if(isEnd) _popupService.HideProgressCircular();
            }
            catch (OperationCanceledException) { break; } // 正常取消
            catch (Exception ex)
            {
                await ShowSnackbar.Handle(new SnackbarInfo { Message = $"WebSocket接收错误: {ex.Message}", Type = AlertTypes.Error });
                break;
            }
        }
    }
    #endregion
}