﻿using Microsoft.Extensions.DependencyInjection;
using UFU.CoreFX.Models;

namespace UFU.IoT.Shared
{
    /// <summary>
    /// 模块配置
    /// </summary>
    public class Module : BaseModule
    {
        /// <summary>
        /// 模块名
        /// </summary>
        public override string Name { get; set; } = "物联网管理系统共享";
        public override string Icon { get; set; } = "iconfont icon-admin";

        /// <summary>
        /// 服务配置
        /// </summary>
        /// <param name="services"></param>
        public override void Configure(IServiceCollection services)
        {

        }
    }
}
