﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace UFU.IoT.Models
{
    /// <summary>
    /// 消息容器
    /// </summary>
    public class MsgContent
    {
        /// <summary>
        /// 通信接口版本
        /// </summary>
        public string Ver { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public Msg Msg { get; set; } = new Msg();
    }
    /// <summary>
    /// 消息内容
    /// </summary>
    public class Msg
    {
        /// <summary>
        /// 消息编号
        /// 由MCU实现
        /// </summary>
        public int MsgId { get; set; }
        /// <summary>
        /// 执行命令
        /// 网络模块和MCU共用
        /// </summary>
        public CMD CMD { get; set; }
        ///// <summary>
        ///// 路由
        ///// 第一个字节是目的地址（00是本机,01是服务器）
        ///// 第二个字节是源地址（00是本机,01是服务器）
        ///// 第三个字节是总地址数
        ///// 第四个字节是路由级数
        ///// </summary>
        //public uint? Route { get; set; }
        /// <summary>
        /// 设备信息
        /// MCU只包括一部分字段，剩余由WIFI补充完整
        /// </summary>
        public DeviceInfo Device { get; set; }
        /// <summary>
        /// 网络数据
        /// </summary>
        public NetData Net { get; set; }
        /// <summary>
        /// WiFi列表
        /// 设备扫描到的WiFi列表，只读
        /// </summary>
        public List<Network> Networks { get; set; }
        /// <summary>
        /// 在线升级
        /// </summary>
        public OTA OTA { get; set; }
        /// <summary>
        /// 用户配置
        /// 由MCU实现
        /// </summary>
        public Dictionary<string, dynamic> Config { get; set; }
        /// <summary>
        /// 用户数据
        /// 由MCU实现
        /// </summary>
        public Dictionary<string, dynamic> Data { get; set; }
        /// <summary>
        /// 查找数据
        /// </summary>
        public int? Find { get; set; }
        /// <summary>
        /// 当前时间（Unix时间戳）
        /// </summary>
        public long? Time { get; set; }
        /// <summary>
        /// 错误码
        /// </summary>
        public Error? Error { get; set; }
        /// <summary>
        /// 是否是历史数据
        /// </summary>
        public bool IsHistory { get; set; }
    }
    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备编号
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 通信模块唯一硬件编号
        /// </summary>
        public string MAC { get; set; }
        /// <summary>
        /// 通信模块应用程序版本号（只读）
        /// </summary>
        public string AppVer { get; set; }
        /// <summary>
        /// 授权码
        /// </summary>
        public string Token { get; set; }
        /// <summary>
        /// 过期时间（单位秒）
        /// </summary>
        public int? Expires { get; set; }

        //MCU每次发以下内容==================
        /// <summary>
        /// 设备类型（只读）
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 设备密钥（只读）
        /// </summary>
        public string Secret { get; set; }
        /// <summary>
        /// MCU芯片编号（只读）
        /// </summary>
        public string ChipSN { get; set; }


        /// <summary>
        /// 服务器地址（读写）
        /// </summary>
        [Obsolete]
        public string Server { get; set; }
        /// <summary>
        /// 超时时间（单位秒）
        /// </summary>
        [Obsolete]
        public int? Timeout { get; set; }
        /// <summary>
        /// 心跳间隔（单位秒）
        /// </summary>
        [Obsolete]
        public string Heartbeat { get; set; }
        /// <summary>
        /// 连接到的SSID
        /// </summary>
        [Obsolete]
        public string SSID { get; set; }
        /// <summary>
        /// WIFI密码
        /// </summary>
        [Obsolete]
        public string Password { get; set; }
        /// <summary>
        /// 程序升级地址
        /// 只从服务器下发，不用回传，不用发给MCU
        /// </summary>
        [Obsolete]
        public string AppUrl { get; set; }
    }
    /// <summary>
    /// 网络数据
    /// </summary>
    public class NetData
    {
        /// <summary>
        /// 服务器地址（读写）
        /// </summary>
        public string Server { get; set; }
        /// <summary>
        /// 超时时间（单位秒）
        /// </summary>
        public int? Timeout { get; set; }
        /// <summary>
        /// 心跳间隔（单位秒）
        /// </summary>
        public string Heartbeat { get; set; }
        /// <summary>
        /// 连接到的SSID
        /// </summary>
        public string SSID { get; set; }
        /// <summary>
        /// WIFI密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 联网状态
        /// </summary>
        public NetStatus? Status { get; set; }
        /// <summary>
        /// 联网模式
        /// </summary>
        public NetMode? NetMode { get; set; }
        /// <summary>
        /// LoRa工作模式
        /// </summary>
        public LoRaMode? LoRaMode { get; set; }
        /// <summary>
        /// GPRS模式
        /// </summary>
        public GPRSMode? GPRSMode { get; set; }
        /// <summary>
        /// WiFi模式
        /// </summary>
        public WiFiMode? WiFiMode { get; set; }
        /// <summary>
        /// WiFi信号强度
        /// </summary>
        public int? WiFiSignal { get; set; }
        /// <summary>
        /// GPRS信号强度
        /// </summary>
        public int? GPRSSignal { get; set; }
        /// <summary>
        /// IoT信号强度
        /// </summary>
        public int? IoTSignal { get; set; }
    }

    /// <summary>
    /// 在线升级
    /// </summary>
    public class OTA
    {
        /// <summary>
        /// 升级方式
        /// </summary>
        public OTAMode Mode { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string File { get; set; }
        /// <summary>
        /// 文件MD5值
        /// </summary>
        public string MD5 { get; set; }
        /// <summary>
        /// 文件长度
        /// </summary>
        public long? Length { get; set; }
        /// <summary>
        /// 升级偏移地址，由设备发送给服务器，如果返回-1表示升级异常
        /// </summary>
        public long? Offset { get; set; }
    }

    /// <summary>
    /// 网络数据
    /// </summary>
    public class Network
    {
        /// <summary>
        /// SSID
        /// </summary>
        public string SSID { get; set; }
        /// <summary>
        /// 网络类型
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 信号级别
        /// </summary>
        public int Level { get; set; }
    }

    /// <summary>
    /// 联网状态
    /// </summary>
    public enum OTAMode
    {
        /// <summary>
        /// Http方式
        /// </summary>
        Http = 1,
        /// <summary>
        /// Tcp方式
        /// </summary>
        Tcp = 2,
    }

    /// <summary>
    /// 执行命令
    /// </summary>
    public enum CMD
    {
        /// <summary>
        /// 设备注册
        /// </summary>
        Reg = 1,
        /// <summary>
        /// 设备登陆
        /// </summary>
        Login,
        /// <summary>
        /// 设备注销
        /// </summary>
        Logout,
        /// <summary>
        /// 心跳检测
        /// </summary>
        Ping,
        /// <summary>
        /// 回复
        /// </summary>
        Res,
        /// <summary>
        /// 配置
        /// 用于通信参数配置
        /// </summary>
        Config,
        /// <summary>
        /// 参数设置
        /// 设备控制，只针对终端设备，不针对通信模块
        /// </summary>
        Set,
        /// <summary>
        /// 参数读取
        /// </summary>
        Get,
        /// <summary>
        /// 通知
        /// </summary>
        Notify,
        /// <summary>
        /// 事件
        /// </summary>
        Event,
        /// <summary>
        /// 测试
        /// </summary>
        Find,
    }

    /// <summary>
    /// 联网状态
    /// </summary>
    public enum NetStatus
    {
        /// <summary>
        /// 未连接
        /// </summary>
        None = 0,
        /// <summary>
        /// 已连接
        /// </summary>
        Connected,
        /// <summary>
        /// 不可用
        /// </summary>
        Unavailable,
    }
    /// <summary>
    /// 联网模式
    /// </summary>
    public enum NetMode
    {
        /// <summary>
        /// 自动
        /// </summary>
        Auto = 0,
        /// <summary>
        /// WiFi
        /// </summary>
        WiFi,
        /// <summary>
        /// GPRS
        /// </summary>
        GPRS,
        /// <summary>
        /// IoT
        /// </summary>
        IoT
    }

    /// <summary>
    /// WiFi模式
    /// </summary>
    public enum GPRSMode
    {
        /// <summary>
        /// 不可用
        /// </summary>
        Unavailable = 0,
        /// <summary>
        /// 禁用
        /// </summary>
        Disable = 4
    }

    /// <summary>
    /// WiFi模式
    /// </summary>
    public enum WiFiMode
    {
        /// <summary>
        /// 不可用
        /// </summary>
        Unavailable = 0,
        /// <summary>
        /// Station模式
        /// </summary>
        Station,
        /// <summary>
        /// AP模式
        /// </summary>
        AP,
        /// <summary>
        /// APStation模式
        /// </summary>
        APStation,
        /// <summary>
        /// 不可用
        /// </summary>
        Disable
    }

    /// <summary>
    /// 物联网工作模式
    /// </summary>
    public enum LoRaMode
    {
        /// <summary>
        /// 不可用
        /// </summary>
        Unavailable = 0,
        /// <summary>
        /// 客户端
        /// </summary>
        Client,
        /// <summary>
        /// 服务端
        /// </summary>
        Server,
        /// <summary>
        /// 客户端+服务端
        /// </summary>
        ServerClient,
        /// <summary>
        /// 禁用
        /// </summary>
        Disable
    }

    /// <summary>
    /// 错误信息
    /// </summary>
    public enum Error
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success = 0,
        /// <summary>
        /// 超时
        /// </summary>
        Timeout,
        /// <summary>
        /// 未注册
        /// </summary>
        Unregistered,
        /// <summary>
        /// 未登录
        /// </summary>
        Unauthorized,
        /// <summary>
        /// 异常
        /// </summary>
        Exception,
        /// <summary>
        /// 格式错误
        /// </summary>
        FormatError,
        /// <summary>
        /// 网络错误
        /// </summary>
        NetError,
        /// <summary>
        /// 等待中
        /// </summary>
        WAITING,
        /// <summary>
        /// 操作失败
        /// </summary>
        FAILED
    }

    //public class UserData
    //{
    //    /// <summary>
    //    /// 水机状态
    //    /// </summary>
    //    public int Status { get; set; }
    //    /// <summary>
    //    /// 放水状态（0-关，1-开）
    //    /// </summary>
    //    public int FlowStatus { get; set; }
    //    /// <summary>
    //    /// 进水TDS
    //    /// </summary>
    //    public int TDSIn { get; set; }
    //    /// <summary>
    //    /// 出水TDS
    //    /// </summary>
    //    public int TDSOut { get; set; }
    //    /// <summary>
    //    /// TDS平均值
    //    /// </summary>
    //    public int TDSAverage { get; set; }
    //    /// <summary>
    //    /// 温度（10倍）
    //    /// </summary>
    //    public int Temperature { get; set; }
    //    /// <summary>
    //    /// 滤芯1剩余时间（秒）
    //    /// </summary>
    //    public int Filter1 { get; set; }
    //    /// <summary>
    //    /// 滤芯2剩余时间（秒）
    //    /// </summary>
    //    public int Filter2 { get; set; }
    //    /// <summary>
    //    /// 滤芯3剩余时间（秒）
    //    /// </summary>
    //    public int Filter3 { get; set; }
    //    /// <summary>
    //    /// 滤芯4剩余时间（秒）
    //    /// </summary>
    //    public int Filter4 { get; set; }
    //    /// <summary>
    //    /// 滤芯5剩余时间（秒）
    //    /// </summary>
    //    public int Filter5 { get; set; }
    //    /// <summary>
    //    /// 电机累计使用时间（秒）
    //    /// </summary>
    //    public int MotorTime { get; set; }
    //    /// <summary>
    //    /// 累计流量（毫升）
    //    /// </summary>
    //    public int Flow { get; set; }
    //}

    //public class UserConfig
    //{
    //    /// <summary>
    //    /// 滤芯1寿命（秒）
    //    /// </summary>
    //    public int Filter1 { get; set; }
    //    /// <summary>
    //    /// 滤芯2寿命（秒）
    //    /// </summary>
    //    public int Filter2 { get; set; }
    //    /// <summary>
    //    /// 滤芯3寿命（秒）
    //    /// </summary>
    //    public int Filter3 { get; set; }
    //    /// <summary>
    //    /// 滤芯4寿命（秒）
    //    /// </summary>
    //    public int Filter4 { get; set; }
    //    /// <summary>
    //    /// 滤芯5寿命（秒）
    //    /// </summary>
    //    public int Filter5 { get; set; }
    //    /// <summary>
    //    /// 通知间隔（读写）
    //    /// </summary>
    //    public int Interval { get; set; }
    //    /// <summary>
    //    /// TDS取平均时间
    //    /// </summary>
    //    public int TDSAverageTime { get; set; }
    //}


    /// <summary>
    /// 消息类型
    /// </summary>
    public enum MsgType
    {
        /// <summary>
        /// 接收
        /// </summary>
        Rececive = 1,
        /// <summary>
        /// 发送
        /// </summary>
        Send,
    }
}
