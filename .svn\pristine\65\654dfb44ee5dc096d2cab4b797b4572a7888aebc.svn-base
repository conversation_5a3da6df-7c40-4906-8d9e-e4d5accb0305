﻿using AntDesign;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;

namespace UFU.IoT.Shared.Pages.DeviceType.DataItemPage;

public partial class DataItemList : ComponentBase
{
    private List<DataItem> _dataItems = new();

    private Dictionary<string, List<DataItem>> _groupData ;
    private ThreeDataItem[] _groupDataItem { get;set; }
    [Parameter] /*[FromRoute]*/ public string Id { get; set; }
    private static readonly string _baseUrl = "/api/v2.0/IoT/DataItems/";
    [Inject] private StateService StateService { get; set; }
    [Inject] private MessageService MessageService { get; set; }
    public  int PageSize  = 100;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {  
        await base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnInitializedAsync()
    {
        _dataItems = new List<DataItem>();
        await InitDataListAsync();
        await base.OnInitializedAsync();
    }

    private async Task InitDataListAsync()
    {
        var res = await StateService.GetAsJsonAsync<List<DataItem>>(_baseUrl + "List",new Dictionary<string, string>()
        {
            {"typeId",Id},
            
        });
        if (res.Success)
        {    
            _dataItems = res.Data;
            var group = _dataItems.GroupBy(m => m.GroupName??string.Empty, m => m);
            _groupData = group
                .ToDictionary( m => string.IsNullOrEmpty(m.Key) ? "未分组" : m.Key.Trim(), m => m.ToList());
            _groupDataItem = _groupData.Select(m =>new ThreeDataItem()
            {
                GroupDataItem = new DataItem()
                {
                    GroupName = m.Key.Trim(),
                    ShowName = m.Key.Trim()
                },Children =m.Value.Select(o => new ThreeDataItem()
                {
                    GroupDataItem = o
                }).ToArray()
            }).ToArray();
        }
    }
    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="keys"></param>

    private async Task DeleteDeviceDataItemTypeAsync(string[] keys)
    {
        var res = await StateService.PostAsJsonAsync<bool>(_baseUrl+"BatchDelete",keys,new Dictionary<string, string>()
        {
            {"typeId",Id}
        });
        if (res.Success)
        {
            await this.MessageService.Success("已删除成功")
                .ContinueWith(async _ =>await InitDataListAsync());
        }
        else
        {
            await MessageService.Error(res.Message);
        }
    }
    
    public class ThreeDataItem
    {
        public DataItem GroupDataItem { get; set; }
        public ThreeDataItem[] Children { get; set; }
    }
   
}