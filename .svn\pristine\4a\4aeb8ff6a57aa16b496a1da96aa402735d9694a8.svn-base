﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Shared.Models;

namespace UFU.IoT.Services
{
    /// <summary>
    /// IoT服务
    /// </summary>
    public class BinaryIoTService
    {
        /// <summary>
        /// 实时数据保存队列
        /// </summary>
        public static BlockingCollection<KeyValuePair<ConnectDevice, DataModel>> RealTimeDataQueue { get; } =
            new BlockingCollection<KeyValuePair<ConnectDevice, DataModel>>();

        /// <summary>
        /// 历史数据保存队列
        /// </summary>
        public static BlockingCollection<KeyValuePair<ConnectDevice, DataModel>> HistoryDataQueue { get; } =
            new BlockingCollection<KeyValuePair<ConnectDevice, DataModel>>();

        /// <summary>
        /// 全部设备类型
        /// </summary>
        public static ConcurrentDictionary<string, DeviceTypeModel> DeviceTypes { get; } =
            new ConcurrentDictionary<string, DeviceTypeModel>();

        /// <summary>
        /// 全部在线设备
        /// </summary>
        public static ConcurrentDictionary<string, ConnectDevice> Devices { get; } =
            new ConcurrentDictionary<string, ConnectDevice>();

        /// <summary>
        /// 设备上线
        /// </summary>
        public delegate void OnDeviceOnlineEventHandler(ConnectDevice device);

        /// <summary>
        /// 设备离线
        /// </summary>
        public delegate void OnDeviceOfflineEventHandler(ConnectDevice device);

        /// <summary>
        /// 收到设备消息
        /// </summary>
        public delegate void OnReceivedDeviceMsgEventHandler(IConnect connect, string msg);

        /// <summary>
        /// 准备保存数据
        /// </summary>
        public delegate void OnSaveDataEventHandler(ConnectDevice connect, DataModel data);

        /// <summary>
        /// 收到用户消息(IWebSocket在后台发出消息时会为null)
        /// </summary>
        public delegate void OnReceivedUserMsgEventHandler(IWebSocket ws, string msg);

        public static ConcurrentDictionary<string, AdminWebSocket> Admins { get; } =
            new ConcurrentDictionary<string, AdminWebSocket>();


        public  static void Use()
        {
         
        }

        /// <summary>
        /// 设备上线
        /// </summary>
        public static event OnDeviceOnlineEventHandler OnDeviceOnline;

        /// <summary>
        /// 设备离线
        /// </summary>
        public static event OnDeviceOfflineEventHandler OnDeviceOffline;

        /// <summary>
        /// 收到设备消息
        /// </summary>
        public static event OnReceivedDeviceMsgEventHandler OnReceivedDeviceMsg;

        /// <summary>
        /// 准备保存数据
        /// </summary>
        public static event OnSaveDataEventHandler OnSavingData;

        /// <summary>
        /// 数据保存完成
        /// </summary>
        public static event OnSaveDataEventHandler OnSavedData;


        /// <summary>
        /// 收到用户消息
        /// </summary>
        public static event OnReceivedUserMsgEventHandler OnReceivedUserMsg;

        public static async Task UpdateDeviceTypesAsync()
        {
            using (DataRepository db = new DataRepository())
            {
                var types = await db.Query<DeviceTypeModel>().AsNoTracking().Select(m => m.Data).ToListAsync();
                var mappings = await db.Query<DataMappingModel>().AsNoTracking().Select(m => m.Data).ToListAsync();
                foreach (var item in types)
                {
                    item.ConfigUpdateTime = DateTime.Now;
                    item.DataMapping = mappings.FirstOrDefault(m => m.Id == item.Id);
                    DeviceTypes.AddOrUpdate(item.Id, item, (k, v) => item);
                }

                var device = Devices.Values.Select(m => m.Device).OrderByDescending(m => m.UpdateTime).FirstOrDefault();
                if (device != null)
                {
                    var devices = await db.Query<DeviceModel>().Where(m => m.UpdateTime > device.UpdateTime)
                        .Select(m => m.Data).ToListAsync();
                    foreach (var item in devices)
                    {
                        if (Devices.TryGetValue(item.Id, out var connectDevice))
                        {
                            item.DeviceType = connectDevice.Device.DeviceType;
                            item.LatestData = connectDevice.Device.LatestData;
                            connectDevice.Device = item;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 启动定时任务
        /// </summary>
        static BinaryIoTService()
        {
            _ = UpdateDeviceTypesAsync();
            //更新设备类型
            Task.Run(async () =>
            {
                while (true)
                {
                    await Task.Delay(5000);
                    try
                    {
                        await UpdateDeviceTypesAsync();
                    }
                    catch (Exception e)
                    {
                       LogTool.Logger.Error(e,"更新设备类型失败" );
                    }
                }
            });

            //更新最新数据
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        var latestDatas = new Dictionary<string, LatestDataModel>();
                        var devices = Devices.Values.Where(m => m.NeedUpdate);
                        foreach (var item in devices)
                        {
                            item.NeedUpdate = false;
                            latestDatas[item.Device.LatestData.Id] = item.Device.LatestData;
                        }

                        if (latestDatas.Count <= 0)
                        {
                            await Task.Delay(50);
                            continue;
                        }

                        using (DataRepository db = new DataRepository(UserInfo.System))
                        {
                            foreach (var model in latestDatas.Values)
                            {
                                var temp = new DataModel<LatestDataModel>()
                                {
                                    Id = model.Id,
                                    Data = model,
                                    OrganId = UserInfo.System.Organ.GetTopOrganId(),
                                    UserId = UserInfo.System.Id,
                                    AddTime = model.DataTime.Value,
                                    UpdateTime = model.UpdateTime,
                                    Version = model.Version
                                };
                                //更新设备最新数据
                                db.Update(temp);
                            }
                            var count = await db.SaveChangesAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, $"IoTService保存设备最新数据失败");
                        await Task.Delay(10);
                    }
                }
            });

            //实时数据处理
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        using (DataRepository db = new DataRepository(UserInfo.System))
                        {
                            var datas = new Dictionary<string, DataModel>();
                            while (true)
                            {
                                var data = RealTimeDataQueue.Take();

                                //设备数据
                                if (data.Value != null)
                                {
                                    datas[data.Value.Id] = data.Value;
                                }

                                //如果队列中还有数据，并且待保存数据不足100条，则本次不保存（实现批量保存,如果没有数据则立即保存）
                                if (RealTimeDataQueue.Count > 0 && datas.Count < 100)
                                {
                                    continue;
                                }

                                //负载判断
                                if (RealTimeDataQueue.Count > 200)
                                {
                                    LogTool.Logger.Error($"IoTService实时数据队列剩余{RealTimeDataQueue.Count}条，服务器可能超过负载！");
                                }

                                foreach (var model in datas.Values)
                                {
                                    var temp = new DataModel<DataModel>()
                                    {
                                        Id = model.Id,
                                        Data = model,
                                        OrganId = UserInfo.System.Organ.GetTopOrganId(),
                                        UserId = UserInfo.System.Id,
                                        AddTime = model.ServerTime,
                                        UpdateTime = model.ServerTime,
                                        Version = Guid.NewGuid().ToString()
                                    };
                                    //更新设备最新数据
                                    db.Add(temp);
                                }

                                try
                                {
                                    var count = await db.SaveChangesAsync();
                                }
                                catch (Exception ex)
                                {
                                    LogTool.Logger.Error(ex, $"IoTService保存实时数据失败");
                                }

                                //触发数据保存事件
                                foreach (var item in datas.Values)
                                {
                                    try
                                    {
                                        if (Devices.TryGetValue(item.DeviceId, out ConnectDevice device))
                                        {
                                            OnSavedData?.Invoke(device, item);
                                        }
                                        else
                                        {
                                            OnSavedData?.Invoke(null, item);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        LogTool.Logger.Error(ex, $"IoTService触发实时数据保存事件异常");
                                    }
                                }

                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, $"IoTService保存设备实时数据失败");
                        await Task.Delay(10);
                    }
                }
            });

            //历史数据处理
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        using (DataRepository db = new DataRepository(UserInfo.System))
                        {
                            var datas = new Dictionary<string, DataModel>();
                            while (true)
                            {
                                var data = HistoryDataQueue.Take();

                                //设备数据
                                if (data.Value != null)
                                {
                                    datas[data.Value.Id] = data.Value;
                                }

                                //如果队列中还有数据，并且待保存数据不足200条，则本次不保存（实现批量保存,如果没有数据则立即保存）
                                if (HistoryDataQueue.Count > 0 && datas.Count < 200)
                                {
                                    continue;
                                }
                                //if (datas.Count < 5)
                                //{
                                //    continue;
                                //}

                                //负载判断
                                if (HistoryDataQueue.Count > 2000)
                                {
                                    LogTool.Logger.Error($"IoTService历史数据队列剩余{HistoryDataQueue.Count}条，服务器可能超过负载！");
                                }

                                //查询待更新的历史数据
                                var historyDatas = datas.Values.ToList();

                                IQueryable<DataModel<DataModel>> query = null;
                                for (int i = 0; i < historyDatas.Count; i++)
                                {
                                    var dataTimeStart = historyDatas[i].Time.Date;
                                    var dataTimeEnd = historyDatas[i].Time.Date.AddDays(1);
                                    var deviceId = historyDatas[i].DeviceId;
                                    var deviceTime = historyDatas[i].DeviceTime;

                                    if (i == 0)
                                    {
                                        query = db.Query<DataModel>().Where(m =>
                                            m.Data.Time >= dataTimeStart && m.Data.Time < dataTimeEnd &&
                                            m.Data.DeviceId == deviceId && m.Data.DeviceTime == deviceTime);
                                    }
                                    else
                                    {
                                        query = query.Union(query = db.Query<DataModel>().Where(m =>
                                            m.Data.Time >= dataTimeStart && m.Data.Time < dataTimeEnd &&
                                            m.Data.DeviceId == deviceId && m.Data.DeviceTime == deviceTime));
                                    }
                                }
                                var historyList = await query.ToListAsync();
                                foreach (var item in historyList)
                                {
                                    var updateData = historyDatas.FirstOrDefault(m =>
                                        m.DeviceId == item.Data.DeviceId && item.Data.DeviceTime == m.DeviceTime);
                                    if (updateData != null)
                                    {
                                        item.Data.Data["Data"] = updateData.DataDic;
                                        db.Update(item);
                                    }
                                }

                                //计算待添加历史的数据
                                var addDatas = datas.Values.Where(m => !historyList.Any(n =>
                                    m.DeviceId == n.Data.DeviceId && m.DeviceTime == n.Data.DeviceTime)).ToList();
                                foreach (var model in addDatas)
                                {
                                    var temp = new DataModel<DataModel>()
                                    {
                                        Id = model.Id,
                                        Data = model,
                                        OrganId = UserInfo.System.Organ.GetTopOrganId(),
                                        UserId = UserInfo.System.Id,
                                        AddTime = model.ServerTime,
                                        UpdateTime = model.ServerTime,
                                        Version = Guid.NewGuid().ToString()
                                    };
                                    //更新设备最新数据
                                    db.Add(temp);
                                }

                                LogTool.Logger.Information(
                                    $"收到历史数据，共{historyDatas.Count}条，更新{historyList.Count}条，添加{addDatas.Count}条");

                                try
                                {
                                    await db.SaveChangesAsync();
                                }
                                catch (Exception ex)
                                {
                                    LogTool.Logger.Error(ex,
                                        $"IoTService保存历史数据失败:{string.Join(",", datas.Values.Where(m => m.Time > DateTime.Now).Select(m => m.DeviceId))}");
                                }

                                //触发数据保存事件
                                foreach (var item in datas.Values)
                                {
                                    try
                                    {
                                        if (Devices.TryGetValue(item.DeviceId, out ConnectDevice device))
                                        {
                                            OnSavedData?.Invoke(device, item);
                                        }
                                        else
                                        {
                                            OnSavedData?.Invoke(null, item);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        LogTool.Logger.Error(ex, $"IoTService触发历史数据保存事件异常");
                                    }
                                }

                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, $"IoTService保存设备历史数据失败");
                        await Task.Delay(10);
                    }
                }
            });
        }


        /// <summary>
        /// 1.0版消息处理
        /// </summary>
        /// <param name="connect"></param>
        /// <param name="msg"></param>
        public static void ReceiveDeviceMsg(IConnect connect, string msg)
        {
            try
            {
                // 反序列化 JSON 字符串为对象
                //  var parsedData = JsonSerializer.Deserialize<Dictionary<string, object>>(recvMsg);
                // var msg = parsedData["Command"].ToString();
                //触发消息接收事件
               // LogTool.Logger.Information($"[{DateTime.Now:HH:mm:ss:fff}]ReceiveDeviceMsg：{msg}");
                var jsonNoe = JsonNode.Parse(msg);
                var cmd = jsonNoe!["CMD"]!.GetValue<int>();
                var time = jsonNoe!["Time"]!.GetValue<long>();
                var msgId = jsonNoe!["MsgId"]!.GetValue<ushort>();
                var binaryDevice = JsonTool.Deserialize<BinaryDevice>(jsonNoe!["Device"].ToString());
                if (!DeviceTypes.TryGetValue(binaryDevice.Type, out var deviceType)
                    && deviceType == null)
                {
                    LogTool.Logger.Information($"[{DateTime.Now:HH:mm:ss:fff}]未找到设备类型：{binaryDevice.Type},{msg}");
                    return;
                }
                OnReceivedDeviceMsg?.Invoke(connect, msg);
                var cmdResult = false;
                switch (cmd)
                {
                    case (int)BinaryCMD.Read:
                        cmdResult = ReadCommandHandler(connect, msgId, jsonNoe);
                        break;
                    case (int)BinaryCMD.Write:
                        
                        //判断设备是否已下线
                        if (Devices.Values.All(m => m.Device.DeviceSN != binaryDevice.SN))
                        {
                            RegCommandHandler(connect, msgId, time, binaryDevice);
                        }
                        cmdResult = WriteCommandHandler(connect, binaryDevice, msgId, jsonNoe);
                        if (cmdResult)
                        {
                            var ackReturnMsg = new
                            {
                                CMD = (int)BinaryCMD.Ack,
                                Time = DateTime.Now.ToUnixMs(),
                                MsgId = msgId,
                                Error = (int)Error.Success,
                                ErrorMsg = ""   
                            };
                            SendMsgToDevice(connect, JsonSerializer.Serialize(ackReturnMsg));
                        }
                        break;
                    case (int)BinaryCMD.Ack:
                        break;
                    case (int)BinaryCMD.Reg:
                        cmdResult = RegCommandHandler(connect, msgId, time, binaryDevice);
                        var result = new
                        {
                            CMD = (int)BinaryCMD.Ack,
                            Time = DateTime.Now.ToUnixMs(),
                            MsgId = msgId,
                            Error = (int)Error.Success,
                            ErrorMsg = ""
                        };
                        SendMsgToDevice(connect, JsonSerializer.Serialize(result));
                        break;
                    default:
                        LogTool.Logger.Error("收到未知的设备消息：{0}", msg);
                        return;
                }

                if (cmdResult)
                {
                    //判断设备是否已下线
                    if (Devices.TryGetValue(connect.ConnectId, out var device))
                    {
                        SendMsgToClient(device.Device, jsonNoe, 1);
                    }
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"消息处理异常:{msg}");
            }
        }


        /// <summary>
        /// 写命令
        /// </summary>
        /// <param name="connect"></param>
        /// <param name="binaryDevice"></param>
        /// <param name="msgId"></param>
        /// <param name="jsonNode"></param>
        private static bool WriteCommandHandler(IConnect connect, BinaryDevice binaryDevice, ushort msgId,
            JsonNode jsonNode)
        {
            
            var device = Devices.Values.First(m => m.Device.DeviceSN == binaryDevice.SN); 
            SendMsgToAdmin(device.Device, jsonNode.ToJsonString());
            if (jsonNode == null)
            {
                LogTool.Logger.Error("收到未知消息：{0}", jsonNode);
                return false;
            }
            //保存数据
            SaveDeviceData(device, msgId, jsonNode);
            // connect.SendBinary(GenerateResponseBytes(res.ToJsonString()));
            return true;
        }

        /// <summary>
        /// 设备注册
        /// </summary>
        /// <param name="connect"></param>
        /// <param name="msg"></param>
        private static bool RegCommandHandler(IConnect connect, ushort msgId, long time, BinaryDevice device)
        {
            var type = device.Type;
            var secret = device.Secret;
            var sn = device.SN;
            var swVer = device.SWVer;
            var hwVer = device.HWVer;
            var token = device.Token;
            //如果缺少必要参数，返回格式错误
            if (string.IsNullOrWhiteSpace(type) || string.IsNullOrWhiteSpace(sn))
            {
                var result = new
                {
                    CMD = (int)BinaryCMD.Ack,
                    Time = DateTime.Now.ToUnix(),
                    MsgId = msgId,
                    Error = (int)Error.FormatError,
                    ErrorMsg = "格式错误"
                };
                SendMsgToDevice(connect, JsonSerializer.Serialize(result));
                return false;
            }
            using var db = new DataRepository(UserInfo.System);
            DeviceTypes.TryGetValue(type, out var deviceType);
            //设备类型不存在返回异常
            if (deviceType == null)
            {
                var result = new
                {
                    CMD = (int)BinaryCMD.Ack,
                    Time = DateTime.Now.ToUnix(),
                    MsgId = msgId,
                    Error = (int)Error.Exception,
                    ErrorMsg = "设备类型不存在"
                };
                SendMsgToDevice(connect, JsonSerializer.Serialize(result));
                // connect.SendBinary(GenerateResponseBytes());
                return false;
            }

            //如果设备类型存在，则根据类型编号和芯片序列号查找对应设备
            //设备存在，直接返回注册成功
            var deviceModelData = db
                .Query<DeviceModel>()
                .AsNoTracking()
                .FirstOrDefault(dataModel => dataModel.Data.TypeId == deviceType.Id && dataModel.Data.DeviceSN == sn);
            //如果设备不存在，则添加设备并返回注册信息
            if (deviceModelData == null)
            {
                var allDevice = db.Query<DeviceModel>().AsNoTracking().ToList();
                var maxName = allDevice.Max(m =>
                {
                    if (int.TryParse(m.Data.Name, out var num))
                    {
                        return num;
                    }
                    return 0;
                });
                var model = new DeviceModel();
                model.Name = (maxName+1).ToString();
                model.ChipSN = sn;
                model.DeviceSN = sn;
                model.Id = UId.GetNewId();
                model.TypeId = type;
                model.OrganId = deviceType.OrganId;
                model.AddTime = DateTime.Now;
                model.UpdateTime = DateTime.Now;
                model.Version = Guid.NewGuid().ToString();
                deviceModelData = new DataModel<DeviceModel>()
                {
                    Id = model.Id,
                    Data = model,
                    OrganId = UserInfo.System.Organ.GetTopOrganId(),
                    UserId = UserInfo.System.Id,

                    AddTime = model.AddTime,
                    UpdateTime = model.UpdateTime,
                    Version = model.Version
                };
                var latestData = new LatestDataModel()
                {
                    Id = UId.GetNewId(),
                    DeviceId = model.Id,
                    DeviceInfo = new DeviceInfo
                    {
                        Id = deviceModelData.Id,
                        Type = type,
                        MAC = sn,
                        AppVer = hwVer,
                        Secret = deviceType.Secret
                    },
                    UpdateTime = DateTime.Now
                };
                model.LatestData = latestData;
                db.Add(deviceModelData);
                var latestDataTemp = new DataModel<LatestDataModel>()
                {
                    Id = latestData.Id,
                    Data = latestData,
                    OrganId = UserInfo.System.Organ.GetTopOrganId(),
                    UserId = UserInfo.System.Id,
                    AddTime = DateTime.Now,
                    UpdateTime = latestData.UpdateTime,
                    Version = latestData.Version
                };
                db.Add(latestDataTemp);
                db.SaveChanges();
                LogTool.Logger.Information(
                    $"[{DateTime.Now.ToString("HH:mm:ss:fff")}]设备注册:{connect.ConnectId}新设备注册:[{deviceModelData.Data.Name}-{deviceModelData.Data.Id}]；|{sn}|{type}");
            }  
            //添加到在线设备列表
            var connectDevice = new ConnectDevice(connect, deviceModelData.Data);
            
            var exist = Devices.Values.FirstOrDefault(m => m.Device.Id == connectDevice.Device.Id);
            if (exist==null||(exist.Connect as IWebSocket )?.Socket?.State!=WebSocketState.Open)
            {
                LogTool.Logger.Information("设备再次上线：{0}", connectDevice.Device.Name);
                Devices.AddOrUpdate(deviceModelData.Id, connectDevice, (key, value) => connectDevice);
            }
            OnDeviceOnline?.Invoke(connectDevice);
            return true;
        }

        /// <summary>
        /// 保存设备数据
        /// </summary>
        /// <param name="device"></param>
        /// <param name="msg"></param>
        /// 
        /// <returns></returns>
        private static void SaveDeviceData(ConnectDevice device, ushort msgId, JsonNode jsonNodeData)
        {
            bool needUpdate = false;
            List<KeyValuePair<string, string>> alarms = new List<KeyValuePair<string, string>>();

            //保存数据
            if (jsonNodeData != null)
            {
                if (!DeviceTypes.TryGetValue(device.Device.TypeId, out var deviceType))
                {
                    var result = new
                    {
                        CMD = (int)BinaryCMD.Ack,
                        Time = DateTime.Now.ToUnix(),
                        MsgId = msgId,
                        Error = (int)Error.Exception,
                        ErrorMsg = "设备类型不存在"
                    };
                    SendMsgToDevice(device.Connect, JsonSerializer.Serialize(result));
                    return;
                }

                try
                {
                    //device.Device.DeviceType != deviceType，设备类型被更改
                    //更新配置数据
                    if (device.Device.DeviceType != deviceType)
                    {
                        foreach (var item in device.Device.LatestData.Data.Keys)
                        {
                            if (!deviceType.DataItemList.Any(m => m.Key == item))
                            {
                                device.Device.LatestData.Data.TryRemove(item, out var dataItem);
                            }
                        }
                        //移除设备类型中不存在的配置项
                        foreach (var item in device.Device.LatestData.Config.Keys)
                        {
                            if (!deviceType.ConfigItemList.Any(m => m.Key == item))
                            {
                                device.Device.LatestData.Config.TryRemove(item, out var dataItem);
                            }
                        }

                        device.Device.DeviceType = deviceType;
                    }
                }
                catch (Exception ex)
                {
                    LogTool.Logger.Error(ex, "更新设备配置数据异常");
                }

                //计算设备时间
                var deviceTime = DateTime.Now;
                var dataTime = deviceTime;
                device.Device.LatestData.DataTime = dataTime;
                device.Device.LatestData.UpdateTime = DateTime.Now;

                //逐项读取数据
                Dictionary<string, Dictionary<string, dynamic>> Data =
                    new Dictionary<string, Dictionary<string, dynamic>>();
                foreach (var (name, jsonNode) in jsonNodeData.AsObject())
                {
                    dynamic value = null;
                        if (jsonNode.GetValueKind() == JsonValueKind.Object)
                        {
                            var GroupData = new Dictionary<string, dynamic>();
                            Data[name] = GroupData;

                            foreach (var (jsonValueKey, jsonValueValue) in jsonNode.AsObject())
                            {
                                try
                                {
                                    if (jsonValueValue is JsonValue&&GenerateDataFromDataItem(device.Device.DeviceType.DataMapping?.Items,
                                            device.Device?.DeviceType?.DataItemList, 
                                            jsonValueKey,
                                            jsonValueValue.AsValue(), 
                                            name,
                                            out value))
                                    {
                                        GroupData[jsonValueKey] = value;
                                    }
                                }
                                catch (Exception e)
                                {
                                    LogTool.Logger.Error(e,
                                        $"[{device.Device?.Name}-{device.DeviceId}]数据项{name}:{jsonValueKey}异常");
                                    throw;
                                }
                            }
                        }
                        else
                        {
                            try
                            {
                                if (GenerateDataFromDataItem(device.Device.DeviceType.DataMapping?.Items,
                                        device.Device?.DeviceType?.DataItemList, name, jsonNode.AsValue(), null,
                                        out value))
                                {
                                    Data[name] = value;
                                }
                            }
                            catch (Exception e)
                            {
                                LogTool.Logger.Error(e,
                                    $"[{device.Device?.Name}-{device.DeviceId}]数据项{name}:{jsonNode}异常");
                                throw;
                            }
                        }
                  
                }

                //新增实时数据，更新最新数据
                //if (msg.Data.Count > 0)
                //{
                var data = new DataModel();
                data.Id = UId.GetNewId();
                data.Time = dataTime;
                data.DeviceTime = deviceTime;
                data.ServerTime = DateTime.Now;
                data.DeviceId = device.DeviceId;
                data.TypeId = device.Device.TypeId;
                data.MsgId = msgId;
                //data.DataDic = rtdData;
                data.Data = Data;
                //保存前事件触发，方便其他程序修改数据
                OnSavingData?.Invoke(device, data);
                //更新最新数据
              
                Dictionary<string, ConcurrentDictionary<string, LatestDataItem>> latestData =
                    new Dictionary<string, ConcurrentDictionary<string, LatestDataItem>>();

                foreach (var (datakey,dataValue) in Data)
                {
                    var concurrentDictionary = new ConcurrentDictionary<string, LatestDataItem>();
                    foreach (var (key,value) in dataValue)
                    {
                        concurrentDictionary.TryAdd(key,  new LatestDataItem
                        {
                            Key = key,
                            Value = value,
                            Time = DateTime.Now,
                            Status = LatestDataStatus.正常,
                            Group =datakey
                        });
                    }
                    latestData[datakey] = concurrentDictionary;
                }

                if ((latestData?.Count>0)&&latestData.Any(m=>m.Value?.Count>0))
                {
                    needUpdate = true;
                    device.Device.LatestData.GroupData = latestData;
                }
                RealTimeDataQueue.Add(new KeyValuePair<ConnectDevice, DataModel>(device, data));
            }

            //保存数据
            if (needUpdate)
            {
                device.NeedUpdate = true;
            }

            //报警通知
            foreach (var item in alarms)
            {
                string str = JsonTool.SerializeIgnoreNull(new WebSocketMsg
                {
                    Func = "Alarm",
                    Data = new
                    {
                        Device = new DeviceInfo() { Id = device.DeviceId },
                        Time = item.Key,
                        Msg = item.Value
                    }
                });
            }
        }

        public static void SendMsgToAdmin(DeviceModel device, string msg)
        {
            //发给管理员
            var adminws = Admins.Values.Where(m => m.ClientSource == 0)
                ?.Where(m => m.ConnectAll || m.DeviceIds.Contains(device.Id));
            foreach (var admin in adminws)
            {
                admin.SendTextAsync(msg).ConfigureAwait(false);
            }
        }

        public static void SendMsgToClient(DeviceModel device, JsonNode msg, int clientSource)
        {
            //发给客户端
            var adminws = Admins
                .Values
                .Where(m => m.ClientSource == clientSource);
            msg["deviceId"] = device.Id;
            foreach (var admin in adminws)
            {
                admin.SendTextAsync(JsonSerializer.Serialize(msg)).ConfigureAwait(false);
            }
        }

        private static bool GenerateDataFromDataItem(List<DataMappingItem> mappingItems, List<DataItem> dataItems,
            string key, JsonValue jsonNode, string groupName, out dynamic value)
        {
            value = null;
            try
            {
               

                DataItem dataItem;
                if (!string.IsNullOrEmpty(groupName))
                {
                    dataItem = dataItems.FirstOrDefault(m => groupName.Equals(m.GroupName)&& m.Key == key);
                }
                else
                {
                    dataItem = dataItems.FirstOrDefault(m => m.Key == key);
                }
                if (dataItem == null)
                {
                    return false;
                }
                switch (dataItem?.DataType)
                {
                    case DataItemType.Int:
                        value = dataItem.DataLength == 64 ? jsonNode.GetValue<long>() : jsonNode.GetValue<int>();
                        break;
                    case DataItemType.Float:
                        //系数转换
                        value = jsonNode.GetValue<double>();
                        var dataMapping =
                            mappingItems?.FirstOrDefault(m => m.SrcName == key);
                        if (dataMapping != null)
                        {
                            value *= dataMapping.Coefficient;
                        }

                        break;
                    case DataItemType.Latitude:
                    case DataItemType.Longitude:
                        value = jsonNode.GetValue<double>();
                        break;
                    case DataItemType.Enum:
                        value = jsonNode.GetValue<int>();
                        break;
                    case DataItemType.Bool:
                        if (jsonNode.GetValueKind() == JsonValueKind.Number)
                        {
                            value = jsonNode.GetValue<int>() != 0;
                        }
                        else
                        {
                            value = jsonNode.GetValue<bool>();
                        }

                        break;
                    default:
                        value = jsonNode.GetValue<string>();
                        break;
                }

                if (new[] { DataItemType.Float, DataItemType.Int }.Contains(dataItem.DataType))
                {
                    //检查是否超出正常值,历史数据不报警
                    if ((dataItem.MaxValue.HasValue && value > dataItem.MaxValue.Value) ||
                        (dataItem.MinValue.HasValue && value < dataItem.MinValue.Value))
                    {
                        var alarm =
                            $"[{dataItem.ShowName}]值[{value}]超出正常范围[{dataItem.MinValue}-{dataItem.MaxValue}]";
                        LogTool.GetLogger("alarm", "limit").Information(alarm);
                        if (dataItem.SaveOutOfRangData == false)
                        {
                            return false;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }

            return true;
        }


        /// <summary>
        /// 从Json的DataGroup中读取所有的KEY
        /// </summary>
        /// <param name="connect"></param>
        /// <param name="msgId"></param>
        /// <param name="jsonReadNode"></param>
        private static bool ReadCommandHandler(IConnect connect, ushort msgId, JsonNode jsonReadNode)
        {
            var jsonGroupData = jsonReadNode["Read"];
            if (jsonGroupData == null || jsonGroupData.GetValueKind() != JsonValueKind.Object)
            {
                LogTool.Logger.Error("收到未知的设备消息：{0}", jsonGroupData);

                var result = new
                {
                    CMD = (int)BinaryCMD.Ack,
                    Time = DateTime.Now.ToUnix(),
                    MsgId = msgId,
                    Error = (int)Error.FormatError,
                    ErrorMsg = "格式错误"
                };
                SendMsgToDevice(connect, JsonSerializer.Serialize(result));
                //connect.SendBinary(GenerateResponseBytes());
                return false;
            }

            //需要的数据
            var resultJson = new JsonObject();
            var device = Devices[connect.ConnectId];
            var dataSources = device.Device.LatestData.Data;
            foreach (var (propertyName, jsonNode) in jsonGroupData.AsObject())
            {
                var array = jsonNode.AsArray().GetValues<string>();
                foreach (var arrayName in array)
                {
                    var key = propertyName == EmptyObjectKey ? arrayName : $"{propertyName}.{arrayName}";
                    var dataSource = dataSources[key.ToLower()];
                    resultJson[propertyName] ??= new JsonObject();
                    var value = resultJson[propertyName] as JsonObject;
                    value.Add(arrayName, dataSource.Value);
                }
            }

            resultJson["CMD"] = (int)BinaryCMD.Ack;
            resultJson["Time"] = DateTime.Now.ToUnix();
            resultJson["MsgId"] = msgId;
            SendMsgToDevice(connect, resultJson.ToJsonString());
            //connect.SendBinary(GenerateResponseBytes(resultJson));
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        private static string EmptyObjectKey = "EmptyGroup";

        private static void SendMsgToDevice(IConnect connect, string msg)
        {
            connect.SendText(msg);
        }

        /// <summary>
        /// 构建返回包结构
        /// </summary>
        /// <param name="dataContent"></param>
        /// <returns></returns>
        private static byte[] GenerateResponseBytes(byte[] dataContent)
        {
            // 定义固定的结构部分
            byte[] header = { 0x55, 0x80 };
            byte[] dataLength = BitConverter.GetBytes(dataContent.Length);
            byte compressionType = 0x01; // 假设压缩类型为0x01
            // 组合所有部分
            byte[] result = new byte[header.Length + dataLength.Length + 1 + dataContent.Length + 2];
            Array.Copy(header, 0, result, 0, 2);
            Array.Copy(dataLength, 0, result, 2, 4);
            result[6] = compressionType;
            Array.Copy(dataContent, 0, result, 7, dataContent.Length);
            var crc = CalculateCrc16(dataContent);
            Array.Copy(crc, 0, result, result.Length - 2, 2);
            return result;
        }

        /// <summary>
        ///  构建返回包结构
        /// </summary>
        /// <param name="jsonData"></param>
        /// <returns></returns>
        private static byte[] GenerateResponseBytes(string jsonData)
        {
            var dataContent = Encoding.UTF8.GetBytes(jsonData);
            return GenerateResponseBytes(dataContent);
        }

        /// <summary>
        ///  构建返回包结构
        /// </summary>
        /// <param name="jsonData"></param>
        /// <returns></returns>
        private static byte[] GenerateResponseBytes(object jsonData)
        {
            var dataContent = Encoding.UTF8.GetBytes(JsonTool.SerializeIgnoreNull(jsonData));
            return GenerateResponseBytes(dataContent);
        }

        // CRC16校验和计算函数
        private static byte[] CalculateCrc16(byte[] data)
        {
            ushort crc = 0xFFFF;
            for (int i = 0; i < data.Length; i++)
            {
                crc = (ushort)(crc ^ (data[i]));
                for (int j = 0; j < 8; j++)
                {
                    crc = (crc & 1) != 0 ? (ushort)((crc >> 1) ^ 0xA001) : (ushort)(crc >> 1);
                }
            }

            byte[] crcBytes = BitConverter.GetBytes(crc);
            return crcBytes;
        }

        public static void ReceiveUserMsg(IWebSocket ws, string deviceId, string msg)
        {
            if (!string.IsNullOrWhiteSpace(deviceId))
            {
                //集群时要判断此连接在不在本服务器
                if (Devices.Values.Any(m => m.DeviceId == deviceId)
                    || Devices.Values.Any(m => m.Device.DeviceSN == deviceId))
                {
                    var device = Devices.Values.FirstOrDefault(m => m.DeviceId == deviceId||m.Device.DeviceSN == deviceId);
                    LogTool.GetLogger("IoT", "ReceiveUserMsg").Information($"[{deviceId}][{device.Device?.Name}]{msg}");
                    SendMsgToDevice(device.Connect, msg);
                }
            }
            else
            {
                var jsonNode = JsonNode.Parse(msg);
                var sn = jsonNode?["Device"]?["SN"]?.ToString();
                if (!string.IsNullOrWhiteSpace(sn))
                {
                    var device = Devices.Values.FirstOrDefault(m => m.Device.DeviceSN == sn);
                    if (device != null)
                    {
                        LogTool.GetLogger("IoT", "ReceiveUserMsg").Information($"[{sn}][{device.Device?.Name}]{msg}");
                        SendMsgToDevice(device.Connect, msg);
                    }
                }
            }
        }
        /// <summary>
        /// 连接断开
        /// </summary>
        /// <param name="connectId"></param>
        public static void DeviceConnectClosed(string connectId)
        {
            //移除此连接下所有在线设备
            var devices = Devices.Values.Where(m => m.Connect.ConnectId == connectId).ToList();
            foreach (var item in devices)
            {
                OnDeviceOffline?.Invoke(item);
            }
        }
    }
}