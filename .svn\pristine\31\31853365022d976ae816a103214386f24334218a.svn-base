<script>var s1="<!DOCTYPE html><html><head><meta charset='utf-8'><meta name='viewport' content='width=device-width,initial-scale=1,user-scalable=no'><title>设备配网<\/title><style>body{margin:0;padding:0}.container{margin:0 auto;padding:0;background:#fff;max-width:500px;padding-top:37px}.choiceBtn{color:#fff;display:block;border-radius:4px;padding:10px 0;text-align:center;margin:20px 15px 0;border:1px solid #13a8e2;background:#13a8e2;text-decoration:none}.netList{list-style:none;margin:0;padding:0}.netList>li{padding:12px 0;position:relative;overflow:hidden;border-bottom:.5px solid #dcdcdc}.netNam",s2="e{width:80%;float:left;text-indent:20px}.wifi{width:20%;float:left;line-height:1;text-align:center}.dialogMask{position:fixed;z-index:100;left:0;right:0;top:0;bottom:0;display:none;background-color:rgba(150,150,150,.7)}.dialog{width:260px;height:160px;border:1px solid #eee;border-radius:5px;padding:15px 20px 20px;background-color:#fff;position:absolute;left:50%;top:20%;transform:translateX(-50%)}.dialog_bd,.dialog_ft,.dialog_hd{padding:10px 0;position:relative}.dialog_bd,.dialog_ft{text-align:center}.dialog_hd{color:#333;text-align:center;font-size:16px}.formError{position:abs",s3="olute;left:50%;top:-10px;color:red;font-size:13px;transform:translateX(-50%)}.dialog_bd>input{width:240px;outline:0;border:1px solid #eee;border-radius:4px;padding:10px}.dialog_ft>input{width:49%;float:left;height:44px;border:1px solid #eee;border-radius:5px;background-color:#efefef;font-size:16px;color:#333}.dialog_ft>input.submit{color:#fff;margin-right:1%;background:#13a8e2;border-color:#13a8e2}.dislog_ft>input.cancel{margin-left:1%}.loading{position:fixed;left:0;right:0;top:0;bottom:0;z-index:1000;display:none;background-color:rgba(255,255,255,.3)}.spinBox{padding:18px;pos",s4="ition:absolute;left:50%;top:50%;border-radius:5px;background-color:rgba(0,0,0,.8);transform:translate(-50%,-50%)}.spins{text-align:center;display:inline-block}.spin{display:block;width:40px;height:40px;border-radius:50%;border:4px solid #dcdcdc;border-bottom-color:transparent;animation:loading 1s infinite linear}@keyframes loading{from{transform:rotate(0)}to{transform:rotate(1turn)}}.tip{width:100%;font-size:13px;padding:10px 15px;background-color:#f1f8fb;position:fixed;top:0}.tip>.info{color:#666}.tip>.error{color:red}<\/style><\/head><body><div class='container'><div class='ti",s5="p'><span class='info'>选取网络...<\/span> <span class='error'><\/span><\/div><div class='loading'><div class='spinBox'><div class='spins'><em class='spin'><\/em><\/div><\/div><\/div><ul class='netList'><\/ul><li id='demo' style='display:none'><div class='netName'><\/div><div class='wifi'><svg viewBox='0 0 200 200' width='21' height='21'><path d='M103,175l30.9-41.4c-8.6-6.4-19.3-10.3-30.9-10.3s-22.4,3.9-30.9,10.3L103,175'/><path d='M103,71.7c-23.2,0-44.7,7.7-61.9,20.7l15.5,20.7c12.9-9.8,28.9-15.5,46.4-15.5s33.6,5.7,46.4,15.5l15.5-20.7C147.6,79.5,126.2,71.7,103,71.7z'/><path d='M103,20.1c-34",s6=".8,0-67.1,11.6-93,30.9l15.5,20.7C47,55.6,73.9,45.8,103,45.8s56,9.6,77.5,25.9L196,51.1C169.9,31.7,137.8,20.1,103,20.1'/><\/svg><\/div><\/li><a class='choiceBtn' href='#' onclick='getWifi()'>重新扫描<\/a><div class='dialogMask'><div class='dialog'><div id='error'><\/div><div class='dialog_hd'>请输入“<span class='wifiName'><\/span>”的密码<\/div><div class='dialog_bd'><span class='formError'><\/span> <input type='hidden' id='na'> <input type='password' id='ps' placeholder='密码'><\/div><div class='dialog_ft'><input class='submit' type='button' value='确定' onclick='connectWifi()'> <input class='cancel'",s7="type='button' value='取消' onclick='cancel()'><\/div><\/div><\/div><\/div><script>var $=function(e){return document.querySelector(e)},cancel=function(){$('.dialogMask').style.display='none'},getWifi=function(){$('.loading').style.display='block';var n=new XMLHttpRequest;n.open('GET','/scan',!0),n.send(null),n.onreadystatechange=function(){if(4==n.readyState&&200==n.status){var e=JSON.parse(n.response);$('.netList').innerHTML='',sessionStorage.setItem('wifiId',e.id),e.wifi.sort(function(e,n){return n.level-e.level}).forEach(function(t){var e=$('#demo').innerHTML,n=document.createElem",s8="ent('li');n.onclick=selectWifi,n.innerHTML=e,n.setAttribute('data-val',JSON.stringify(t)),n.querySelector('.netName').innerText=t.ssid,n.querySelectorAll('path').forEach(function(e,n){e.setAttribute('fill',t.level>=n+2?'#1E1E1E':'#BBBBBB')}),$('.netList').appendChild(n)}),$('.info').innerText='选取网络...',$('.error').innerText='',setTimeout(function(){$('.loading').style.display='none'},300)}else $('.info').innerText='',$('.error').innerText='获取失败，请重新扫描',setTimeout(function(){$('.loading').style.display='none'},300)}};getWifi();var count=0,connecting=!1,connectWifi=function(){if(",s9="connecting=!0,10<++count)setTimeout(function(){$('.loading').style.display='none',$('.info').innerText='',$('.error').innerText='连接超时，请重试！'},500);else{var e=$('#ps').value,n=$('#na').value;if(''==e)$('.formError').innerText='密码不能为空';else{$('.loading').style.display='block',$('.dialogMask').style.display='none';var t='/conn?ssid='+n+'&password='+e;$('.formError').innerText='';var i=new XMLHttpRequest;i.open('GET',t,!0),i.send(),i.onreadystatechange=function(){if(4==i.readyState&&200==i.status){connecting=!1;var e=JSON.parse(i.response);1==e.status?alginConnect():2==e.status?(se",s10="tInterval(function(){navigator.onLine&&(loaction.href=e.url)},1e3),$('.loading').style.display='none'):alginConnect()}else alginConnect(),connecting=!1}}}},alginConnect=function(){!connecting&&count<=10&&setTimeout(connectWifi,1e3)},selectWifi=function(e){count=0,$('#ps').value='',$('.info').innerText='选取网络...',$('.error').innerText='';var n=JSON.parse(this.getAttribute('data-val'));'None'==n.type?($('#na').value=n.ssid,querySelector('.loading').style.display='block'):($('#na').value=n.ssid,$('.wifiName').innerText=n.ssid,$('.dialogMask').style.display='block')}<\/script><\/body><\/html>"</script>