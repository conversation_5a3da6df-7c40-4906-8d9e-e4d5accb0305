﻿@using HX.HRV.SCI.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@inherits HX.HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringHead
@inject StateService StateService
<span style="display: inline-flex;">
    @if (_tags != null) {
        @foreach (var tag in _tags) {
            <MButton
                OnClick="@(e => Callback(@DeviceStatusData?.Device?.Id, @tag?.Data?.Name))"
                OnClickStopPropagation
                XSmall
                Class="ml-1"
                Color="#faad14">
            @tag?.Data?.Name
        </MButton>
        }
    }  
</span>

@code {

    [Inject] DeviceStateService DeviceStateService { get; set; }
    private List<DataModel<EventTagModel>> _tags = new();
    [Inject] IPopupService PopupService { get; set; }

    protected override async Task OnInitializedAsync() {
        await InitListAsync();
        await base.OnInitializedAsync();
    }

    private async Task InitListAsync() {
        var url = $"/api/v2/SCI/EventTag/List";
        var result = await StateService.GetAsJsonAsync<List<DataModel<EventTagModel>>>(url);
        if (result is { Data: not null }) {
            _tags = result.Data?.Take(5)?.ToList();
        }
    }

    private async Task Callback(string id, string mark) {
        await StateService.PostAsJsonAsync<bool>("api/v2/SCI/EventTag/Mark", null, new Dictionary<string, string>() {
            { "deviceIds", id },
            { "mark", mark }
        });
        await PopupService.EnqueueSnackbarAsync("打标成功", AlertTypes.Success, timeout: 300);
    }

}