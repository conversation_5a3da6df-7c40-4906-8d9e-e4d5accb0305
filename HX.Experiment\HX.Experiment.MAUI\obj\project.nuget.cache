{"version": 2, "dgSpecHash": "T/0DlyxRoj0=", "success": true, "projectFilePath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\HX.Experiment.MAUI.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\antdesign.0.15.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bemit\\2.2.0\\bemit.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazor-dragdrop\\2.4.0\\blazor-dragdrop.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\deepcloner.core\\0.1.0\\deepcloner.core.0.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.4.0\\fluentvalidation.11.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.dependencyinjectionextensions\\11.4.0\\fluentvalidation.dependencyinjectionextensions.11.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\googlegson\\2.13.0.1\\googlegson.2.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\majorsoft.blazor.extensions.browserstorage\\1.5.0\\majorsoft.blazor.extensions.browserstorage.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\masa.blazor.1.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor.mobilecomponents\\1.10.2\\masa.blazor.mobilecomponents.1.10.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\9.0.8\\microsoft.aspnetcore.authorization.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\9.0.8\\microsoft.aspnetcore.components.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\9.0.8\\microsoft.aspnetcore.components.analyzers.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.authorization\\9.0.7\\microsoft.aspnetcore.components.authorization.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.dataannotations.validation\\3.2.0-rc1.20223.4\\microsoft.aspnetcore.components.dataannotations.validation.3.2.0-rc1.20223.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\9.0.8\\microsoft.aspnetcore.components.forms.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\9.0.8\\microsoft.aspnetcore.components.web.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly\\9.0.7\\microsoft.aspnetcore.components.webassembly.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webview\\9.0.5\\microsoft.aspnetcore.components.webview.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webview.maui\\9.0.90\\microsoft.aspnetcore.components.webview.maui.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\9.0.8\\microsoft.aspnetcore.metadata.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.7\\microsoft.extensions.configuration.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.7\\microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.7\\microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.7\\microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.7\\microsoft.extensions.configuration.json.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.8\\microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.8\\microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.7\\microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\9.0.5\\microsoft.extensions.fileproviders.composite.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\9.0.5\\microsoft.extensions.fileproviders.embedded.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.7\\microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.7\\microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.7\\microsoft.extensions.logging.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.8\\microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.7\\microsoft.extensions.logging.debug.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.8\\microsoft.extensions.options.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.8\\microsoft.extensions.primitives.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graphics.win2d\\1.3.2\\microsoft.graphics.win2d.1.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.1\\microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\9.0.8\\microsoft.jsinterop.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop.webassembly\\9.0.7\\microsoft.jsinterop.webassembly.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls\\9.0.90\\microsoft.maui.controls.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.build.tasks\\9.0.90\\microsoft.maui.controls.build.tasks.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.core\\9.0.90\\microsoft.maui.controls.core.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.xaml\\9.0.90\\microsoft.maui.controls.xaml.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.core\\9.0.90\\microsoft.maui.core.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.essentials\\9.0.90\\microsoft.maui.essentials.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics\\9.0.90\\microsoft.maui.graphics.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics.win2d.winui.desktop\\9.0.90\\microsoft.maui.graphics.win2d.winui.desktop.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.resizetizer\\9.0.90\\microsoft.maui.resizetizer.9.0.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.illink.tasks\\9.0.7\\microsoft.net.illink.tasks.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.web.webview2\\1.0.3179.45\\microsoft.web.webview2.1.0.3179.45.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.buildtools\\10.0.22621.756\\microsoft.windows.sdk.buildtools.10.0.22621.756.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.7.250606001\\microsoft.windowsappsdk.1.7.250606001.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oneof\\3.0.223\\oneof.3.0.223.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oneof.sourcegenerator\\3.0.223\\oneof.sourcegenerator.3.0.223.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4\\4.11.0.20250507\\opencvsharp4.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4.extensions\\4.11.0.20250507\\opencvsharp4.extensions.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4.runtime.win\\4.11.0.20250507\\opencvsharp4.runtime.win.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4.windows\\4.11.0.20250507\\opencvsharp4.windows.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.11\\system.drawing.common.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.6.3\\system.memory.4.6.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.7\\system.text.json.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\util.reflection\\1.0.3\\util.reflection.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide\\4.16.0.12\\xamarin.android.glide.4.16.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide.annotations\\4.16.0.12\\xamarin.android.glide.annotations.4.16.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide.disklrucache\\4.16.0.12\\xamarin.android.glide.disklrucache.4.16.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide.gifdecoder\\4.16.0.12\\xamarin.android.glide.gifdecoder.4.16.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.activity\\1.10.1.1\\xamarin.androidx.activity.1.10.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.activity.ktx\\1.10.1.1\\xamarin.androidx.activity.ktx.1.10.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.annotation\\1.9.1.3\\xamarin.androidx.annotation.1.9.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.annotation.experimental\\1.4.1.9\\xamarin.androidx.annotation.experimental.1.4.1.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.annotation.jvm\\1.9.1.3\\xamarin.androidx.annotation.jvm.1.9.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.appcompat\\1.7.0.6\\xamarin.androidx.appcompat.1.7.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.appcompat.appcompatresources\\1.7.0.6\\xamarin.androidx.appcompat.appcompatresources.1.7.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.arch.core.common\\2.2.0.16\\xamarin.androidx.arch.core.common.2.2.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.arch.core.runtime\\2.2.0.16\\xamarin.androidx.arch.core.runtime.2.2.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.browser\\1.8.0.9\\xamarin.androidx.browser.1.8.0.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.cardview\\1.0.0.34\\xamarin.androidx.cardview.1.0.0.34.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.collection\\1.5.0.1\\xamarin.androidx.collection.1.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.collection.jvm\\1.5.0.1\\xamarin.androidx.collection.jvm.1.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.collection.ktx\\1.5.0.1\\xamarin.androidx.collection.ktx.1.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.concurrent.futures\\*******\\xamarin.androidx.concurrent.futures.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.constraintlayout\\2.2.1.1\\xamarin.androidx.constraintlayout.2.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.constraintlayout.core\\1.1.1.1\\xamarin.androidx.constraintlayout.core.1.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.coordinatorlayout\\1.3.0.1\\xamarin.androidx.coordinatorlayout.1.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.core\\1.16.0.1\\xamarin.androidx.core.1.16.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.core.core.ktx\\1.16.0.1\\xamarin.androidx.core.core.ktx.1.16.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.core.viewtree\\1.0.0.1\\xamarin.androidx.core.viewtree.1.0.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.cursoradapter\\1.0.0.32\\xamarin.androidx.cursoradapter.1.0.0.32.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.customview\\*******1\\xamarin.androidx.customview.*******1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.customview.poolingcontainer\\1.0.0.18\\xamarin.androidx.customview.poolingcontainer.1.0.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.drawerlayout\\1.2.0.16\\xamarin.androidx.drawerlayout.1.2.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.dynamicanimation\\1.1.0.1\\xamarin.androidx.dynamicanimation.1.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.emoji2\\1.5.0.4\\xamarin.androidx.emoji2.1.5.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.emoji2.viewshelper\\1.5.0.4\\xamarin.androidx.emoji2.viewshelper.1.5.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.exifinterface\\1.4.0.1\\xamarin.androidx.exifinterface.1.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.fragment\\1.8.6.1\\xamarin.androidx.fragment.1.8.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.fragment.ktx\\1.8.6.1\\xamarin.androidx.fragment.ktx.1.8.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.interpolator\\1.0.0.32\\xamarin.androidx.interpolator.1.0.0.32.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.common\\2.8.7.3\\xamarin.androidx.lifecycle.common.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.common.jvm\\2.8.7.3\\xamarin.androidx.lifecycle.common.jvm.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.livedata\\2.8.7.3\\xamarin.androidx.lifecycle.livedata.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.livedata.core\\2.8.7.3\\xamarin.androidx.lifecycle.livedata.core.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.livedata.core.ktx\\2.8.7.3\\xamarin.androidx.lifecycle.livedata.core.ktx.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.process\\2.8.7.3\\xamarin.androidx.lifecycle.process.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime\\2.8.7.3\\xamarin.androidx.lifecycle.runtime.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime.android\\2.8.7.3\\xamarin.androidx.lifecycle.runtime.android.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime.ktx\\2.8.7.3\\xamarin.androidx.lifecycle.runtime.ktx.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime.ktx.android\\2.8.7.3\\xamarin.androidx.lifecycle.runtime.ktx.android.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodel\\2.8.7.3\\xamarin.androidx.lifecycle.viewmodel.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodel.android\\2.8.7.3\\xamarin.androidx.lifecycle.viewmodel.android.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodel.ktx\\2.8.7.3\\xamarin.androidx.lifecycle.viewmodel.ktx.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodelsavedstate\\2.8.7.3\\xamarin.androidx.lifecycle.viewmodelsavedstate.2.8.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.loader\\*******2\\xamarin.androidx.loader.*******2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.common\\2.8.9.1\\xamarin.androidx.navigation.common.2.8.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.fragment\\2.8.9.1\\xamarin.androidx.navigation.fragment.2.8.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.runtime\\2.8.9.1\\xamarin.androidx.navigation.runtime.2.8.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.ui\\2.8.9.1\\xamarin.androidx.navigation.ui.2.8.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.profileinstaller.profileinstaller\\1.4.1.3\\xamarin.androidx.profileinstaller.profileinstaller.1.4.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.recyclerview\\1.4.0.1\\xamarin.androidx.recyclerview.1.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.resourceinspection.annotation\\1.0.1.20\\xamarin.androidx.resourceinspection.annotation.1.0.1.20.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.savedstate\\*******6\\xamarin.androidx.savedstate.*******6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.savedstate.savedstate.ktx\\*******6\\xamarin.androidx.savedstate.savedstate.ktx.*******6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.security.securitycrypto\\*******-alpha07\\xamarin.androidx.security.securitycrypto.*******-alpha07.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.slidingpanelayout\\********\\xamarin.androidx.slidingpanelayout.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.startup.startupruntime\\*******\\xamarin.androidx.startup.startupruntime.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.swiperefreshlayout\\********\\xamarin.androidx.swiperefreshlayout.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.tracing.tracing\\********\\xamarin.androidx.tracing.tracing.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.transition\\*******\\xamarin.androidx.transition.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.vectordrawable\\*******\\xamarin.androidx.vectordrawable.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.vectordrawable.animated\\*******\\xamarin.androidx.vectordrawable.animated.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.versionedparcelable\\*******\\xamarin.androidx.versionedparcelable.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.viewpager\\*******\\xamarin.androidx.viewpager.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.viewpager2\\*******\\xamarin.androidx.viewpager2.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.window\\1.3.0.6\\xamarin.androidx.window.1.3.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.window.extensions.core.core\\1.0.0.14\\xamarin.androidx.window.extensions.core.core.1.0.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.android.material\\1.12.0.3\\xamarin.google.android.material.1.12.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.code.findbugs.jsr305\\3.0.2.19\\xamarin.google.code.findbugs.jsr305.3.0.2.19.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.crypto.tink.android\\1.17.0.1\\xamarin.google.crypto.tink.android.1.17.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.errorprone.annotations\\2.37.0.1\\xamarin.google.errorprone.annotations.2.37.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.errorprone.typeannotations\\2.37.0.1\\xamarin.google.errorprone.typeannotations.2.37.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.guava.listenablefuture\\1.0.0.27\\xamarin.google.guava.listenablefuture.1.0.0.27.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.jetbrains.annotations\\26.0.2.1\\xamarin.jetbrains.annotations.26.0.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.jspecify\\1.0.0.2\\xamarin.jspecify.1.0.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlin.stdlib\\2.0.21.3\\xamarin.kotlin.stdlib.2.0.21.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.atomicfu\\0.26.1.2\\xamarin.kotlinx.atomicfu.0.26.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.atomicfu.jvm\\0.26.1.2\\xamarin.kotlinx.atomicfu.jvm.0.26.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.coroutines.android\\1.9.0.3\\xamarin.kotlinx.coroutines.android.1.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.coroutines.core\\1.9.0.3\\xamarin.kotlinx.coroutines.core.1.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.coroutines.core.jvm\\1.9.0.3\\xamarin.kotlinx.coroutines.core.jvm.1.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.serialization.core\\1.7.3.4\\xamarin.kotlinx.serialization.core.1.7.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.serialization.core.jvm\\1.7.3.4\\xamarin.kotlinx.serialization.core.jvm.1.7.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ufu.corefx.shared\\5.0.6\\ufu.corefx.shared.5.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\microsoft.windows.sdk.net.ref.10.0.19041.57.nupkg.sha512"], "logs": []}