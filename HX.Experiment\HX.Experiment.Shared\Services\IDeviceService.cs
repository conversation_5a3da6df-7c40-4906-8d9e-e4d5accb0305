﻿using HX.Experiment.Shared.Model;
using UFU.CoreFX.Models;
using UFU.IoT.Models;

namespace HX.Experiment.Shared.Services;

public interface IDeviceService
{
    
    public DeviceModel CurrentDevice { get; set; }
    public DiskInfo CurrentLinkedDevice  { get; }
    public event Action<List<DiskInfo>> OnUDiskAfterInsertedEvent;
    public  string ComputerId  { get; }
    /// <summary>
    ///  刷新设备
    /// </summary>
    /// <returns></returns>
    Task RefreshLinkedDeviceAsync();
    void InitDevice();


    void ClearData(string deviceSN);
}