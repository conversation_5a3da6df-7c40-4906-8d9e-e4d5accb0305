﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="HX.HRV.MAUI.MainPage"
             Shell.PresentationMode="Modal"
             Title="">
    <!-- <StackLayout> -->
    <!--     <HybridWebView x:Name="hybridWebView" -->
    <!--                    RawMessageReceived="hybridWebView_RawMessageReceived" /> -->
    <!--     <Button Text="ccccccccccc"> </Button> -->
    <!-- </StackLayout> -->
    
    <Grid>
        <HybridWebView x:Name="hiddenWebView" IsVisible="False" />
        <HybridWebView x:Name="hybridWebView"
                       Grid.Row="0"
                       Grid.ColumnSpan="0"
                       RawMessageReceived="hybridWebView_RawMessageReceived" />
    </Grid>
</ContentPage>