﻿using System.Runtime.InteropServices;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.SCI.Shared.Models;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Services;
using HX.HRV.Web.Units;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.SCI.Web.Services;

public static class ALGDataAnalysisService
{
    public static void Use()
    {
        HXAdminWebSocket.OnReceiveUserMsgToALG += AnalysisData;
    }

    public static void AnalysisData(IWebSocket webSocket, string json)
    {
        var jsonNode = JsonNode.Parse(json);
        var recordId = jsonNode["Data"]?["RecordId"].GetValue<string>();
        var startIndex = jsonNode["Data"]?["StartIndex"]?.GetValue<int>();
        var endIndex = jsonNode["Data"]?["EndIndex"]?.GetValue<int>();
        var name = jsonNode["Data"]?["Name"]?.GetValue<string>();
        using var db = new DataRepository(UserInfo.System);
        var record = db.Query<PatientRecordModel>(PermissionSwitch.Off)
            .FirstOrDefault(m => m.Id == recordId);
        if (record == null) return;
        if (startIndex < 0 || endIndex < 0)
        {
            LogTool.Logger.Error("开始和结束时间必须大于0");
        }

        var id = UId.GetNewId();
        var dataAnalysis = new DataModel<DataAnalysisRecordModel>()
        {
            Id = id,
            AddTime = DateTime.Now,
            UpdateTime = DateTime.Now,
            OrganId = record.OrganId,
            UserId = record.UserId,
            Data = new DataAnalysisRecordModel
            {
                Name = name,
                RecordId = recordId,
                StartIndex = startIndex.Value,
                EndIndex = endIndex.Value
            }
        };
        var path = record.Data.GetRecordDirectoryPath();
        var ppgDic = FileDataHelper.ReadAllDataFromCsv(path, HXDataType.PPG.ToString());
        var ppgGBytes = ppgDic["PPG_G"].Skip(startIndex.Value).Take(endIndex.Value - startIndex.Value).ToList();
        var ppgRBytes = ppgDic["PPG_R"].Skip(startIndex.Value).Take(endIndex.Value - startIndex.Value).ToList();
        var ppgIBytes = ppgDic["PPG_I"].Skip(startIndex.Value).Take(endIndex.Value - startIndex.Value).ToList();
        var timeData = ppgDic["Time"].Skip(startIndex.Value).Take(endIndex.Value - startIndex.Value).ToList();
        var dic = new Dictionary<string, List<uint>>
        {
            ["PPG_G"] = ppgGBytes.Cast<uint>().ToList(),
            ["PPG_R"] = ppgRBytes.Cast<uint>().ToList(),
            ["PPG_I"] = ppgIBytes.Cast<uint>().ToList(),
        };
        db.Add(dataAnalysis);
        db.SaveChanges();
        var dir = dataAnalysis.Data.GetPath(id);
        AlgWebSocketClient.SendMsgToAlg(id, dic, record.Data.PpgRate, record.Data.DeviceStartCheckTime!=DateTime.MinValue? record.Data.DeviceStartCheckTime.ToUnixMs():record.Data.CollectStartTime.ToUnixMs(),
            true, callback: AnalysisData);
        var csv = new Dictionary<string, List<object>>
        {
            ["PPG_G"] = ppgGBytes,
            ["PPG_R"] = ppgRBytes,
            ["PPG_I"] = ppgIBytes,
            ["Time"] = timeData
        };
        FileDataHelper.SaveDataToCsv(dir,HXDataType.PPG.ToString(),csv);
    }

    private static void AnalysisData(ParsedPacket data)
    {
        var jsonData = JsonNode.Parse(data.JsonContent);
        var clientId = jsonData?["ClientId"]?.ToString();
        var hr_len = jsonData?["Data"]?["hr_len"]?.GetValue<int>() ?? 0;
        var hr_data = MemoryMarshal.Cast<byte, int>(data.BinaryData.Take(hr_len).ToArray())
            .ToArray();
        var nni_len = jsonData?["Data"]?["nni_len"]?.GetValue<int>() ?? 0;
        var nni_data = MemoryMarshal.Cast<byte, int>(data.BinaryData[hr_len..(hr_len + nni_len)].ToArray())
            .ToArray();
        var frequency_len = jsonData?["Data"]?["frequency_len"]?.GetValue<int>() ?? 0;
        var frequency_data = MemoryMarshal
            .Cast<byte, long>(data.BinaryData[(hr_len + nni_len)..(hr_len + nni_len + frequency_len)]
                .ToArray())
            .ToArray();
        var psd_len = jsonData?["Data"]?["psd_len"]?.GetValue<int>() ?? 0;
        var psd_data = MemoryMarshal
            .Cast<byte, int>(data
                .BinaryData[(hr_len + nni_len + frequency_len)..(hr_len + nni_len + frequency_len + psd_len)]
                .ToArray())
            .ToArray();
        using var db = new DataRepository();
        var recordData = db
            .Query<DataAnalysisRecordModel>()
            .FirstOrDefault(m => m.Id == clientId);
        if (recordData?.Data == null)
        {
            LogTool.Logger.Error($"不存在检测记录{clientId}");
            return;
        }

        var record = recordData.Data;
        record.Statistics = new ReportDataStatistics();
        var dataIndex = jsonData["Data"]?["data_index"];
        var resultDictionary = new Dictionary<string, string>();
        foreach (var (key, value) in dataIndex.AsObject())
        {
            string formattedValue;
            if (value.GetValueKind() == JsonValueKind.Number)
            {
                formattedValue = key.Contains("hr")
                    ? value.GetValue<float>().ToString("F0")
                    : value.GetValue<float>().ToString("F2");
            }
            else
            {
                formattedValue = value.ToString();
            }
            resultDictionary.Add(key, formattedValue);
        }
        record.Statistics.StatisticsDictionary = resultDictionary;
        record.Statistics.HRList = hr_data.ToList();
        record.Statistics.PsdList = psd_data.ToList();
        record.Statistics.NniList = nni_data.ToList();
        record.Statistics.FrequencyList = frequency_data.Select(x => (int)x).ToList();
        recordData.Data = record;
        recordData.UpdateTime = DateTime.Now;
        db.Update(recordData);
        db.SaveChanges();
        var resultData = new
        {
            Record = recordData,
            Function = "FromALG"
        };
        _ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(resultData));
    }
}