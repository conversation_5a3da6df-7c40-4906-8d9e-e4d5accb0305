﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
<MRow Class="mt-2">
	<MListItemGroup>
		@foreach (var item in IsLinkedDeviceDataList)
		{
			<MListItem>
				<ItemContent Context="ItemContent_name">
					<MListItemAction>
						<MListItemTitle>@item.DeviceSN - @item.Device?.Name</MListItemTitle>
					</MListItemAction>
					<MListItemContent>
						<MAutocomplete Class="customer-input"
									   TItem="PatientModel"
									   TItemValue="string"
									   TValue="string"
									   Items="@_entries"
									   Dense
									   Solo
									   Chips
									   OnSelect="(x)=>Callback(x.Item,item.Device?.Id)"
									   Clearable
									   Loading="IsIdCardLoading"
									   OnSearchInputUpdate="UpdateSearchInputAsync"
									   ItemText="r => { return r.Name + ' ' + r.<PERSON>; }"
									   ItemValue="r => r.CardId">
							<PrependContent>
								<MLabel Class="mr-2">身份证号:<label style="color:red">*</label></MLabel>
							</PrependContent>
							<SelectionContent Context="SelectionItemContext">
								@SelectionItemContext.Item?.CardId
							</SelectionContent>
						</MAutocomplete>
					</MListItemContent>
				</ItemContent>
			</MListItem>
		}
	</MListItemGroup>
</MRow>
<MRow Justify="JustifyTypes.Center">
	<MCol Cols="4">
		<MButton Height="56" Class="dialog-button cancel" Color="#fff" OnClick="CancelAsync">取消</MButton>
	</MCol>
	<MCol Cols="4">
		<MButton Height="56" Class="dialog-button confirm" OnClick="SaveAsync">
			确认
		</MButton>
	</MCol>
</MRow>


@code {
	[Parameter] public EventCallback CloseDialog { get; set; }
	protected bool IsIdCardLoading;
	[Parameter] public string DialogTitle { get; set; }
	[Inject] StateService StateService { get; set; }
	protected async Task SaveAsync()
	{
		await HandleSubmit();
	}

	[CascadingParameter(Name = "DeviceDataList")]
	protected List<DeviceStatusViewModel> DeviceDataList { get; set; }

	private List<DeviceStatusViewModel> IsLinkedDeviceDataList => DeviceDataList.Where(m => m.IsUsbLinked)?.ToList() ?? new List<DeviceStatusViewModel>();
	[Inject] IPopupService PopupService { get; set; }
	private List<PatientModel> _entries = new();

	/// <summary>
	///
	/// </summary>
	private Dictionary<string, PatientModel> LinedDeviceDataList { get; set; } = new Dictionary<string, PatientModel>();

	public async Task UpdateSearchInputAsync(string val)
	{
		IsIdCardLoading = true;
		var res = await StateService
			.GetAsJsonAsync<List<DataModel<PatientModel>>>("/api/v2.0/HRV_HX/Patient/GetPatientList", new Dictionary<string, string>()
					{
				{ nameof(PatientModel.CardId), val }
					});
		_entries = res.Data?.Select(m => m.Data)?.ToList();
		IsIdCardLoading = false;
	}


	private async Task HandleSubmit()
	{
		foreach (var deviceStatusViewModel in IsLinkedDeviceDataList)
		{
			var recordModel = new PatientRecordModel
				{
					CheckTime = 0
				};
			recordModel.PatientId = LinedDeviceDataList[deviceStatusViewModel.Device.Id].Id;
			recordModel.Patient = LinedDeviceDataList[deviceStatusViewModel.Device.Id];
			recordModel.DeviceId = deviceStatusViewModel?.Device?.Id;
			recordModel.BuildReportStatus = BuildReportStatus.IsLinked;
			recordModel.EnumPatientCheckStatus = EnumPatientCheckStatus.UnKnown;
			//新增
			var result = await StateService
						.PostAsJsonAsync<DataModel<PatientRecordModel>>("/api/v2.0/HRV_HX/PatientRecord/Add",
							recordModel);
			if (result.Success)
			{
				var record = result.Data?.Data;
				await Js.InvokeVoidAsync("SendExportToClient", deviceStatusViewModel.Device.ChipSN, record.Id);
				await CloseDialog.InvokeAsync();
			}
			else
			{
				PopupService.HideProgressCircular();
				await PopupService.EnqueueSnackbarAsync("保存失败，请稍后再试");
			}
		}
	}
	[Inject]
	public IJSRuntime Js { get; set; }

	protected async Task CancelAsync()
	{
		await CloseDialog.InvokeAsync();
	}


	protected override async Task OnInitializedAsync()
	{
		await InitHxSystemConfig();
		await base.OnInitializedAsync();
	}

	/// <summary>
	/// 初始化系统配置
	/// </summary>
	private async Task InitHxSystemConfig()
	{
		var result = await StateService
			.GetAsJsonAsync<DataModel<HxSystemConfigModel>>("/api/v2.0/HRV_HX/HxSystemConfig/GetSystemConfigModelDetail");
		this.SystemConfig = result?.Data?.Data ?? new HxSystemConfigModel
			{
				IsAutoPrint = false,
				CheckTimeList = new(),
				PatientSource = new()
			};
	}
	protected HxSystemConfigModel SystemConfig { get; set; } = new HxSystemConfigModel
		{
			IsAutoPrint = false,
			CheckTimeList = new(),
			PatientSource = new()
		};

	[Parameter] public HxSystemConfigModel ConfigModel { get; set; }

	private void Callback(PatientModel Item, string deviceId)
	{
		LinedDeviceDataList[deviceId] = Item;
	}
}