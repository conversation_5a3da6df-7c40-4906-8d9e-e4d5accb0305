using HX.Experiment.Shared.Services;
using HX.Experiment.Window.Services;
using Masa.Blazor;
using Microsoft.Extensions.DependencyInjection;

namespace HX.Experiment.Window;

internal static class Program
{

    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        AppDomain.CurrentDomain.UnhandledException += (sender, error) =>
{
#if DEBUG
    MessageBox.Show(text: error.ExceptionObject.ToString(), caption: "Error");
#else
    MessageBox.Show(text: "An error has occurred.", caption: "Error");
#endif

    // Log the error information (error.ExceptionObject)
};
        ApplicationConfiguration.Initialize();
        System.Windows.Forms.Application.Run(new MainForm());
    }
}

internal static class MainApp
{
    public static ServiceProvider? ServiceProvider;
    private static ServiceCollection services = new();
    public static void Init()
    {
        services.AddWindowsFormsBlazorWebView();
#if DEBUG
        services.AddBlazorWebViewDeveloperTools();
#endif
        services.AddMasaBlazor(options =>
        {
            options.Locale = new Locale("zh-CN");
        })
        .AddMobileComponents();
        services.AddScoped<IDeviceService, DeviceService>();
        services.AddSingleton<ICameraFrameService, CameraFrameService>();


        services.AddHttpClient("DefaultClient", client =>
        {
            client.BaseAddress = new Uri("http://127.0.0.1:5222");
            client.Timeout = TimeSpan.FromSeconds(30);
        }).ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(15),
            MaxConnectionsPerServer = 10,
        });
        services.AddScoped<HttpClient>(provider =>
        {
            var httpClientFactory = provider.GetService<IHttpClientFactory>();
            return httpClientFactory.CreateClient("DefaultClient");
        });
        services.AddSingleton<ISocketService, SocketService>();
        services.AddSingleton<UDiskService>();
#if DEBUG
        services.AddBlazorWebViewDeveloperTools();
#endif
        try
        {
            UFU.CoreFX.Shared.BlazorApp.BaseAddress = "http://127.0.0.1:5223";
            UFU.CoreFX.Shared.BlazorApp.ConfigureServices(services);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"配置服务时发生错误：{ex.Message}\n\n堆栈跟踪：\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        ServiceProvider = services.BuildServiceProvider();
    }
}