using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using HX.HRV.SCI.Shared.Models;
using HX.HRV.Web.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;

namespace HX.HRV.SCI.Web.Areas.SCI.Controllers;

/// <summary>
/// 事件标签
/// </summary>
[Area("SCI")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class EventTagController : Controller
{
    private readonly DataRepository _context;
    private readonly CoreDbContext _coreContext;
    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// 事件标签
    /// </summary>
    /// <param name="context"></param>
    /// <param name="coreContext"></param>
    public EventTagController(DataRepository context, CoreDbContext coreContext)
    {
        _context = context;
        _coreContext = coreContext;
        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.IgnoreNullValues = true;
    }


    /// <summary>
    ///事件标签/详情
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Permission("多参数生理及行为研究平台事件标签接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<EventTagModel>>> GetSystemConfigModelDetail()
    {
        var model = await _context.Query<EventTagModel>()
            .FirstOrDefaultAsync();
        return new Result<DataModel<EventTagModel>>(model);
    }
    /// <summary>
    /// 事件标签/添加
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Permission("多参数生理及行为研究平台事件标签接口/添加", IsAPI = true)]
    public async Task<Result<DataModel<EventTagModel>>> Add([FromBody] EventTagModel model)
    {
        UserInfo userInfo = ViewBag.User;
        var query = await _context.Query<EventTagModel>()
            .FirstOrDefaultAsync(m => m.Data.Name == model.Name);
        
        
        
        var result = new Result<DataModel<EventTagModel>>();
        if (query != null)
        {
            result.AddError("事件标签已存在");
            return result;
        }
        var addModel = new DataModel<EventTagModel>
        {
            Data = model,
            Id = UId.GetNewId(),
            OrganId = userInfo?.Organ?.GetTopOrganId(),
            UserId = userInfo?.Id,
            AddTime = DateTime.Now,
            UpdateTime = DateTime.Now
        };
     
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(addModel, result))
        {
            return result;
        }
        _context.Add(addModel);
        await _context.SaveChangesAsync();
        return result;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [Permission("多参数生理及行为研究平台事件标签接口/列表", IsAPI = true)]
    public async Task<Result<PageList<DataModel<EventTagModel>>>> List([FromQuery] int? page = 1,[FromQuery] int? pageSize = 100)
    {
        var query = _context.Query<EventTagModel>();
        var dataModels = await query.ToPageListAsync(pageIndex: page.Value, pageSize: pageSize.Value);
        return new Result<PageList<DataModel<EventTagModel>>>(){ Success = true, Data = dataModels,Page = dataModels.PageInfo};;
    }
    
    /// <summary>
    /// 事件标签/删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("{id?}")]
    [Permission("多参数生理及行为研究平台事件标签接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string id)
    {
        var result = new Result<bool>();
        if (id == null)
        {
            return result;
        }
        var model = await _context.Query<EventTagModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (model == null)
        {
            result.AddError("事件标签不存在");
            return result;
        }
        _context.Remove(model);
        await _context.SaveChangesAsync();
        result.Data=true;
        return result;
    }
    
    /// <summary>
    /// 事件标签/编辑
    /// </summary>
    /// <returns></returns>
    [HttpPost()]
    [Permission("多参数生理及行为研究平台事件标签接口/编辑", IsAPI = true)]
    public async Task<Result<DataModel<EventTagModel>>> Edit(string id, [FromBody] DataModel<EventTagModel> model)
    {
        var result = new Result<DataModel<EventTagModel>>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }
        var queryModel = await _context.Query<EventTagModel>().FirstOrDefaultAsync(m => m.Id == model.Id);
        if (model == null)
        {
            result.AddError("事件标签信息不存在");
            return result;
        }
        queryModel.Data = model.Data;
        _context.Update(queryModel);
        await _context.SaveChangesAsync();
        result.Data = queryModel;
        return result;
    }
    /// <summary>
    /// 事件标签/编辑
    /// </summary>
    /// <returns></returns>
    [HttpPost()]
    [Permission("多参数生理及行为研究平台事件标签接口/编辑", IsAPI = true)]
    public async Task<Result<DataModel<EventTagModel>>> SetIsDefault([FromQuery]string id)
    {
        var result = new Result<DataModel<EventTagModel>>();
        var defaultModel = await _context.Query<EventTagModel>().FirstOrDefaultAsync(m => m.Data.IsDefault);
        if (defaultModel != null)
        {
            defaultModel.Data.IsDefault = false;
            _context.Update(defaultModel);
        }
        
        var queryModel = await _context.Query<EventTagModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (queryModel == null)
        {
            result.AddError("事件标签信息不存在");
            return result;
        }
        queryModel.Data.IsDefault = true;
        _context.Update(queryModel);
        await _context.SaveChangesAsync();
        result.Data = queryModel;
        return result;
    }
    
    /// <summary>
    /// 事件标签/打标
    /// </summary>
    /// <returns></returns>
    [HttpPost()]
    [Permission("多参数生理及行为研究平台事件标签接口/打标", IsAPI = true)]
    public async  Task<Result<bool>> Mark(string deviceIds, string mark)
    {
        var result = new Result<bool>();
        
        var ids = deviceIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var markDevices = DeviceStatusDataService.GetDeviceDataList()
        .Where(m => ids.Contains(m.Device.Id))
        .ToList();

        if (string.IsNullOrEmpty(mark))
        {
            var defaultMark =await _context.Query<EventTagModel>()
                .FirstOrDefaultAsync(m => m.Data.IsDefault);
            mark = defaultMark.Data.Name;
        }
        foreach (var device in markDevices)
        {
            device.IsMarked=true;
            device.MarkedValue=mark;
        }
        result.Data=true;
        return result;
    }
    
}