﻿using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using PdfSharp;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
/// 
/// </summary>
public class GenerateEmotionReportService : BackgroundService
{
	private readonly DataRepository _context;
	private static List<DataModel<PatientRecordModel>> _pendingDatas;

    private static ConcurrentDictionary<string, IsSendingRecord> _isSending = new();

    private class IsSendingRecord
    {
        public IsSendingRecord(string id, DateTime createTime)
        {
            Id = id;
            CreateTime = createTime;
        }

        public string Id { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public GenerateEmotionReportService()
    {
	    _context = new DataRepository();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        LogTool.GetLogger("GenerateReport", "GenerateReport").Information("开始执行情绪报告任务");
        _= Task.Run(async () =>
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
                    _pendingDatas = await GetPendingGenerateAsync();
                    if (_pendingDatas is not { Count: > 0 })
                    {
                        continue;
                    }

                    for (int i = 0; i < _pendingDatas.Count; i++)
                    {
                        if (stoppingToken.IsCancellationRequested)
                        {
                            break;
                        }
                        var record = _pendingDatas[i];
                        if (!_isSending.ContainsKey(record.Id))
                        {
                            // 执行任务
                            await ExePythonAsync(record);
                            await Task.Delay(1000, stoppingToken);
                        }
                        _pendingDatas.Remove(record);
                        var isSending =
                            _isSending.GetOrAdd(record.Id, new IsSendingRecord(record.Id, DateTime.Now));
                        _isSending.AddOrUpdate(record.Id, isSending, (k, v) => isSending);
                    }
                }
                catch (Exception ex)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(ex, "处理情绪报告任务发生错误");
                }
            }
        }, stoppingToken);
        _= Task.Run(async () =>
        {
           
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                    for (int i = 0; i < _isSending.Count; i++)
                    {
                        var record = _isSending.Values.ToList()[i];
                        if (!(DateTime.Now.Subtract(record.CreateTime).Seconds >10)) continue;
                        _isSending.TryRemove(record.Id, out _);
                        LogTool.Logger.Information($"移除超时任务:{record.Id}");
                    }
                }
                catch (Exception e)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(e, "处理情绪报告任务发生错误");
                }
            }
        }, stoppingToken);
    }


    /// <summary>
    /// 获取待生成报告的记录
    /// </summary>
    /// <param name="_dbContext"></param>
    /// <returns></returns>
    private async Task<List<DataModel<PatientRecordModel>>> GetPendingGenerateAsync()
    {
        // 获取未生成报告的记录
        var endTime = DateTime.Now.AddSeconds(-20);
        var isSendingIds = _isSending.Keys.ToList();
        var records = await _context.Query<PatientRecordModel>(PermissionSwitch.Off)
            .Where(t =>
                t.Data.CollectEndTime < endTime
                &&t.Data.CollectEndTime>DateTime.MinValue
                &&t.Data.BuildReportStatus == BuildReportStatus.Completed
                && t.Data.IsNotEmotion == true
                && !isSendingIds.Contains(t.Id))
            .OrderByDescending(m => m.Id)
            .Take(20)
            .ToListAsync();
        return records;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="record"></param>
    private static async Task ExePythonAsync(DataModel<PatientRecordModel> record)
    {
        using Process process = new Process();
        var filePath = record.Data.GetRecordDirectoryPath();

		var edaDic = FileDataHelper.ReadDataFromCsv<float>(filePath, HXDataType.EDA.ToString());
		var edaList = edaDic["EDA"];

        var spo2Dic = FileDataHelper.ReadDataFromCsv<uint>(filePath, HXDataType.HRSPO2.
	        ToString());
        var hrList = spo2Dic["bpm"];

        var imuDic= FileDataHelper.ReadDataFromCsv<int>(filePath, HXDataType.IMU
	        .ToString());
        var accXList = imuDic["ACC-X"];
	    var accYList = imuDic["ACC-Y"];
        var accZList = imuDic["ACC-Z"];

		//为每一项数据生成tasks
        var recordId = record.Id;
		var edaTask = async () =>
		{
			
			byte[] dataBytes = new byte[edaList.Count() * sizeof(float)];
			var data = edaList.Cast<float>().ToArray();
			Buffer.BlockCopy(data, 0, dataBytes, 0, dataBytes.Length);
			var jsonData = new
			{
				MessageType = 3,
				ClientId = record.Id,
				Algorithm = "EmotionCalculator",
				IsEnd = false,
				Data = new
				{
					eda_Length = dataBytes.Length
				}
			};
			var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
			await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, dataBytes, recordId);
		};
		var hrTask = async () =>
		{
            byte[] dataBytes = new byte[hrList.Count() * sizeof(uint)];
            var data = hrList.Cast<uint>().ToArray();
            Buffer.BlockCopy(data, 0, dataBytes, 0, dataBytes.Length);
            var jsonData = new
            {
	            MessageType = 3,
	            ClientId = record.Id,
	            Algorithm = "EmotionCalculator",
	            IsEnd = false,
	            Data = new
	            {
		            hr_Length = dataBytes.Length
	            }
            };
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
			await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, dataBytes, recordId);
		};
		var accXTask = async () =>
		{
			var dataBytes = new List<byte>();
            var accX = accXList.Cast<int>().ToArray();
			var accY = accYList.Cast<int>().ToArray();
            var accZ = accZList.Cast<int>().ToArray();

            var accXBytes = new byte[accX.Length * sizeof(int)];
            var accYBytes = new byte[accY.Length * sizeof(int)];
            var accZBytes = new byte[accZ.Length * sizeof(int)];
			Buffer.BlockCopy(accX, 0, accXBytes, 0, accXBytes.Length);
			Buffer.BlockCopy(accY, 0, accYBytes, 0, accYBytes.Length);
			Buffer.BlockCopy(accZ, 0, accZBytes, 0, accZBytes.Length);
			dataBytes.AddRange(accXBytes);
			dataBytes.AddRange(accYBytes);
			dataBytes.AddRange(accZBytes);
            var jsonData = new
            {
	            MessageType = 3,
	            ClientId = record.Id,
	            Algorithm = "EmotionCalculator",
	            IsEnd = false,
	            Data = new
	            {
					acc_x_Length = accXBytes.Length,
                    acc_y_Length = accYBytes.Length,
                    acc_z_Length = accZBytes.Length
				}
            };
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, dataBytes.ToArray(), recordId);
		};
		await Task.WhenAll(edaTask(), hrTask(), accXTask()).ContinueWith(async (_) =>
		{
            Task.Delay(1000).Wait();
			var jsonData = new
			{
				MessageType = 3,
				ClientId = record.Id,
				Algorithm = "EmotionCalculator",
				IsEnd = true,
                Data = new
                {

                }
			};
			var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
			await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, Array.Empty<byte>(), recordId);
		});
    }




	private static void AnalysisEmotionCalculatorData(ParsedPacket data)
	{
		var jsonData = JsonNode.Parse(data.JsonContent);
		var clientId = jsonData?["ClientId"]?.ToString();
		using var db = new DataRepository();
		var recordData = db
			.Query<PatientRecordModel>()
			.FirstOrDefault(m => m.Id == clientId);
		if (recordData?.Data == null)
		{
			LogTool.Logger.Error($"不存在分析记录{clientId}");
			return;
		}
		
	}


	public override async Task StopAsync(CancellationToken stoppingToken)
    {
        LogTool.Logger.Information("生成情绪报告服务正在停止");
        await base.StopAsync(stoppingToken);
    }
}