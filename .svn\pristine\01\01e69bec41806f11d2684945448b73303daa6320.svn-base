﻿using AntDesign;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using UFU.CoreFX.Models;
using UFU.CoreFX.Models.ViewModels;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;


namespace UFU.IoT.Shared.Pages.Device;

public partial class Edit : ComponentBase
{    
    [Inject] private StateService StateService { get; set; }
    [Inject] MessageService Message { get; set; }
    private DeviceModel deviceModel { get; set; } = new DeviceModel();
    public LatestDataModel LatestData { get; set; }
    private List<OrganModel> _organList = new();
    private List<UserModel> _userList = new();
    protected  override async Task OnInitializedAsync()
    {
        await InitOrgansAsync();
        await InitDeviceModelAsync();
        await InitUsersAsync();
        await base.OnInitializedAsync();
    }
    [Parameter] [FromRoute] public string DeviceId { get; set; }
    private async Task InitDeviceModelAsync()
    {
        var res = await StateService.GetAsJsonAsync<DataModel<DeviceModel>>($"/api/v2/IoT/Devices/Get/{DeviceId}");
        if (res.Success)
        {
            deviceModel = res.Data?.Data;
            LatestData = res.Data?.Data?.LatestData;
        }
    }
    /// <summary>
    /// 初始化组织机构
    /// </summary>
    private async Task InitOrgansAsync()
    {
        var result = await StateService.GetAsJsonAsync<List<OrganModel>>("/api/v2/Core/Organs/List?pageSize=999");
        if (result.Success && result.Data != null)
        {
            _organList = result.Data.ToList();
        }
    }
    
    /// <summary>
    /// 初始化用户
    /// </summary>
    private async Task InitUsersAsync()
    {
        var result = await StateService.GetAsJsonAsync<List<UserModel>>("/api/v2/Core/Users/<USER>");
        if (result.Success && result.Data != null)
        {
            _userList = result.Data.ToList();
        }
    }
    private async Task HandleValidSubmit()
    {
        var res = await StateService.PostAsJsonAsync<DataModel<DeviceModel>>("/api/v2/IoT/Devices/Edit/"+DeviceId,
            deviceModel);
        if (res.Success)
        {
            await Message.Success("保存成功");
            StateService.NavigationManager.NavigateTo($"/IoT/Device/Edit/{DeviceId}");
        }
        else
        {
            await Message.Error(res.Message);
        }
    }
   
    private void Cancel()
    {
        // Handle cancel
    }
}