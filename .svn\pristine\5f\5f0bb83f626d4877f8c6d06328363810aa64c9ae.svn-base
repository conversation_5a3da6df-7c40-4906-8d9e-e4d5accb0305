﻿using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using PdfSharp;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
/// 
/// </summary>
public class GenerateEmotionReportService : BackgroundService
{
	private readonly DataRepository _context;
	private static List<DataModel<PatientRecordModel>> _pendingDatas;

    private static ConcurrentDictionary<string, IsSendingRecord> _isSending = new();

    private class IsSendingRecord
    {
        public IsSendingRecord(string id, DateTime createTime)
        {
            Id = id;
            CreateTime = createTime;
        }

        public string Id { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public GenerateEmotionReportService()
    {
	    _context = new DataRepository();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        LogTool.GetLogger("GenerateReport", "GenerateReport").Information("开始执行情绪报告任务");
        _= Task.Run(async () =>
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
                    _pendingDatas = await GetPendingGenerateAsync();
                    if (_pendingDatas is not { Count: > 0 })
                    {
                        continue;
                    }
                    for (int i = 0; i < _pendingDatas.Count; i++)
                    {
                        if (stoppingToken.IsCancellationRequested)
                        {
                            break;
                        }
                        var record = _pendingDatas[i];
                        if (!_isSending.ContainsKey(record.Id))
                        {
                            // 执行任务
                            await AlgWebSocketClient.ExeGenerateEmotionAsync(record.Id,record.Data.GetRecordDirectoryPath());
                            await Task.Delay(1000, stoppingToken);
                        }
                        _pendingDatas.Remove(record);
                        var isSending =
                            _isSending.GetOrAdd(record.Id, new IsSendingRecord(record.Id, DateTime.Now));
                        _isSending.AddOrUpdate(record.Id, isSending, (k, v) => isSending);
                    }
                }
                catch (Exception ex)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(ex, "处理情绪报告任务发生错误");
                }
            }
        }, stoppingToken);
        _= Task.Run(async () =>
        {
           
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                    for (int i = 0; i < _isSending.Count; i++)
                    {
                        var record = _isSending.Values.ToList()[i];
                        if (!(DateTime.Now.Subtract(record.CreateTime).Seconds >10)) continue;
                        _isSending.TryRemove(record.Id, out _);
                        LogTool.Logger.Information($"移除超时情绪生成任务:{record.Id}");
                    }
                }
                catch (Exception e)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(e, "处理情绪报告任务发生错误");
                }
            }
        }, stoppingToken);
    }


    /// <summary>
    /// 获取待生成报告的记录
    /// </summary>
    /// <param name="_dbContext"></param>
    /// <returns></returns>
    private async Task<List<DataModel<PatientRecordModel>>> GetPendingGenerateAsync()
    {
        // 获取未生成报告的记录
        var endTime = DateTime.Now.AddSeconds(-20);
        var isSendingIds = _isSending.Keys.ToList();
        var records = await _context.Query<PatientRecordModel>(PermissionSwitch.Off)
            .Where(t =>
                t.Data.CollectEndTime < endTime
                &&t.Data.CollectEndTime>DateTime.MinValue
                &&t.Data.BuildReportStatus == BuildReportStatus.Completed
                && t.Data.IsNotEmotion == true
                && !isSendingIds.Contains(t.Id))
            .OrderByDescending(m => m.Id)
            .Take(20)
            .ToListAsync();
        return records;
    }

	private static void AnalysisEmotionCalculatorData(ParsedPacket data)
	{
		var jsonData = JsonNode.Parse(data.JsonContent);
		var clientId = jsonData?["ClientId"]?.ToString();
		using var db = new DataRepository();
		var recordData = db
			.Query<PatientRecordModel>()
			.FirstOrDefault(m => m.Id == clientId);
		if (recordData?.Data == null)
		{
			LogTool.Logger.Error($"不存在分析记录{clientId}");
			return;
		}
	}


	public override async Task StopAsync(CancellationToken stoppingToken)
    {
        LogTool.Logger.Information("生成情绪报告服务正在停止");
        await base.StopAsync(stoppingToken);
    }
}