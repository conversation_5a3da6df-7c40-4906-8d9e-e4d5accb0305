﻿using HX.HRV.Shared.Models.ViewModel;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Pages.Client.SystemConfig;
using Masa.Blazor;
using Microsoft.JSInterop;
using System.Text;
using System.Text.Json.Nodes;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;
using System.Net.WebSockets;
using System.Text.Json;

namespace HX.HRV.Shared.Services
{
    public class DeviceStateService : IDisposable
    {
        private readonly StateService _stateService;
        private readonly IJSRuntime _jsRuntime;
        private readonly CancellationTokenSource _cancellationToken;

        public IPopupService PopupService;
        public ClientWebSocket ClientSocket { get; private set; }


        private bool disposedValue;

        public DeviceStateService(StateService stateService, IJSRuntime jsRuntime, IPopupService popupService)
        {
            _stateService = stateService;
            _jsRuntime = jsRuntime;
            PopupService = popupService;
            _cancellationToken = new CancellationTokenSource();
            Task.Run(async () =>
            {
                await InitHxSystemConfig();
                await InitDevicesAsync();
                InitWebsocket();
                await InitPatientRecordModelListAsync();
                UpdateDeviceTaskTimer();
                return Task.CompletedTask;
            }).Wait();
        }

        public List<DeviceStatusViewModel> SerialPortDeviceDataList =>
            DeviceDataList.Where(m => SerialPortDevices.Any(x => x.DeviceSN == m.Device.DeviceSN))?.ToList() ??
            new List<DeviceStatusViewModel>();

        public List<SerialPortDevice> SerialPortDevices { get; private set; } = new List<SerialPortDevice>();


        public HxSystemConfigModel SystemConfig { get; private set; } = new();

        // 示例状态
        public List<DeviceStatusViewModel> DeviceDataList { get; private set; } = new List<DeviceStatusViewModel>();

        // 状态变更事件
        //public event Action OnChange;

        public event Action OnAfterBegianCheck;

        //设备变更
        public event Action OnDeviceNumberChange;

        //统计数据更新
        public event Action OnSummaryChange;


        public Action<JsonNode> OnMessageReceived;


        private const string DeviceTypeId = "2407160100000001";

        public async Task InitDevicesAsync()
        {
            var queryParam = new Dictionary<string, string>
            {
                { "typeId", DeviceTypeId },
                { "page", "0 " },
                { "pageSize", "100" },
            };
            var deviceListUrl = "/api/v2.0/IoT/Devices/List";
            var result = await _stateService
                .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
            var DeviceList = result?.Data
                ?.Select(m => m.Data)
                ?.OrderBy(m => int.TryParse(m.Name, out var num) ? num : 0)
                .ToList();
            if (SystemConfig.LayoutConfig is { DisplayNumber: > 0 })
            {
                DeviceDataList = DeviceList?.Take(SystemConfig.LayoutConfig.DisplayNumber)?.Select(m =>
                {
                    var batteryStr = m?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");

                    var battery = batteryStr?.Value?.Value switch
                    {
                        JsonElement value => value.GetDouble(),
                        double d => d,
                        int i => i,
                        _ => 0d
                    };
                    return new DeviceStatusViewModel()
                    {
                        Device = m,
                        DeviceSN = m.DeviceSN,
                        Battery = battery,
                        DeviceStatus = m.IsOnline ? EnumDeviceStatus.空闲 : EnumDeviceStatus.离线,
                    };
                }).ToList();
            }
            else
            {
                DeviceDataList = DeviceList?.Select(m =>
                {
                    var batteryStr = m?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");

                    var battery = batteryStr?.Value?.Value switch
                    {
                        JsonElement value => value.GetDouble(),
                        double d => d,
                        int i => i,
                        _ => 0d
                    };
                    return new DeviceStatusViewModel()
                    {
                        Device = m,
                        DeviceSN = m.DeviceSN,
                        Battery = battery,
                        DeviceStatus = m.IsOnline ? EnumDeviceStatus.空闲 : EnumDeviceStatus.离线,
                    };
                }).ToList();
            }
        }

        /// <summary>
        /// 初始化系统配置
        /// </summary>
        public async Task InitHxSystemConfig()
        {
            var systemConfig = await _stateService
                .GetAsJsonAsync<DataModel<HxSystemConfigModel>>(
                    "/api/v2.0/HRV_HX/HxSystemConfig/GetSystemConfigModelDetail");
            SystemConfig = systemConfig?.Data?.Data ?? new HxSystemConfigModel
            {
                IsAutoPrint = false,
                CheckTimeList = new(),
                PatientSource = new()
            };
            SystemConfig.SystemRateConfig ??= DefalultData.SystemRateConfig;
        }


        public void InitWebsocket()
        {
            // _ = UpdateDeviceTask();
            _ = Task.Run(async () =>
            {
                while (!_cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        if (ClientSocket == null ||
                            (ClientSocket.State != WebSocketState.Open
                             && ClientSocket.State != WebSocketState.Connecting))
                        {
                            var currentUri =
                                _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
                            var webSocketUrl =
                                $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
                            ClientSocket = new ClientWebSocket();
                            var serverUri = new Uri(webSocketUrl);
                            await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
                            Console.WriteLine(" \r\nDefaultLayout,OnReceiveMessageAsync ClientWebSocket State：\r\n" +
                                              ClientSocket.State);
                            await Task.Delay(50);
                        }

                        if (ClientSocket.State != WebSocketState.Open)
                        {
                            Console.WriteLine(" \r\nDefaultLayout,OnReceiveMessageAsync ClientWebSocket State：\r\n" +
                                              ClientSocket.State);
                            continue;
                        }

                        var buffer = new byte[1024 * 1024];
                        var result = await ClientSocket.ReceiveAsync(buffer, CancellationToken.None);
                        if (result.MessageType == WebSocketMessageType.Close)
                        {
                            var currentUri =
                                _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
                            var webSocketUrl =
                                $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
                            ClientSocket = new ClientWebSocket();
                            var serverUri = new Uri(webSocketUrl);
                            await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
                            await Task.Delay(50);
                        }

                        if (result.MessageType != WebSocketMessageType.Close && result.Count > 0)
                        {
                            var msg = Encoding.UTF8.GetString(buffer, 0, result.Count);
                            var jsonNode = JsonNode.Parse(msg);
                            await OnReceiveMessageAsync(jsonNode);
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(" \r\nDefaultLayout,OnReceiveMessageAsync error：\r\n" + e);
                        Console.WriteLine(e.ToString());
                        await Task.Delay(50);
                        if (ClientSocket.State != WebSocketState.Open)
                        {
                            var currentUri =
                                _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
                            var webSocketUrl =
                                $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
                            ClientSocket = new ClientWebSocket();
                            var serverUri = new Uri(webSocketUrl);
                            await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
                            await Task.Delay(50);
                        }
                    }
                }
            });
        }

        private async Task OnReceiveMessageAsync(JsonNode jsonNode)
        {
            try
            {
                var deviceSn = jsonNode?["SN"]?.ToString();

                if (!string.IsNullOrEmpty(deviceSn))
                {
                    OnMessageReceived?.Invoke(jsonNode);
                    var deviceViewModel = DeviceDataList.FirstOrDefault(m =>
                        m.DeviceSN.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
                    if (deviceViewModel != null)
                    {
                        if (!string.IsNullOrEmpty(deviceSn))
                        {
                            var status = jsonNode["Status"]?.GetValue<int>();
                            if (status != null && Enum.IsDefined(typeof(EnumDeviceStatus), status))
                            {
                                if ((EnumDeviceStatus)status == EnumDeviceStatus.检测中)
                                {
                                    if (deviceViewModel.DeviceStatus == EnumDeviceStatus.空闲)
                                    {
                                        OnAfterBegianCheck?.Invoke();
                                    }

                                    deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
                                    deviceViewModel.IsBegining = false;
                                    deviceViewModel.IsPrint = false;
                                }
                                else if ((EnumDeviceStatus)status == EnumDeviceStatus.空闲 &&
                                         deviceViewModel.DeviceStatus == EnumDeviceStatus.检测中)
                                {
                                    deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
                                }
                                else
                                {
                                    deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
                                }
                            }

                            //电量
                            var battery = jsonNode["Battery"]?.GetValue<double>();
                            if (battery != null)
                            {
                                deviceViewModel.Battery = (double)battery;
                            }

                            //SPO2
                            var SPO2 = jsonNode["SPO2"]?.GetValue<uint>();
                            if (SPO2 != null)
                            {
                                deviceViewModel.OnSPO2Data?.Invoke(SPO2 ?? 0);
                                deviceViewModel.SPO2Data.Add((uint)SPO2);
                            }

                            //Bmp
                            var Bmp = jsonNode["Bmp"]?.GetValue<uint>();
                            if (Bmp != null)
                            {
                                deviceViewModel.OnReciveBMPDatas?.Invoke(Bmp ?? 0);
                                deviceViewModel.BMPDatas.Add((uint)Bmp);
                            }

                            //PPG
                            if (jsonNode["PPG"] is JsonArray PPG)
                            {
                                var ppgData = PPG.GetValues<int>()?.ToList();
                                deviceViewModel.OnRecivePPGData?.Invoke(ppgData);
                                deviceViewModel.PPGData.AddRange(ppgData);
                            }

                            if (jsonNode["ORGPPG"] is JsonArray ORGPPG)
                            {
                                var ppgData = ORGPPG.GetValues<uint>()?.ToList();
                                deviceViewModel.OnOrgPPGData?.Invoke(ppgData);
                                deviceViewModel.OrgPPGData.AddRange(ppgData);
                            }

                            //PPG
                            if (jsonNode["IMU"] is JsonArray IMU)
                            {
                                var imuData = IMU.GetValues<short>();
                                deviceViewModel.ImuDatas.AddRange(imuData);
                            }

                            //PPG
                            if (jsonNode["SKINTEMP"] is JsonArray SKINTEMP)
                            {
                                var imuData = SKINTEMP.GetValues<float>();
                                deviceViewModel.SKINTEMPs.AddRange(imuData);
                            }

                            //PPG
                            if (jsonNode["SCL"] is JsonArray SCL)
                            {
                                var SCLData = SCL.GetValues<float>()?.ToList();
                                deviceViewModel.SCLDatas.AddRange(SCLData);
                                deviceViewModel.OnReciveSCLDatasData?.Invoke(SCLData);
                            }

                            //PPG
                            if (jsonNode["SCR"] is JsonArray SCR)
                            {
                                var SCRData = SCR.GetValues<float>()?.ToList();
                                deviceViewModel.SCRDatas.AddRange(SCRData);
                                deviceViewModel.OnReciveSCRDatasData?.Invoke(SCRData);
                            }

                            if (jsonNode["EDA"] is JsonArray EDA)
                            {
                                var EDAData = EDA.GetValues<float>()?.ToList();
                                deviceViewModel.OnReciveEDAsData?.Invoke(EDAData);
                                deviceViewModel.EDAs.AddRange(EDAData);
                            }

                            //deviceRecord
                            var deviceRecord = jsonNode["DeviceRecord"]?.GetValue<PatientRecordModel>();
                            if (deviceRecord != null)
                            {
                                deviceViewModel.RecordModel = deviceRecord;
                            }

                            //deviceRecord
                            var progress = jsonNode["Progress"]?.GetValue<double>();
                            if (progress != null)
                            {
                                deviceViewModel.Progress = (double)progress;
                            }

                            if (jsonNode.AsObject().ContainsKey("CollectTime"))
                            {
                                var collectTime = jsonNode["CollectTime"]?.ToString();
                                if (collectTime != null && TimeSpan.TryParse(collectTime, out var timeSpan))
                                {
                                    deviceViewModel.CollectTime = timeSpan;
                                }
                            }

                            var IsChecked = jsonNode["IsChecked"]?.GetValue<bool>();
                            if (IsChecked ?? false)
                            {
                                deviceViewModel.ClearData();
                            }

                            var isPrint = jsonNode["IsPrint"]?.GetValue<bool>();
                            if (isPrint == true)
                            {
                                var printId = jsonNode["PrintId"]?.ToString();
                                await _jsRuntime.InvokeAsync<object>("SendPrintToWebView", printId);
                            }

                            deviceViewModel.NotifyPropertyChanged();
                        }
                    }
                    else
                    {
                        var status = jsonNode["Status"]?.GetValue<int>();
                        if (status != null && Enum.IsDefined(typeof(EnumDeviceStatus), status))
                        {
                            if ((EnumDeviceStatus)status == EnumDeviceStatus.空闲)
                            {
                                var deviceListUrl = "/api/v2.0/IoT/Devices/List";
                                var queryParam = new Dictionary<string, string>
                                {
                                    { "deviceSN", deviceSn },
                                    { "page", "0 " },
                                    { "pageSize", "1" },
                                };
                                var result = await _stateService
                                    .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
                                var device = result?.Data.FirstOrDefault()?.Data;
                                DeviceDataList.Add(new DeviceStatusViewModel()
                                {
                                    Device = device,
                                    DeviceSN = device.DeviceSN,
                                    Battery = 0,
                                    DeviceStatus = EnumDeviceStatus.空闲,
                                });
                                OnDeviceNumberChange?.Invoke();
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"OnReceiveMessageAsync error：\r\n{e}");
            }
        }

        private void UpdateDeviceTaskTimer()
        {
            _ = Task.Run(async () =>
            {
                await Task.Delay(2000);
                while (true)
                {
                    try
                    {
                        await Task.Delay(2000);
                        if (_cancellationToken.IsCancellationRequested)
                        {
                            break;
                        }

                        var deviceListUrl = "/api/v2.0/IoT/Devices/List";
                        var queryParam = new Dictionary<string, string>
                        {
                            { "typeId", DeviceTypeId },
                            { "page", "0 " },
                            { "pageSize", "100" },
                        };
                        var result = await _stateService
                            .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
                        var devices = result
                            ?.Data
                            ?.Select(m => m.Data)
                            ?.ToList();
                        if (devices != null && DeviceDataList.Exists(m => !devices.Exists(x => x.Id == m.Device.Id)))
                        {
                            DeviceDataList.RemoveAll(m => !devices.Exists(x => x.Id == m.Device.Id));
                            OnDeviceNumberChange?.Invoke();
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine("HXDefaultLayout  Error" + e.Message);
                    }
                }
            }, _cancellationToken.Token);
        }

        private async Task InitPatientRecordModelListAsync()
        {
            var deviceIds = DeviceDataList.Select(m => m.Device.Id).ToArray();
            var res = await _stateService
                .GetAsJsonAsync<List<PatientRecordModel>>(
                    "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelList", new Dictionary<string, string>()
                    {
                        { "deviceIds", string.Join(",", deviceIds) }
                    });
            var recordModels = res?.Data?.ToList();
            if (recordModels != null)
            {
                foreach (var record in recordModels)
                {
                    var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == record.DeviceId);
                    if (deviceData != null && deviceData.RecordModel == null) deviceData.RecordModel = record;
                }
            }
        }

        public void Dispose()
        {
            _cancellationToken.Cancel();
        }

        public PatientRecordModel GetPatientRecordModel(string deviceId)
        {
            var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
            return deviceData?.RecordModel;
        }

        public DeviceStatusViewModel GetDeviceStatusViewModel(string deviceId)
        {
            var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
            return deviceData;
        }
    }
}