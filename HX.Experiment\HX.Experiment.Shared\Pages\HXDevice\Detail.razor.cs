using HX.Experiment.Shared.Services;
using Microsoft.AspNetCore.Components;
using UFU.IoT.Models;
using HX.Experiment.Shared.Model;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using Masa.Blazor;
using Microsoft.AspNetCore.Components.Web;

namespace HX.Experiment.Shared.Pages.HXDevice;

public partial class Detail : ComponentBase
{
    [Inject] IDeviceService DeviceService { get; set; }
    [Inject] public StateService StateService { get; set; }
    [Inject] private IPopupService PopupService { get; set; }
    [Inject] private IDataImportService DataImportService { get; set; }
    [Inject] private IDataAnalysisService DataAnalysisService { get; set; }

    private DeviceModel DeviceModel => DeviceService.CurrentDevice;
    private DiskInfo CurrentLinkedDevice => DeviceService.CurrentLinkedDevice;
    private DataModel<DeviceBindComputer>? BindInfo;
    private bool IsLoading = true;
    private string? ErrorMsg;

    // 新建检测相关属性
    private bool isShowNewTest = false;
    private StudentInfo DialogStudentData = new();

    // 数据导入相关属性
    private bool _isImporting = false;
    private int _importProgress = 0;

    // 数据分析相关属性
    private bool isShowDataAnalysis = false;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var res = await StateService.GetAsJsonAsync<DataModel<DeviceBindComputer>>("/api/v2/hx_experiment/DeviceBindComputer/Get",
            new Dictionary<string, string> { { nameof(DeviceBindComputer.ComputerId), DeviceService.ComputerId } });
            if (res.Success)
            {
                BindInfo = res.Data;
            }
            else
            {
                ErrorMsg = res.Message;
            }

        }
        catch (Exception ex)
        {
            ErrorMsg = ex.Message;
        }
        finally
        {
            IsLoading = false;
        }
    }



    private Task HandleBind(MouseEventArgs args)
    {
        return PopupService.ConfirmAsync(
              "绑定设备",
              "确认将设备绑定到当前电脑?",
              AlertTypes.Info,
              async args =>
              {
                  try
                  {
                      var url = "/api/v2/hx_experiment/DeviceBindComputer/Add";
                      var  data = new DeviceBindComputer()
                      {
                          ComputerId = DeviceService.ComputerId,
                      };
                      var res = await StateService.PostAsJsonAsync<DataModel<DeviceBindComputer>>(url, data);
                      if (res.Success)
                      {
                          await PopupService.EnqueueSnackbarAsync("绑定成功", AlertTypes.Success);
                      }
                      else
                      {
                          await PopupService.EnqueueSnackbarAsync(res.Message, AlertTypes.Error);
                      }
                  }
                  catch (Exception e)
                  {
                      args.Cancel();
                      await PopupService.EnqueueSnackbarAsync(e, true);
                  }
              });
    }

    /// <summary>
    /// 打开新建检测对话框
    /// </summary>
    private async Task OpenNewTestDialog()
    {
        if (DeviceModel == null || !DeviceModel.IsOnline)
        {
            await PopupService.EnqueueSnackbarAsync("设备未在线，无法开始检测！", AlertTypes.Warning);
            return;
        }

        // 重置学生数据
        DialogStudentData = new StudentInfo();
        isShowNewTest = true;
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    private async Task RefreshData()
    {
        try
        {
            var res = await StateService.GetAsJsonAsync<DataModel<DeviceBindComputer>>("/api/v2/hx_experiment/DeviceBindComputer/Get",
            new Dictionary<string, string> { { nameof(DeviceBindComputer.ComputerId), DeviceService.ComputerId } });
            if (res.Success)
            {
                BindInfo = res.Data;
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"刷新数据失败: {ex.Message}", AlertTypes.Error);
        }
    }

    private async Task HandleClearData()
    {
        if (CurrentLinkedDevice == null)
        {
            await PopupService.EnqueueSnackbarAsync("设备未连接！", AlertTypes.Warning);
            return;
        }

        var confirmed = await PopupService.ConfirmAsync(
            "确认清空数据",
            "此操作将清空设备中的所有检测数据，是否继续？",
            AlertTypes.Warning);

        if (confirmed)
        {
            try
            {
                var success = await DataImportService.ClearDeviceDataAsync(CurrentLinkedDevice.ShowName);
                if (success)
                {
                    await PopupService.EnqueueSnackbarAsync("设备数据已清空！", AlertTypes.Success);
                    await RefreshData();
                }
                else
                {
                    await PopupService.EnqueueSnackbarAsync("清空数据失败！", AlertTypes.Error);
                }
            }
            catch (Exception ex)
            {
                await PopupService.EnqueueSnackbarAsync($"清空数据失败: {ex.Message}", AlertTypes.Error);
            }
        }
    }

    /// <summary>
    /// 数据导入
    /// </summary>
    private async Task ImportDataAsync()
    {
        if (DeviceModel == null || string.IsNullOrEmpty(DeviceModel.DeviceSN))
        {
            await PopupService.EnqueueSnackbarAsync("设备未连接，无法导入数据！", AlertTypes.Warning);
            return;
        }

        try
        {
            _isImporting = true;
            _importProgress = 0;
            StateHasChanged();

            // 检查是否有数据
            var hasData = await DataImportService.HasDataAsync(DeviceModel.DeviceSN);
            if (!hasData)
            {
                await PopupService.EnqueueSnackbarAsync("设备中没有检测数据！", AlertTypes.Warning);
                return;
            }

            // 订阅进度事件
            DataImportService.ProgressChanged += OnImportProgressChanged;

            // 这里需要一个检测记录ID，实际应用中可能需要让用户选择或创建
            var recordId = Guid.NewGuid().ToString("N"); // 临时生成，实际应该从检测记录中获取

            var success = await DataImportService.ImportDataAsync(DeviceModel.DeviceSN, recordId);

            if (success)
            {
                await PopupService.EnqueueSnackbarAsync("数据导入成功！", AlertTypes.Success);
            }
            else
            {
                await PopupService.EnqueueSnackbarAsync("数据导入失败！", AlertTypes.Error);
            }
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"数据导入失败: {ex.Message}", AlertTypes.Error);
        }
        finally
        {
            _isImporting = false;
            _importProgress = 0;
            DataImportService.ProgressChanged -= OnImportProgressChanged;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 导入进度变化事件处理
    /// </summary>
    private async void OnImportProgressChanged(object? sender, DataImportProgressEventArgs e)
    {
        _importProgress = e.Progress;
        await InvokeAsync(StateHasChanged);

        if (e.HasError)
        {
            await InvokeAsync(async () =>
                await PopupService.EnqueueSnackbarAsync(e.ErrorMessage ?? "导入过程中发生错误", AlertTypes.Error));
        }
    }

    /// <summary>
    /// 打开数据分析对话框
    /// </summary>
    private void OpenDataAnalysisDialog()
    {
        isShowDataAnalysis = true;
    }
}