﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Shared.Models;
using UFU.IoT.Shared.Utils;
namespace UFU.IoT.Services
{
    /// <summary>
    /// IoT服务
    /// </summary>
    public class CommandConfigWebSocketService
    {


        public class CommandConfigConnect
        {


            /// <summary>
            /// 当前网关
            /// </summary>
            public ConnectDevice ProxyDevice { get; set; }

            /// <summary>
            ///  命令配置
            /// </summary>
            public CommandConfigBindingModel CommandConfigBinding { get; set; }

            /// <summary>
            ///  接收到的设备
            /// </summary>
            public ConnectDevice ReceiveToDevice { get; set; }
        }

        /// <summary>
        /// 命令配置
        /// </summary>
        private static List<CommandConfigConnect> CommandConfigBindings;

        /// <summary>
        /// 实时数据保存队列
        /// </summary>
        private static Dictionary<string, ConcurrentQueue<QueueData>> RealTimeDataQueue { set; get; } =
            new();

        private class QueueData
        {

            public CommandConfigBindingModel CommandConfig { get; set; }

            /// <summary>
            /// 最后一次入队时间
            /// </summary>
            public DateTime LastEnqueueDate { get; set; }
        }


        /// <summary>
        ///  获取命令配置
        /// </summary>
        /// <returns></returns>
        public static async Task InitOnlineDeviceCommandConfig()
        {

            if (IoTService.Devices == null || IoTService.Devices.Count == 0)
            {
                LogTool.Logger.Information("没有找到设备");
                return;
            }
            
            var proxyDeviceIds = IoTService.Devices.Select(m => m.Value.Device.Id).ToArray();

            using (DataRepository db = new DataRepository())
            {
                var query = from binding in db.Query<CommandConfigBindingModel>().DefaultIfEmpty()
                            from command in db.Query<CommandConfigModel>(PermissionSwitch.Default)
                                                    .Where(m => m.Data.Id == binding.Data.CommandConfigId)
                                                    .DefaultIfEmpty()
                            select new CommandConfigBindingModel
                            {
                                Id = binding.Id,
                                DeviceAddress = binding.Data.DeviceAddress,
                                CommandConfigId = binding.Data.CommandConfigId,
                                DeviceId = binding.Data.DeviceId,
                                ProxyDeviceId = binding.Data.ProxyDeviceId,
                                CommandConfig = command.Data,
                            };

                if (proxyDeviceIds != null && proxyDeviceIds.Length > 0)
                {
                    query = query.Where(m => proxyDeviceIds.Contains(m.ProxyDeviceId));
                }
                var list = await query
                   .ToListAsync();
                var allDevices = await db.Query<DeviceModel>()
                .Where(m => proxyDeviceIds.Contains(m.Id))
                .Select(m => m.Data).ToListAsync();
                foreach (var item in list)
                {
                    item.DeviceModel = allDevices.FirstOrDefault(m => m.Id == item.DeviceId);
                    item.ProxyDeviceModel = allDevices.FirstOrDefault(m => m.Id == item.ProxyDeviceId);
                    var toDevice = IoTService.Devices.FirstOrDefault(m => m.Value.Device.Id == item.ProxyDeviceId);

                    var proxyDevice = IoTService.Devices.FirstOrDefault(m => m.Value.Device.Id
                    == item.DeviceId);
                    if (toDevice.Value != null && proxyDevice.Value != null)
                    {
                        LogTool.Logger.Information($@"【网关:{proxyDevice.Value.Device.Name}】 接收设备：【{item.DeviceModel.Name}】,【配置地址：{item.DeviceAddress}】");
                        CommandConfigBindings.Add(new CommandConfigConnect()
                        {
                            ProxyDevice = toDevice.Value,
                            CommandConfigBinding = item,
                            ReceiveToDevice = proxyDevice.Value
                        });
                    }
                }
            }
        }
        /// <summary>
        /// 发送命令
        /// </summary>
        static public async void BatchSendCommand()
        {
            foreach (var item in CommandConfigBindings)
            {
                var ProxyDeviceId = item.ProxyDevice.Connect.ConnectId;
                var command = item.CommandConfigBinding.BuildCommand();
                if (!RealTimeDataQueue.ContainsKey(ProxyDeviceId))
                {
                    RealTimeDataQueue[ProxyDeviceId] = new ConcurrentQueue<QueueData>();
                }
                RealTimeDataQueue[ProxyDeviceId].Enqueue(new QueueData(){
                    CommandConfig = item.CommandConfigBinding,
                    LastEnqueueDate = DateTime.Now
                });
                await SendCommand(item.ProxyDevice, command);
            }
        }

        static async Task SendCommand(ConnectDevice connectDevice, string command)
        {
            connectDevice.Connect.SendBinary(Encoding.UTF8.GetBytes(command));
            await Task.CompletedTask;
        }

        /// <summary>
        /// 
        /// </summary>
        static CommandConfigWebSocketService()
        {
            //初始化
            InitOnlineDeviceCommandConfig().GetAwaiter().GetResult();
            //定时任务 每个5秒执行一次
            Task.Run(() =>
            {
                while (true)
                {
                    BatchSendCommand();
                    Thread.Sleep(1000);
                }
            });
            //定时任务 定时更新Command
            Task.Run(async () =>
            {
                while (true)
                {
                    await InitOnlineDeviceCommandConfig();
                    Thread.Sleep(6000);
                }
            });
            //定时任务 每个100ms执行一次 检查队列中的数据 超时时间为1秒
            Task.Run(() =>
            {
                while (true)
                {
                    CheckQueueData();
                    Thread.Sleep(100);
                }    
            });
            IoTService.OnReceivedDeviceMsg += ReceiveCommand;
        }
        private static int Timeout = 1000;
        private static void CheckQueueData()
        {
            foreach (var item in RealTimeDataQueue)
            {
                if (item.Value == null||item.Value.IsEmpty) continue;
                if (DateTime.Now - item.Value.Last().LastEnqueueDate > TimeSpan.FromSeconds(Timeout))
                {
                    item.Value.TryDequeue(out var _);
                }
            }
        }

        static void ReceiveCommand(IConnect connect, string command)
        {
            var json = JsonNode.Parse(command);
            if (json == null) return;
            if (!RealTimeDataQueue.ContainsKey(connect.ConnectId)) return;
            var quenue = RealTimeDataQueue[connect.ConnectId];
            if (quenue == null) return;
            var connectConfig = quenue.TryDequeue(out var queueData);
            if (queueData != null)
            {
                var key = queueData.CommandConfig.DataItem.Key;
                if (!json.AsObject().ContainsKey(key)) return;
                var jsonObject = json[key];
                if (jsonObject == null) return;
                var str = jsonObject.ToString();
                var data = queueData.CommandConfig.AnalysisToJson(str);
                var config = CommandConfigBindings
                .FirstOrDefault(m => m.ProxyDevice.Connect.ConnectId == connect.ConnectId);
                if (config == null) return;
                IoTService.ReceiveDeviceMsgV1(config.ReceiveToDevice.Connect, command);
            }
        }




    }
}