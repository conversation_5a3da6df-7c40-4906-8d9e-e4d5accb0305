﻿namespace HX.HRV.Shared.Models;

public class ReportDataModel
{
    public ReportDataModel(string key, string name, string unit)
    {
        Key = key;
        Name = name;
        Unit = unit;
    }

    public ReportDataModel()
    {
        
    }
    public ReportDataModel(ReportDataModel model,string value)
    {
        Key = model.Key;
        Name = model.Name;
        Unit = model.Unit;
        Value = value;
    }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    public string Key { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public string Value { get; set; }

    public static ReportDataModel[] timeDomain = new ReportDataModel[]
    {
        new ReportDataModel("hr_std", "STDHR", "ms"),
        new ReportDataModel("sdnn", "SDNN", "ms"),
        new ReportDataModel("rmssd", "RMSSD", "ms"),
        new ReportDataModel("nn50", "NN50", "次"),
        new ReportDataModel("pnn50", "pNN50", "%"),
        new ReportDataModel("tinn", "TINN", "ms")
    };
    
    
  

    public static ReportDataModel[] nonlinearAnalysis = new ReportDataModel[]
    {
        new ReportDataModel("sd1", "SD1", "ms"),
        new ReportDataModel("sd2", "SD2", "ms"),
         new ReportDataModel("dfa_alpha1", "α1", "-"),
        new ReportDataModel("dfa_alpha2", "α2", "-"),
        new ReportDataModel("Apen", "ApEn", "-"),
        new ReportDataModel("Sampen", "SampEn", "-"),
        new ReportDataModel("D2", "D2", "-"),
       
    };

    public static ReportDataModel[] frequencyDomain = new ReportDataModel[]
    {
        new ReportDataModel("VLF_peak", "VLF", "Hz"),
        new ReportDataModel("LF_peak", "LF", "Hz"),
        new ReportDataModel("HF_peak", "HF", "Hz"),
        new ReportDataModel("VLF_power", "VLF功率", "ms²"),
        new ReportDataModel("LF_power", "LF功率", "ms²"),
        new ReportDataModel("HF_power", "HF功率", "ms²"),
        new ReportDataModel("LF_norm", "LF标化功率", "%"),
        new ReportDataModel("HF_norm", "HF标化功率", "%"),
        new ReportDataModel("TP_power", "总功率", "ms²"),
        new ReportDataModel("VLF_ratio", "VLF功率比", "%"),
        new ReportDataModel("LF_ratio", "LF功率比", "%"),
        new ReportDataModel("HF_ratio", "HF功率比", "%"),
        new ReportDataModel("LF_HF_ratio", "LF/HF", "-"),
    };
    
    
    public static ReportDataModel[] ShowTimeDomain = {
        new ReportDataModel("sdnn", "SDNN", "ms"),
        new ReportDataModel("rmssd", "RMSSD", "ms"),
        new ReportDataModel("nn50", "NN50", "次"),
        new ReportDataModel("pnn50", "pNN50", "%"),
    };
    
    public static ReportDataModel[] ShowFrequencyDomain = {
        new ReportDataModel("VLF_peak", "VLF", "Hz"),
        new ReportDataModel("LF_peak", "LF", "Hz"),
        new ReportDataModel("HF_peak", "HF", "Hz"),
        new ReportDataModel("VLF_power", "VLF功率", "ms²"),
        new ReportDataModel("LF_power", "LF功率", "ms²"),
        new ReportDataModel("HF_power", "HF功率", "ms²"),
        new ReportDataModel("LF_norm", "LF标化功率", "%"),
        new ReportDataModel("HF_norm", "HF标化功率", "%"),
        new ReportDataModel("TP_power", "总功率", "ms²"),
        new ReportDataModel("LF_HF_ratio", "LF/HF", "-"),
    };
    
    public static ReportDataModel[] ShowNonlinearAnalysis = new[]
    {
        new ReportDataModel("Apen", "ApEn", "-"),
    };
}



public class ReportDataStatistics
{
    public Dictionary<string, string> StatisticsDictionary { get; set; } = new Dictionary<string, string>();
    public List<int> HRList { get; set; }
    public List<int> NniList { get; set; }
    public List<int> FrequencyList { get; set; }
    public List<int> PsdList { get; set; }
}