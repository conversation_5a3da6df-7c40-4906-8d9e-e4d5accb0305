﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UFU.CoreFX.Models;

namespace UFU.IoT.Models
{
    /// <summary>
    /// 自动策略
    /// </summary>
    [Table("iot_automatic_policy")]
    public class AutomaticPolicyModel
    {
        /// <summary>
        /// 策略编号
        /// </summary>
        [Column("id"), Key, Display(Name = "策略编号")] 
        public string Id { get; set; }
        /// <summary>
        /// 策略名称
        /// </summary>
        [Column("name"), Required, Display(Name = "策略名称")] 
        public string Name { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("enable"), Required, Display(Name = "是否启用")] 
        public bool Enable { get; set; }
        /// <summary>
        /// 条件（同时满足才生效）
        /// </summary>
        [Column("conditions"), Required, Display(Name = "条件")]
        public List<ConditionModel> Conditions { get; set; } = new List<ConditionModel>();
        /// <summary>
        /// 执行动作
        /// </summary>
        [Column("actions"), Display(Name = "执行动作")]
        public List<ActionModel> Actions { get; set; }
        /// <summary>
        /// 发送通知
        /// </summary>
        [Column("messages"), Display(Name = "发送通知")]
        public List<dynamic> Messages { get; set; }
    }

    /// <summary>
    /// 条件
    /// </summary>
    public class ConditionModel
    { 
        /// <summary>
        /// 场地编号
        /// </summary>
        public string PlaceId { get; set; }
        /// <summary>
        /// 设备类型编号
        /// </summary>
        public string DeviceTypeId { get; set; }
        /// <summary>
        /// 设备编号
        /// </summary>
        public string DeviceId { get; set; }
        /// <summary>
        /// 数据分组
        /// </summary>
        public string DataGroup { get; set; }
        /// <summary>
        /// 数据项
        /// </summary>
        public string DataItem { get; set; }
        /// <summary>
        /// 数据周期（秒）
        /// </summary>
        public decimal Cycle { get; set; }
        /// <summary>
        /// 持续时间（秒）
        /// </summary>
        public decimal Duration { get; set; }
        /// <summary>
        /// 统计数据类型
        /// 实时值-RTD，平均值-AVG，最大值-MAX，最小值-MIN,累计值-SUM
        /// </summary>
        public string StatisticType { get; set; }
        /// <summary>
        /// 操作符
        /// 等于-[=]，不等于-[!=]，大于-[>]，大于等于-[>=]，小于-[&lt;]，小于等于-[&lt;=]，包含-[C]，以此开头-[SW]，以此结尾-[ED]
        /// </summary>
        public string Operator { get; set; }
        /// <summary>
        /// 阈值
        /// </summary>
        public dynamic Value { get; set; }
        /// <summary>
        /// 首次触发时间
        /// </summary>
        public DateTime? TriggerTime { get; set; }
    }

    /// <summary>
    /// 条件
    /// </summary>
    public class ActionModel
    {
        /// <summary>
        /// 场地编号
        /// </summary>
        public string PlaceId { get; set; }
        /// <summary>
        /// 设备类型编号
        /// </summary>
        public string DeviceTypeId { get; set; }
        /// <summary>
        /// 设备编号
        /// </summary>
        public string DeviceId { get; set; }
        /// <summary>
        /// 数据项
        /// </summary>
        public string DataItem { get; set; }
        /// <summary>
        /// 延迟执行时间（秒）
        /// </summary>
        public decimal Delay { get; set; }
        /// <summary>
        /// 阈值
        /// </summary>
        public dynamic Value { get; set; }
    }

    /// <summary>
    /// 通知消息
    /// </summary>
    public class PolicyMsg : IMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public PolicyMsg()
        {
            Area = "IoT";
            Func = "Policy";
        }
        /// <summary>
        /// 通知场地用户
        /// 管理员（Admin），场地用户（User）
        /// </summary>
        public List<string> PlaceUser { get; set; }


    }



}
