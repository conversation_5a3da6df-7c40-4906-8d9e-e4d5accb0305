﻿using Microsoft.Extensions.Configuration;

namespace HX.HRV.Shared.Units;

public class HrvVariable
{
    public string SysName { get; set; }
    public string ReportReturnUrl { get; set; }
    public string ExportRegex { get; set; }
    public string DefaultPassword { get; set; }
    public bool IsShowExport { get; set; }
    public string PPGALG { get; set; }

    private static HrvVariable _Instance;
    private static Object _Lock = new();



    public HrvVariable(IConfiguration configuration)
    {
        configuration.GetSection("HrvVariable").Bind(this);
        HrvVariableInstance = this;
    }
    public static HrvVariable HrvVariableInstance{get;private set;}
   
}