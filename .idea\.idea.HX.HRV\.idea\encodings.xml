<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" addBOMForNewFiles="with BOM under Windows, with no BOM otherwise">
    <file url="file://$PROJECT_DIR$/../HX.Base/HX.Base.Shared/Components/HXEchartSegmentLazyComponent.razor" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/HX.HRV.MAUI/publish.bat" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/HX.HRV.MDns/install.bat" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/HX.HRV.Shared/Pages/Client/DeviceStatus/DeviceSummaryComponent.razor" charset="GBK" />
    <file url="file://$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT.Shared/Pages/AutoRule/Edit.razor" charset="GBK" />
    <file url="file://$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT.Shared/Pages/AutoRule/Edit.razor.cs" charset="GBK" />
    <file url="file://$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT.Shared/Pages/AutoRule/List.razor" charset="GBK" />
    <file url="file://$PROJECT_DIR$/../../NetCore/UFU.IoT.V3/UFU.IoT.Shared/Pages/AutoRule/List.razor.cs" charset="GBK" />
  </component>
</project>