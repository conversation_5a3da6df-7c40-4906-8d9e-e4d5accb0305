using System.Globalization;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using System.Threading.Tasks;
using HX.HRV.SCI.Shared.Models;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.CSVDataModel;
using HX.HRV.Shared.Units;
using HX.HRV.Web.Units;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.IoT.Shared.Models;

namespace HX.HRV.SCI.Web.Areas.SCI.Controllers;

/// <summary>
/// 检测记录
/// </summary>
[Area("SCI")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class DataAnalysisRecordController : Controller
{
    private readonly DataRepository _context;
    private readonly CoreDbContext _coreContext;

    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// 多参数生理及行为研究平台数据分析接口
    /// </summary>
    /// <param name="context"></param>
    /// <param name="coreContext"></param>
    public DataAnalysisRecordController(DataRepository context, CoreDbContext coreContext)
    {
        _context = context;
        _coreContext = coreContext;

        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.IgnoreNullValues = true;
    }


    /// <summary>
    /// 多参数生理及行为研究平台数据分析接口/详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("多参数生理及行为研究平台数据分析接口/详情", IsAPI = true)]
    public async Task<Result<DataModel<DataAnalysisRecordModel>>> GetDataAnalysisRecordModelDetailById(string id)
    {
        var dataAnalysisRecordModel = await _context.Query<DataAnalysisRecordModel>()
            .OrderByDescending(m => m.Id)
            .FirstOrDefaultAsync(m => m.Id == id);
        var user = await new UserService().GetUserInfoByIdAsync(_coreContext, dataAnalysisRecordModel.UserId);
        dataAnalysisRecordModel.User = user.Data;
        var records = await _context.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == dataAnalysisRecordModel.Data.RecordId);
        dataAnalysisRecordModel.Data.PatientRecord = records?.Data;
        dataAnalysisRecordModel.Data.Patient = records.Data?.Patient;
        return new Result<DataModel<DataAnalysisRecordModel>>(dataAnalysisRecordModel);
    }

    /// <summary>
    /// 检测记录/详情
    /// </summary>
    /// <param name="id">产品详情</param>
    /// <returns></returns>
    [HttpGet]
    [Permission("多参数生理及行为研究平台数据分析接口/详情", IsAPI = true)]
    public async Task<Result<List<DataAnalysisRecordModel>>> GetDataAnalysisRecordModelList(
        [FromQuery] string recordIds)
    {
        var idlist = recordIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var dataAnalysisRecordModel = await _context.Query<DataAnalysisRecordModel>()
            .Where(m => idlist.Contains(m.Data.RecordId))
            .OrderByDescending(m => m.Id)
            .ToListAsync();

        var list = dataAnalysisRecordModel?.Select(m => m.Data)?.ToList() ?? new List<DataAnalysisRecordModel>();
        return new Result<List<DataAnalysisRecordModel>>(list);
    }

    /// <summary>
    /// 多参数生理及行为研究平台数据分析接口/编辑
    /// </summary>
    /// <param name="id">编号</param>
    /// <param name="DataAnalysisRecordModel">产品信息</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("多参数生理及行为研究平台数据分析接口/编辑", IsAPI = true)]
    public async Task<Result<DataAnalysisRecordModel>> Edit(string id,
        [FromBody] DataAnalysisRecordModel DataAnalysisRecordModel)
    {
        var result = new Result<DataAnalysisRecordModel>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(DataAnalysisRecordModel, result))
        {
            return result;
        }

        var model = await _context.Query<DataAnalysisRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (model == null)
        {
            result.AddError("设备类型不存在");
            return result;
        }

        _context.Update(model);
        await _context.SaveChangesAsync();
        result.Data = model.Data;
        return result;
    }
    [HttpGet()]
    [Permission("多参数生理及行为研究平台数据分析接口/列表", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<PageList<DataModel<DataAnalysisRecordModel>>>> List(
        [FromQuery] string recordCodeOrId,
        [FromQuery] int? page = 1,
        [FromQuery] int? pageSize = 100)
    {
        var query = _context.Query<DataAnalysisRecordModel>();
        if (!string.IsNullOrEmpty(recordCodeOrId?.Trim()))
        {
            var record = await _context.Query<PatientRecordModel>()
                .FirstOrDefaultAsync(m => m.Id == recordCodeOrId || m.Data.RecordCode == recordCodeOrId);
            UserInfo userInfo = ViewBag.User;
            query = query.Where(m => m.Data.RecordId == record.Id);
        }
        var dataModels = await query
            .OrderByDescending(m => m.AddTime)
            .ToPageListAsync(pageIndex: page.Value, pageSize: pageSize.Value);

        var recordIDs = dataModels.Select(m => m.Data.RecordId).ToList();

        var records = await _context.Query<PatientRecordModel>().Where(m => recordIDs.Contains(m.Id)).ToListAsync();


        foreach (var dataModel in dataModels)
        {
            dataModel.Data.PatientRecord = records.FirstOrDefault(m => m.Id == dataModel.Data.RecordId)?.Data;
            dataModel.Data.Patient = records.FirstOrDefault(m => m.Id == dataModel.Data.RecordId)?.Data?.Patient;
        }


        return new Result<PageList<DataModel<DataAnalysisRecordModel>>>()
        { Success = true, Data = dataModels, Page = dataModels.PageInfo };
    }

    /// <summary>
    /// 设备管理接口/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("多参数生理及行为研究平台数据分析接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string id)
    {
        var result = new Result<bool>();
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<DataAnalysisRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }

        _context.Remove(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        return result;
    }


    [HttpGet("{id}")]
    [Permission("多参数生理及行为研究平台数据分析接口/下载", AllowAllUser = true, IsAPI = true)]
    public async Task<IActionResult> DownloadStatic([FromRoute] string id)
    {
        var analysisRecord = _context.Query<DataAnalysisRecordModel>().FirstOrDefault(m => m.Id == id);
        var statistics = analysisRecord.Data.Statistics;
        var csvData = new List<List<string>>();
        csvData.Add(new List<string>() { "TimeDomain" }) ;
       
        csvData.Add( ReportDataModel.timeDomain.Select(m => $"{m.Name}")?.ToList());
        csvData.Add(ReportDataModel.timeDomain.Select(m =>
        {
            var unit = m.Unit;
            var value = statistics.StatisticsDictionary?.FirstOrDefault(o => o.Key == m.Key).Value;
            return $"{value} {unit}";
        })?.ToList());
        csvData.Add(new List<string>() { "FrequencyDomain" });
        csvData.Add(ReportDataModel.frequencyDomain.Select(m => $"{m.Name}")?.ToList());
        csvData.Add(ReportDataModel.frequencyDomain.Select(m =>
        {
            var unit = m.Unit;
            var value = statistics.StatisticsDictionary?.FirstOrDefault(o => o.Key == m.Key).Value;
            return $"{value} {unit}";
        })?.ToList());
        csvData.Add(new List<string>() { "NonlinearDomain" });
        csvData.Add(ReportDataModel.nonlinearAnalysis.Select(m => $"{m.Name}")?.ToList());
        csvData.Add(ReportDataModel.nonlinearAnalysis.Select(m =>
        {
            var unit = m.Unit; 
            var value = statistics.StatisticsDictionary?.FirstOrDefault(o => o.Key == m.Key).Value;
            return $"{value} {unit}";
        })?.ToList());
        
        var EDADatas = await CSVDataHelper.GetDirectoryEDAData(analysisRecord.Data.GetPath(analysisRecord.Id));

        var  EdaDaCalculateModel = new EDADataCalculateModel(EDADatas.Select(m => m.EDA).ToList());

        var ScrDaCalculateModel = new EDADataCalculateModel(EDADatas.Select(m => m.SCR).ToList());

        var SclDaCalculateModel = new EDADataCalculateModel(EDADatas.Select(m => m.SCL).ToList());

        csvData.Add(new List<string>() { "EDA TimeDomain" });
        
        csvData.Add(new List<string>() { "Name",
            "Mean",
            "MAX",
            "MIN",
            "Standard Deviation",
            "Variance" });
        csvData.Add(new List<string>() { "SC",
            EdaDaCalculateModel.AVG.ToString(),
            EdaDaCalculateModel.MAX.ToString(),
            EdaDaCalculateModel.MIN.ToString(),
            EdaDaCalculateModel.STD.ToString(),
            EdaDaCalculateModel.VAR.ToString() });
        
        csvData.Add(new List<string>() { "SCR",
            ScrDaCalculateModel.AVG.ToString(),
            ScrDaCalculateModel.MAX.ToString(),
            ScrDaCalculateModel.MIN.ToString(),
            ScrDaCalculateModel.STD.ToString(),
            ScrDaCalculateModel.VAR.ToString() });
        
        csvData.Add(new List<string>() { "SCL",
            SclDaCalculateModel.AVG.ToString(),
            SclDaCalculateModel.MAX.ToString(),
            SclDaCalculateModel.MIN.ToString(),
            SclDaCalculateModel.STD.ToString(),
            SclDaCalculateModel.VAR.ToString() });
   
        var str = string.Join("\n", csvData.Select(m => string.Join(",", m)));
        var fileBytes = Encoding.UTF8.GetBytes(str); // 读取文件内容
        return File(fileBytes, "application/octet-stream", $"{analysisRecord.Data.Name}_Statistics.csv"); // 返回文件下载
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [Permission("多参数生理及行为研究平台数据分析接口/分段加载", AllowAllUser = true, IsAPI = true)]
    public async Task<Result<Dictionary<string,  List<string>>>> GetSegmentData(string recordId,
        string startTime,
        string endTime)
    {
        DateTime segmentStartTime = default;
        DateTime segmentEndTime = default;
        if (!string.IsNullOrEmpty(startTime))
        { 
            segmentStartTime = DateTime.ParseExact(startTime, "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
        }
        if (!string.IsNullOrEmpty(endTime))
        {
            segmentEndTime = DateTime.ParseExact(endTime, "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
        }
        
        var record = await _context.Query<PatientRecordModel>()
            .FirstOrDefaultAsync(m => m.Id == recordId);
        var filePath = record.Data.GetRecordDirectoryPath();
    
        
        // Create a dictionary to store results
        var dic = new Dictionary<string, List<string>>
        {
            { "PPG", await FileDataHelper.ProcessFilesAsync(filePath, "*PPG*.csv", segmentStartTime, segmentEndTime) },
            { "EDA", await FileDataHelper.ProcessFilesAsync(filePath, "*EDA*.csv", segmentStartTime, segmentEndTime) }
        };
        
        return new Result<Dictionary<string,  List<string>>>(dic);
    }
    
  
}