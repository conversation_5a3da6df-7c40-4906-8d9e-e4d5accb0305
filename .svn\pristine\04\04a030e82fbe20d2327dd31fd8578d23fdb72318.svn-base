var RealTimeEchartData = []

RealTimeEchart = {
    InitChart: function (data, id, take, smooth, color) {
        console.log(id);
        RealTimeEchartData[id] = {
            data: [],
            markData: [],
            take: take,
            echart: {},
            color: color,
            option: {},
            smooth: smooth || false
        };
        this.InitEchartDom(id);
    },
    InitEchartDom: function (id) {
        let chartDom = document.getElementById(id);
        let ops = {};
        RealTimeEchartData[id].echart = echarts.init(chartDom, "", ops);
        let option = this.BuildOption(id);
        RealTimeEchartData[id].echart.setOption(option);
    },
    UpdateChartData: function (id, data) {
        if (data instanceof Array) {
            RealTimeEchartData[id].data=RealTimeEchartData[id].data.concat(data);
        } else {
            RealTimeEchartData[id].data.push(data);
        }
        if (RealTimeEchartData[id].data.length > RealTimeEchartData[id].take) {
            RealTimeEchartData[id].data.splice(0, RealTimeEchartData[id].data.length - RealTimeEchartData[id].take);
        }
        let option = this.BuildOption(id);
        try {
            RealTimeEchartData[id] && RealTimeEchartData[id].echart && RealTimeEchartData[id].echart.setOption(option);
            RealTimeEchartData[id] && RealTimeEchartData[id].echart && RealTimeEchartData[id].echart.resize();
        } catch (e) {
            this.InitEchartDom(id);
        }
    },
    AddMarkerData: function (id, tag) {
        RealTimeEchartData[id].markData.push({
            xAxis: RealTimeEchartData[id].data.length - 1,
            label:{
                formatter:tag
            },
            symbol:'none',
        });
    },
    DisposeChart: function (id) {
        RealTimeEchartData[id] && RealTimeEchartData[id].echart && RealTimeEchartData[id].echart.dispose && RealTimeEchartData[id].echart.dispose();
    },
    BuildOption: function (id, isMultiple) {
        let chartDom = document.getElementById(id);
        let max = Math.max(...RealTimeEchartData[id].data);
        let min = Math.min(...RealTimeEchartData[id].data);
        if (id.indexOf("HR") > 0) {
            max += 20;
            min -= 20;
        } else if (id.indexOf("SPO2") > 0) {
            max = 100;
            min -= 20;
        } else {
            max = "dataMax";
            min = "dataMin";
        }
        let grid = {};
        let series = [];
        if (isMultiple) {
            grid = RealTimeEchartData[id].data.map((_, index) => ({
                top: index * (RealTimeEchartData[id].getHeight() / (RealTimeEchartData[id].length - 1)) + 40,
                height: (RealTimeEchartData[id].getHeight() - 200) / (RealTimeEchartData[id].length - 1),
                left: '10%',
                right: '10%',
            }))
            ;
            series = RealTimeEchartData[id].data.map((data, index) => ({
                type: 'line', 
                showSymbol: false,
                color: RealTimeEchartData[id].color,
                smooth: RealTimeEchartData[id].smooth || false,
                data: data
            }));
        } else {

            grid = {
                top: 20,
                bottom: 10,
                left: 100,
                right: 10
            };
            series = [
                {
                    type: 'line',
                    showSymbol: false,
                    color: RealTimeEchartData[id].color,
                    smooth: RealTimeEchartData[id].smooth || false,
                    data: RealTimeEchartData[id].data,
                    markLine: {
                        symbol: ['none'],
                        label: {
                            show: true
                        },
                        lineStyle: {
                            color: 'red',
                            width: 2
                        },
                        data:  RealTimeEchartData[id].markData
                    }
                }
            ]
        }
        return {
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                axisLabel: {
                    formatter: function (value, index) {
                        return index;
                    },
                    show: false,

                },
                axisLine: {
                    onZero: false
                }
            },
            yAxis: {
                type: 'value',
                max: max,
                min: min,
            },
            grid: grid,
            series: series
        };
    },

}

