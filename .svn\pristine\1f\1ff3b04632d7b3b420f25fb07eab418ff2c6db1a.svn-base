﻿@page "/client/SCI/DataPlayback/{id}"
@attribute [Permission("数据回放", AllowAnonymous = true)]
@using HX.Base.Shared.Components
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Models
@using Masa.Blazor
@using Microsoft.AspNetCore.Components.Web
@using UFU.CoreFX.Models
@using UFU.CoreFX.Permission
@using UFU.CoreFX.Shared.Services
@using UFU.IoT.Shared.Models
@using UFU.IoT.Shared.Utils
<div>
        <MOverlay Value="_overlay">
            <MProgressCircular indeterminate Color="primary"
                               Size="100" Width="6" ></MProgressCircular>
        </MOverlay>
        <MSelect Items="HXDataTypes"
                 @bind-Value="_dataType"
                 TValue="HXDataType"
                 TItem="HXDataType"
                 TItemValue="HXDataType"
                 ItemText="u => u.GetDisplayName() + u.GetUnit()"
                 ItemValue="u => u"
                 Label="选择需要回放的数据类型："
                 Dense
                 Style="width: 200px;"
                 OnChange="OnSelectedChanged"
                 Solo></MSelect>
        <DocumentDetail
            @ref="childComponentRef"
            minDate="@patientRecordModel.CollectStartTime" Height="40"></DocumentDetail>
</div>

@code {
    bool _overlay= true;

    protected HXDataType[] HXDataTypes { get; } = Enum.GetValues<HXDataType>()
        .Where(m => m != HXDataType.TICK).ToArray();

    private HXDataType _dataType = HXDataType.PPG;
    [Parameter] public string id { get; set; }
    private PatientRecordModel patientRecordModel = new PatientRecordModel();
    private string FileContent { get; set; }

    [Inject] private StateService _stateService { get; set; }
    private bool isShow;
    private DocumentDetail? childComponentRef;
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }

    [Inject] DeviceStateService deviceStateService { get; set; }
    public List<string>? Rows { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var res = await _stateService
                .GetAsJsonAsync<DataModel<PatientRecordModel>>(
                    "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string>()
                    {
                        { "id", id }
                    });
            if (res == null || res.Success == false)
            {
                await deviceStateService.PopupService.EnqueueSnackbarAsync(!string.IsNullOrEmpty(res.Message) ? res.Message : "获取数据失败");
                return;
            }
            patientRecordModel = res.Data.Data;
            await InitFileContent();
        }

        await base.OnAfterRenderAsync(firstRender);
    }
    
    
    private void OnSelectedChanged(HXDataType hXDataType)
    {
        _overlay = true;
        Task.Run(async () =>
        {
            await InitFileContent(hXDataType);
            _overlay = false;
        });
    }

    private async Task InitFileContent(HXDataType hXDataType = HXDataType.PPG)
    {
        
        try
        {
            var res = await _stateService
                .GetAsJsonAsync<List<string>>(
                    "/api/v2.0/HRV_HX/PatientRecord/GetFileContent",
                    new Dictionary<string, string>()
                    {
                        { "recordId", id },
                        { "dataType", hXDataType.ToString() }
                    });
            if (res is not { Success: true } || res.Data == null || !res.Data.Any())
            {
                return;
            }
            Rows = res.Data;
            if (Rows == null || !Rows.Any())
            {
                return;
            }
            if (patientRecordModel?.CollectStartTime != null)
            {
                await childComponentRef.ParseCsv(Rows);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"InitFileContent:{e.Message}");
        }
        finally
        {
            _overlay = false;
            await InvokeAsync(StateHasChanged);
        }
    }
}