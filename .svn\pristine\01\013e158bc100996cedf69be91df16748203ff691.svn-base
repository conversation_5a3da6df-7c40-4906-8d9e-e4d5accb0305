﻿using UFU.CoreFX.Shared.Pages.Core.File;

namespace HX.HRV.Shared.Models;

public class SystemRateConfig
{
    /// <summary>
    ///  PPG
    /// </summary>
    public int PPGRate { get; set; }

    /// <summary>
    /// 九轴
    /// </summary>
    public int IMURate { get; set; }

    /// <summary>
    /// SKT采样率 皮肤温度
    /// </summary>
    public int SKTRate { get; set; }

    /// <summary>
    ///  血氧
    /// </summary>
    public int SPO2Rate { get; set; }

    /// <summary>
    ///  心率
    /// </summary>
    public int HRRate { get; set; }

    /// <summary>
    /// 气压 环境温度 光照
    /// </summary>
    public int ENVRate { get; set; }

    /// <summary>
    ///  皮肤电阻
    /// </summary>
    public int EDARate { get; set; }
}

public static class SystemRateConfigStatic
{
    public static readonly List<int> PpgRates = new List<int>()
    {
         50,
         100,
         200,
         500
    };
    /// <summary>
    /// 九轴
    /// </summary>

    public static List<int> ImuRates = new()
    {
        
        1,25,  50
    };
    
    /// <summary>
    /// 皮肤温度
    /// </summary>
    public static List<int> SktRates = new()
    {
        1,
        5,
        10,
    };
    
    /// <summary>
    /// 皮肤电
    /// </summary>
    public static List<int> EdaRates = new()
    {
        1,
        25,
        50,
    };
    
}