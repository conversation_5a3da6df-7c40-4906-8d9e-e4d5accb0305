﻿@page "/client/system/device/list"
@using HX.HRV.Shared.Services
@using UFU.IoT.Models
@layout HRVDefaultLayout
@attribute [Permission("系统配置/设备管理",MenuOrder = "6", Icon = "mdi-view-dashboard|xitong", IsMenu = true)]

<div>
    <MForm Class="ma-2">
        <MRow Class="search-row">
            <MCol Cols="3" Class="d-flex align-center justify-center">
                <MTextField Class="customer-input" @bind-Value="DeviceName"
                            Solo
                            Dense>
                    <PrependContent>
                        <MLabel Class="mr-2">设备编号:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
            <MCol Cols="2" Class="d-flex align-start justify-space-between">
                <MButton Class="customer-button" OnClick="InitDataList">搜索</MButton>
            </MCol>
        </MRow>
    </MForm>
    <MCard Class="ma-2">
        <MDataTable
            OnOptionsUpdate="@HandleOnOptionsUpdate"
            TItem="DeviceModel"
            Headers="_headers"
            Items="DeviceModels"
            DisableSort
            Page="@_page"
            ItemsPerPage="_options.ItemsPerPage"
            FooterProps="@(props =>
                         {
                             props.ShowFirstLastPage = false;
                             props.ShowCurrentPage = false;
                             props.PageText = @"共{2}条数据";
                             props.DisableItemsPerPage = true;
                         })"
            ServerItemsLength="@_total"
        >
            <HeaderColContent Context="header">
                <MLabel Style="font-weight: 400;color: #28333E;">@header.Text</MLabel>
            </HeaderColContent>
            <ItemColContent Context="item">
                @if (item.Header.Value == "actions")
                {
                    <div class="d-flex align-center justify-center">
                        <MButton OnClick="() =>OpenEditDialog(item.Item)" Color="blue"
                                 Class="text-decoration-underline" Plain>
                            编辑名称
                        </MButton>
                        @* <MButton Style="font-size: 1.25rem;" Color="blue" Class="text-decoration-underline" Plain> *@
                        @*     格式化 *@
                        @* </MButton> *@
                        <MButton OnClick="async () => await DeleteConfirmAsync(item.Item.Id)"
                                 Color="red"
                                 Class="text-decoration-underline" Plain>
                            删除设备
                        </MButton>
                    </div>
                }
                else
                {
                    <MLabel>@item.ValueContent</MLabel>
                }
            </ItemColContent>
        </MDataTable>
    </MCard>

</div>
<MDialog
    Width="400"
    Scrollable="false"
    @bind-Value="isShowEditDevice">
    <ChildContent>
        <MCard Height="300">
            <MCardTitle Style="background: linear-gradient(90deg, #bbd2ec, #c6ddf7);"
                        Class="d-flex align-center justify-center">
                <div class="hx-card-title">
                    编辑设备名称
                </div>
                <MButton OnClick="CloseEditDialog" Class="ml-auto" Icon>
                    <MIcon>mdi-close</MIcon>
                </MButton>
            </MCardTitle>
            <MCardText>
                <MRow Class="mt-4">
                    <MCol Cols="12">
                        <MTextField
                            Dense
                            Disabled
                            Solo
                            Class="customer-input"
                            @bind-Value="DialogData.DeviceSN">
                            <PrependContent>
                                <MLabel Class="mr-2">设备编号：</MLabel>
                            </PrependContent>
                        </MTextField>

                    </MCol>
                    <MCol Cols="12">
                        <MTextField
                            Dense
                            Solo
                            Class="customer-input"
                            @bind-Value="DialogData.Name">
                            <PrependContent>
                                <MLabel Class="mr-2">设备名称：</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                </MRow>


            </MCardText>
            <MCardActions>
                <MRow Justify="@JustifyTypes.Center">
                    <MCol Cols="4">
                        <MButton Class="dialog-button  cancel" OnClick="() => { isShowEditDevice = false; }">取消
                        </MButton>
                    </MCol>
                    <MCol Cols="4">
                        <MButton Class="dialog-button  confirm" OnClick="EditConfirmAsync">确认</MButton>
                    </MCol>
                </MRow>
            </MCardActions>
        </MCard>
    </ChildContent>
</MDialog>

@code {
    private int _page = 1;
    private int _itemsPerPage = 10;
    private bool isShowEditDevice = false;
    [Inject] IPopupService PopupService { get; set; }
    private DeviceModel DialogData { get; set; } = new();

    private string DeviceName { get; set; }

    private DataOptions _options = new(1, 10);
    private int _total;
    private List<DeviceModel> DeviceModels { get; set; } = new();

    private List<DataTableHeader<DeviceModel>> _headers => new()
    {
        new()
        {
            Text = "设备名称",
            Align = DataTableHeaderAlign.Start,
            Sortable = false,
            Value = nameof(DeviceModel.Name)
        },
        new() { Text = "设备编号", Value = nameof(DeviceModel.DeviceSN) },
      new()
      {
          Text = "电量",
          CellRender = c =>
          {
              var battery = c?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");
              RenderFragment icon = @<div>
               @(battery?.Value?.Value?? 0)
                  </div>;
              return icon;
          }
      },
        // new() { Text = "内存使用情况", Value = nameof(DeviceModel.DeviceSN) },
        new()
        {
            Text = "设备状态",
            CellRender = c =>
            {
                RenderFragment icon = c?.IsOnline == true
                    ? @<div>
                          <MIcon Color="green"> mdi-circle-medium</MIcon>
                          在线
                      </div>
                    : @<div>
                          <MIcon Color="black"> mdi-circle-medium</MIcon>
                          离线
                      </div>;
                return icon;
            }
        },
        new() { Text = "连接方式", CellRender = c => "wifi" },
        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Align = DataTableHeaderAlign.Center,
        }
    };

    private async Task HandleOnOptionsUpdate(DataOptions options)
    {
        _options = options;
        await InitDataList();
    }

    [Inject] StateService _stateService { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await InitDataList();
        await base.OnInitializedAsync();
    }

    private const string DeviceTypeId = "2407160100000001";
    private async Task InitDataList()
    {
        PopupService.ShowProgressCircular();        
        var queryParam = new Dictionary<string, string>
        {
            { "typeId", DeviceTypeId },
            { "name", DeviceName },
            { "page", _options.Page.ToString() },
            { "pageSize", _options.ItemsPerPage.ToString() },
            {"orderBy", $"AddTime"}
        };
        var deviceListUrl = "/api/v2.0/IoT/Devices/List";
        var result = await _stateService
            .GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
        _total = result.Page?.TotalCount ?? 0;
        DeviceModels = result?.Data?.Select(m => m.Data)?.ToList() ?? new List<DeviceModel>();
        PopupService.HideProgressCircular();        

    }

    private async Task DeleteConfirmAsync(string id)
    {
        var confirmed = await PopupService.ConfirmAsync(
            "警告",
            "删除后将无法恢复，确认删除吗?",
            AlertTypes.Warning);
        if (confirmed)
        {
            var deleteResult = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/IoT/Devices/Delete/{id}");
            await PopupService.EnqueueSnackbarAsync(deleteResult.Success ? "删除成功！" :  (!string.IsNullOrEmpty(deleteResult.Message) ? "删除失败！" : deleteResult.Message),
                deleteResult.Success ? AlertTypes.Success : AlertTypes.Error);
            await InitDataList();
        }
    }

    private async Task EditConfirmAsync()
    {
        var result = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/IoT/Devices/Edit/{DialogData.Id}", DialogData);
        if (result.Success)
        {
            await PopupService.EnqueueSnackbarAsync("修改成功！", AlertTypes.Success);
            CloseEditDialog();
            await InitDataList();
        }
    }

    private void CloseEditDialog()
    {
        isShowEditDevice = false;
    }

    private void OpenEditDialog(DeviceModel model)
    {
        DialogData = model;
        isShowEditDevice = true;
    }

}