2025-08-05 18:22:34.224 +08:00 [ERR] 服务器发生未处理的异常
System.Exception: 表单[2307180100000002]不存在
   at UFU.CoreFX.Data.DataRepository.GetFormContext(String form)
   at UFU.CoreFX.Data.DataRepository.Query[TEntity](String form, PermissionSwitch pSwitch)
   at UFU.CoreFX.Data.DataRepository.Query[T](PermissionSwitch pSwitch)
   at HX.Experiment.Web.Service.Area.DeviceBindComputerController.Add(DeviceBindComputer data) in D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Web.Service\Area\DeviceBindComputerController.cs:line 81
   at lambda_method1358(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
