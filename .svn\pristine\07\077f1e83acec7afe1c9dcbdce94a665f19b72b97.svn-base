﻿using System.Collections.Concurrent;
using System.Text;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using PdfSharp;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
/// 
/// </summary>
public class GenerateReportService : BackgroundService
{
    private static List<DataModel<PatientRecordModel>> _pendingDataList;

    private static  ConcurrentDictionary<string, IsSendingRecord> IsSending = new();

    private class IsSendingRecord
    {
        public IsSendingRecord(string id, DateTime createTime)
        {
            Id = id;
            CreateTime = createTime;
        }

        public string Id { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        LogTool.GetLogger("GenerateReport", "GenerateReport").Information("开始执行生成报告任务");
        _= Task.Run(async () =>
        {
            await using var db = new DataRepository(UserInfo.System);
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
                    _pendingDataList = await GetPendingGenerateAsync(db);
                    if (_pendingDataList is not { Count: > 0 })
                    {
                        continue;
                    }

                    for (int i = 0; i < _pendingDataList.Count; i++)
                    {
                        if (stoppingToken.IsCancellationRequested)
                        {
                            break;
                        }
                        var record = _pendingDataList[i];
                        if (!IsSending.ContainsKey(record.Id))
                        {
                            // 执行任务
                            await SendMsgToAlgAsync(record, db);
                        }
                        _pendingDataList.Remove(record);
                        var isSending =
                            IsSending.GetOrAdd(record.Id, new IsSendingRecord(record.Id, DateTime.Now));
                        IsSending.AddOrUpdate(record.Id, isSending, (k, v) => isSending);
                    }
                }
                catch (Exception ex)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(ex, "处理导出报告任务发生错误");
                }
            }
        }, stoppingToken);
        _= Task.Run(async () =>
        {
           
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                    for (int i = 0; i < IsSending.Count; i++)
                    {
                        var record = IsSending.Values.ToList()[i];
                        if (!(DateTime.Now.Subtract(record.CreateTime).Seconds >10)) continue;
                        IsSending.TryRemove(record.Id, out _);
                        LogTool.Logger.Information($"移除超时任务:{record.Id}");
                    }
                }
                catch (Exception e)
                {
                    LogTool.GetLogger("GenerateReport", "GenerateReport").Error(e, "处理导出报告任务发生错误");
                }
            }
        }, stoppingToken);
        return Task.CompletedTask;
    }


    /// <summary>
    /// 获取待生成报告的记录
    /// </summary>
    /// <param name="dbContext"></param>
    /// <returns></returns>
    private async Task<List<DataModel<PatientRecordModel>>> GetPendingGenerateAsync(DataRepository dbContext)
    {
        // 获取未生成报告的记录
        var endTime = DateTime.Now.AddSeconds(-20);
        var isSendingIds = IsSending.Keys.ToList();
        var records = await dbContext.Query<PatientRecordModel>(PermissionSwitch.Off)
            .Where(t =>
                t.Data.CollectEndTime < endTime
                &&t.Data.CollectEndTime>DateTime.MinValue
                && !isSendingIds.Contains(t.Id)
                && t.Data.BuildReportStatus != BuildReportStatus.Completed 
                && t.Data.BuildReportStatus != BuildReportStatus.IsLinked 
                && t.Data.BuildReportStatus != BuildReportStatus.NoCondition)
            .OrderByDescending(m => m.Id)
            .Take(20)
            .ToListAsync();
        return records;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="record"></param>
    private static async Task SendMsgToAlgAsync(DataModel<PatientRecordModel> record, DataRepository _dbContext)
    {
        var path = record.Data.GetRecordDirectoryPath();
        if (!Directory.Exists(path) || !File.Exists(Path.Combine(path, nameof(HXDataType.PPG) + ".csv")))
        {
            record.Data.BuildReportStatus = BuildReportStatus.NoCondition;
            _dbContext.Update(record);
            await _dbContext.SaveChangesAsync();
            return;
        }
        var ppgRate = record.Data.PpgRate;
        var ppgDic = FileDataHelper.ReadDataFromCsv<uint>(path, nameof(HXDataType.PPG));
        var ppgGBytes = ppgDic["PPG_G"];
        var ppgRBytes = ppgDic["PPG_R"];
        var ppgIBytes = ppgDic["PPG_I"];
        //var pageSize = 500 * 60;
        var pageSize = 500;
		if (ppgGBytes.Count <= pageSize)
        {
			var dic = new Dictionary<string, List<uint>>
			{
				["PPG_G"] = ppgGBytes,
				["PPG_R"] = ppgRBytes,
				["PPG_I"] = ppgIBytes
			};
			await AlgWebSocketClient.SendMsgToAlg(record.Id, dic, ppgRate == 0 ? 500 : ppgRate,
                record.Data.DeviceStartCheckTime!=DateTime.MinValue? record.Data.DeviceStartCheckTime.ToUnixMs():record.Data.CollectStartTime.ToUnixMs(),
				true);
        }
        else
        {
            var page = 0;
			var totalPage = (ppgGBytes.Count + pageSize - 1) / pageSize;
			while (page * pageSize < ppgGBytes.Count)
			{
				page++;
				var dic = new Dictionary<string, List<uint>>
				{
					["PPG_G"] = ppgGBytes.Skip(page * pageSize).Take(pageSize).ToList(),
					["PPG_R"] = ppgRBytes.Skip(page * pageSize).Take(pageSize).ToList(),
					["PPG_I"] = ppgIBytes.Skip(page * pageSize).Take(pageSize).ToList()
				};
				await AlgWebSocketClient.SendMsgToAlg(record.Id, dic, ppgRate == 0 ? 500 : ppgRate,
					record.Data.DeviceStartCheckTime!=DateTime.MinValue? record.Data.DeviceStartCheckTime.ToUnixMs():record.Data.CollectStartTime.ToUnixMs(),
					page== totalPage);
                await Task.Delay(10);
			}
        } 
        try
        {
            if (!Directory.Exists(path) || !File.Exists(Path.Combine(path, HXDataType.EDA.ToString() + ".csv")))
            {
                record.Data.BuildReportStatus = BuildReportStatus.NoCondition;
                _dbContext.Update(record);
                await _dbContext.SaveChangesAsync();
                return;
            }
            var edaData = FileDataHelper.ReadDataFromCsv<float>(path, nameof(HXDataType.EDA));
            var edaBytes = edaData["Value"];
            var edaPageSize = 50 * 60;
            var edaPage = 0;
            var edaTotalPage = (edaBytes.Count + edaPageSize - 1) / edaPageSize;
            while (edaPage * edaPageSize < edaBytes.Count)
            {
                edaPage++;
                var startTime = record.Data.DeviceStartCheckTime != DateTime.MinValue
                        ? record.Data.DeviceStartCheckTime.ToUnixMs()
                        : record.Data.CollectStartTime.ToUnixMs();
                var data = edaBytes.Skip(edaPage * edaPageSize).Take(edaPageSize).ToList();
                await SendGsrMsgToAlg(record.Id, data.ToArray(), record.Data.DeviceStartCheckTime.ToUnixMs(),
                    startTime,edaPage== edaTotalPage);
            }
        }
        catch (Exception e)
        {
            LogTool.Logger.Error("GenerateReportService SendGsrMsgToAlg:" + e.Message);
        }
    }
    private static async Task SendGsrMsgToAlg(
        string recordId,
        float[] gsrGData,
        long timeSpan,
        long startTime,
        bool isEnd
    )
    {
        byte[] dataBytes = new byte[gsrGData.Count() * sizeof(float)];
        Buffer.BlockCopy(gsrGData.ToArray(), 0, dataBytes, 0, dataBytes.Length);
        var jsonData = new
        {
            MessageType = 3,
            ClientId = recordId,
            Algorithm = "GSRSeparated",
            IsEnd = isEnd,
            StartTime = startTime,
            Time = timeSpan * 1000,
            Data = new { gsr_Length = dataBytes.Length, }
        };
        var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
        //LogTool.Logger.Information("GenerateReportService SendGsrMsgToAlg:" + JsonTool.Serialize(new { gsr_g_data }));
        await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, dataBytes, recordId);
    }
    public override async Task StopAsync(CancellationToken stoppingToken)
    {
        LogTool.Logger.Information("生成报告服务正在停止");
        await base.StopAsync(stoppingToken);
    }
}