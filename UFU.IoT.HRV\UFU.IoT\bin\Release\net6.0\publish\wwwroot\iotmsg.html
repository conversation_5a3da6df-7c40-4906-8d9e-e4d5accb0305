﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <script src="lib/jquery/dist/jquery.min.js"></script>
</head>
<body>
    <button id="post">POST</button>
    <div id="result_div" style="width:800px;height:800px;border:1px solid #808080"></div>

    <script>
        var data = {
            "apiver": "string",
            "msgver": "string",
            "msgid": "string",
            "action": "string",
            "module": "string",
            "mac": "string",
            "device": "string",
            "did": "string",
            "sn": "string",
            "secret": "string",
            "token": "string",
            "expires": 0,
            "time": 0,
            "timeout": 0,
            "server": "string",
            "heartbeat": "string",
            "appver": "string",
            "upurl": "string",
            "err": 0,
            "msg": "string"
        };
        $("#post").click(function () {
            $.ajax({
                type: "post",
                url: "/api/IoT/Msg",
                contentType: "application/json",
                data: JSON.stringify(data),
                success: function (data, status) {
                    if (status == "success") {
                        $("#result_div").html(data);
                    }
                }
            });
        });
    </script>

</body>
</html>