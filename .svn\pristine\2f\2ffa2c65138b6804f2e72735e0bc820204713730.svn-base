﻿using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using HX.HRV.Web.Services;
namespace HX.HRV.Web
{
    public class Module : BaseModule
    {
        public override string Name { get; set; } = "心率变异性系统";
        public override string Override { get; set; } = "心率变异性系统";
        public override string Icon { get; set; } = "iconfont icon-admin";
        public override string Url { get; set; } = "";
        public override string Description { get; set; } = "";

        public override async void Configure(IServiceCollection services)
        {
            UFU.IoT.IoTModule ioTModule;
            HRV.Shared.Module module;

            if (!Env.HostEnvironment.IsDevelopment())
            {
                ConfigureItems.Add(new ConfigureItem
                {
                    
                });
            }
        }
    }
}
