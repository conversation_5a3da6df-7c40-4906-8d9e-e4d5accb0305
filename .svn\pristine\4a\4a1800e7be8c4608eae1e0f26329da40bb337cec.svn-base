﻿using System.Linq.Expressions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Models;
using UFU.CoreFX.Models.ApiModels;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web.Areas.HRV_HX.Controllers;

/// <summary>
/// 心率变异性账号管理接口
/// </summary>
[Area("HRV_HX")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class OrgUserController : Controller
{
    private readonly CoreDbContext _context;
    private readonly OrganService _organService;
    private const string OrganId = "2409030100008359";

    public OrgUserController(CoreDbContext context)
    {
        this._context = context;
        this._organService = new OrganService();
    }
    /// <summary>
    /// 添加机构用户
    /// </summary>
    /// <param name="model"></param>
    /// <param name="organId"></param>
    /// <returns></returns>
    [HttpPost]
    [Permission("心率变异性账号管理接口/添加", IsAPI = true, AllowAnonymous = true)]
    public async Task<Result<bool>> AddOrganUser([FromBody] RegisterOrganUserRequestModel model)
    {
        var result = new Result<bool>();
        try
        {
            UserInfo userInfo = ViewBag.User;
            if (string.IsNullOrEmpty(model.RealName))
            {
                result.AddError("姓名不能为空");
                return result;
            }
            var name = model.Name.Trim();
            //查询系统用户
            var user = _context.Users.FirstOrDefault(m => m.Name == name);
            if ( user != null )
            {
                result.AddError("用户名已存在");
                return result;
            }
            //如果用户不存在，添加系统用户
                user = new UserModel
                {
                    Id = UId.GetNewId(),
                    OrganId = userInfo.Organ.Id,
                    UserType = UserType.Custom,
                    Status = UserStatus.正常,
                    Name = model.Name,
                    EncryptionMode = EncryptionMode.SHA1,
                    Salt = VerifyCode.CreateVerifyCode(30),
                    RealName = model.RealName,
                    Profile = null,
                    Sex = Sex.保密,
                    RegisterTime = DateTime.Now,
                    RegisterIP = MyHttpContext.GetClientIP(),
                    LoginCount = 0,
                    LoginTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    Version = Guid.NewGuid().ToString()
                };
                user.Password = Encrypt.SHA1(model.Password + user.Salt);
                user.RefereeId = "01";
                _context.Users.Add(user);
            //添加用户机构
            var userOrgan = new UserOrganModel
            {
                UserId = user.Id,
                OrganId = userInfo.Organ.Id,
                Roles = model.Roles,
                Departments = model.Departments,
                Name = model.Name,
                Status = UserOrganStatus.Passed,
                IsDeleted = false,
                AddTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                Version = Guid.NewGuid().ToString("N")
            };
            _context.Add(userOrgan);
            await _context.SaveChangesAsync();
            //重置缓存
            PermissionTable.RemovePermissions(user.Id, user.Organ?.Id);
            return result;
        }
        catch (Exception ex)
        {
            LogTool.Logger.Error(ex, "添加机构用户异常");
            result.AddError("", ex.Message);
            return result;
        }
    }
    
    
     /// <summary>
    /// 编辑机构用户（管理员）
    /// </summary>
    /// <param name="model"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    [HttpPost]
    [Permission("心率变异性账号管理接口/编辑", IsAPI = true, AllowAnonymous = true)]
    public async Task<Result<bool>> EditOrganUser([FromBody] RegisterOrganUserRequestModel model,[FromQuery] string userId)
    {
        var result = new Result<bool>();
        try
        {
            if (string.IsNullOrEmpty(model.RealName))
            {
                result.AddError("姓名不能为空");
                return result;
            }
            //查询机构用户
            var organModel = _context.UserOrgans
                .Include(m => m.User)
                .Include(userOrganModel => userOrganModel.Organ)
                .FirstOrDefault(m => m.UserId ==userId );
            organModel.User.RealName= model.RealName;
            organModel.User.Password = Encrypt.SHA1(model.Password +organModel.User.Salt);
            _context.Update(organModel);
            await _context.SaveChangesAsync();
            //重置缓存
            PermissionTable.RemovePermissions(organModel.UserId, organModel.Organ?.Id);
            return result;
        }
        catch (Exception ex)
        {
            LogTool.Logger.Error(ex, "添加机构用户异常");
            result.AddError("", ex.Message);
            return result;
        }
    }

    [HttpGet]
    [Permission("心率变异性账号管理接口/列表", IsAPI = true, AllowAnonymous = true)]
    public async Task<Result<PageList<UserOrganModel>>> OrganUserListAsync(string keywords,
        int? page, int? pageSize)
    {
        var query = _context.UserOrgans.AsQueryable();
        query= query.Include(m => m.User);
        query = query.Where(m => m.User.UserType==UserType.Custom);
        if (!string.IsNullOrEmpty(keywords))
        {
            query = query.Where(m => m.Name.Contains(keywords) || m.User.Name.Contains(keywords));
        }
        var list = await query.ToPageListAsync(page, pageSize);
        var res = new Result<PageList<UserOrganModel>>
        {
            Success = true,
            Data = list,
            Page = list.PageInfo
        };
        return res;
    }

    /// <summary>
    ///  获取角色列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Permission("心率变异性角色管理接口/列表", IsAPI = true)]
    public async Task<Result<List<RoleModel>>> RoleListAsync()
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Roles
            .Where(m => m.OrganId == userInfo.Organ.Id)
            .AsQueryable();
        var list = await query.ToListAsync();
        var result = new Result<List<RoleModel>>
        {
            Success = true,
            Data = list
        };
        return result;
    }
    
    
    [HttpPost()]
    [Permission("心率变异性账号管理接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string userId)
    {
        var result = new Result<bool>();
        if (string.IsNullOrEmpty(userId))
        {
            return result;
        }
        var user =await _context.UserOrgans.AsQueryable()
            .FirstOrDefaultAsync(m=>m.UserId==userId);
        if (user == null)
        {
            result.AddError("账号信息不存在");
            return result;
        }
        _context.Remove(user);
        await _context.SaveChangesAsync();
        result.Data=true;
        return result;
    }
}