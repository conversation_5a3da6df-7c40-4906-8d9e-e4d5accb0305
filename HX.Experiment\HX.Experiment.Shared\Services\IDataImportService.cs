using HX.Experiment.Shared.Model;

namespace HX.Experiment.Shared.Services;

/// <summary>
/// 数据导入服务接口
/// </summary>
public interface IDataImportService
{
    /// <summary>
    /// 导入设备数据到服务器
    /// </summary>
    /// <param name="deviceSn">设备序列号</param>
    /// <param name="recordId">检测记录ID</param>
    /// <returns></returns>
    Task<bool> ImportDataAsync(string deviceSn, string recordId);

    /// <summary>
    /// 获取设备数据文件数量
    /// </summary>
    /// <param name="deviceSn">设备序列号</param>
    /// <returns></returns>
    Task<int> GetDeviceFileCountAsync(string deviceSn);

    /// <summary>
    /// 检查设备是否有数据
    /// </summary>
    /// <param name="deviceSn">设备序列号</param>
    /// <returns></returns>
    Task<bool> HasDataAsync(string deviceSn);

    /// <summary>
    /// 清理设备数据
    /// </summary>
    /// <param name="deviceSn">设备序列号</param>
    /// <returns></returns>
    Task<bool> ClearDeviceDataAsync(string deviceSn);

    /// <summary>
    /// 数据导入进度事件
    /// </summary>
    event EventHandler<DataImportProgressEventArgs> ProgressChanged;
}

/// <summary>
/// 数据导入进度事件参数
/// </summary>
public class DataImportProgressEventArgs : EventArgs
{
    public string DeviceSn { get; set; } = string.Empty;
    public int Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public bool IsCompleted { get; set; }
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
}
