﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@implements IDisposable
<MRow Style="width: 100%" Justify="JustifyTypes.SpaceBetween">
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">电量:</MLabel>
		<MLabel>@DeviceStatusData.Battery</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">当前患者:</MLabel>
		<MLabel>@DeviceStatusData.RecordModel?.Patient?.Name</MLabel>
	</MCol>
	<MCol Cols="3">
		<MLabel Class="font-weight-bold">倒计时:</MLabel>
		<MLabel>@DeviceStatusData.CollectTime.ToString(@"mm\:ss")</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">连接方式:</MLabel>
		<MLabel>@DeviceStatusData.ConnectionType</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">状态:</MLabel>
		<MLabel>@DeviceStatusData.DeviceStatus</MLabel>
	</MCol>
	<MCol Cols="12">
		<MSwitch TValue="bool"
		         @bind-Value="isOrg"
		         Label="@($"PPG降噪: {isOrg}")">
		</MSwitch>
	</MCol>
</MRow>
@if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.检测中) {
	<MRow Style="display: flex;justify-content: center;">

		<MCol Cols="12" Style="@(GetnoneStyle())">
			<div style="height: 200px; border-radius: 10px;background: #fff;position: relative"
			     class="d-flex align-center justify-space-start ">
				<div style="height: 100%;width: 200px; background-color:#354de5;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">PPG</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "ORGPPG")'>
				</div>
			</div>
		</MCol>
		<MCol Cols="12" Style="@(GetVisibilityStyle())">
			<div style="height: 200px; border-radius: 10px;background: #fff; position: relative"
			     class="d-flex align-center justify-space-start ">
				<div style="height: 100%; width: 200px; background-color:#9ac5fa;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">PPG</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "PPG")'>
				</div>
			</div>
		</MCol>

		<MCol Cols="12">
			<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
			     class="d-flex align-center justify-space-between">
				<div style="height: 100%;width: 200px; background-color:#aac57f;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">HR(bpm)</div>
					<div class="text-center">@DeviceStatusData.BMPDatas?.LastOrDefault()</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "HR")'>
				</div>
			</div>
		</MCol>
	</MRow>
}

@code {

	/// <summary>
	///
	/// </summary>
	[Parameter]
	public EventCallback CloseDialogEvent { get; set; }

	[Inject] protected InternalHttpClientService InternalHttpClientService { get; set; }

	/// <summary>
	///
	/// </summary>
	[Parameter]
	public DeviceStatusViewModel DeviceStatusData { get; set; }

	[Parameter] public EventCallback<DeviceStatusViewModel> DeviceStatusDataChanged { get; set; }
	protected PatientRecordModel RecordModel => DeviceStatusData.RecordModel;
	public bool isOrg { get; set; }
	[Inject] IPopupService PopupService { get; set; }
	[Inject] DeviceStateService DeviceStateService { get; set; }
	protected async Task CloseDialog() {
		IsDisposed = true; // 标记组件已销毁
		await CloseDialogEvent.InvokeAsync();
	}


	protected string GetVisibilityStyle() {
		return isOrg ? "visibility: visible !important;" : "visibility: hidden !important;height: 0px !important;padding: 0px !important;";
	}

	protected string GetnoneStyle() {
		return !isOrg ? "visibility: visible !important;" : "visibility: hidden !important;height: 0px !important;padding: 0px !important;";
	}

	[Inject] protected IJSRuntime _jsRuntime { get; set; }
	protected bool isChartInitialized = false;
	protected bool IsDisposed { get; set; } = false;


// 定义委托处理器以便后续可以取消订阅
	protected Action<List<int>> OnRecivePPGHandler;
	protected Action<List<uint>> OnOrgPPGHandler;
	protected Action<List<float>> OnReciveEDAHandler;
	protected Action<uint> OnReciveBMPHandler;
	protected Action<uint> OnSPO2Handler;
	protected Action<List<float>> OnReciveSCLHandler;
	protected Action<List<float>> OnReciveSCRHandler;

// 更新方法
	protected async Task UpdateChartData(string chartId, object data, int interval, bool append) {
		try {
			if (!IsDisposed) {
				try {
					if (isChartInitialized) {
						await InvokeAsync(async () => { await _jsRuntime.InvokeVoidAsync("RealTimeEchart.UpdateChartData", chartId, data, interval, append); });
					}
				}
				catch (Exception ex) {
					Console.WriteLine($"Error invoking JS: {ex.Message}");
				}
			}
		}
		catch (Exception e) {
			Console.WriteLine(e.Message);
		}
	}

// 注册事件
	protected void RegisterDeviceStatusEvents() {
		OnRecivePPGHandler = data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "PPG", data, 500, false);
		};
		OnOrgPPGHandler = data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "ORGPPG", data, 500, false);
		};

		OnReciveEDAHandler = data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "GSR", data, 50, false);
		};

		OnReciveBMPHandler = data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "HR", data, 1, true);
		};

		OnSPO2Handler = data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "SPO2", data, 1, true);
		};

		OnReciveSCLHandler = data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "SCL", data, 1, false);
		};

		OnReciveSCRHandler =  data => {
			UpdateChartData(DeviceStatusData?.RecordModel?.Id + "SCR", data, 1, false);
		};

		// 订阅事件
		DeviceStatusData.OnRecivePPGData += OnRecivePPGHandler;
		DeviceStatusData.OnOrgPPGData += OnOrgPPGHandler;
		DeviceStatusData.OnReciveEDAsData += OnReciveEDAHandler;
		DeviceStatusData.OnReciveBMPDatas += OnReciveBMPHandler;
		DeviceStatusData.OnSPO2Data += OnSPO2Handler;
		DeviceStatusData.OnReciveSCLDatasData += OnReciveSCLHandler;
		DeviceStatusData.OnReciveSCRDatasData += OnReciveSCRHandler;
		DeviceStatusData.OnDeviceStatusChange += OnChange;
	}

	protected override async Task OnInitializedAsync() {
		await base.OnInitializedAsync();
	}

	protected override void OnInitialized() {
		base.OnInitialized();
		this.DeviceStatusData.OnDeviceStatusChange+=OnChange;
	}

	private void OnChange() {
		InvokeAsync(StateHasChanged);
	}


	protected override async Task OnAfterRenderAsync(bool firstRender) {
		if (!firstRender) {
			if (DeviceStatusData?.DeviceStatus == EnumDeviceStatus.空闲 || DeviceStatusData?.DeviceStatus == EnumDeviceStatus.完成) {
				await PopupService.EnqueueSnackbarAsync("检测完成！", AlertTypes.Success, timeout: 1500);
				await CloseDialogEvent.InvokeAsync();
			}

			if (!isChartInitialized) {
				var ppgtake = 2500;
				// 等待 ECharts 初始化
				var dataList = DeviceStatusData.PPGData.ToArray();

				if (dataList.Length > ppgtake) {
					dataList = dataList[ppgtake..];
				}

				var key = $"{DeviceStatusData?.RecordModel?.Id}PPG";
				await _jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", dataList, key, ppgtake, false, "#9ac5fa");


				var orgppgtake = 2500;
				// 等待 ECharts 初始化
				var ORGPPGdataList = DeviceStatusData.OrgPPGData.ToArray();

				if (ORGPPGdataList.Length > orgppgtake) {
					ORGPPGdataList = ORGPPGdataList[orgppgtake..];
				}

				key = $"{DeviceStatusData?.RecordModel?.Id}ORGPPG";
				await _jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", ORGPPGdataList, key, orgppgtake, false, "#354de5");


				var GSRtake = 1000;
				// 等待 ECharts 初始化
				var EDAsdataList = DeviceStatusData.EDAs.ToArray();

				if (EDAsdataList.Length > GSRtake) {
					EDAsdataList = EDAsdataList[GSRtake..];
				}

				key = $"{DeviceStatusData?.RecordModel?.Id}GSR";
				_jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", EDAsdataList, key, GSRtake, false, "#9988d5");


				var SCLtake = 1000;
				// 等待 ECharts 初始化
				var SCLdataList = DeviceStatusData.SCLDatas.ToArray();
				if (SCLdataList.Length > SCLtake) {
					SCLdataList = SCLdataList[SCLtake..];
				}

				key = $"{DeviceStatusData?.RecordModel?.Id}SCL";
				_jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", SCLdataList, key, SCLtake, false, "#73c5b7");


				var SCRtake = 1000;
				// 等待 ECharts 初始化
				var SCRdataList = DeviceStatusData.SCRDatas.ToArray();
				if (SCRdataList.Length > SCRtake) {
					SCRdataList = SCRdataList[SCRtake..];
				}

				key = $"{DeviceStatusData?.RecordModel?.Id}SCR";
				_jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", SCRdataList, key, SCRtake, false, "#e3ad90");


				var BMPtake = 60;
				// 等待 ECharts 初始化
				var BMPdataList = DeviceStatusData.BMPDatas.ToArray();

				if (BMPdataList.Length > BMPtake) {
					BMPdataList = BMPdataList[BMPtake..];
				}

				key = $"{DeviceStatusData?.RecordModel?.Id}HR";
				_jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", BMPdataList, key, BMPtake, true, "#aac57f");

				var SpO2take = 60;
				// 等待 ECharts 初始化
				var SpO2dataList = DeviceStatusData.SPO2Data.ToArray();

				if (SpO2dataList.Length > SpO2take) {
					SpO2dataList = SpO2dataList[SpO2take..];
				}

				key = $"{DeviceStatusData?.RecordModel?.Id}SPO2";
				_jsRuntime.InvokeVoidAsync("RealTimeEchart.InitChart", SpO2dataList, key, SpO2take, true, "#3896bb");
				isChartInitialized = true;
				RegisterDeviceStatusEvents();
			}
		}
	}

	public void Dispose() {
		IsDisposed = true;
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "PPG");
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "ORGPPG");
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "GSR");
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "HR");
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "SPO2");
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "SCL");
		_jsRuntime.InvokeVoidAsync("RealTimeEchart.DisposeChart", DeviceStatusData?.RecordModel?.Id + "SCR");
		if (OnRecivePPGHandler != null) {
			DeviceStatusData.OnRecivePPGData -= OnRecivePPGHandler;
		}
	}

}