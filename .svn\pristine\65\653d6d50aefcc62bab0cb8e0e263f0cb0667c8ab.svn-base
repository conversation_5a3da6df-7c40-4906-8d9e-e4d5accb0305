﻿using Dapper;
using Microsoft.EntityFrameworkCore;
using NPOI.OpenXmlFormats.Spreadsheet;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.Json;
using System.Threading.Tasks;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Services
{
    /// <summary>
    /// 数据分析
    /// </summary>
    public class DataAnalysisService
    {
        //统计周期（后面需要根据设备类型的数据项设置来确定）
        private static int[] Intervals = new int[] { 60, 60 * 5, 60 * 10, 60 * 60, 60 * 60 * 24, /*60 * 60 * 24 * 30*/ };
        //接收数据队列（避免阻塞接收线程）
        private static BlockingCollection<DataModel> _deviceDataQueue = new BlockingCollection<DataModel>();
        //设备连接状态
        public static ConcurrentDictionary<string, string> DeviceConnStatus = new ConcurrentDictionary<string, string>();
        /// <summary>
        /// 数据统计任务列表
        /// </summary>
        public static ConcurrentDictionary<string, DataStatisticsTask> DataStatisticsTasks = new ConcurrentDictionary<string, DataStatisticsTask>();
        /// <summary>
        /// 分析数据计算完成委托
        /// </summary>
        public delegate void AnalysisDataEventHandler(string deviceId, AnalysisDataModel data);
        /// <summary>
        /// 分析数据计算完成事件
        /// 对于历史数据来说，数据Id可能不准确
        /// </summary>
        public static event AnalysisDataEventHandler OnAnalysisDataComputed;
        /// <summary>
        /// 分析数据计算完成事件
        /// </summary>
        public static event AnalysisDataEventHandler OnAnalysisDataSaved;

        static DataAnalysisService()
        {
            IoTService.OnSavedData += OnSavedData;
        }

        private static void OnSavedData(ConnectDevice connect, DataModel data)
        {
            //将数据加入队列
            if (data.Time > DateTime.Now.AddYears(-1))
            {
                _deviceDataQueue.Add(data);
            }
        }

        /// <summary>
        /// 开始计算
        /// </summary>
        public static void Start()
        {
            //创建统计任务
            Task.Run(() =>
            {
                while (true)
                {
                    try
                    {
                        var data = _deviceDataQueue.Take();
                        CreateTask(data.DeviceId, data.TypeId, data.Time, data);

                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, "处理分析数据异常");
                    }
                }
            });

            //计算统计数据
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        //取出计算时间已到的实时任务（计算时间不超过5秒钟）或者更新超过5秒的历史数据，防止历史数据频繁计算
                        //var taskKeys = DataStatisticsTasks.Where(m => m.Value.CycleEndTime <= DateTime.Now && (m.Value.CycleEndTime > DateTime.Now.AddSeconds(-5) || m.Value.UpdateTime < DateTime.Now.AddSeconds(-5))).Select(m => m.Key).ToList();
                        //延迟3秒计算
                        var taskKeys = DataStatisticsTasks.Where(m => m.Value.CycleEndTime <= DateTime.Now.AddSeconds(-3) && m.Value.UpdateTime <= DateTime.Now.AddSeconds(-3)).Select(m => m.Key).ToList();

                        if (taskKeys.Count <= 0)
                        {
                            await Task.Delay(100);
                            continue;
                        }

                        var statisticsTasks = new List<DataStatisticsTask>();
                        foreach (var item in taskKeys)
                        {
                            if (DataStatisticsTasks.TryRemove(item, out var task))
                            {
                                statisticsTasks.Add(task);
                            }
                        }

                        if (statisticsTasks.Count <= 0)
                        {
                            await Task.Delay(100);
                            continue;
                        }

                        var time = DateTime.Now;
                        //按计算时间和统计周期分组，同一周期同一时间的数据可以并行计算
                        var group = statisticsTasks.GroupBy(m => new { m.CycleEndTime, m.Cycle }).OrderBy(m => m.Key.CycleEndTime).ThenBy(m => m.Key.Cycle).ToList();
                        LogTool.GetLogger("statistics", "statistics").Information($"分组统计开始，数据{statisticsTasks.Count}条，分组{group.Count}个，剩余{DataStatisticsTasks.Count}条");
                        foreach (var item in group)
                        {
                            //LogTool.GetLogger("statistics", "statistics").Information($"开始统计，计算时间：{item.Key.CycleEndTime:yyyy-MM-dd HH:mm:ss}，周期：{item.Key.Cycle}，任务数：{item.Count()}，设备：{string.Join(",", item.Select(m => m.DeviceId))}");
                            var tasks = new List<Task<int>>();
                            var list = item.OrderBy(m => m.Cycle).ToList();
                            for (int i = 0; i < list.Count; i += 50)
                            {
                                //分批计算统计数据，每次50条
                                var pageTasks = list.Skip(i).Take(50).ToList();
                                tasks.Add(GroupDataCalcAsync(pageTasks));
                            }
                            var results = await Task.WhenAll(tasks);
                            //LogTool.GetLogger("statistics", "statistics").Information($"结束统计，计算时间：{item.Key.CycleEndTime:yyyy-MM-dd HH:mm:ss}，周期：{item.Key.Cycle}，成功数：{results.Sum()}\n");
                        }
                        var total = (DateTime.Now - time).TotalSeconds;
                        if (total > 0)
                        {
                            LogTool.GetLogger("statistics", "statistics").Information($"分组统计结束，数据{statisticsTasks.Count}条，分组{group.Count}个，统计计算耗时{total}秒");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, $"统计计算任务异常");
                        await Task.Delay(100);
                    }
                }
            });
        }

        /// <summary>
        /// 手动创建数据统计任务
        /// 最长只能创建24小时的计算任务
        /// </summary>
        /// <param name="deviceIds"></param>
        /// <param name="deviceTypeId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        public static bool CreateTask(List<string> deviceIds, string deviceTypeId, DateTime startTime, DateTime endTime)
        {
            var hours = (endTime - startTime).TotalHours;
            if (hours > 24 || hours < 0)
            {
                return false;
            }

            var time = startTime;
            while (time <= endTime)
            {
                foreach (var deviceId in deviceIds)
                {
                    CreateTask(deviceId, deviceTypeId, time, null);
                }
                time = time.AddSeconds(Intervals[0]);
            }
            return true;
        }

        /// <summary>
        /// 创建数据统计任务
        /// </summary>
        /// <param name="deviceId"></param>
        /// <param name="deviceTypeId"></param>
        /// <param name="time"></param>
        /// <param name="data"></param>
        public static void CreateTask(string deviceId, string deviceTypeId, DateTime time, DataModel data)
        {
            try
            {
                //按统计周期确定计算时间
                //统计周期取设备类型数据项中选中的统计周期的并集（尚未实现）
                foreach (var cycle in Intervals)
                {
                    //周期开始时刻
                    DateTime cycleStartTime;
                    //周期结束时刻
                    DateTime cycleEndTime;
                    //按月的倍数统计
                    if (cycle % (60 * 60 * 24 * 30) == 0)
                    {
                        var months = time.Month + (time.Year - DateTime.MinValue.Year) * 12;
                        var cycleCount = cycle / (60 * 60 * 24 * 30);
                        cycleStartTime = DateTime.MinValue.AddMonths(months - months % cycleCount);
                        cycleEndTime = cycleStartTime.AddMonths(cycleCount);
                    }
                    //按年的倍数统计
                    else if (cycle % (60 * 60 * 24 * 30 * 365) == 0)
                    {
                        var years = time.Year - DateTime.MinValue.Year;
                        var cycleCount = cycle / (60 * 60 * 24 * 30 * 365);
                        cycleStartTime = DateTime.MinValue.AddYears(years - years % cycleCount);
                        cycleEndTime = cycleStartTime.AddYears(cycleCount);
                    }
                    //其他统计周期的计算时间都从1/1/1 0:00开始确定
                    else
                    {
                        var seconds = (long)(time - DateTime.MinValue).TotalSeconds;
                        cycleStartTime = DateTime.MinValue.AddSeconds(seconds - seconds % cycle);
                        cycleEndTime = cycleStartTime.AddSeconds(cycle);
                    }

                    //设备计算任务
                    var taskKey = $"{cycleEndTime:yyyyMMddHHmmss}_{cycle}_{deviceId}";
                    if (!DataStatisticsTasks.TryGetValue(taskKey, out var dataStatisticsTask))
                    {
                        dataStatisticsTask = new DataStatisticsTask()
                        {
                            DeviceId = deviceId,
                            DeviceTypeId = deviceTypeId,
                            CycleStartTime = cycleStartTime,
                            CycleEndTime = cycleEndTime,
                            Cycle = cycle
                        };
                    }

                    //如果设备不在线，或者ConnectId不相等，认为是第一次计算
                    if (!IoTService.Devices.TryGetValue(deviceId, out var device) ||
                        !DeviceConnStatus.TryGetValue(deviceId, out var connId) ||
                        !string.IsNullOrWhiteSpace(connId) ||
                        connId != device?.Connect?.ConnectId)
                    {
                        dataStatisticsTask.FirstTimeCalc = true;
                        DeviceConnStatus.AddOrUpdate(deviceId, device?.Connect?.ConnectId, (k, v) => device?.Connect?.ConnectId);
                    }

                    //如果数据不为null，周期小于60秒，并且不是第一次计算，缓存数据减少数据库查询
                    if (data != null && cycle <= 60 && !dataStatisticsTask.FirstTimeCalc)
                    {
                        var aData = new AnalysisDataModel()
                        {
                            Id = data.Id,
                            DeviceId = data.DeviceId,
                            Interval = 0,
                            Time = data.Time,
                            TypeId = data.TypeId,
                            LatestData = data.DataDic,
                            AvgData = data.DataDic,
                            MaxData = data.DataDic,
                            MinData = data.DataDic,
                            SumData = data.DataDic
                        };
                        dataStatisticsTask.Datas.AddOrUpdate(data.Id, aData, (k, v) => aData);
                    }

                    //更新任务时间，避免历史数据更新时频繁计算
                    dataStatisticsTask.UpdateTime = DateTime.Now;

                    //添加或更新计算任务
                    DataStatisticsTasks.AddOrUpdate(taskKey, dataStatisticsTask, (k, v) => dataStatisticsTask);
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"数据统计任务创建异常：[{deviceId}][{time:yyyy-MM-dd HH:mm:ss}][{JsonTool.SerializeIgnoreNull(data)}]");
            }
        }

        /// <summary>
        /// 分组数据统计计算
        /// </summary>
        /// <param name="dataTasks">相同周期、相同计算时间的统计数据计算任务</param>
        private static async Task<int> GroupDataCalcAsync(List<DataStatisticsTask> dataTasks)
        {
            try
            {
                var result = 0;
                if (dataTasks == null || dataTasks.Count == 0)
                {
                    return result;
                }

                try
                {
                    //准备数据
                    using DataRepository db = new DataRepository();
                    if (dataTasks[0].Cycle <= 60)
                    {
                        var deviceIds = dataTasks.Where(m => m.FirstTimeCalc || m.Datas.Count == 0).Select(m => m.DeviceId).Distinct().ToList();
                        var deviceDatas = await db.Query<DataModel>().Where(m => m.Data.Time >= dataTasks[0].CycleStartTime && m.Data.Time < dataTasks[0].CycleEndTime && deviceIds.Contains(m.Data.DeviceId)).ToListAsync();
                        foreach (var dataTask in dataTasks.Where(m => m.FirstTimeCalc || m.Datas.Count == 0))
                        {
                            dataTask.Datas = new ConcurrentDictionary<string, AnalysisDataModel>();
                            var datas = deviceDatas.Where(m => m.Data.DeviceId == dataTask.DeviceId).ToList();
                            foreach (var data in datas)
                            {
                                var aData = new AnalysisDataModel()
                                {
                                    Id = data.Id,
                                    DeviceId = data.Data.DeviceId,
                                    Interval = 0,
                                    Time = data.Data.Time,
                                    TypeId = data.Data.TypeId,
                                    LatestData = data.Data.DataDic,
                                    AvgData = data.Data.DataDic,
                                    MaxData = data.Data.DataDic,
                                    MinData = data.Data.DataDic,
                                    SumData = data.Data.DataDic
                                };
                                dataTask.Datas.AddOrUpdate(data.Id, aData, (k, v) => aData);
                            }
                        }
                    }
                    else
                    {
                        int itv;
                        if (dataTasks[0].Cycle >= 60 * 60 * 24)
                        {
                            itv = 60 * 60;
                        }
                        else
                        {
                            itv = 60;
                        }

                        //查数据库
                        var deviceIds = dataTasks.Select(m => m.DeviceId).Distinct().ToList();
                        var deviceDatas = await db.Query<AnalysisDataModel>().Where(m => m.Data.Time >= dataTasks[0].CycleStartTime && m.Data.Time < dataTasks[0].CycleEndTime && deviceIds.Contains(m.Data.DeviceId) && m.Data.Interval == itv).ToListAsync();
                        foreach (var dataTask in dataTasks)
                        {
                            dataTask.Datas = new ConcurrentDictionary<string, AnalysisDataModel>();
                            var datas = deviceDatas.Where(m => m.Data.DeviceId == dataTask.DeviceId).OrderBy(m => m.Data.Time).ToList();
                            foreach (var data in datas)
                            {
                                dataTask.Datas.AddOrUpdate(data.Id, data.Data, (k, v) => data.Data);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogTool.Logger.Error(ex, $"统计计算准备数据异常");
                }

                //统计计算
                var analysisDatas = new ConcurrentDictionary<string, AnalysisDataModel>();
                foreach (var dataTask in dataTasks)
                {
                    try
                    {
                        //没有有效数据不计算
                        if (dataTask.Datas.Count == 0)
                        {
                            continue;
                        }

                        //获取设备类型
                        IoTService.DeviceTypes.TryGetValue(dataTask.DeviceTypeId, out var deviceType);
                        if (deviceType == null || deviceType.DataItemList == null || deviceType.DataItemList.Count == 0)
                        {
                            LogTool.GetLogger("statistics", "statistics").Error($"设备类型或数据项不存在，无法计算统计数据：{JsonTool.Serialize(dataTask)}");
                            continue;
                        }
                        //计算统计数据
                        AnalysisDataModel analysisData = DataCalc(dataTask, deviceType);
                        if (analysisData != null && analysisData.LatestData.Count > 0)
                        {
                            analysisDatas.AddOrUpdate(analysisData.Id, analysisData, (k, v) => analysisData);
                        }
                        else
                        {
                            //如果没有有效数据，不保存
                            LogTool.GetLogger("statistics", "statistics").Error($"计算结果无有效数据：{JsonTool.Serialize(dataTask)}");
                            continue;
                        }

                        try
                        {
                            //调用计算完成事件，对于历史数据来说，数据Id可能不准确
                            OnAnalysisDataComputed?.Invoke(dataTask.DeviceId, analysisData);
                        }
                        catch (Exception ex)
                        {
                            LogTool.Logger.Error(ex, $"统计数据计算完成事件异常：{JsonTool.Serialize(dataTask)}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, $"设备[{dataTask.DeviceId}]统计数据计算异常[{dataTask.Cycle}][{dataTask.CycleStartTime:yyyy-MM-dd HH:mm:ss}][{dataTask.CycleEndTime:yyyy-MM-dd HH:mm:ss}]");
                    }
                }

                //保存数据
                try
                {
                    if (analysisDatas.Count <= 0)
                    {
                        return 0;
                    }
                    var first = analysisDatas.Values.First();
                    var deviceIds = analysisDatas.Select(m => m.Value.DeviceId).Distinct().ToList();

                    //查询现有数据
                    using DataRepository db = new DataRepository(UserInfo.System);
                    var datas = await db.Query<AnalysisDataModel>().Where(m => m.Data.Time == first.Time && deviceIds.Contains(m.Data.DeviceId) && m.Data.Interval == first.Interval).ToListAsync();

                    //foreach (var key in analysisDatas.Keys.ToList())
                    //{
                    //    if (analysisDatas.TryGetValue(key, out var analysisData))
                    //    {
                    //        var model = datas.Where(m => m.Time == analysisData.Time && m.DeviceId == analysisData.DeviceId && m.Interval == analysisData.Interval).FirstOrDefault();
                    //        if (model == null)
                    //        {
                    //            db.Add(analysisData);
                    //        }
                    //        else
                    //        {
                    //            model.AvgData = analysisData.AvgData;
                    //            model.SumData = analysisData.SumData;
                    //            model.MaxData = analysisData.MaxData;
                    //            model.MinData = analysisData.MinData;
                    //            model.LatestData = analysisData.LatestData;
                    //            db.Update(model);
                    //            analysisDatas.AddOrUpdate(model.Id, model, (k, v) => model);
                    //        }
                    //    }
                    //}

                    var updateList = new List<AnalysisDataModel>();
                    foreach (var key in analysisDatas.Keys.ToList())
                    {
                        if (analysisDatas.TryGetValue(key, out var analysisData))
                        {
                            var model = datas.Where(m => m.Data.Time == analysisData.Time && m.Data.DeviceId == analysisData.DeviceId && m.Data.Interval == analysisData.Interval).FirstOrDefault();
                            if (model != null)
                            {
                                //updateList.Add(model);
                            }
                        }
                    }
                    if (updateList.Count > 0)
                    {

                    }
                    //db.RemoveRange(updateList);
                    //db.AddRange(analysisDatas.Values);
                    result = await db.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    LogTool.Logger.Error(ex, $"统计数据保存异常");
                }

                //调用保存完成事件
                foreach (var item in analysisDatas.Values)
                {
                    try
                    {
                        OnAnalysisDataSaved?.Invoke(item.DeviceId, item);
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error(ex, $"统计数据保存完成事件异常[{item.Interval}]");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"设备统计数据计算异常");
                return 0;
            }
        }

        /// <summary>
        /// 统计数据数值计算
        /// </summary>
        /// <param name="dataTask"></param>
        /// <param name="deviceType"></param>
        /// <returns></returns>
        private static AnalysisDataModel DataCalc(DataStatisticsTask dataTask, DeviceTypeModel deviceType)
        {
            //开始计算
            var analysisData = new AnalysisDataModel
            {
                Id = UId.GetNewId(),
                DeviceId = dataTask.DeviceId,
                TypeId = deviceType.Id,
                Time = dataTask.CycleStartTime,
                Interval = dataTask.Cycle,
                AvgData = new Dictionary<string, dynamic>(),
                MaxData = new Dictionary<string, dynamic>(),
                MinData = new Dictionary<string, dynamic>(),
                SumData = new Dictionary<string, dynamic>(),
                LatestData = new Dictionary<string, dynamic>(),
            };

            try
            {
                //全部数据类型
                var dataItems = deviceType.DataItemList.Select(m => m.Key).ToList();

                //筛选出数值类型的数据项
                var numberTypeDataItems = deviceType.DataItemList.Where(m => m.DataType == DataItemType.Float || m.DataType == DataItemType.Int).Select(m => m.Key).ToList();

                //计算最新值
                foreach (var data in dataTask.Datas.Values.OrderBy(m => m.Time).Select(m => m.LatestData).ToList())
                {
                    foreach (var key in data.Keys)
                    {
                        analysisData.LatestData[key] = data[key];//最新值
                    }
                }

                //计算累加值
                foreach (var data in dataTask.Datas.Values.Select(m => m.SumData).ToList())
                {
                    foreach (var key in data.Keys)
                    {
                        if (!numberTypeDataItems.Contains(key))
                        {
                            continue;
                        }

                        double value = GetDataValue(data[key]);
                        if (analysisData.SumData.ContainsKey(key))
                        {
                            analysisData.SumData[key] += value;
                        }
                        else
                        {
                            analysisData.SumData[key] = value;
                        }
                    }
                }
                foreach (var key in analysisData.SumData.Keys.ToList())
                {
                    analysisData.SumData[key] = Math.Round(analysisData.SumData[key], 2);
                }

                //计算最大值
                foreach (var data in dataTask.Datas.Values.Select(m => m.MaxData).ToList())
                {
                    foreach (var key in data.Keys)
                    {
                        if (!numberTypeDataItems.Contains(key))
                        {
                            continue;
                        }
                        double value = GetDataValue(data[key]);
                        if (!analysisData.MaxData.ContainsKey(key) || analysisData.MaxData[key] < value)
                        {
                            analysisData.MaxData[key] = value;
                        }
                    }
                }

                //计算最小值
                foreach (var data in dataTask.Datas.Values.Select(m => m.MinData).ToList())
                {
                    foreach (var key in data.Keys)
                    {
                        if (!numberTypeDataItems.Contains(key))
                        {
                            continue;
                        }
                        double value = GetDataValue(data[key]);
                        if (!analysisData.MinData.ContainsKey(key) || analysisData.MinData[key] > value)
                        {
                            analysisData.MinData[key] = value;
                        }
                    }
                }

                //平均值加和
                var sumCount = new Dictionary<string, int>();
                var sumValue = new Dictionary<string, double>();
                foreach (var data in dataTask.Datas.Values.Select(m => m.AvgData).ToList())
                {
                    foreach (var key in data.Keys)
                    {
                        if (!numberTypeDataItems.Contains(key))
                        {
                            continue;
                        }
                        double value = GetDataValue(data[key]);
                        if (sumValue.ContainsKey(key))
                        {
                            sumValue[key] += value;
                            sumCount[key]++;
                        }
                        else
                        {
                            sumValue[key] = value;
                            sumCount[key] = 1;
                        }
                    }
                }

                //计算平均值（可能会存在误差）
                foreach (string item in sumValue.Keys)
                {
                    if (sumCount[item] > 0)
                    {
                        analysisData.AvgData[item] = Math.Round(sumValue[item] / sumCount[item], 2);
                    }
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"统计数据数值计算过程异常");
            }
            return analysisData;
        }

        /// <summary>
        /// 数据类型转换
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private static double GetDataValue(dynamic data)
        {
            try
            {
                if (data is JsonElement)
                {
                    return data.GetDouble();
                }
                else
                {
                    return Convert.ToDouble(data);
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"数据类型转换异常：{data}");
                return 0;
            }
        }
    }

    /// <summary>
    /// 数据统计任务
    /// </summary>
    public class DataStatisticsTask
    {
        /// <summary>
        /// 设备编号
        /// </summary>
        public string DeviceId { get; set; }
        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceTypeId { get; set; }
        /// <summary>
        /// 统计周期（秒）
        /// </summary>
        public int Cycle { get; set; }
        /// <summary>
        /// 周期开始时刻
        /// </summary>
        public DateTime CycleStartTime { get; set; }
        /// <summary>
        /// 周期结束时刻
        /// </summary>
        public DateTime CycleEndTime { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
        /// <summary>
        /// 是否第一次计算
        /// </summary>
        public bool FirstTimeCalc { get; set; }
        /// <summary>
        /// 原始数据,只用于计算最小周期
        /// 集群环境下设备离线后应该将数据清空，防止数据不全计算错误（尚未实现）
        /// </summary>
        public ConcurrentDictionary<string, AnalysisDataModel> Datas { get; set; } = new ConcurrentDictionary<string, AnalysisDataModel>();
    }
}
