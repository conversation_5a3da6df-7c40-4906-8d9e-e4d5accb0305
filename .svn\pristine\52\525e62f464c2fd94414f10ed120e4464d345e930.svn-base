﻿using System.Text;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web.Units;

/// <summary>
/// 文件数据
/// </summary>
public static class FileDataHelper
{
    // 保存数据到 CSV 文件
    public static void SaveDataToCsv<T>(string dirPath, long timeSpan, string dataType,
        Dictionary<string, List<T>> data, string markValue = "",Action afterMarked = null)
        where T : struct
    {
        Task.Run(() =>
        {
            try
            {
                var strBuild = new StringBuilder();
                var filePath = Path.Combine(dirPath, $"{dataType}.csv");
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }

                if (!File.Exists(filePath))
                {
                    var str = new List<string> { "Time" };
                    str.AddRange(data.Keys);
                    strBuild.AppendLine(string.Join(",", str));
                }
                var dataFirst = data.FirstOrDefault();
                var count = dataFirst.Value.Count;
                // 将时间戳转换为 DateTime
                DateTime dateTime = DateTimeOffset.FromUnixTimeSeconds(timeSpan).DateTime;
                var startTime = dateTime.AddSeconds(-1);
                for (int i = 0; i < count; i++)
                {
                    var date = startTime.AddMilliseconds((1000.0 / count) * i);
                    var str = new List<string> { date.ToString("yyyy-MM-dd HH:mm:ss.fff") };
                    foreach (var key in data.Keys)
                    {
                        str.Add(data[key]?[i].ToString());
                    }

                   
                    if (!string.IsNullOrEmpty(markValue) && i == 0)
                    {
                        str.Add(markValue);
                        afterMarked?.Invoke();
                    }
                    strBuild.AppendLine(string.Join(",", str));
                }
                File.AppendAllText(filePath, strBuild.ToString());
            }
            catch (Exception e)
            {
                LogTool.Logger.Error(e, "SavePPGData__保存文件错误");
            }
        });
    }

    public static void SaveDataToCsv(string dirPath, string dataType,
        Dictionary<string, List<object>> data)
    {
        Task.Run(() =>
        {
            try
            {
                var filePath = Path.Combine(dirPath, $"{dataType}.csv");
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }
                var strBuild = new StringBuilder();
                var dataFirst = data.FirstOrDefault();
                var count = dataFirst.Value.Count;
                for (int i = 0; i < count; i++)
                {
                    var str = new List<string> { };
                    foreach (var key in data.Keys)
                    {
                        str.Add(data[key]?[i].ToString());
                    }
                    strBuild.AppendLine(string.Join(",", str));
                }
                File.AppendAllText(filePath, strBuild.ToString());
            }
            catch (Exception e)
            {
                LogTool.Logger.Error(e, "SavePPGData__保存文件错误");
            }
        });
    }


    // 从 CSV 文件读取数据
    public static Dictionary<string, List<T>> ReadDataFromCsv<T>(string fileDir, string dataType,
        bool removeMark = true)
        where T : struct
    {
        var result = new Dictionary<string, List<T>>();
        try
        {
            var filePath = Path.Combine(fileDir, $"{dataType}.csv");
            if (!File.Exists(filePath))
            {
                LogTool.GetLogger("FileDataHelper", "ReadDataFromCsv").Error($"File not found: {filePath}");
            }

            var lines = File.ReadAllLines(filePath);
            if (lines.Length < 2)
            {
                LogTool.GetLogger("FileDataHelper", "ReadDataFromCsv")
                    .Error($"CSV file is empty or contains only headers.: {filePath}");
            }

            var headers = lines[0].Split(',');
            //去掉Mark列
            if (removeMark && headers.Contains("Marked"))
            {
                headers = headers.Where(x => x != "Marked").ToArray();
            }

            for (int i = 1; i < headers.Length; i++)
            {
                result[headers[i]] = new List<T>();
            }

            for (int i = 1; i < lines.Length; i++)
            {
                var rowValues = lines[i].Split(',');

                for (int j = 1; j < headers.Length; j++)
                {
                    if (typeof(T) == typeof(double) && double.TryParse(rowValues[j], out var doubleValue))
                    {
                        result[headers[j]].Add((T)(object)doubleValue);
                    }
                    else if (typeof(T) == typeof(int) && int.TryParse(rowValues[j], out var intValue))
                    {
                        result[headers[j]].Add((T)(object)intValue);
                    }
                    else if (typeof(T) == typeof(uint) && uint.TryParse(rowValues[j], out var uintValue))
                    {
                        result[headers[j]].Add((T)(object)uintValue);
                    }
                    else
                    {
                        throw new InvalidCastException($"Unable to parse value '{rowValues[j]}' to type {typeof(T)}");
                    }
                }
            }
        }
        catch (Exception e)
        {
            LogTool.Logger.Error(e, "ReadDataFromCsv__读取文件错误");
            throw;
        }

        return result;
    }

    // 从 CSV 文件读取数据
    public static Dictionary<string, List<object>> ReadAllDataFromCsv(string fileDir, string dataType,
        bool removeMark = true)
    {
        var result = new Dictionary<string, List<object>>();
        try
        {
            var filePath = Path.Combine(fileDir, $"{dataType}.csv");
            if (!File.Exists(filePath))
            {
                LogTool.GetLogger("FileDataHelper", "ReadDataFromCsv").Error($"File not found: {filePath}");
            }

            var lines = File.ReadAllLines(filePath);
            if (lines.Length < 2)
            {
                LogTool.GetLogger("FileDataHelper", "ReadDataFromCsv")
                    .Error($"CSV file is empty or contains only headers.: {filePath}");
            }

            var headers = lines[0].Split(',');
            //去掉Mark列
            if (removeMark && headers.Contains("Marked"))
            {
                headers = headers.Where(x => x != "Marked").ToArray();
            }

            for (int i = 0; i < headers.Length; i++)
            {
                result[headers[i]] = new List<object>();
            }

            for (int i = 0; i < lines.Length; i++)
            {
                var rowValues = lines[i].Split(',');
                if (rowValues.Length != headers.Length)
                {
                    throw new InvalidDataException($"Invalid data on line {i + 1}");
                }

                for (int j = 1; j < headers.Length; j++)
                {
                    if (typeof(uint) == typeof(uint) && uint.TryParse(rowValues[j], out var uintValue))
                    {
                        result[headers[j]].Add((uint)uintValue);
                    }
                    else if (int.TryParse(rowValues[j], out var intValue))
                    {
                        result[headers[j]].Add((int)intValue);
                    }
                    else if (double.TryParse(rowValues[j], out var doubleValue))
                    {
                        result[headers[j]].Add((double)doubleValue);
                    }
                    else
                    {
                        result[headers[j]].Add(rowValues[j]);
                    }
                }
            }
        }
        catch (Exception e)
        {
            LogTool.Logger.Error(e, "ReadDataFromCsv__读取文件错误");
            throw;
        }

        return result;
    }


    public static void MarkClosestTimestamp(string fileDir, string dataType, DateTime targetTime, string markValue)
    {
        try
        {
            var filePath = Path.Combine(fileDir, $"{dataType}.csv");
            var lines = File.ReadAllLines(filePath);
            if (lines.Length < 2)
            {
                throw new InvalidDataException($"CSV file is empty or contains only headers: {filePath}");
            }

            var headers = lines[0].Split(',').ToList();
            if (!headers.Contains("Marked"))
            {
                headers.Add("Marked");
                lines[0] = string.Join(",", headers);
            }

            int closestIndex = -1;
            double closestDifference = double.MaxValue;
            for (int i = 1; i < lines.Length; i++)
            {
                var values = lines[i].Split(',');
                if (DateTime.TryParse(values[0], out DateTime timestamp))
                {
                    var difference = Math.Abs((timestamp - targetTime).TotalMilliseconds);
                    if (difference < closestDifference)
                    {
                        closestDifference = difference;
                        closestIndex = i;
                    }
                }
            }

            if (closestIndex != -1)
            {
                var closestValues = lines[closestIndex].Split(',').ToList();
                if (closestValues.Count < headers.Count) // 如果行中没有“Marked”列，则添加
                {
                    closestValues.Add(markValue);
                }
                else // 否则更新“Marked”列
                {
                    closestValues[headers.Count - 1] = markValue;
                }

                string updatedLine = string.Join(",", closestValues);
                // 使用 FileStream 直接更新特定行
                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite);
                long bytePosition = 0;
                for (int i = 0; i < closestIndex; i++)
                {
                    bytePosition += lines[i].Length + Environment.NewLine.Length;
                }

                fileStream.Seek(bytePosition, SeekOrigin.Begin);
                var lineBytes = Encoding.UTF8.GetBytes(updatedLine + Environment.NewLine);
                fileStream.Write(lineBytes, 0, lineBytes.Length);
            }
        }
        catch (Exception e)
        {
            LogTool.Logger.Error(e, "MarkClosestTimestamp__打标错误");
            throw;
        }
    }
}