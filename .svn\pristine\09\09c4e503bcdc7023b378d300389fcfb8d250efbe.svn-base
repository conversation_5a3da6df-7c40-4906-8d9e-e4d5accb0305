﻿using HX.HRV.MAUI.Services;
using Makaretu.Dns;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.LifecycleEvents;

namespace HX.HRV.MAUI;

public static class MauiProgram
{

	static MauiProgram()
	{
		AppContext.SetSwitch("HybridWebView.InvokeJavaScriptThrowsExceptions", isEnabled: true);
	}
	public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
		builder.Services.AddSingleton<SerialPortService>(); 
		builder.Services.AddSingleton<UDiskService>(); 

		builder
			.UseMauiApp<App>();


#if WINDOWS
        builder.ConfigureLifecycleEvents(x =>
        {
            x.AddWindows(x =>
            {
                x.OnActivated((w, e) => { });

                x.OnWindowCreated(w =>
                {
                    if (w is not MauiWinUIWindow window)
                        return;

                    window.ExtendsContentIntoTitleBar = false;
                    var handle = WinRT.Interop.WindowNative.GetWindowHandle(window);
                    var id = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(handle);
                    var appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(id);
                    switch (appWindow.Presenter)
                    {
                        case Microsoft.UI.Windowing.OverlappedPresenter overlappedPresenter:
                            overlappedPresenter.IsMaximizable = false;
                            overlappedPresenter.IsResizable = false;
                            overlappedPresenter.Maximize();
                            break;
                    }
                });
            });
        });
#endif

      
        
        
#if DEBUG
        builder.Logging.AddDebug();
        builder.Services.AddHybridWebViewDeveloperTools();
#endif

        return builder.Build();
    }
}