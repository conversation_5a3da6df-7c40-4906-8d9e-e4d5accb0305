<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JsonSchemaMappingsProjectConfiguration">
    <state>
      <map>
        <entry key="JSConfig">
          <value>
            <SchemaInfo>
              <option name="name" value="JSConfig" />
              <option name="relativePathToSchema" value="http://json.schemastore.org/jsconfig" />
              <option name="applicationDefined" value="true" />
              <option name="patterns">
                <list>
                  <Item>
                    <option name="path" value="HX.HRV.SCI.Web/Properties/launchSettings.json" />
                  </Item>
                </list>
              </option>
            </SchemaInfo>
          </value>
        </entry>
      </map>
    </state>
  </component>
</project>