﻿@page "/client/system/config"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Pages.Client.Components
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Utils
@using UFU.IoT.Models
@using UFU.IoT.Shared.Models
@layout HRVDefaultLayout
@attribute [Permission("系统配置/系统项配置", MenuOrder = "6", Icon = "mdi-forum|xitong", IsMenu = true)]
@inject IJSRuntime js

<style>
	.customer-input .m-input__prepend-outer .m-label {
		width: 140px;
		font-size: 1.25rem;
	}
</style>

@if (SystemConfig != null)
{
	<MCard Class="pa-4">
		<MSwitch Label="是否自动打印报告" OnChange="EventCallback.Factory.Create<bool>(this, HandleIsAutoChange)"
		@bind-Value="SystemConfig.IsAutoPrint" />
		<MCol Cols="12">
			<!-- 患者来源 -->
			<div class="horizontal-stack">
				<MLabel>患者来源:</MLabel>
				<HRVSourceGroupButton IsOnlyShow="true" ConfigModel="SystemConfig" SourceText="SourceText">
				</HRVSourceGroupButton>
			</div>
		</MCol>
		<MCol Cols="12">
			<div class="horizontal-stack">
				<MLabel>检测时长:</MLabel>
				<HRVCheckTimeButton IsOnlyShow="true" @bind-CheckTimeValue="CheckTimeValue" ConfigModel="SystemConfig">
				</HRVCheckTimeButton>
			</div>
		</MCol>
		@if (isShowExport)
		{
			<MCol Cols="12">
				<MCard Title="频率设置">
					<MRow>
					@* 	<MCol Cols="12" Md="4"> *@
					@* 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.HRRate" Dense *@
					@* 		Solo *@
					@* 		Filled> *@
					@* 			<PrependContent> *@
					@* 				<MLabel Class="mr-2">心率:</MLabel> *@
					@* 			</PrependContent> *@
					@* 		</MTextField> *@
					@* 	</MCol> *@
					@* 	<MCol Cols="12" Md="4"> *@
					@* 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.EDARate" Dense *@
					@* 		Solo *@
					@* 		Label="皮肤电阻" Filled> *@
					@* 			<PrependContent> *@
					@* 				<MLabel Class="mr-2">皮肤电阻:</MLabel> *@
					@* 			</PrependContent> *@
					@* 		</MTextField> *@
					@* 	</MCol> *@
					@* 	<MCol Cols="12" Md="4"> *@
					@* 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.ENVRate" Dense *@
					@* 		Solo *@
					@* 		Label="气压 环境温度 光照" Filled> *@
					@* 			<PrependContent> *@
					@* 				<MLabel Class="mr-2">气压 环境温度 光照:</MLabel> *@
					@* 			</PrependContent> *@
					@* 		</MTextField> *@
					@* 	</MCol> *@
					@* 	<MCol Cols="12" Md="4"> *@
					@* *@
					@* 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.IMURate" Dense *@
					@* 		Solo *@
					@* 		Label="九轴(IMU)" Filled> *@
					@* 			<PrependContent> *@
					@* 				<MLabel Class="mr-2">九轴(IMU):</MLabel> *@
					@* 			</PrependContent> *@
					@* 		</MTextField> *@
					@* 	</MCol> *@
					@* 	<MCol Cols="12" Md="4"> *@
					@* 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.PPGRate" Dense *@
					@* 		Solo *@
					@* 		Label="PPGRate" Filled> *@
					@* 			<PrependContent> *@
					@* 				<MLabel Class="mr-2">PPG:</MLabel> *@
					@* 			</PrependContent> *@
					@* 		</MTextField> *@
					@* 	</MCol> *@
					@* 	<MCol Cols="12" Md="4"> *@
					@* 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.SPO2Rate" *@
					@* 		Dense *@
					@* 		Solo *@
					@* 		Label="血氧" Filled> *@
					@* 			<PrependContent> *@
					@* 				<MLabel Class="mr-2">血氧:</MLabel> *@
					@* 			</PrependContent> *@
					@* 		</MTextField> *@
					@* 	</MCol> *@
				@* <MCol Cols="12" Md="3"> *@
				@* 	<MSelect Items="SystemRateConfigStatic.PpgRates" *@
				@* 	         @bind-Value="@SystemConfig.SystemRateConfig.HRRate" *@
				@* 	         ItemText="u => u.ToString()" *@
				@* 	         ItemValue="u => u" *@
				@* 	         Label="PPG" *@
				@* 	         Outlined *@
				@* 	         Dense></MSelect> *@
				@* *@
				@* </MCol> *@
				@* <MCol Cols="12" Md="3"> *@
				@* *@
				@* 	<MSelect Items="SystemRateConfigStatic.EdaRates" *@
				@* 	         @bind-Value="@SystemConfig.SystemRateConfig.EDARate" *@
				@* 	         ItemText="u => u.ToString()" *@
				@* 	         ItemValue="u => u" *@
				@* 	         Label="皮肤电阻" *@
				@* 	         Outlined *@
				@* 	         Dense></MSelect> *@
				@* </MCol> *@
				@* *@
				@* <MCol Cols="12" Md="3"> *@
				@* 	<MSelect Items="SystemRateConfigStatic.ImuRates" *@
				@* 	         @bind-Value="@SystemConfig.SystemRateConfig.IMURate" *@
				@* 	         ItemText="u => u.ToString()" *@
				@* 	         ItemValue="u => u" *@
				@* 	         Label="九轴(IMU)" *@
				@* 	         Outlined *@
				@* 	         Dense></MSelect> *@
				@* </MCol> *@
				@* *@
				@* <MCol Cols="12" Md="3"> *@
				@* 	<MSelect Items="SystemRateConfigStatic.SktRates" *@
				@* 	         @bind-Value="@SystemConfig.SystemRateConfig.SKTRate" *@
				@* 	         ItemText="u => u.ToString()" *@
				@* 	         ItemValue="u => u" *@
				@* 	         Label="皮肤温度" *@
				@* 	         Outlined *@
				@* 	         Dense></MSelect> *@
				@* </MCol> *@
					</MRow>
				</MCard>
			</MCol>
		}
		<MCol Cols="12">
			<MCard Title="设备布局设置" Class="pl-4">
				<MRow>
					<MCol Cols="3">
						<MTextField @bind-Value="SystemConfig.LayoutConfig.Row" Dense Solo
						Label="行"
						Filled>
							<PrependContent>
								<MLabel Class="mr-2">行:</MLabel>
							</PrependContent>
						</MTextField>
					</MCol>
					<MCol Cols="3">
						<MTextField @bind-Value="SystemConfig.LayoutConfig.Col" Dense Solo
						Label="列"
						Filled>
							<PrependContent>
								<MLabel Class="mr-2">列:</MLabel>
							</PrependContent>
						</MTextField>
					</MCol>
					<MCol Cols="3">
						<MTextField @bind-Value="SystemConfig.LayoutConfig.DisplayNumber" Dense
						Solo
						Label="显示数量" Filled>
							<PrependContent>
								<MLabel Class="mr-2">显示数量:</MLabel>
							</PrependContent>
						</MTextField>
					</MCol>
				</MRow>
				<MRow>
					<MCol Cols="3">
						<MSwitch @bind-Value="SystemConfig.LayoutConfig.IsShowHeader" Dense Solo
						Label="是否显示顶部操作" Filled>
						</MSwitch>
					</MCol>
					<MCol Cols="3">
						<MSwitch @bind-Value="SystemConfig.LayoutConfig.IsPage" Dense Solo
						Label="是否分页" Filled>
						</MSwitch>
					</MCol>
				</MRow>
			</MCard>
		</MCol>
		<MCol Cols="12" Md="4">
			<MButton OnClick="HandleIsAutoChange">保存</MButton>
		</MCol>
	</MCard>
}


@code {

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
	}



	public string SourceText { get; set; }
	[Inject] IPopupService PopupService { get; set; }
	public int CheckTimeValue { get; set; }
	public List<StringNumber> CheckTimeList { get; set; }
	private HxSystemConfigModel SystemConfig => DeviceStateService.SystemConfig;
	[Inject] IConfiguration Configuration { get; set; }
	private bool isShowExport => Configuration.GetValue<bool>("HrvVariable:IsShowExport");
	[Inject] public StateService stateService { get; set; }
	[Inject] public DeviceStateService	DeviceStateService { get; set; }




	private List<DeviceStatusViewModel> DeviceDataList=> DeviceStateService.DeviceDataList;

	private string DeviceSN { get; set; }="";

	private bool ServerLocation { get; set; }

	private bool SoftwareVersion { get; set; }

	private bool IsOnline { get; set; }
	

	[Inject] public InternalHttpClientService InternalHttpClientService { get; set; }

	private async Task HandleIsAutoChange()
	{
		var res = await InternalHttpClientService.EditSystemConfigAsync(SystemConfig);
		await PopupService.EnqueueSnackbarAsync("保存成功", AlertTypes.Success);
	}

	private const string DeviceTypeId = "2407160100000001";


	private async Task SendMsgToDeviceSerialPort() {
		var device = DeviceDataList.FirstOrDefault(m => m.Device?.DeviceSN ==DeviceSN);
		var msgJson = new {
			MsgId = 555555,
			action = "toSerialPort",
			//时间戳
			Time = DateTime.Now.ToUnixMs(),
			Device = new {
				SN = device?.Device.DeviceSN,
				Type = device?.Device.TypeId,
			},
			CMD = (int)BinaryCMD.Write,
			Data = new {
				ServerLocation = ServerLocation ? 1 : 0,
				SoftwareVersion = SoftwareVersion ? 1 : 0,
				IsOnline = IsOnline ? 1 : 0
			}
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeAsync<object>("SendMessageToClientByMsg", msgStr);
		
	}


}