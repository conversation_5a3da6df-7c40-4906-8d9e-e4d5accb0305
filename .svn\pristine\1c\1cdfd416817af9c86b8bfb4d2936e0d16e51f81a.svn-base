﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "Microsoft.AspNetCore.Authentication": "Warning"
      }
    }
  },
  "AllowedHosts": "*",

  "Cors": {
    "Origins": "http://localhost:8080",
    "AllowCredentials": true
  },
  "Permission": {
    "EnableDataPermission": true,
    "EnableFunctionPermission": true,
    "EnableVersionPermission": false
  },
//  "ConnectionStrings": {
//    "DefaultDbContext": "User ID=postgres;Password=*******************;Host=pve01.ufutech.com;Port=7432;Database=LingLongTu;Pooling=true;"
//  },
  "Redis": {
    "Enable": false,
    "ConnectionString": "127.0.0.1:6379,password=xBarvRgVJEEmBbe2"
  },
  "Email": {
    "Host": "smtpdm.aliyun.com",
    "Port": "465",
    "UseSSL": "true",
    "FromName": "WTC",
    "FromAddress": "<EMAIL>",
    "User": "<EMAIL>",
    "Password": "UFUYun2018Email",
    "ReplyTo": "<EMAIL>",
    "RegisterVerify": {
      "Subject": "注册验证邮件",
      "Body": "你正在注册WTC系统，验证码为：{0}，30分钟内有效，如非本人操作，请忽略此邮件。"
    }
  },
  "Blazor": {
    "Enable": true,
    "Layout": "_Layout",
    "RenderMode": "Server" //WebAssembly, WebAssemblyPrerendered, Server, ServerPrerendered, Auto
    // "RenderMode": "WebAssembly" //WebAssembly, WebAssemblyPrerendered, Server, ServerPrerendered, Auto
  },
  "DefaultUrl": {
    "Home": "/",
    "Login": "/Core/Auth/Login",
    "Register": "/"
  },
  "Weixin": {
    "AppId": "wxf4f49f85f25df338",
    "Secret": "68fc73fdf65860bdaaddc0509d65f9ec",
    "APIKey": "mall1qazWSXApimall1qazWSXApiPayP"
  },
  "Aliyun": {
    "AccessKeyId": "LTAIqYOYMNYDv9cd",
    "AccessKeySecret": "OPSjl0ZY8wk15xrhovwQHugJMBbN6e"
  },
  "AliSms": {
    "SignName": "阿里云短信测试专用", //短信签名
    "RegisterVerify": "SMS_130918257", //注册验证码模板，参数为{"code":"xxx"}
    "ChangePhone": "SMS_130913286",
    "ChangePassword": "SMS_130923240",
    "ChangePayPassword": "SMS_130913284"
  },
  "QCloudSms": {
    "AppID": "1400084011",
    "AppKey": "610881d031398c61ae75dc6b8f687ce2",
    "SignName": "海清新", //短信签名
    "RegisterVerify": 107639,
    "ChangePhone": 107871,
    "ChangePassword": 107869,
    "ChangePayPassword": 107870
  }
}
