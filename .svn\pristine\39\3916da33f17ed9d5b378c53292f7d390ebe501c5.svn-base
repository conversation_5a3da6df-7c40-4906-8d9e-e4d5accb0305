﻿using System.ComponentModel.DataAnnotations;
using UFU.CoreFX.Models;

namespace HX.HRV.Shared.Models;
/// <summary>
/// 导出任务
/// </summary>
[DataEntity("2408270500000001")]
public class HxExportTaskModel
{
    public string Id { get; set; }
    /// <summary>
    ///  检测记录ID
    /// </summary>
    public List<string> PatientRecordIds { get; set; }
    
    /// <summary>
    /// 导出数量
    /// </summary>
    public int ExportCount { get; set; }

    /// <summary>
    ///  导出进度
    ///  0-100
    /// </summary>
    public double Progress { get; set; }

    /// <summary>
    ///  导出状态
    /// </summary>
    public EnumExportTaskStatus ExportStatus { get; set; }
    
    public string FilePath { get; set; }

    public string OutputFilePath => Id;
    
}

/// <summary>
///  导出任务状态
/// </summary>
public enum EnumExportTaskStatus
{
    /// <summary>
    ///  未开始
    /// </summary>
    [Display(Name = "未开始")]
    UnStart = 0,
    /// <summary>
    ///  导出中
    /// </summary>
    [Display(Name = "导出中")]
    Exporting = 1, 
    /// <summary>
    ///  已完成
    /// </summary>
    [Display(Name = "已完成")]
    Finished = 2
}