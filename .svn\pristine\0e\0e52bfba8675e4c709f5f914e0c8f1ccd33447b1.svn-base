using System.Buffers.Binary;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Shared.Models;

namespace HX.Experiment.Web.Service.Services;

/// <summary>
/// 
/// </summary>
public class HRVSocketHandleService : ISocketHandleService
{
    /// <summary>
    /// 全部设备类型
    /// </summary>
    public static ConcurrentDictionary<string, DeviceTypeModel> DeviceTypes { get; } =
        new ConcurrentDictionary<string, DeviceTypeModel>();

    private IWebSocket _webSocket { get; set; }

    private DataModel<DeviceModel> _deviceModel { get; set; }

    public void SendMsgToDevice(string msg)
    {
        _webSocket.Socket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msg)), WebSocketMessageType.Text,
            true,
            CancellationToken.None);
    }


    public JsonNode ParseTextData(JsonNode jsonNode, string data)
    {
        return JsonNode.Parse(data);
    }

    public void OnReceiveUserTextMsg(string data)
    {
        LogTool.GetLogger(nameof(HRVSocketHandleService), nameof(OnReceiveUserTextMsg)).Information(data);
    }


    public void OnReceiveUserBinaryMsg(Span<byte> bytes, JsonNode jsonNode)
    {
        
        ParseByteData(bytes, jsonNode);
    }

    public void OnConnect(IWebSocket socket)
    {
        LogTool.GetLogger(nameof(HRVSocketHandleService), nameof(OnReceiveUserTextMsg)).Information(" Device Connect");
    }

    public event Action<DeviceModel> OnDeviceConnected;


    /// <summary>
    /// 
    /// </summary>
    /// <param name="bytes"></param>
    /// <param name="jsonNode"></param>
    /// <returns></returns>
    public JsonNode ParseByteData(Span<byte> bytes, JsonNode jsonNode)
    {
        var type = jsonNode?["Data"]?["Type"]?.GetValue<int>();
        if (type >= 0)
        {
            switch (type)
            {
                case (int)HXDataType.PPG:
                {
                    var data = ParsePPGDataByDataType(bytes);
                    var rData = data["PPG-G"];
                    jsonNode["Data"]["PPG_G"] = JsonValue.Create(rData);
                    jsonNode["Data"]["PPG_R"] = JsonValue.Create(data["PPG-R"]);
                    jsonNode["Data"]["PPG_I"] = JsonValue.Create(data["PPG-I"]);
                    break;
                }
                case (int)HXDataType.HRSPO2:
                {
                    var data = ParseSPO2DataByDataType(bytes);
                    jsonNode["Data"]["Bmp"] = JsonValue.Create(data.Item1);
                    jsonNode["Data"]["SPO2"] = JsonValue.Create(data.Item2);
                    jsonNode["Data"]["Battery"] = JsonValue.Create(data.Item3);
                    break;
                }
                case (int)HXDataType.IMU:
                {
                    var data = ParseSensorData(bytes);
                    jsonNode["Data"]["IMU"] = JsonValue.Create(data);
                    break;
                }
                case (int)HXDataType.EDA:
                {
                    var data = ParseByData(bytes);
                    jsonNode["Data"]["EDA"] = JsonValue.Create(data);
                    break;
                }
                case (int)HXDataType.TEMPSKIN:
                case (int)HXDataType.LIGHT:
                case (int)HXDataType.TEMPAIR:
                case (int)HXDataType.AP:
                {
                    var hxDataType = (HXDataType)type;
                    var data = ParseByData(bytes);
                    jsonNode["Data"][hxDataType.ToString()] = JsonValue.Create(data);
                    break;
                }
                case (int)HXDataType.TICK:
                    break;
            }
        }
        return jsonNode;
    }


    /// <summary>
    ///  九轴传感器数据
    /// </summary>
    /// <param name="bytes"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    private static Dictionary<string, List<short>> ParseSensorData(Span<byte> bytes)
    {
        // 初始化字典存储各传感器数据
        var data = new Dictionary<string, List<short>>()
        {
            ["GYRO-X"] = [],
            ["GYRO-Y"] = [],
            ["GYRO-Z"] = [],
            ["ACC-X"] = [],
            ["ACC-Y"] = [],
            ["ACC-Z"] = [],
            ["GEO-X"] = [],
            ["GEO-Y"] = [],
            ["GEO-Z"] = [],
        };

        // 每次采样的数据长度为 18 个字节（9 方向，每方向 2 字节）
        int frameSize = 18;

        // 检查输入字节数组的长度是否为 18 的倍数
        if (bytes.Length % frameSize != 0)
        {
            throw new ArgumentException("数据长度不符合传感器格式！");
        }

        // 遍历每一帧数据
        for (int i = 0; i < bytes.Length / frameSize; i++)
        {
            try
            {
                // 解析陀螺仪数据（小端模式）
                data["GYRO-X"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize, 2))); // C1C2 陀螺仪 X 轴
                data["GYRO-Y"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 2))); // B1B2 陀螺仪 Y 轴
                data["GYRO-Z"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 4))); // A1A2 陀螺仪 Z 轴

                // 解析加速度计数据（小端模式）
                data["ACC-X"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 6))); // F1F2 加速度计 X 轴
                data["ACC-Y"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 8))); // E1E2 加速度计 Y 轴
                data["ACC-Z"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 10))); // D1D2 加速度计 Z 轴

                // 解析地磁传感器BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice (
                data["GEO-X"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 12))); // 9192 地磁 X 轴
                data["GEO-Y"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 14))); // 8182 地磁 Y 轴
                data["GEO-Z"]
                    .Add(BinaryPrimitives.ReadInt16LittleEndian(bytes.Slice(i * frameSize + 16))); // A1A2 地磁 Z 轴
            }
            catch (Exception ex)
            {
                Console.WriteLine("解析数据异常：" + ex.Message);
            }
        }

        return data;
    }

    /// <summary>
    /// 9个字节一组, 一组三个数据 ，每个数据三个字节
    /// //红光;//红外光//绿光
    /// 将数据添加到JsonNoe["data"]
    /// </summary>
    /// <param name="bytes"></param>
    /// <returns></returns>
    private static Dictionary<string, List<uint>> ParsePPGDataByDataType(Span<byte> bytes)
    {
        var data = new Dictionary<string, List<uint>>()
        {
            ["PPG-R"] = new(),
            ["PPG-I"] = new(),
            ["PPG-G"] = new(),
        };
        // 遍历每9个字节，分别解析PPG-R, PPG-I, 和 PPG-G数据
        for (int i = 0; i < bytes.Length / 9; i++)
        {
            try
            {
                var dataBytes = bytes[(i * 9 + 0)..(i * 9 + 3)];
                dataBytes.Reverse();
                data["PPG-R"].Add(dataBytes[2] | ((uint)dataBytes[1] << 8) | ((uint)dataBytes[0] << 16));

                dataBytes = bytes[(i * 9 + 3)..(i * 9 + 6)];
                dataBytes.Reverse();
                data["PPG-I"].Add(dataBytes[2] | ((uint)dataBytes[1] << 8) | ((uint)dataBytes[0] << 16));

                dataBytes = bytes[(i * 9 + 6)..(i * 9 + 9)];
                dataBytes.Reverse();
                data["PPG-G"].Add(dataBytes[2] | ((uint)dataBytes[1] << 8) | ((uint)dataBytes[0] << 16));
            }
            catch (Exception ex)
            {
                Console.WriteLine("解析数据异常：" + ex.Message);
            }
        }

        return data;
    }

    /// <summary>
    /// 解析数据类型中的心率、血氧和电量数据。
    /// </summary>
    /// <param name="bytes">要解析的字节数组。</param>
    /// <returns>包含心率、血氧和电量的元组。</returns>
    private static (uint, uint, uint) ParseSPO2DataByDataType(Span<byte> bytes)
    {
        return (bytes[0], bytes[1], bytes[2]);
    }

    private static float[] ParseByData(Span<byte> bytes)
    {
        var data = MemoryMarshal.Cast<byte, float>(bytes)
            .ToArray();
        return data;
    }

    private bool RegCommandHandler(DeviceTypeModel deviceType, ushort msgId, long time, DeviceInfo device)
    {
        var type = device.Type;
        var secret = device.Secret;
        var sn = device.ChipSN;
        var swVer = device.AppVer;
        Console.WriteLine($"收到设备注册消息:swVer:{swVer},AppVer:{device.AppVer}");
        var token = device.Token;
        //如果缺少必要参数，返回格式错误
        if (string.IsNullOrWhiteSpace(type) || string.IsNullOrWhiteSpace(sn))
        {
            var result = new
            {
                CMD = (int)BinaryCMD.Ack,
                Time = DateTime.Now.ToUnix(),
                MsgId = msgId,
                Error = (int)Error.FormatError,
                ErrorMsg = "格式错误"
            };
            SendMsgToDevice(JsonSerializer.Serialize(result));
            return false;
        }

        using var db = new DataRepository(UserInfo.System);
        if (deviceType == null)
        {
            var result = new
            {
                CMD = (int)BinaryCMD.Ack,
                Time = DateTime.Now.ToUnix(),
                MsgId = msgId,
                Error = (int)Error.Exception,
                ErrorMsg = "设备类型不存在"
            };
            SendMsgToDevice(JsonSerializer.Serialize(result));
            return false;
        }

        //如果设备类型存在，则根据类型编号和芯片序列号查找对应设备
        //设备存在，直接返回注册成功
        _deviceModel = db
            .Query<DeviceModel>()
            .AsNoTracking()
            .FirstOrDefault(dataModel => dataModel.Data.TypeId == deviceType.Id
                                         && dataModel.Data.DeviceSN == sn);
        //如果设备不存在，则添加设备并返回注册信息
        if (_deviceModel == null)
        {
            var allDevice = db.Query<DeviceModel>().AsNoTracking().ToList();
            var maxName = allDevice == null || allDevice?.Count == 0
                ? 0
                : allDevice.Max(m =>
                {
                    if (int.TryParse(m.Data.Name, out var num))
                    {
                        return num;
                    }

                    return 0;
                });
            var model = new DeviceModel();
            model.Name = (maxName + 1).ToString();
            model.ChipSN = sn;
            model.DeviceSN = sn;
            model.Id = UId.GetNewId();
            model.TypeId = type;
            model.OrganId = deviceType.OrganId;
            model.AddTime = DateTime.Now;
            model.UpdateTime = DateTime.Now;
            model.Version = Guid.NewGuid().ToString();
            _deviceModel = new DataModel<DeviceModel>()
            {
                Id = model.Id,
                Data = model,
                OrganId = UserInfo.System.Organ.GetTopOrganId(),
                UserId = UserInfo.System.Id,
                AddTime = model.AddTime,
                UpdateTime = model.UpdateTime,
                Version = model.Version
            };
            db.Add(_deviceModel);
            db.SaveChanges();
            LogTool.Logger.Information(
                "[{DateTime:HH:mm:ss:fff}]新设备注册:[{DataName}-{DataId}]；|{Sn}|{Type}", DateTime.Now, _deviceModel.Data.Name, _deviceModel.Data.Id, sn, type);
        }
        return true;
    }
}