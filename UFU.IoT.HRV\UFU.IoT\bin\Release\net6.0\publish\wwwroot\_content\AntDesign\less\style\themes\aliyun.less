// Generated by @ant-design/aliyun-theme

@font-size-sm: 12px;
@font-size-base: 12px;
@primary-color: #0070cc;
@border-radius-base: 0;
@border-radius-sm: 0;
@text-color: fade(#000, 65%);
@text-color-secondary: fade(#000, 45%);
@background-color-base: hsv(0, 0, 96%);
@success-color: #1e8e3e;
@error-color: #d93026;
@warning-color: #ffc440;
@info-color: @primary-color;
@danger-color: @error-color;
@processing-color: @primary-color;
@border-color-base: #dedede;
@border-color-split: #dedede;
@outline-width: 0;
@outline-color: #737373;
@input-height-lg: 36px;
@input-height-base: 32px;
@input-height-sm: 24px;
@input-hover-border-color: #737373;
@form-item-margin-bottom: 16px;
@btn-default-bg: #fafafa;
@btn-default-border: #dedede;
@btn-danger-color: #fff;
@btn-danger-bg: @error-color;
@btn-danger-border: @error-color;
@switch-color: @success-color;
@table-header-bg: #fafafa;
@table-row-hover-bg: #fafafa;
@table-padding-vertical: 15px;
@badge-color: @error-color;
@breadcrumb-base-color: @text-color;
@breadcrumb-last-item-color: @text-color-secondary;
@slider-rail-background-color: @background-color-base;
@slider-rail-background-color-hover: #e1e1e1;
@slider-track-background-color: @primary-color;
@slider-track-background-color-hover: @primary-color;
@slider-handle-border-width: 1px;
@slider-handle-color: #dedede;
@slider-handle-color-hover: #dedede;
@slider-handle-color-focus: #dedede;
@slider-handle-color-tooltip-open: #ddd;
@slider-handle-color-focus-shadow: transparent;
@slider-handle-shadow: 1px 1px 4px 0 rgba(0,0,0,.13);
@alert-success-border-color: #dff4e5;
@alert-success-bg-color: #dff4e5;
@alert-info-border-color: #e5f3ff;
@alert-info-bg-color: #e5f3ff;
@alert-error-border-color: #fcebea;
@alert-error-bg-color: #fcebea;
@alert-warning-border-color: #fff7db;
@alert-warning-bg-color: #fff7db;
@radio-button-bg: transparent;
@radio-button-checked-bg: transparent;
@progress-radius: 0;
@tabs-card-gutter: -1px;
@tabs-card-tab-active-border-top: 2px solid @primary-color;
@layout-body-background: #fafafa;

// Patch
@line-height-base: 1.5717;
@input-padding-vertical-lg: 8px;
@input-padding-vertical-base: 6px;
