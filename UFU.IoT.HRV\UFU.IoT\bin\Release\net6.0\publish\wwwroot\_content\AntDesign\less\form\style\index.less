@import '../../style/themes/index';
@import '../../style/mixins/index';
@import '../../input/style/mixin';
@import '../../button/style/mixin';
@import '../../grid/style/mixin';
@import './components';
@import './inline';
@import './horizontal';
@import './vertical';
@import './status';
@import './mixin';

@form-prefix-cls: ~'@{ant-prefix}-form';
@form-item-prefix-cls: ~'@{form-prefix-cls}-item';
@form-font-height: ceil(@font-size-base * @line-height-base);

.@{form-prefix-cls} {
  .reset-component();
  .reset-form();

  .@{form-prefix-cls}-text {
    display: inline-block;
    padding-right: 8px;
  }

  // ================================================================
  // =                             Size                             =
  // ================================================================
  .formSize(@input-height) {
    .@{form-item-prefix-cls}-label > label {
      height: @input-height;
    }

    .@{form-item-prefix-cls}-control-input {
      min-height: @input-height;
    }
  }

  &-small {
    .formSize(@input-height-sm);
  }

  &-large {
    .formSize(@input-height-lg);
  }
}

.explainAndExtraDistance(@num) when (@num >= 0) {
  padding-top: floor(@num);
}

.explainAndExtraDistance(@num) when (@num < 0) {
  margin-top: ceil(@num);
  margin-bottom: ceil(@num);
}

// ================================================================
// =                             Item                             =
// ================================================================
.@{form-item-prefix-cls} {
  .reset-component();

  margin-bottom: @form-item-margin-bottom;
  vertical-align: top;

  &-with-help {
    transition: none;
  }

  &-hidden,
  &-hidden.@{ant-prefix}-row {
    // https://github.com/ant-design/ant-design/issues/26141
    display: none;
  }

  // ==============================================================
  // =                            Label                           =
  // ==============================================================
  &-label {
    display: inline-block;
    flex-grow: 0;
    overflow: hidden;
    white-space: nowrap;
    text-align: right;
    vertical-align: middle;

    &-left {
      text-align: left;
    }

    &-wrap {
      overflow: unset;
      line-height: (@line-height-base - 0.25em);
      white-space: unset;
    }

    > label {
      position: relative;
      display: inline-flex;
      align-items: center;
      max-width: 100%;
      height: @form-item-label-height;
      color: @label-color;
      font-size: @form-item-label-font-size;

      > .@{iconfont-css-prefix} {
        font-size: @form-item-label-font-size;
        vertical-align: top;
      }

      // Required mark
      &.@{form-item-prefix-cls}-required:not(.@{form-item-prefix-cls}-required-mark-optional)::before {
        display: inline-block;
        margin-right: 4px;
        color: @label-required-color;
        font-size: @form-item-label-font-size;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';

        .@{form-prefix-cls}-hide-required-mark & {
          display: none;
        }
      }

      // Optional mark
      .@{form-item-prefix-cls}-optional {
        display: inline-block;
        margin-left: @margin-xss;
        color: @text-color-secondary;

        .@{form-prefix-cls}-hide-required-mark & {
          display: none;
        }
      }

      // Optional mark
      .@{form-item-prefix-cls}-tooltip {
        color: @text-color-secondary;
        cursor: help;
        writing-mode: horizontal-tb;
        margin-inline-start: @margin-xss;
      }

      &::after {
        & when (@form-item-trailing-colon=true) {
          content: ':';
        }
        & when not (@form-item-trailing-colon=true) {
          content: ' ';
        }

        position: relative;
        top: -0.5px;
        margin: 0 @form-item-label-colon-margin-right 0 @form-item-label-colon-margin-left;
      }

      &.@{form-item-prefix-cls}-no-colon::after {
        content: ' ';
      }
    }
  }

  // ==============================================================
  // =                            Input                           =
  // ==============================================================
  &-control {
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    &:first-child:not([class^=~"'@{ant-prefix}-col-'"]):not([class*=~"' @{ant-prefix}-col-'"]) {
      width: 100%;
    }
  }

  &-control-input {
    position: relative;
    display: flex;
    align-items: center;
    min-height: @input-height-base;

    &-content {
      flex: auto;
      max-width: 100%;
    }
  }

  // ==============================================================
  // =                           Explain                          =
  // ==============================================================
  &-explain,
  &-extra {
    clear: both;
    color: @text-color-secondary;
    font-size: @font-size-base;
    line-height: @line-height-base;
    transition: color 0.3s @ease-out; // sync input color transition
    .explainAndExtraDistance((@form-item-margin-bottom - @form-font-height) / 2);
  }

  &-explain-connected {
    width: 100%;
  }

  &-extra {
    min-height: @form-item-margin-bottom;
  }

  &-with-help &-explain {
    height: auto;
    // min-height: @form-item-margin-bottom;
    opacity: 1;
  }

  // ==============================================================
  // =                        Feedback Icon                       =
  // ==============================================================
  &-feedback-icon {
    font-size: @font-size-base;
    text-align: center;
    visibility: visible;
    animation: zoomIn 0.3s @ease-out-back;
    pointer-events: none;

    &-success {
      color: @success-color;
    }

    &-error {
      color: @error-color;
    }

    &-warning {
      color: @warning-color;
    }

    &-validating {
      color: @primary-color;
    }
  }
}

// >>>>>>>>>> Motion <<<<<<<<<<
// Explain holder
.@{ant-prefix}-show-help {
  transition: opacity @animation-duration-slow @ease-in-out;

  &-appear,
  &-enter {
    opacity: 0;

    &-active {
      opacity: 1;
    }
  }

  &-leave {
    opacity: 1;

    &-active {
      opacity: 0;
    }
  }
}

// Explain
.@{ant-prefix}-show-help-item {
  overflow: hidden;
  transition: height @animation-duration-slow @ease-in-out,
    opacity @animation-duration-slow @ease-in-out, transform @animation-duration-slow @ease-in-out !important;

  &-appear,
  &-enter {
    transform: translateY(-5px);
    opacity: 0;

    &-active {
      transform: translateY(0);
      opacity: 1;
    }
  }

  &-leave {
    transition: height @animation-duration-base @ease-in-out,
      opacity @animation-duration-base @ease-in-out, transform @animation-duration-base @ease-in-out !important;
  }

  &-leave-active {
    transform: translateY(-5px);
  }
}

// need there different zoom animation
// otherwise won't trigger anim
@keyframes diffZoomIn1 {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes diffZoomIn2 {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes diffZoomIn3 {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@import './rtl';
