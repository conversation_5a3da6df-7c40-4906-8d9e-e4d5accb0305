﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@implements IDisposable
<MRow Style="width: 100%" Justify="JustifyTypes.SpaceBetween">
	@* <MCol Cols="2"> *@
	@* 	<MLabel Class="font-weight-bold">电量:</MLabel> *@
	@* 	<MLabel>@DeviceStatusData.Battery</MLabel> *@
	@* </MCol> *@
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">当前患者:</MLabel>
		<MLabel>@DeviceStatusData.RecordModel?.Patient?.Name</MLabel>
	</MCol>
	<MCol Cols="3">
		<MLabel Class="font-weight-bold">倒计时:</MLabel>
		<MLabel>@DeviceStatusData.CollectTime.ToString(@"mm\:ss")</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">连接方式:</MLabel>
		<MLabel>@DeviceStatusData.ConnectionType</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">状态:</MLabel>
		<MLabel>@DeviceStatusData.DeviceStatus</MLabel>
	</MCol>
	<MCol Cols="12">
		<MSwitch TValue="bool"
		         @bind-Value="isOrg"
		         Label="@($"PPG降噪: {isOrg}")">
		</MSwitch>
	</MCol>
</MRow>
@if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.检测中)
{
	<MRow Style="display: flex;justify-content: center;">
		<MCol Cols="12" Style="@(GetnoneStyle())">
			<DeviceStatusDialogChartComponent
				Take="500*5"
				ChartTitle="PPG"
				LineColor="#354de5"
				DataKey="ORGPPG"
				DisplayNameAction="@((deviceStatusData) => { return deviceStatusData.OrgPPGData?.Last().ToString("F"); })"
				DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false" IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12" Style="@(GetVisibilityStyle())">
			<DeviceStatusDialogChartComponent
				Take="500*5"
				ChartTitle="PPG"
				LineColor="#9ac5fa"
				DataKey="PPG"
				DisplayNameAction="@((deviceStatusData) => { return deviceStatusData.PPGData?.Last().ToString("F"); })"
				DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false" IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12">
			<DeviceStatusDialogChartComponent
				Take="60"
				ChartTitle="HR(bpm)"
				LineColor="#3896bb"
				DataKey="Bmp"
				DisplayNameAction="@((deviceStatusData) => { return deviceStatusData.SPO2Data?.Last().ToString("F"); })"
				DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false" IsSmooth="false">
			</DeviceStatusDialogChartComponent>

		</MCol>
	</MRow>
}

@code {

	/// <summary>
	///
	/// </summary>
	[Parameter]
	public EventCallback CloseDialogEvent { get; set; }

	[Inject] protected InternalHttpClientService InternalHttpClientService { get; set; }

	/// <summary>
	///
	/// </summary>
	[Parameter]
	public DeviceStatusViewModel DeviceStatusData { get; set; }

	[Parameter] public EventCallback<DeviceStatusViewModel> DeviceStatusDataChanged { get; set; }
	protected PatientRecordModel RecordModel => DeviceStatusData.RecordModel;
	public bool isOrg { get; set; }
	[Inject] public IPopupService PopupService { get; set; }
	[Inject] DeviceStateService DeviceStateService { get; set; }

	protected async Task CloseDialog()
	{
		await CloseDialogEvent.InvokeAsync();
	}


	protected string GetVisibilityStyle()
	{
		return isOrg ? "visibility: visible !important;" : "visibility: hidden !important;height: 0px !important;padding: 0px !important;";
	}

	protected string GetnoneStyle()
	{
		return !isOrg ? "visibility: visible !important;" : "visibility: hidden !important;height: 0px !important;padding: 0px !important;";
	}

	[Inject] protected IJSRuntime _jsRuntime { get; set; }
	protected bool isChartInitialized = false;
	protected bool IsDisposed { get; set; } = false;

	protected void RegisterDeviceStatusEvents()
	{
		DeviceStatusData.OnDeviceStatusChange += OnChange;
	}

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
	}

	protected override void OnInitialized()
	{
		base.OnInitialized();
		this.DeviceStatusData.OnDeviceStatusChange += OnChange;
	}

	private void OnChange()
	{
		InvokeAsync(StateHasChanged);
	}


	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (!firstRender)
		{
			if (DeviceStatusData?.DeviceStatus == EnumDeviceStatus.空闲 || DeviceStatusData?.DeviceStatus == EnumDeviceStatus.完成)
			{
				await PopupService.EnqueueSnackbarAsync("检测完成！", AlertTypes.Success, timeout: 1500);
				await CloseDialogEvent.InvokeAsync();
			}
		}
	}

	public void Dispose()
	{
	}

}