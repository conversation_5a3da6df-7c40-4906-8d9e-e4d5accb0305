﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@implements IDisposable


@inject DeviceStateService DeviceStateService
@{
    var style = GetHeadStyle();
}

@if (DeviceStatusData != null) {
    <div Style="@style"
         class="d-flex justify-space-between ">
        <div class="d-flex align-center justify-center">
            <MTooltip Left>
                <ActivatorContent>
                    <MCard @attributes="@context.Attrs"
                           style="width:1rem; 
                           height: 1rem; 
                           background-color:rgba(0,0,0,0); 
                           border-radius: 50%; 
                           border: 2px solid #FFFFFF;"></MCard>
                </ActivatorContent>
            </MTooltip>
            @if (IsShowDeviceSN)
            {
                <span class="ml2"
                      style="color: #fff;">@DeviceStatusData.Device.Name _ @DeviceStatusData.Device.DeviceSN[6..] 文件数: @DeviceStatusData.FileCount</span>
            }
            else
            {
                <span class="ml2"
                      style="color: #fff;">@DeviceStatusData.Device.Name</span>
          
            }
        </div>
        @if (HeaderContent != null && (DeviceStatusData?.Device?.IsOnline ?? false)) {
            <div class="d-flex align-center justify-center">
                @HeaderContent
            </div>
        }
        @if (DeviceStatusData?.IsUsbLinked ?? false) {
            <div class="d-flex align-center justify-center">
                <span class="mdi mdi-usb" style="font-size: 1.4rem;color: #fff;"> </span>
            </div>
        }
        @if ((DeviceStatusData?.Device?.IsOnline ?? false) && DeviceStatusData.DeviceStatus != EnumDeviceStatus.离线) {
            <div class="d-flex align-center justify-center">
                @* @if (DeviceStatusData.IsCharging) *@
                @* { *@
                @*     var spanClass = "mdi mdi-battery"; *@
                @*     var spanColor = "color: #fff;"; *@
                @*     if (DeviceStatusData?.Battery < 20) { *@
                @*         spanClass = "mdi mdi-battery-outline "; *@
                @*         if (DeviceStatusData?.Battery < 10) { *@
                @*             spanColor = "color: #F44336"; *@
                @*         } *@
                @*     } else if (DeviceStatusData?.Battery is > 20 and < 50) { *@
                @*         spanClass = $"mdi mdi-battery-{(int)(DeviceStatusData?.Battery / 10) * 10} "; *@
                @*     } else if (DeviceStatusData?.Battery is > 50 and < 80) { *@
                @*         spanClass = $"mdi mdi-battery-{(int)(DeviceStatusData?.Battery / 10) * 10}"; *@
                @*     } else if (DeviceStatusData?.Battery < 100) { *@
                @*         spanClass = $"mdi mdi-battery-{(int)(DeviceStatusData?.Battery / 10) * 10}"; *@
                @*        *@
                @*     } *@
                @*     var  spanStyle =  "font-size: 1.4rem;"+ spanColor;  *@
                @*     <span  class="@spanClass" *@
                @*           style="@spanStyle"> *@
                @*     </span> *@
                @* } *@
                @* else *@
                @* { *@
                @*     <img style="width:28px" src="/images/icon/electricity-full.png" alt=""/> *@
                @* } *@
                <img style="width:28px" src="/images/icon/<EMAIL>" alt=""/>

            </div>
        }
    </div>
}

@code {
    [Parameter] public RenderFragment? HeaderContent { get; set; }
    [Parameter] public bool IsShowDeviceSN { get; set; }
    /// <summary>
    /// 连接类型
    /// </summary>
    [Parameter]
    public int LinkType { get; set; } = 1;

    public string DeviceId { get; set; }

    private List<DeviceStatusViewModel> DeviceDataList => DeviceStateService.DeviceDataList;
    [Parameter] public DeviceStatusViewModel DeviceStatusData { get; set; }

    private string GetHeadStyle() {
        var style = "font-size: 1rem;height: 20%;padding: 1rem;width: 100%;border-radius: 8px 8px 0 0;";
        if (this.DeviceStatusData?.IsUsbLinked == true) {
            style += "background: linear-gradient(90deg, rgb(0, 96, 204),rgb(86, 151, 222));";
        } else {
            if (this.DeviceStatusData?.DeviceStatus != Models.EnumDeviceStatus.离线) {
                style += this.DeviceStatusData?.ColorLinearString;
            } else {
                style += "background: linear-gradient(90deg, #a6b8cd, #BBD2EC);";
            }
        }
        return style;
    }
    protected override void OnInitialized() {
        DeviceStatusData.OnDeviceStatusChange += OnChange;
        base.OnInitialized();
    }
    private void OnChange() {
        InvokeAsync(StateHasChanged);
    }
    public void Dispose() {
        DeviceStatusData.OnDeviceStatusChange -= OnChange;
    }

}
