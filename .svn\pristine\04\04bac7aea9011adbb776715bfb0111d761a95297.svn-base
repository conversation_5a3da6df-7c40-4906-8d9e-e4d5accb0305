using UFU.CoreFX.Utils;

namespace HX.Experiment.Web.Service.Services;

public interface IAdminSocketHandleService
{
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="adminSocketConnect"></param>
    void OnConnect(IWebSocket adminSocketConnect);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="data"></param>
    void OnReceiveUserTextMsg(string data);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="span"></param>
    void OnReceiveUserBinaryMsg(Span<byte> span);
}