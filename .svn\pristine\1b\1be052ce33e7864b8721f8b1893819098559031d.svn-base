﻿@page "/client/system/device-config-list"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using HX.Encrypt.Models
@layout HRVDefaultLayout
@attribute [Permission("系统配置/设备配置", MenuOrder = "9", Icon = "mdi-view-dashboard|xitong", IsMenu = true)]
<div>
    <MForm Class="ma-2">
        <MRow Class="search-row">
            <MCol Cols="3" Class="d-flex align-center justify-center">
                <MTextField Class="customer-input" @bind-Value="DeviceName"
                            Solo
                            Dense>
                    <PrependContent>
                        <MLabel Class="mr-2">设备编号:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
            <MCol Cols="2" Class="d-flex align-start justify-space-between">
                <MButton Class="customer-button" OnClick="InitDataList">搜索</MButton>
            </MCol>
        </MRow>
    </MForm>
    <MCard Class="ma-2">
        <MDataTable
            OnOptionsUpdate="@HandleOnOptionsUpdate"
            TItem="DeviceStatusViewModel"
            Headers="_headers"
            Items="SerialPortDeviceDataList"
            DisableSort
            ItemsPerPage="10"
        >
            <HeaderColContent Context="header">
                <MLabel Style="font-weight: 400;color: #28333E;">@header.Text</MLabel>
            </HeaderColContent>
            <ItemColContent Context="item">
                @if (item.Header.Value == "actions")
                {
                    <div class="d-flex align-center justify-center">
                        <MButton OnClick="() =>OpenEditDialog(item.Item.Device.Id)" Color="blue"
                                 Class="text-decoration-underline" Plain>
                            设置
                        </MButton>
                    </div>
                }
                else
                {
                    <MLabel>@item.ValueContent</MLabel>
                }
            </ItemColContent>
        </MDataTable>
    </MCard>

</div>

@code {

    protected override void OnInitialized()
    {
        InitDataList();
        base.OnInitialized();
    }

    private string DeviceName { get; set; }
    private DataOptions _options = new(1, 10);
    private int _total;

    private List<DataTableHeader<DeviceStatusViewModel>> _headers => new()
    {
        new()
        {
            Text = "设备名称",
            Align = DataTableHeaderAlign.Start,
            Sortable = false,
            CellRender = c => c.Device.Name
        },
        new()
        {
            Text = "设备编号",
            CellRender = c => c.Device.DeviceSN
        },
        new()
        {
            Text = "设备状态",
            CellRender = c =>
            {
                RenderFragment icon = new[] { EnumDeviceStatus.空闲, EnumDeviceStatus.检测中 }.Contains(c.DeviceStatus)
                    ? @<div>
                          <MIcon Color="green"> mdi-circle-medium</MIcon>
                          在线
                      </div>
                    : @<div>
                          <MIcon Color="black"> mdi-circle-medium</MIcon>
                          离线
                      </div>;
                return icon;
            }
        },
        new()
        {
            Text = "连接方式",
            CellRender = c => "wifi"
        },
        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Align = DataTableHeaderAlign.Center,
        }
    };

    [Inject] public DeviceStateService DeviceStateService { get; set; }
    private List<DeviceStatusViewModel> SerialPortDeviceDataList = new();

    private void InitDataList()
    {
        var query = DeviceStateService.DeviceDataList.AsQueryable();
        if (!string.IsNullOrEmpty(DeviceName))
        {
            query = query
                .Where(m => m.Device.DeviceSN.Contains(DeviceName) || m.Device.Name.Contains(DeviceName));
        }

        SerialPortDeviceDataList = query?.Where(m => m.IsUsbLinked)?.ToList();
    }

    private void HandleOnOptionsUpdate(DataOptions obj)
    {
        InitDataList();
        StateHasChanged();
    }

    [Inject] StateService _stateService { get; set; }

    private void OpenEditDialog(string item)
    {
        var license = HX.Encrypt.RsaRegister.VerifyAndParseLicenseKey(out var licenseData);
        if (licenseData.SoftwareVersion == SoftwareVersion.Comm)
        {
            _stateService.NavigationManager.NavigateTo("/client/system/device-model-switch?id=" + item);
        }
        else
        {
            _stateService.NavigationManager.NavigateTo("/client/system/device-config-edit?id=" + item);
        }
        // _stateService.NavigationManager.NavigateTo("/client/system/device-model-switch?id=" + item);
    }


}