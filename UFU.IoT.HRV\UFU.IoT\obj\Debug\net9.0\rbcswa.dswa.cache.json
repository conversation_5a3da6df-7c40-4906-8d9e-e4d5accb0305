{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["1BJ/VldrmRo0CwP05GuBIcNt/q3ZOb0zl6jR3BYvuEA=", "jj9OCfTMwx3q8KnDQhRODN3BVyyaCMfv1JLF5LQ0RqI=", "m1U2vRUQRma4nZHqGh2vLn3/9at/2yqPkUzonK/dBXs=", "QxDdBaagqjzpVrjuj8uuX1fWGSa731zu+6fI1tdC0Rk=", "iK22h+yiyliz7ZgELTTgTPIWkepy3r3OD6SBuKPV+5Q=", "jdfwM0ckdgdeBtyZzfZGmfsvEKmjVNO/vzmo9/YpqYo=", "EXj+XK3R5iMjICBa3iT0glyIVl0jb5NNa5f/MBIAPws=", "tXOoAQt0meUL+LYUXwmTO1Vs7+ZA4dEaD6J9JPGACG4=", "BSkqrx5bgYwX4M4r+tSP1SNK/TKlGu5rqwq7JVV/99I=", "c9gQj4IXVb1adDi4IpUzTw+Whj7qRsRPM7XK0YSY8kU=", "DHDaYLIK7Lvn2dPKAOR/eF+Nq0ZAZapQ5/ffUsvDFbs=", "vH5k472xMkko8Ns2d42q7vxfvxeSOqSaPoeNiCs2uSw=", "L5xllIrjtooPG4Uso0guCPytLxK11T2tE4vr0yF2grg=", "QVBbBFEAfXf05bd5yRILRR/ydHcvjETLb1BlrN0lO/s=", "PIyQEF1ai40k0kDKURcFwyDnb9SWJasPmNyPUVEJYUg=", "ly1kFu46NNRJnsG/I8uLV7mrBlNm2x86wbW0AWSEiAQ=", "m7gCETJoXz52FTZfJo4lLcpWA9W8xzwqwyGfiW3TYvY=", "v/ePGKgep+Nf68DxwrdZm0AQLiyyxm7em4vdLxJOL64=", "ON4DOQGieQ+6NZc+PVN1DXX5bCWkgvuSNxdLqHGacy0=", "8a5fBAhBF2HW57KExAvOtt2CBUPDXHs8k4i6FzTzWiE=", "9Pk7nQrFPU2MPFaJsAEgxEu0wuTefOT2SE2QFro5mFw=", "9jTF9OXB7vFravAgUYrIJLJMFCGcHKTnd33J79XDvBE="], "CachedAssets": {"1BJ/VldrmRo0CwP05GuBIcNt/q3ZOb0zl6jR3BYvuEA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\6q525j3tlr-less8k5dp3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.aliyun.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e402i885od", "Integrity": "4mBwQ/MqaMqYqTYb2HFPmMMltxsnU8sHyzi7sq+P5n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.css", "FileLength": 75127, "LastWriteTime": "2025-07-28T05:52:06.7909577+00:00"}, "jj9OCfTMwx3q8KnDQhRODN3BVyyaCMfv1JLF5LQ0RqI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\34blflduah-vhh2jufqs3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.aliyun.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfry0ibibp", "Integrity": "YXftugZCMhIfHCBMODujtXIpw3wgk4M+NelcU37rZYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.min.css", "FileLength": 71500, "LastWriteTime": "2025-07-28T05:52:06.7874886+00:00"}, "m1U2vRUQRma4nZHqGh2vLn3/9at/2yqPkUzonK/dBXs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\1zsqth3rcz-x67401ppqt.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.compact.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0c5yxzlw3", "Integrity": "8T0v0lIgEXCx0/8hv5A4IOc3DJG1hrSfxrIYfDFbKPM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.css", "FileLength": 75109, "LastWriteTime": "2025-07-28T05:52:06.7899581+00:00"}, "QxDdBaagqjzpVrjuj8uuX1fWGSa731zu+6fI1tdC0Rk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\vyboxlqsxg-82f7tjxk3h.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.compact.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8w5f9zha1b", "Integrity": "aI8eX8SgkFjsvq09UBplk+Oz2beVaNyFxzQY9IjAOwM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.min.css", "FileLength": 71608, "LastWriteTime": "2025-07-28T05:52:06.7839809+00:00"}, "iK22h+yiyliz7ZgELTTgTPIWkepy3r3OD6SBuKPV+5Q=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\5ozbnmqx4c-less8k5dp3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e402i885od", "Integrity": "4mBwQ/MqaMqYqTYb2HFPmMMltxsnU8sHyzi7sq+P5n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.css", "FileLength": 75127, "LastWriteTime": "2025-07-28T05:52:06.784985+00:00"}, "jdfwM0ckdgdeBtyZzfZGmfsvEKmjVNO/vzmo9/YpqYo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\sqjvzvz5ns-0i9c2pqcbz.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f73vv6kcvr", "Integrity": "+7hT3MIQEl4CF/AwyoUxvp1BGa56glRh+VZ3Obj9XIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.css", "FileLength": 76595, "LastWriteTime": "2025-07-28T05:52:06.7839809+00:00"}, "EXj+XK3R5iMjICBa3iT0glyIVl0jb5NNa5f/MBIAPws=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\zb5s6wfjbh-l551dbsw1z.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.dark.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ha881xsv8", "Integrity": "9VwzILe8TJfU+97Qu7p2EgzMtbaAMV3/IRjPcV4VC9I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.min.css", "FileLength": 73010, "LastWriteTime": "2025-07-28T05:52:06.788494+00:00"}, "tXOoAQt0meUL+LYUXwmTO1Vs7+ZA4dEaD6J9JPGACG4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\vivduzjdd9-vhh2jufqs3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfry0ibibp", "Integrity": "YXftugZCMhIfHCBMODujtXIpw3wgk4M+NelcU37rZYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.min.css", "FileLength": 71500, "LastWriteTime": "2025-07-28T05:52:06.7874886+00:00"}, "BSkqrx5bgYwX4M4r+tSP1SNK/TKlGu5rqwq7JVV/99I=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\qwz8zeekkt-koqihchfaq.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.variable.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "atlazl7hc1", "Integrity": "7rPj7qxQWVl+Rd3Xro7bn4U/VJdDRoxTT9ouP0L0gIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.css", "FileLength": 75707, "LastWriteTime": "2025-07-28T05:52:06.7839809+00:00"}, "c9gQj4IXVb1adDi4IpUzTw+Whj7qRsRPM7XK0YSY8kU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\s2vzjyofgz-pvc8ota2an.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.variable.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9sxdqkycsq", "Integrity": "t8l3Yu1GR6nXUHCp8c8aX4BA70UOGbOvC0rHtpEznc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.min.css", "FileLength": 72059, "LastWriteTime": "2025-07-28T05:52:06.7859849+00:00"}, "DHDaYLIK7Lvn2dPKAOR/eF+Nq0ZAZapQ5/ffUsvDFbs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\1xpyc92csh-2jzru2q90w.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "js/ant-design-blazor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktyn9s7183", "Integrity": "+KaSx0H99B28TLdnTl+uqeU2PhlSUf0jb37ZnvY5SLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js", "FileLength": 20880, "LastWriteTime": "2025-07-28T05:52:06.7726245+00:00"}, "vH5k472xMkko8Ns2d42q7vxfvxeSOqSaPoeNiCs2uSw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\9prptnwetl-islnrq0c31.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "js/ant-design-blazor.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4scx56kee", "Integrity": "opGGUImPQDDEVCzW3cI4J2Z5t3xVcLTlicJ8Hos8mcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js.map", "FileLength": 69608, "LastWriteTime": "2025-07-28T05:52:06.7874886+00:00"}, "L5xllIrjtooPG4Uso0guCPytLxK11T2tE4vr0yF2grg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\exbutxx8ni-dkhcs8bo8w.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "airkiss#[.{fingerprint=dkhcs8bo8w}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\airkiss.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ryhp1ooza", "Integrity": "z1jti9qmWdiRorK2H1wFz4kJaaM0wlmcH3at7LJ6cnA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\airkiss.html", "FileLength": 890, "LastWriteTime": "2025-07-28T05:52:06.7726245+00:00"}, "QVBbBFEAfXf05bd5yRILRR/ydHcvjETLb1BlrN0lO/s=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\bfl3hiu4mo-ktkzw6ryo0.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "index#[.{fingerprint=ktkzw6ryo0}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g0qw05nm6b", "Integrity": "sG9CjR8G+vEdGW/YHW9HFOAlpOfQYfl4ppilGFBv6xk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.html", "FileLength": 2871, "LastWriteTime": "2025-07-28T05:52:06.7706252+00:00"}, "PIyQEF1ai40k0kDKURcFwyDnb9SWJasPmNyPUVEJYUg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\st4l1xspxa-tfad1g4lu9.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "index.min#[.{fingerprint=tfad1g4lu9}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.min.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8qkyp7gu93", "Integrity": "cFI1QqX31EwPJadRnVsjkno8p+kFmSm/M4zxqZgj3s8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.min.html", "FileLength": 2458, "LastWriteTime": "2025-07-28T05:52:06.7726245+00:00"}, "ly1kFu46NNRJnsG/I8uLV7mrBlNm2x86wbW0AWSEiAQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\0x8h6u5z5u-teovowdu0s.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "iotmsg#[.{fingerprint=teovowdu0s}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\iotmsg.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "22uevpn3ff", "Integrity": "Vr0gpJP7l2mYDgokRI4+R/SmyFRnCll8S4mhnMf2TkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\iotmsg.html", "FileLength": 527, "LastWriteTime": "2025-07-28T05:52:06.7736241+00:00"}, "m7gCETJoXz52FTZfJo4lLcpWA9W8xzwqwyGfiW3TYvY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\5q6ay5uweb-006o1sg79w.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "netconfig301#[.{fingerprint=006o1sg79w}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfig301.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5q21ewnpqq", "Integrity": "8z2jCMlICEqNOd3irXvArrXbPUbz9eQV+x2YlFaz7ps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfig301.html", "FileLength": 108, "LastWriteTime": "2025-07-28T05:52:06.769619+00:00"}, "v/ePGKgep+Nf68DxwrdZm0AQLiyyxm7em4vdLxJOL64=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\tn2hn4vb9h-ganed4i40t.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "netconfigindex#[.{fingerprint=ganed4i40t}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfigindex.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kl6b0nn91p", "Integrity": "KGIcKglubx95ajX2HP4b2ogisW0IiOb5fmq9ITZCeqU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfigindex.html", "FileLength": 2865, "LastWriteTime": "2025-07-28T05:52:06.7726245+00:00"}, "ON4DOQGieQ+6NZc+PVN1DXX5bCWkgvuSNxdLqHGacy0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\7d1xf3nowi-3n88wbcuej.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "websocket#[.{fingerprint=3n88wbcuej}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\websocket.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "27v1a3ju64", "Integrity": "LjpBcqronpBcsXUA/DVbKdWcSYQgD2tamfQYVwuchI4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\websocket.html", "FileLength": 861, "LastWriteTime": "2025-07-28T05:52:06.7716265+00:00"}, "8a5fBAhBF2HW57KExAvOtt2CBUPDXHs8k4i6FzTzWiE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\cpqkk24civ-giqexvpcp8.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "wificonfig#[.{fingerprint=giqexvpcp8}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "er59l42exv", "Integrity": "aurtKZfAg8pMCs+udsygB9c92X6Hxsvcb57Y43Hqu2g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.html", "FileLength": 3056, "LastWriteTime": "2025-07-28T05:52:06.7706252+00:00"}, "9Pk7nQrFPU2MPFaJsAEgxEu0wuTefOT2SE2QFro5mFw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\ttp178sm2q-aown4g87o9.gz", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "wificonfig.min#[.{fingerprint=aown4g87o9}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.min.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zwf1oqp5v", "Integrity": "UcrOzHusvzjjK44yjEwZdxXq7MmIwBWyORTmGmfwguY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.min.html", "FileLength": 2443, "LastWriteTime": "2025-07-28T05:52:06.7716265+00:00"}, "9jTF9OXB7vFravAgUYrIJLJMFCGcHKTnd33J79XDvBE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\xcr7auxe5f-vo82sr88ek.gz", "SourceId": "UFU.IoT", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/UFU.IoT", "RelativePath": "UFU.IoT#[.{fingerprint=vo82sr88ek}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\scopedcss\\bundle\\UFU.IoT.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6qls9zaqsj", "Integrity": "/pHwogR7zo1muc0aImuu5mStSL+2mwPYL/NIurwOZwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\Debug\\net9.0\\scopedcss\\bundle\\UFU.IoT.styles.css", "FileLength": 112, "LastWriteTime": "2025-07-28T05:52:06.769619+00:00"}}, "CachedCopyCandidates": {}}