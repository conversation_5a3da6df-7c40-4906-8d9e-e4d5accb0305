﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Areas.IoT.Controllers.V2
{
    /// <summary>
    /// 数据项管理
    /// </summary>
    [Area("IoT")]
    [ApiVersion("2.0")]
    [Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
    public class DataItemsController : Controller
    {
        private readonly DataRepository _context;
        /// <summary>
        /// 数据项
        /// </summary>
        /// <param name="context"></param>
        public DataItemsController(DataRepository context)
        {
            _context = context;
        }

        #region 接口

        /// <summary>
        /// 数据项管理
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <returns></returns>
        [HttpGet()]
        [Permission("数据项管理接口/列表", IsAPI = true)]
        public async Task<Result<List<DataItem>>> List(string typeId)
        {
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            var list = deviceType?.Data?.DataItemList ?? new List<DataItem>();
            return new Result<List<DataItem>>(list);
        }

        /// <summary>
        /// 数据项管理接口/存在
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">变量名</param>
        /// <returns></returns>
        [HttpGet()]
        [Permission("数据项管理接口/存在", IsAPI = true)]
        public async Task<Result<bool>> Exists(string typeId, string key)
        {
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            return new Result<bool>(deviceType?.Data?.DataItemList?.Any(m => m.Key == key) ?? false);
        }

        /// <summary>
        /// 数据项管理接口/详情
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">变量名</param>
        /// <param name="groupName">groupName</param>
        /// <returns></returns>
        [HttpGet()]
        [Permission("数据项管理接口/详情", IsAPI = true)]
        public async Task<Result<DataItem>> Get([FromQuery] string typeId, [FromQuery] string key,
            [FromQuery] string groupName)
        {
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (!string.IsNullOrEmpty(groupName))
            {
                return new Result<DataItem>(deviceType?.Data?.DataItemList?.FirstOrDefault(m => m.Key == key && m.GroupName == groupName));
            }
            return new Result<DataItem>(deviceType?.Data?.DataItemList?.FirstOrDefault(m => m.Key == key
               ));
        }

        /// <summary>
        /// 数据项管理接口/添加
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="dataItem">数据项</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("数据项管理接口/添加", IsAPI = true)]
        public async Task<Result<DataItem>> Add(string typeId, [FromBody] DataItem dataItem)
        {
            var result = new Result<DataItem>();
            if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(dataItem, result))
            {
                return result;
            }

            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }

            if (deviceType.Data?.DataItemList == null)
            {
                deviceType.Data.DataItemList = new List<DataItem>();
            }

            if (deviceType.Data.DataItemList.Any(m => m.Key == dataItem.Key&&m.GroupName==dataItem.GroupName))
            {
                result.AddError("[Key] 变量名已存在");
                ModelState.AddModelError("Key", "变量名已存在");
                return result;
           
            }
            deviceType.Data.DataItemList.Add(dataItem);
            deviceType.Version = Guid.NewGuid().ToString();
            deviceType.UpdateTime = DateTime.Now;
            _context.Update(deviceType);
            await _context.SaveChangesAsync();
            result.Data= dataItem;
            return result;
        }

        /// <summary>
        /// 数据项管理接口/编辑
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">原变量名</param>
        /// <param name="dataItem">数据项</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("数据项管理接口/编辑", IsAPI = true)]
        public async Task<Result<DataItem>> Edit(string typeId, string key, string groupName,[FromBody] DataItem dataItem)
        {
            var result = new Result<DataItem>();
            if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(dataItem, result))
            {
                return result;
            }
            var deviceType = await _context.Query<DeviceTypeModel>()
                .FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }

            int? index = -1;
            index = !string.IsNullOrEmpty(groupName) 
                ? deviceType?.Data?.DataItemList?.FindIndex(m => m.Key == key && m.GroupName == groupName) 
                : deviceType?.Data?.DataItemList?.FindIndex(m => m.Key == key);
            if (index is null or -1)
            {
                result.AddError("数据项不存在");
                return result;
            }
            deviceType.Data.DataItemList[index.Value] = dataItem;
            deviceType.Version = Guid.NewGuid().ToString();
            deviceType.UpdateTime = DateTime.Now;
            _context.Update(deviceType);
            await _context.SaveChangesAsync();
            result.Data = dataItem;
            return result;

        }

        /// <summary>
        /// 数据项管理接口/删除
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="key">变量名</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("数据项管理接口/删除", IsAPI = true)]
        public async Task<Result<bool>> Delete(string typeId, string key,string groupName)
        {
            var result = new Result<bool>();
            if (string.IsNullOrEmpty(typeId))
            {
                result.AddError("参数错误");
                return result ;
            }

            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }

            var index = deviceType.Data?.DataItemList?.FindIndex(m => m.Key == key&&m.GroupName==groupName);
            if (!index.HasValue || index == -1)
            {
                result.AddError($"数据项【{key}】不存在");
                return result;
            }
            deviceType.Data.DataItemList.RemoveAt(index.Value);
            deviceType.Version = Guid.NewGuid().ToString();
            deviceType.UpdateTime = DateTime.Now;
            _context.Update(deviceType);
            await _context.SaveChangesAsync();
            return result;
        }

        /// <summary>
        /// 数据项管理接口/批量删除
        /// </summary>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="keys">变量名</param>
        /// <returns></returns>
        [HttpPost()]
        [Permission("数据项管理接口/批量删除", IsAPI = true)]
        public async Task<Result<bool>> BatchDelete(string typeId, [FromBody] string[] keys)
        {            var result = new Result<bool>();
            if (keys == null || keys.Count() == 0)
            {
                result.AddError("参数错误");
                return result ;
            }
            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == typeId);
            if (deviceType == null)
            {
                result.AddError("设备类型不存在");
                return result;
            }
            var count = deviceType.Data.DataItemList?.RemoveAll(m => keys.Contains(m.Key));
            if (!(count > 0))
            {
                result.AddError("删除失败！");
                return result;
            }
            deviceType.Version = Guid.NewGuid().ToString();
            deviceType.UpdateTime = DateTime.Now;
            _context.Update(deviceType);
            await _context.SaveChangesAsync();
            return result;
        }

        #endregion

    }
}
