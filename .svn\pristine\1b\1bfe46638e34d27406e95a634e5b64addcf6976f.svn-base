﻿using HX.HRV.Shared.Pages;
using HX.HRV.Web.Services;
using Makaretu.Dns;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using HX.HRV.Web.Units;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web
{
    public class Module : BaseModule
    {
        public override string Name { get; set; } = "心率变异性系统";
        public override string Url { get; set; } = "/client/login";
        public override string Icon { get; set; } = "iconfont icon-admin";
        public override string Description { get; set; } = "";
        public override string Override { get; set; } = "心率变异性系统共享";

        public override async void Configure(IServiceCollection services)
        {
	
			ConfigureItems.Add(new ConfigureItem
            {
                Name = "心率变异性系统",
                Order = 901,
                Services = async (services) =>
                {
                    ByteAnalysisServices.AnalysisData();
                    services.AddHostedService<GenerateReportService>();
                    //services.AddHostedService<GenerateEmotionReportService>();
                    services.AddHostedService<FileExportService>();
                    BlazorApp.SetModuleLayout("心率变异性系统", typeof(HRVDefaultLayout));
                }
            });
        }
    }
}