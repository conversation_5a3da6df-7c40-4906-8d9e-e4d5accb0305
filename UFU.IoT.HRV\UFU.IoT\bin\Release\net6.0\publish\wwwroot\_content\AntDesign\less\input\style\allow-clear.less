@import (reference) '../../style/themes/index';
@input-prefix-cls: ~'@{ant-prefix}-input';

// ========================= Input =========================
.@{iconfont-css-prefix}.@{input-prefix-cls}-clear-icon,
.@{input-prefix-cls}-clear-icon {
  margin: 0;
  color: @disabled-color;
  font-size: @font-size-sm;
  vertical-align: -1px;
  // https://github.com/ant-design/ant-design/pull/18151
  // https://codesandbox.io/s/wizardly-sun-u10br
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: @text-color-secondary;
  }

  &:active {
    color: @text-color;
  }

  &-hidden {
    visibility: hidden;
  }

  &-has-suffix {
    margin: 0 @input-affix-margin;
  }
}

// ======================= TextArea ========================
.@{input-prefix-cls}-affix-wrapper.@{input-prefix-cls}-affix-wrapper-textarea-with-clear-btn {
  padding: 0;

  .@{input-prefix-cls}-clear-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1;
  }
}
