﻿window.initializeEChart = function (chartId, chartOptionJson) {
    console.log("initializeEChart",chartId, newOptionJson);
    var chart = echarts.init(document.getElementById(chartId));
    var chartOptions = JSON.parse(chartOptionJson);
    chart.setOption(chartOptions);
    window.echartsInstances = window.echartsInstances || {};
    window.echartsInstances[chartId] = chart;
};

window.updateEChart = function (chartId, newOptionJson) {
    console.log("updateEChart",chartId, newOptionJson);
    var chart = window.echartsInstances[chartId];
    var newOptions = JSON.parse(newOptionJson);
    if (chart) {
        chart.setOption(newOptions);
    }
};

window.EChartaddData=function (shift) {
    date.push(now);
    data.push((Math.random() - 0.4) * 10 + data[data.length - 1]);
    if (shift) {
        date.shift();
        data.shift();
    }
    now = new Date(+new Date(now) + oneDay);
}
window.stopPropagation = function (e) {
    e.stopPropagation();
};
window.printInIframe=function (divId) {
    const content = document.getElementById(divId).innerHTML;
    const iframe = document.createElement('iframe');
    document.body.appendChild(iframe);
    const doc = iframe.contentDocument || iframe.contentWindow.document;
    doc.open();
    doc.write(`<html><head><title>Print</title></head><body>${content}</body></html>`);
    doc.close();
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    document.body.removeChild(iframe);
}
