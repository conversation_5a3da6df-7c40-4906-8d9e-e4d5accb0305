﻿@page "/client/login"
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Shared.Models
@attribute [Permission(AllowAnonymous = true)]
@layout UFU.CoreFX.Shared.Pages.EmptyLayout
<style>
	* {
		font-family: 'Harmony_Regular', sans-serif !important;
	}

	.hx-login-card {
		flex-direction: column;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		border-radius: 62px;
		background-color: #cdd8ea;
		width: 924px;
		height: 646px;
	}

	input:-webkit-autofill {
		-webkit-box-shadow: 0 0 0 1000px white inset !important;
		-webkit-text-fill-color: #000 !important;
	}

	.m-messages__message {
		color: red;
	}

	.div-body {
		height: 100vh;
		width: 100vw;
		display: flex;
		align-items: center;
		justify-content: center;
		background-image: url('/images/login_bg.png');
		background-size: cover;
	}

	.hx-login-card .hx-login-button {
		background-image: -moz-linear-gradient(90deg, rgb(0, 141, 246) 0%, rgb(56, 170, 255) 100%);
		background-image: -webkit-linear-gradient(90deg, rgb(0, 141, 246) 0%, rgb(56, 170, 255) 100%);
		background-image: -ms-linear-gradient(90deg, rgb(0, 141, 246) 0%, rgb(56, 170, 255) 100%);
		box-shadow: 0px 14px 32px 0px #157bf7d4;
		align-content: center;
		text-align: center;
		width: 712px;
		border-radius: 2rem;
		font-size: 20px;
		color: white;
		margin-bottom: 83px;
	}

	.customer-input {
		height: 60px;
		border-radius: 19px;
		border: none;
		text-indent: 12px
	}

	.customer-input .m-input__slot {
		height: 54px;
		width: 712px;
	}

	.search-row .m-label {
		width: 100px;
		text-align: end;
	}
</style>
@inherits LayoutComponentBase
<MApp>
	<MMain>
		<MContainer Fluid>
			<div class="div-body">
				<MForm @bind-Value="valid" @ref="_form">
					<div class="d-flex flex-column align-center justify-center hx-login-card">
						<img src="images/logo_white.png" Style="margin-top: 62px" alt="" />
						<MLabel Weight="FontWeight.Bolder" Style="color: #0060CC;font-size: 28px;margin-top: 30px"
							Typo="Typography.Header">
							心率变异性分析软件
						</MLabel>
						<MTextField @bind-Value="loginInfo.Name" Solo Class="customer-input" Dense
							Rules="@(CreateRules("账号"))" TValue="string" type="text" style="margin-top: 58px;">
							<PrependInnerContent>
								<img src="/images/zhanghao.png" />
								<span
									style="font-size: 1.625rem;color: rgba(26, 26, 26, 1);height:20px;line-height:20px">账号：</span>
							</PrependInnerContent>
						</MTextField>
						<MTextField @bind-Value="loginInfo.Password" Type="password" Rules="@(CreateRules("密码"))" Solo
							Class="customer-input" Dense TValue="string" Style="margin-top: -10px;">
							<PrependInnerContent>
								<img src="/images/mima.png" />
								<span
									style="font-size: 1.625rem;color: rgba(26, 26, 26, 1);height:20px;line-height:20px">密码：</span>
							</PrependInnerContent>
						</MTextField>
						<MButton OnClick="HandleLogin" Class="hx-login-button" Style="height: 60px">立即登录</MButton>
					</div>
					<div id="bottom_version">
						<span style="opacity: 0.5;margin-top: 30px" Cols="12">
							© @DateTime.Now.Year 汇心健康 @Configuration.GetSection("Version").Value
						</span>
					</div>
				</MForm>
			</div>
		</MContainer>
	</MMain>
</MApp>

@code
{
	  /// <summary>
    /// 路由数据
    /// </summary>
    [CascadingParameter]
    public AuthRouteData AuthRouteData { get; set; }
	[Inject] private NavigationManager NavigationManager { get; set; }
	[Inject] private IConfiguration Configuration { get; set; }
	private async Task HandleLogin()
	{
		if (Validate())
		{
			await UserLogin();
		}
	}
	[Inject] IPopupService PopupService { get; set; }
	[Inject] StateService state { get; set; }
	private MForm? _form;
	private bool valid;
	[Parameter][SupplyParameterFromQuery] public string? backurl { get; set; }

	private List<Func<string?, StringBoolean>> CreateRules(string value)
	{
		return new List<Func<string?, StringBoolean>> { v => !string.IsNullOrEmpty(v) ? true : $"{value}不能为空" };
	}
	public class LoginInfo
	{
		public string Name { get; set; }
		public string Password { get; set; }
	}
	LoginInfo loginInfo = new();
	private async Task UserLogin()
	{
		PopupService.ShowProgressCircular(options => { options.Color = "blue"; });
		//发起登录请求
		var result = await state.PostAsJsonAsync<LoginTokenModel>("/api/v2/Core/Auth/Login", loginInfo);
		if (result.Success)
		{
			//设置cookie
			var cookie = new Cookie
				{
					Name = "token",
					Value = result.Data.Token,
					Expires = DateTime.Now.AddDays(7).ToUnixMs(),
					SameSite = "lax"
				};
			await state.Cookies.SetAsync(cookie);
			if (string.IsNullOrEmpty(backurl) || backurl == "null")
			{
				state.NavigationManager.NavigateTo("/client/device-status", true);
			}
			else
			{
				state.NavigationManager.NavigateTo(backurl, true);
			}
			PopupService.HideProgressCircular();
		}
		else
		{
			PopupService.HideProgressCircular();
			await PopupService.EnqueueSnackbarAsync(result.Message, AlertTypes.Error);
		}
	}

	bool Validate()
	{
		return _form!.Validate();
	}
}