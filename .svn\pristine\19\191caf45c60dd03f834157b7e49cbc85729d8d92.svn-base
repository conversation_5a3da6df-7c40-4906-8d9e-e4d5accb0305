﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Pages.Client.Dialog
@using UFU.CoreFX.Utils
<MDataTable OnOptionsUpdate="@HandleOnOptionsUpdate"
            TItem="PatientRecordModel"
            Headers="_headers"
            SingleSelect="false"
            ShowSelect
            ItemKey="r => r.Id"
            Items="PatientRecordModels"
            Stripe
            @bind-Selected="_selected"
            DisableSort="true"
            ItemsPerPage="_options.ItemsPerPage"
            Loading="_loading"
            FooterProps="@(props => { props.ShowFirstLastPage = false; props.ShowCurrentPage = false; props.PageText = @"共{2}条数据"; props.DisableItemsPerPage = true; })"
            ServerItemsLength="@_total">
    <HeaderColContent Context="header">
        <MLabel Style="font-weight: 400;
font-size: 1.25rem;
color: #28333E;">@header.Text</MLabel>
    </HeaderColContent>
    <ItemColContent Context="item">
        @if (item.Header.Value == "actions")
        {
            <div class="d-flex align-center justify-center">

                <MButton Style="padding: 0" OnClick="() => PrintReport(item.Item.Id)" Color="blue" Class="text-decoration-underline" Plain>
                    查看报告
                </MButton>

            </div>
        }
        else
        {
            <div style="display: flex;align-items: center;justify-content: center;min-width: 64px">
                <MLabel Style=""> @item.Value</MLabel>
            </div>
        }
    </ItemColContent>
</MDataTable>

@code {
    private bool _isShowTaskDialog;
    private bool _isShowDetailDialog;
    private int _total;

    private List<DateOnly> _dates = new()
    {
    };

    [Parameter] public string PatientId { get; set; } = "";
    private IEnumerable<string> _selected = new List<string>();

    private string FormatDate()
    {
        if (_dates.Count >= 2)
        {
            return _dates[0].ToString("yyyy-MM-dd") + "至" + _dates[1].ToString("yyyy-MM-dd");
        }

        return string.Empty;
    }

    private bool _loading = false;
    private bool isShowAddPatient = false;
    [Inject] IPopupService PopupService { get; set; }
    private PatientRecordModel DetailDialogData { get; set; } = new();

    private Sex[] Sexes { get; } = new[]
    {
        Sex.男,
        Sex.女
    };

    private DataOptions _options = new(1, 10);
    private bool _isShowDatePicker;
    private ExportRecordDialog exportRecordDialog;
    private List<PatientRecordModel> PatientRecordModels { get; set; } = new();

    private List<DataTableHeader<PatientRecordModel>> _headers => new()
    {
        new()
        {
            Text = "检测时间",
            Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.CollectStartTime), CellRender = (e) => e.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss")
        },
        new()
        {
            Text = "结束时间",
            Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.CollectEndTime), CellRender = (e) => e.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss")
        },
        new()
        {
            Text = "姓名", Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.Patient.Name),
            CellRender = (e) => e.Patient?.Name
        },
        new()
        {
            Text = "门诊/住院号",
            Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.Patient.OutpatientNumberString),
            CellRender = (e) => e.Patient?.OutpatientNumberString
        },
        new()
        {
            Text = "身份证号", Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.Patient.CardId),
            CellRender = (e) => e.Patient?.CardId
        },
        new()
        {
            Text = "年龄",
            Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.Patient.Age),
            CellRender = (e) => e.Patient?.Age?.ToString() ?? ""
        },
        new()
        {
            Text = "性别", Align = DataTableHeaderAlign.Center,
            CellRender = (e) => e.Patient?.Sex?.ToString()
        },
        new()
        {
            Text = "来源", Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.Patient.Source),
            CellRender = (e) => e.Patient?.Source
        },
        new()
        {
            Text = "报告编号", Align = DataTableHeaderAlign.Center,
            Value = nameof(PatientRecordModel.RecordCode),
            CellRender = (e) => e.RecordCode
        },
        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Align = DataTableHeaderAlign.Center,
        }
    };

    private async Task HandleOnOptionsUpdate(DataOptions options)
    {
        _options = options;
        await InitDataList();
    }

    private PatientModel _queryPatientModel { get; set; } = new();
    private PatientRecordModel _queryPatientRecordModel { get; set; } = new();
    [Inject] InternalHttpClientService _internalHttpClientService { get; set; }
    [Inject] StateService _stateService { get; set; }
    private string StartTaskId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await InitDataList();
        await base.OnInitializedAsync();
    }

    /// <summary>
    /// 初始化列表数据
    /// </summary>
    /// <returns></returns>
    private async Task InitDataList()
    {
        _loading = true;

        await Task.Run(async () =>
        {
         

            var queryParam = new Dictionary<string, string>
            {
                { nameof(PatientModel.Age), _queryPatientModel.ToString() },
                { nameof(PatientRecordModel.DeviceId), _queryPatientRecordModel.DeviceId },
                { nameof(PatientModel.CardId), _queryPatientModel.CardId },
                { nameof(PatientModel.Source), _queryPatientModel.Source },
                { nameof(PatientModel.OutpatientNumberString), _queryPatientModel.OutpatientNumberString },
                { nameof(PatientModel.Sex), _queryPatientModel.Sex.ToString() },
                { "page", _options.Page.ToString() },
                { "pageSize", _options.ItemsPerPage.ToString() }
            };
            if (!string.IsNullOrWhiteSpace(PatientId))
            {
                queryParam.Add("PatientId", PatientId);
            }
            var result = await _internalHttpClientService.GetPatientRecordModelListByParams(queryParam);
            PatientRecordModels = result?.Data?.Select(m => m.Data)?.ToList();
            _total = result.Page?.TotalCount ?? 0;
        });
        _loading = false;
    }

    /// <summary>
    /// 删除
    /// </summary>
    private async Task DeleteConfirmAsync(string id)
    {
        var confirmed = await PopupService.ConfirmAsync(
            "警告",
            "删除后将无法恢复，确认删除吗?",
            AlertTypes.Warning);
        if (confirmed)
        {
            var deleteResult = await _internalHttpClientService.DeletePatientRecordAsync(id);
            await PopupService.EnqueueSnackbarAsync(deleteResult ? "删除成功！" : "删除失败！", AlertTypes.Success);
            await InitDataList();
        }
    }

    private void ShowTaskDetail()
    {
        StartTaskId = string.Empty;
        _isShowTaskDialog = true;
    }

    private void CloseTaskDetail()
    {
        _isShowTaskDialog = false;
    }

    private void ShowRecordDetail()
    {
        _isShowDetailDialog = true;
    }

    private void CloseRecordDetail()
    {
        _isShowDetailDialog = false;
    }

    /// <summary>
    ///  添加导出记录
    /// </summary>
    /// <returns></returns>
    private async Task AddExportRecordAsync(string id = null)
    {
        var selected = !string.IsNullOrEmpty(id) ? new List<string> { id } : _selected.ToList();

        if (!selected.Any())
        {
            await PopupService.EnqueueSnackbarAsync("请选择需要导出的记录", AlertTypes.Warning);
            return;
        }

        var export = new HxExportTaskModel()
        {
           // Id = UId.GetNewId(),
            ExportCount = selected?.Count() ?? 0,
            ExportStatus = EnumExportTaskStatus.UnStart,
            Progress = 0,
            PatientRecordIds = selected?.ToList(),
        };
        //新增
        var result = await _stateService
            .PostAsJsonAsync<DataModel<HxExportTaskModel>>("/api/v2.0/HRV_HX/HxExportTask/Add",
                export);
        if (result.Success)
        {
            PopupService.ShowProgressCircular();
            // StartTaskId= export.Id;
            await Task.Run(async () =>
            {
                ShowTaskDetail();
                await PopupService.EnqueueSnackbarAsync("添加导出任务成功", AlertTypes.Success, closeable: true, timeout: 500);
            });
            PopupService.HideProgressCircular();
        }
        else
        {
            await PopupService.EnqueueSnackbarAsync($"添加导出任务失败,{result.Message}", AlertTypes.Error);
        }
    }

    private void PrintReport(string id)
    {
        _stateService.NavigationManager.NavigateTo($"/client/report/report-detail/{id}");
    }

}