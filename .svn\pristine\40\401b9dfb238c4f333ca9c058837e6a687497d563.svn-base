﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<ServiceWorkerAssetsManifest>service-worker-assets.js</ServiceWorkerAssetsManifest>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="6.0.36" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="6.0.36" PrivateAssets="all" />
		<PackageReference Include="ReactiveUI.Blazor" Version="19.6.12" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\HX.HRV.Shared\HX.HRV.Shared.csproj" />
	</ItemGroup>
	<ItemGroup>
		<ServiceWorker Include="wwwroot\service-worker.js" PublishedContent="wwwroot\service-worker.published.js" />
	</ItemGroup>
	<ItemGroup>
	  <Content Update="wwwroot\appsettings.Development.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
	<ItemGroup>
		<Content Remove="wwwroot\index.html" />
	</ItemGroup>


</Project>

