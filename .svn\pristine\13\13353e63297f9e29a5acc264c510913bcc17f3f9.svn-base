﻿using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using HX.Encrypt.Models;

namespace HX.HRV.Keygen
{
    public static class RsaEncryption
    {

        public const string PemPublicKeyContents = @"-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAwIujv+5DDNpWnXGj4pHUlH2Xd0ed9mG8AalFvMqZVYffIJEx22Lu
SXzXVgwroj4utFk2Kxxbi6mJ+pZMZQKCpFOAm12yaqrY/6iWj4tFAq8nvnE3m574
gnFHGORDpbm3xTYEoV2ZVm7/luZJ291WmaeKaJ5QRZlUJ4tkevXFK2mODCJ4IwU9
u/NjohYgZ0BSFdBF7b7dljo0y0ddcfn6iZv3rk0/0ITLlmfOilhN46KTM2ElA+lR
T43ke7SSdN7kq1Z3qPZXK90LrfKEFB43wvtM7sSTFJLccwEPjEj+ZA9Q+YKEpfu3
N9cyzdpyEnw53hhS08wLRGce2OeDpwJW9QIDAQAB
-----END RSA PUBLIC KEY-----
";
        
        public const string PemPrivateKeyContents = @"*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        
        
        
        private static RSA _publicKeyInstance;
        private static RSA _privateKeyInstance;


        static RsaEncryption()
        {
            LoadPrivateKey();
            LoadPublicKey();
        }
        
        public static void LoadPrivateKey()
        {
            _privateKeyInstance= RSA.Create();
            try
            {
                _privateKeyInstance.ImportFromPem(PemPrivateKeyContents);
            }
            catch (Exception ex)
            {
                throw new CryptographicException($"Failed to import private key from PEM file.  Error: {ex.Message}",
                    ex);
            }
        }

        public static string SignData(string data)
        {
            if (string.IsNullOrEmpty(data))
                throw new ArgumentNullException(nameof(data));
            if (_privateKeyInstance == null)
                throw new ArgumentNullException(nameof(_privateKeyInstance));
            byte[] dataBytes = Encoding.UTF8.GetBytes(data);
            byte[] signatureBytes =
                _privateKeyInstance.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            return Convert.ToBase64String(signatureBytes);
        }

        public static string GenerateLicenseKey(LicenseData licenseDetails)
        {
            string jsonData = JsonSerializer.Serialize(licenseDetails);
            string signature = SignData(jsonData);
            string base64JsonData = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonData));
            return $"{base64JsonData}.{signature}.{Convert.ToBase64String(Encoding.UTF8.GetBytes(PemPublicKeyContents))}";
        }

        private static void LoadPublicKey( )
        {
            _publicKeyInstance = RSA.Create();
            try
            {
                _publicKeyInstance.ImportFromPem(PemPublicKeyContents);
            }
            catch (Exception ex)
            {
                throw new CryptographicException($"从 PEM 导入公钥失败。错误: {ex.Message}", ex);
            }
        }

        private static bool VerifyData(string originalData, string signedData, RSA publicKeyInstance)
        {
            if (publicKeyInstance == null)
            {
                LoadPublicKey();
            };
            byte[] dataBytes = Encoding.UTF8.GetBytes(originalData);
            byte[] signatureBytes = Convert.FromBase64String(signedData);
            return publicKeyInstance.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256,
                RSASignaturePadding.Pkcs1);
        }

        /// <summary>
        /// 验证并解析许可证密钥
        /// </summary>
        /// <param name="licenseContents"></param>
        /// <returns></returns>
        public static LicenseData VerifyAndParseLicenseKey(string licenseContents)
        {
            if (string.IsNullOrEmpty(licenseContents))
            {
                return null;
            }
            string[] parts = licenseContents.Split('.');
            if (parts.Length != 2)
            {
                return null;
            }
            string base64JsonData = parts[0];
            string signature = parts[1];
            string jsonData;
            try
            {
                byte[] jsonBytes = Convert.FromBase64String(base64JsonData);
                jsonData = Encoding.UTF8.GetString(jsonBytes);
            }
            catch (FormatException ex)
            {
                return null;
            }
            bool isValid = VerifyData(jsonData, signature, _publicKeyInstance);
            if (isValid)
            {
                try
                {
                    LicenseData licenseDetails = JsonSerializer.Deserialize<LicenseData>(jsonData);
                    return licenseDetails;
                }
                catch (JsonException ex)
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }
    }
}