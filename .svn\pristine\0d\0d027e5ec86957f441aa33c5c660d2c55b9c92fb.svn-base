{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
        "Default": "Information",
        "Microsoft.AspNetCore": "Warning",
        "Microsoft.EntityFrameworkCore.Database.Command": "Warning",
        "Microsoft.AspNetCore.Routing": "Debug", // 或者 Trace
        "Microsoft.AspNetCore.Hosting": "Debug" // 或者 Trace
    }
  },
  "AlgorithmProxy": {
    "Host": "127.0.0.1",
    "Port": 5220
  },
    "ConnectionStrings": {
        "DefaultDbContext": "User ID=postgres;Password=******************;Host=host.docker.internal;Port=5432;Database=HX.HRV;Pooling=true;"
    }
}
  