var RealTimeEchartData = []

RealTimeEchart = {
    InitChart: function (data, id, take, smooth, isMultiple,color) {
        console.log(id);
        RealTimeEchartData[id] = {
            data: [],
            take: take,
            echart: {},
            isMultiple: isMultiple,
            color: color,
            option: {},
            smooth: smooth || false
        };
        this.InitEchartDom(id);
    },
    InitEchartDom: function (id) {
        let chartDom = document.getElementById(id);
        let ops = {};
        RealTimeEchartData[id].echart = echarts.init(chartDom, "", ops);
        let option = this.BuildOption(id);
        RealTimeEchartData[id].echart.setOption(option);
    },
    UpdateChartData: function (id, data) {
        
        if (data instanceof Array) {
            if (RealTimeEchartData[id].isMultiple) {
                if (RealTimeEchartData[id].data.length <= 0) {
                    RealTimeEchartData[id].data = [[], [], []]
                }
                for (let i = 0; i < RealTimeEchartData[id].data.length; i++) {
                    RealTimeEchartData[id].data[i].push(...data[i])
                }
            } else {
                RealTimeEchartData[id].data.push(...data);
            }

        } else {
            RealTimeEchartData[id].data.push(data);
        }
        
        if (RealTimeEchartData[id].data.length > RealTimeEchartData[id].take) {
            console.log("Echart data length", RealTimeEchartData[id].data.length, RealTimeEchartData[id].take,data.length)
            if (data instanceof Array){
                if (RealTimeEchartData[id].isMultiple) {
                    for (let i = 0; i < RealTimeEchartData[id].data.length; i++) {
                        RealTimeEchartData[id].data[i].splice(0,data[i].length);
                    }
                }else{
                    RealTimeEchartData[id].data.splice(0,data.length);
                }
            }else{
                RealTimeEchartData[id].data.shift();
            }
        }
        let option = this.BuildOption(id);
        RealTimeEchartData[id] && RealTimeEchartData[id].echart && RealTimeEchartData[id].echart.setOption(option,true);
        // RealTimeEchartData[id] && RealTimeEchartData[id].echart && RealTimeEchartData[id].echart.resize();
    },
    DisposeChart: function (id) {
        RealTimeEchartData[id] && RealTimeEchartData[id].echart && RealTimeEchartData[id].echart.dispose && RealTimeEchartData[id].echart.dispose();
    },
    BuildOption: function (id) {
        let chartDom = document.getElementById(id);
        let max = Math.max(...RealTimeEchartData[id].data);
        let min = Math.min(...RealTimeEchartData[id].data);
        if (id.indexOf("HR") > 0) {
            max += 20;
            min -= 20;
        } else if (id.indexOf("HRSPO2") > 0) {
            max = 100;
            min -= 20;
        } else {
            max = "dataMax";
            min = "dataMin";
        }
        let grid = {
            top: 20,
            bottom: 10,
            left: 100,
            right: 10
        };
        let series = [];
        let  xAxis = {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
                formatter: function (value, index) {
                    return index;
                },
                show: false,

            },
            axisLine: {
                onZero: false
            }
        };
        let yAxis={
            type: 'value',
            max: max,
            min: min,
        };
        if (RealTimeEchartData[id].isMultiple) {
            series = RealTimeEchartData[id].data.map((data, index) => ({
                type: 'line',
                showSymbol: false,
                smooth: RealTimeEchartData[id].smooth || false,
                data: data
            }));
            console.log(series)
        } else {
            series = [
                {
                    type: 'line',
                    showSymbol: false,
                    color: RealTimeEchartData[id].color,
                    smooth: RealTimeEchartData[id].smooth || false,
                    data: RealTimeEchartData[id].data
                }
            ];
        }
        return {
            tooltip: {
                trigger: 'axis'
            },
            xAxis:xAxis,
            yAxis:yAxis ,
            grid: grid,
            series: series
        };
    },

}

