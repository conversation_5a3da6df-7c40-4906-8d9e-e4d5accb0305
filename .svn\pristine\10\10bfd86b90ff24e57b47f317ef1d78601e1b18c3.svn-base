﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@implements IDisposable
@inherits   HRV.Shared.Pages.Client.Dialog.DeviceStatusDialog
<MRow Style="width: 100%" Justify="JustifyTypes.SpaceBetween">
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">电量:</MLabel>
		<MLabel>@DeviceStatusData.Battery</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">当前患者:</MLabel>
		<MLabel>@DeviceStatusData.RecordModel?.Patient?.Name</MLabel>
	</MCol>
	<MCol Cols="3">
		<MLabel Class="font-weight-bold">倒计时:</MLabel>
		<MLabel>@DeviceStatusData.CollectTime.ToString(@"mm\:ss")</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">连接方式:</MLabel>
		<MLabel>@DeviceStatusData.ConnectionType</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">状态:</MLabel>
		<MLabel>@DeviceStatusData.DeviceStatus</MLabel>
	</MCol>
	<MCol Cols="12">
		<MSwitch TValue="bool"
		         @bind-Value="isOrg"
		         Label="@($"PPG降噪: {isOrg}")">
		</MSwitch>
	</MCol>
</MRow>
@if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.检测中)
{
	<MRow Style="display: flex;justify-content: center;">

		<MCol Cols="12" Style="@(GetnoneStyle())">
			<div style="height: 200px; border-radius: 10px;background: #fff;position: relative"
			     class="d-flex align-center justify-space-start ">
				<div style="height: 100%;width: 200px; background-color:#354de5;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">PPG</div>
					@* <div class="text-center">@DeviceStatusData.OrgPPGData?.LastOrDefault()</div> *@
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "ORGPPG")'>
				</div>
				@* <HRVChart DataExpression="e=>e.OrgPPGData" TItem="uint" Device="DeviceStatusData" Height="200"  DataKey="ORGPPG"  DataTake="2500" > *@
				@* </HRVChart> *@
			</div>
		</MCol>
		<MCol Cols="12" Style="@(GetVisibilityStyle())">
			<div style="height: 200px; border-radius: 10px;background: #fff; position: relative"
			     class="d-flex align-center justify-space-start ">
				<div style="height: 100%; width: 200px; background-color:#9ac5fa;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">PPG</div>
					@* <div class="text-center">@DeviceStatusData.PPGData?.LastOrDefault()</div> *@
				</div>
				@* <HRVChart DataExpression="e=>e.OrgPPGData" TItem="uint" Device="DeviceStatusData" Height="200" DataTake="2500"  DataKey="PPG"> *@
				@* </HRVChart> *@
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "PPG")'>
				</div>
			</div>
		</MCol>

		<MCol Cols="12">
			<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
			     class="d-flex align-center justify-space-between">
				<div style="height: 100%;width: 200px; background-color:#aac57f;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">HR(bpm)</div>
					<div class="text-center">@DeviceStatusData.BMPDatas?.LastOrDefault()</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "HR")'>
				</div>
			</div>
		</MCol>
		<MCol Cols="12">
			<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
			     class="d-flex align-center justify-space-between">
				<div style="height: 100%;width: 200px; background-color:#3896bb;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">SpO2(%)</div>
					<div class="text-center">@DeviceStatusData.SPO2Data?.LastOrDefault()</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "SPO2")'>
				</div>
			</div>
		</MCol> 
	<MCol Cols="12">
			<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
			     class="d-flex align-center justify-space-between">
				<div style="height: 100%;width: 200px; background-color:#9988d5;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">GSR(μS)</div>
					<div class="text-center">@DeviceStatusData.EDAs?.LastOrDefault()</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "GSR")'>
				</div>
			</div>
		</MCol>
		<MCol Cols="12">
			<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
			     class="d-flex align-center justify-space-between">
				<div style="height: 100%;width: 200px; background-color:#e3ad90;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">SCR</div>
					<div class="text-center">@DeviceStatusData.SCRDatas?.LastOrDefault()</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "SCR")'>
				</div>
			</div>
		</MCol>
		<MCol Cols="12">
			<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
			     class="d-flex align-center justify-space-between">
				<div style="height: 100%;width: 200px; background-color:#73c5b7;border-radius:  10px 0 0 10px "
				     class="d-flex flex-column align-center justify-center ">
					<div class="text-center">SCL</div>
					<div class="text-center">@DeviceStatusData.SCLDatas?.LastOrDefault()</div>
				</div>
				<div style="width: 100%;height: 200px" id='@(DeviceStatusData?.RecordModel?.Id + "SCL")'>
				</div>
			</div>
		</MCol>
	</MRow>
}

@code {
}