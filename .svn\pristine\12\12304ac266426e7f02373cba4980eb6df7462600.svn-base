﻿@page "/Client/SCI/DataAnalysis/list"
@using HX.HRV.SCI.Shared.Models
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Pages.Client.Dialog
@using HX.HRV.Shared.Services
@using UFU.CoreFX.Shared.Models
@using UFU.IoT.Shared.Utils
@using Masa.Blazor.Presets
@using HX.HRV.SCI.Shared.Pages.Client.PatientRecord
@using Masa.Blazor
@using Microsoft.JSInterop
@using UFU.CoreFX.Models
@using UFU.CoreFX.Permission
@using UFU.CoreFX.Shared.Services
@attribute [Permission(MenuName = "数据分析", Name = "多参数生理及行为研究平台/数据分析", Icon = "shuju", MenuOrder = "8", IsMenu = true)]
<style>
	.customer-input .m-input__prepend-outer .m-label {
		width: 120px;
	}
</style>

<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<div>
	<MForm Class="ma-2" Style="height: 15%;width: 98%;">
		<MRow Class="search-row"  >
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MTextField Clearable Class="customer-input" @bind-Value="RecordCodeOrId"
				            Solo
				            Dense>
					<PrependContent>
						<MLabel Class="mr-2">检测记录:</MLabel>
					</PrependContent>
				</MTextField>
			</MCol>
		</MRow>
		<MRow Class="ma-2 d-flex align-start justify-end" >
			<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;"
			         Class="customer-button blue ml-4" OnClick="AddDataAnalysis">
				新建分析
			</MButton>
			<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;"
			         Class="customer-button blue ml-4" OnClick="InitDataList">
				搜索
			</MButton>
		</MRow>
	</MForm>
	<MDataTable Style="height: 85%" OnOptionsUpdate="@HandleOnOptionsUpdate"
	            TItem="DataModel<DataAnalysisRecordModel>"
	            Headers="_headers"
	            SingleSelect="false"
	            ShowSelect
	            ItemKey="r => r.Id"
	            Items="PatientRecordModels"
	            Stripe
	            @bind-Selected="_selected"
	            DisableSort="true"
	            DisablePagination="true"
	            ItemsPerPage="_options.ItemsPerPage"
	            Loading="_loading"
	            FooterProps="@(props =>
	                         {
		                         props.ShowFirstLastPage = false;
		                         props.ShowCurrentPage = false;
		                         props.PageText = @"共{2}条数据";
		                         props.DisableItemsPerPage = true;
	                         })"
	            ServerItemsLength="@_total">
		<HeaderColContent Context="header">
			<MLabel Style="font-weight: 500;
font-size: 1.05rem;
color: #28333E;">@header.Text</MLabel>
		</HeaderColContent>
		<ItemColContent Context="item">
			@if (item.Header.Value == "actions")
			{
				<div class="d-flex align-center justify-center">
					<MButton Style="font-size: 1.25rem;padding: 0"
					         OnClick="() => { DetailDialogData = item.Item;	_isShowDetailDialog = true; }" Color="blue"
					         Class="text-decoration-underline" Plain>
						详情
					</MButton>
					<MButton Style="font-size: 1.25rem;padding: 0" OnClick="() => PrintReport(item.Item.Id)"
					         Color="blue" Class="text-decoration-underline" Plain>
						查看报告
					</MButton>
					<MButton Style="font-size: 1.25rem;padding: 0"
					         OnClick="async () => await DeleteConfirmAsync(item.Item.Id)" Color="red"
					         Class="text-decoration-underline" Plain>
						删除
					</MButton>
				</div>
			}
			else
			{
				<div style="display: flex;align-items: center;justify-content: center;min-width: 2rem;">
					<MLabel Style="font-size: 1rem;"> @item.Value</MLabel>
				</div>
			}
		</ItemColContent>
	</MDataTable>
	<PModal Width="700"
	        @bind-Value="_isShowDetailDialog">
		<TitleContent>
			<div style="flex-grow: 1;text-align: right">@DetailDialogData?.Data.Name</div>
		</TitleContent>
		<ChildContent>
			<MRow>
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">检测开始时间:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.CollectStartTime</MLabel>
				</MCol>
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">检测结束时间:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.CollectEndTime</MLabel>
				</MCol>
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">被试编号:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.Patient?.OutpatientNumberString</MLabel>
				</MCol>
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">身份证号:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.Patient?.CardId</MLabel>
				</MCol>
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">年龄:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.Patient?.Age 岁</MLabel>
				</MCol>
				@*体重*@
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">体重:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.Patient?.Weight KG</MLabel>
				</MCol>

				<MCol Cols="6">
					<MLabel Class="font-weight-bold">性别:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord?.Patient?.Sex.ToString()</MLabel>
				</MCol>
				@*终端*@
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">终端:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.DeviceId</MLabel>
				</MCol>
				@*报告编号*@
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">报告编号:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.RecordCode</MLabel>
				</MCol>
				<MCol Cols="6">
					<MLabel Class="font-weight-bold">被试来源:</MLabel>
					<MLabel>@DetailDialogData?.Data?.PatientRecord.Patient?.Source</MLabel>
				</MCol>
			</MRow>
		</ChildContent>
	</PModal>

</div>

@if (_isShowAddDialog)
{
	
	<PModal
		@bind-Value="_isShowAddDialog"
		
		Persistent
		Title="请选择数据"
		Width="960">
		<RecordListComponents RecordCode="@RecordCodeOrId" OnCancelClick="() => _isShowAddDialog = false" OnSaveClick="async (id) => await SaveAsync(id)"> 
		</RecordListComponents>
	</PModal>
}

@code {
	[Inject] private IJSRuntime js { get; set; }
	private bool _isShowTaskDialog;
	private bool _isShowDetailDialog;
	private int _total;
	private IEnumerable<string> _selected = new List<string>();
	private bool _loading = false;
	private bool isShowAddPatient = false;
	private DataModel<DataAnalysisRecordModel> DetailDialogData { get; set; } = new();

	[Parameter]
	[SupplyParameterFromQuery(Name = "recordId")]
	public string recordId { get; set; }

	[CascadingParameter(Name = "PopupService")]
	IPopupService PopupService { get; set; }

	private Sex[] Sexes { get; } = new[]
	{
		Sex.男,
		Sex.女
	};

	private void PrintReport(string id)
	{
		_stateService.NavigationManager.NavigateTo($"/client/DataAnalysis/ReportDetail/{id}");
	}

	private DataOptions _options = new(1, 10);
	private bool _isShowDatePicker;
	private ExportRecordDialog exportRecordDialog;
	private List<DataModel<DataAnalysisRecordModel>> PatientRecordModels { get; set; } = new();

	private List<DataTableHeader<DataModel<DataAnalysisRecordModel>>> _headers = new()
	{
		new()
		{
			Text = "添加时间",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(DataModel<DataAnalysisRecordModel>.AddTime), CellRender = (e) => e.AddTime.ToString("yyyy-MM-dd HH:mm:ss")
		},

		new()
		{
			Text = "分析名称",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(DataModel<DataAnalysisRecordModel>.Data.Name), CellRender = (e) => e.Data?.Name
		},
		new()
		{
			Text = "姓名", Align = DataTableHeaderAlign.Center,
			CellRender = item => item?.Data?.Patient?.Name.ToString()
		},
		new()
		{
			Text = "被试编号",
			Align = DataTableHeaderAlign.Center,
			CellRender = (e) => e.Data?.Patient?.OutpatientNumberString
		},
		new()
		{
			Text = "报告编号", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.RecordCode),
			CellRender = (e) => e.Data?.PatientRecord?.RecordCode
		},
		new()
		{
			Text = "操作",
			Value = "actions",
			Sortable = false,
			Align = DataTableHeaderAlign.Center,
		}
	};

	private bool _isShowAddDialog;


	private async Task HandleOnOptionsUpdate(DataOptions options)
	{
		_options = options;
		await InitDataList();
	}


	[Inject] StateService _stateService { get; set; }
	private string StartTaskId { get; set; }

	/// <summary>
	/// 路由数据
	/// </summary>
	[CascadingParameter]
	public AuthRouteData AuthRouteData { get; set; }

	protected override async Task OnInitializedAsync()
	{
		RecordCodeOrId = recordId;
		await InitDataList();
		await base.OnInitializedAsync();
	}


	public string RecordCodeOrId { get; set; }

	/// <summary>
	/// 初始化列表数据
	/// </summary>
	/// <returns></returns>
	private async Task InitDataList()
	{
		_loading = true;
		var queryParam = new Dictionary<string, string>
		{
			{ "recordCodeOrId", RecordCodeOrId },
			{ "page", _options.Page.ToString() },
			{ "pageSize", _options.ItemsPerPage.ToString() }
		};
		var result = await _stateService
			.GetAsJsonAsync<List<DataModel<DataAnalysisRecordModel>>>(
				"/api/v2.0/SCI/DataAnalysisRecord/List", queryParam);
		PatientRecordModels = result?.Data?.ToList();
		_total = result.Page?.TotalCount ?? 0;
		_loading = false;
	}

	/// <summary>
	/// 删除
	/// </summary>
	private async Task DeleteConfirmAsync(string id)
	{
		var confirmed = await PopupService.ConfirmAsync(
			"警告",
			"删除后将无法恢复，确认删除吗?",
			AlertTypes.Warning);
		if (confirmed)
		{
			var res = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/SCI/DataAnalysisRecord/Delete?id={id}");
			if (res?.Success == true)
			{
				await PopupService.EnqueueSnackbarAsync("删除成功！", AlertTypes.Success);
				await InitDataList();
			}
			else
			{
				await PopupService.EnqueueSnackbarAsync("删除失败！" + res.Message, AlertTypes.Error);
			}
		}
	}


	private void ShowRecordDetail()
	{
		_isShowDetailDialog = true;
	}

	private void CloseRecordDetail()
	{
		_isShowDetailDialog = false;
	}


	private void AddDataAnalysis()
	{
		_isShowAddDialog = true;
	}

	private async Task SaveAsync(string id)
	{
		_stateService.NavigationManager.NavigateTo($"/Client/SCI/DataAnalysis/{id}");
	}

}