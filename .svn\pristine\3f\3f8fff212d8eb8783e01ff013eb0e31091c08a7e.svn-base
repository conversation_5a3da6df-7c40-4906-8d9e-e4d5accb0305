﻿
//using System;
//using System.Collections.Concurrent;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using UFU.CoreFX.Permission;
//using UFU.CoreFX.Utils;
//using UFU.IoT.Models;

//namespace UFU.IoT.Services
//{
//    /// <summary>
//    /// 物联网设备WebSocket
//    /// </summary>
//    [AllowAnonymous]
//    [Permission("物联网设备WebSocket_Ms", Url = "/iot/device/v1_ms")]
//    public class DeviceWebSocketConnectMs : IWebSocket, IConnect
//    {
//        /// <summary>
//        /// 连接方式
//        /// </summary>
//        public ConnectMode ConnectMode => ConnectMode.WebSocket;

//        /// <summary>
//        /// 收到文本消息
//        /// </summary>
//        /// <param name="data"></param>
//        public override void OnReciveText(string data)
//        {
//            IoTService.ReceiveDeviceMsgV1_Ms(this, data);
//        }

//        /// <summary>
//        /// 收到二进制数据
//        /// </summary>
//        /// <param name="data"></param>
//        public override void OnReciveBinary(byte[] data)
//        {
//            //OTA
//            if (data != null && data.Length >= 9 && data[0] == 1)
//            {
//                var cmd = data[1];
//                var error = data[2];
//                var msgId = BitConverter.ToUInt16(data, 3);
//                var offset = BitConverter.ToInt32(data, 5);

//                var json = JsonTool.Serialize(new { MsgId = msgId, CMD = cmd, OTA = new { Offset = offset }, Error = error });
//                IoTService.ReceiveDeviceMsgV1_Ms(this, json);
//            }
//        }

//        /// <summary>
//        /// 关闭连接
//        /// </summary>
//        public override void Close()
//        {
//            //触发离线事件
//            IoTService.DeviceConnectClosed(ConnectId);
//            //关闭连接
//            base.Close();
//        }

//        /// <summary>
//        /// 发送文本消息
//        /// </summary>
//        /// <param name="data"></param>
//        public bool SendText(string data)
//        {
//            try
//            {
//                SendTextAsync(data).ConfigureAwait(false);
//                return true;
//            }
//            catch (Exception ex)
//            {
//                LogTool.Logger.Error(ex, $"WebSocket发送文本消息到设备异常");
//                Close();
//                return false;
//            }
//        }

//        /// <summary>
//        /// 发送二进制消息
//        /// </summary>
//        /// <param name="data"></param>
//        public bool SendBinary(byte[] data)
//        {
//            try
//            {
//                SendBinaryAsync(data).Wait();
//                return true;
//            }
//            catch (Exception ex)
//            {
//                LogTool.Logger.Error(ex, $"WebSocket发送二进制消息到设备异常");
//                Close();
//                return false;
//            }
//        }
//    }
//}
