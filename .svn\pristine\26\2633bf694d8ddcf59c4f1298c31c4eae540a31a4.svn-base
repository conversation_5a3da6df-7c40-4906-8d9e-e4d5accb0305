﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <_PublishTargetUrl>bin\Release\net6.0\publish\</_PublishTargetUrl>
    <History>False|2025-03-27T10:14:10.3506506Z||;False|2025-03-27T18:13:35.7200590+08:00||;False|2025-02-24T15:26:57.9259270+08:00||;False|2025-02-07T18:06:46.4221819+08:00||;False|2025-01-24T19:36:30.1967181+08:00||;False|2024-11-12T13:54:53.6386830+08:00||;False|2024-11-12T13:53:52.4686310+08:00||;False|2024-11-12T13:51:42.3239160+08:00||;True|2024-10-23T17:08:37.1852797+08:00||;True|2024-09-29T17:43:44.1651865+08:00||;</History>
    <LastFailureDetails />
  </PropertyGroup>
  <ItemGroup>
    <File Include="Services/AlgWebSocketClient.cs">
      <publishTime>05/22/2025 20:00:44</publishTime>
    </File>
    <File Include="Services/ByteAnalysisServices.cs">
      <publishTime>05/20/2025 18:28:48</publishTime>
    </File>
    <File Include="Services/DataAnalysisWebSocket.cs">
      <publishTime>05/22/2025 19:57:32</publishTime>
    </File>
    <File Include="Services/DeviceStatusDataService.cs">
      <publishTime>05/20/2025 16:29:28</publishTime>
    </File>
    <File Include="Services/FileBufferManager.cs">
      <publishTime>05/21/2025 20:02:51</publishTime>
    </File>
    <File Include="Services/FileExportService.cs">
      <publishTime>05/06/2025 18:27:36</publishTime>
    </File>
    <File Include="Services/GenerateEmotionReportService.cs">
      <publishTime>05/21/2025 13:54:30</publishTime>
    </File>
    <File Include="Services/GenerateReportService.cs">
      <publishTime>05/20/2025 15:31:32</publishTime>
    </File>
    <File Include="Services/HXAdminWebSocket.cs">
      <publishTime>03/05/2025 16:42:59</publishTime>
    </File>
    <File Include="Services/RsaEncryptionService.cs">
      <publishTime>05/15/2025 15:47:55</publishTime>
    </File>
  </ItemGroup>
</Project>