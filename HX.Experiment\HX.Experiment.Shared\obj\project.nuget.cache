{"version": 2, "dgSpecHash": "8oUl6Jw4uBs=", "success": true, "projectFilePath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\antdesign.0.15.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bemit\\2.2.0\\bemit.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazor-dragdrop\\2.4.0\\blazor-dragdrop.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\deepcloner.core\\0.1.0\\deepcloner.core.0.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.4.0\\fluentvalidation.11.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.dependencyinjectionextensions\\11.4.0\\fluentvalidation.dependencyinjectionextensions.11.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\majorsoft.blazor.extensions.browserstorage\\1.5.0\\majorsoft.blazor.extensions.browserstorage.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\masa.blazor.1.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor.mobilecomponents\\1.10.2\\masa.blazor.mobilecomponents.1.10.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\9.0.8\\microsoft.aspnetcore.authorization.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\9.0.8\\microsoft.aspnetcore.components.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\9.0.8\\microsoft.aspnetcore.components.analyzers.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.authorization\\9.0.7\\microsoft.aspnetcore.components.authorization.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.dataannotations.validation\\3.2.0-rc1.20223.4\\microsoft.aspnetcore.components.dataannotations.validation.3.2.0-rc1.20223.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\9.0.8\\microsoft.aspnetcore.components.forms.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\9.0.8\\microsoft.aspnetcore.components.web.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly\\9.0.7\\microsoft.aspnetcore.components.webassembly.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\9.0.8\\microsoft.aspnetcore.metadata.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.7\\microsoft.extensions.configuration.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.7\\microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.7\\microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.7\\microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.7\\microsoft.extensions.configuration.json.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.8\\microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.8\\microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.7\\microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.7\\microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.7\\microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.7\\microsoft.extensions.logging.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.8\\microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.8\\microsoft.extensions.options.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.8\\microsoft.extensions.primitives.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\9.0.8\\microsoft.jsinterop.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop.webassembly\\9.0.7\\microsoft.jsinterop.webassembly.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oneof\\3.0.223\\oneof.3.0.223.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oneof.sourcegenerator\\3.0.223\\oneof.sourcegenerator.3.0.223.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.7\\system.text.json.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\util.reflection\\1.0.3\\util.reflection.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ufu.corefx.shared\\5.0.6\\ufu.corefx.shared.5.0.6.nupkg.sha512"], "logs": []}