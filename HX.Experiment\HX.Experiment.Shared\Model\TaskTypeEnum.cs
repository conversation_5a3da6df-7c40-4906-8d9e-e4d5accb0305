﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HX.Experiment.Shared.Model
{
    public enum TaskTypeEnum
    {
        [Display(Name = "眼动实验")] Eye = 1,
        [Display(Name = "脑电实验")] Brain = 2,
        [Display(Name = "面部表情")] Face = 3,
        [Display(Name = "行为实验")] Behavior = 4,
        [Display(Name = "多参数神经生理分析")] HRV = 5
    }

    public static class TaskTypeEnumExtension
    {

        public static string ToColor(TaskTypeEnum taskType) => taskType switch
        {
            TaskTypeEnum.Eye => "indigo",
            TaskTypeEnum.Brain => "secondary",
            TaskTypeEnum.Face => "success",
            TaskTypeEnum.Behavior => "warning",
            TaskTypeEnum.HRV => "error",
            _ => throw new ArgumentOutOfRangeException(nameof(taskType), $"Not expected  value: {taskType}"),
        };

    }

}