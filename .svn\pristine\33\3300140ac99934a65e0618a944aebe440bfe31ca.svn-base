﻿@page "/client/patient-record/list"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Pages.Client.Dialog
@using HX.HRV.Shared.Services
@using UFU.CoreFX.Shared.Models
@using UFU.IoT.Shared.Utils
@layout HRVDefaultLayout
@attribute [Permission(MenuName = "数据管理", Name = "心率变异性/数据管理", Icon = "shuju", MenuOrder = "4", IsMenu = true)]
<style>
	.customer-input .m-input__prepend-outer .m-label {
		width: 120px;
	}

	.m-data-table__wrapper {
		height: 90%;
		overflow: auto;
	}
</style>

<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<div style="height: 100%">
	<MForm Class="pa-2" Style="height: 15%;width: 98%;">
		<MRow Class="search-row">
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MTextField Clearable Class="customer-input" @bind-Value="_queryPatientModel.CardId"
				            Solo
				            Dense>
					<PrependContent>
						<MLabel Class="mr-2">身份证号:</MLabel>
					</PrependContent>
				</MTextField>
			</MCol>
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MTextField Clearable Solo
				            Dense Class="customer-input" @bind-Value="_queryPatientModel.Name">
					<PrependContent>
						<MLabel Class="mr-2">患者姓名:</MLabel>
					</PrependContent>
				</MTextField>
			</MCol>
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MTextField Clearable Solo
				            Dense Class="customer-input" @bind-Value="_queryPatientModel.OutpatientNumberString">
					<PrependContent>
						<MLabel Class="mr-2">门诊/住院号:</MLabel>
					</PrependContent>
				</MTextField>
			</MCol>
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MMenu @bind-Value="_isShowDatePicker"
				       CloseOnContentClick="false"
				       Transition="scale-transition"
				       OffsetY
				       Context="DatePickerContext"
				       MinWidth="@("auto")">
					<ActivatorContent>
						<MTextField Value="FormatDate()"
						            Solo
						            Dense Class="customer-input"
						            Readonly
						            Clearable
						            OnClearClick="() => { _dates = new List<DateOnly>(); }"
						            @attributes="DatePickerContext.Attrs">
							<PrependContent>
								<MLabel Class="mr-2">检测时间:</MLabel>
							</PrependContent>
						</MTextField>

					</ActivatorContent>
					<ChildContent>
						<MDatePicker @bind-Value="_dates"
						             NoTitle
						             Range
						             Scrollable>
							<MSpacer></MSpacer>
							<MButton Text
							         Color="primary"
							         OnClick="() => { _isShowDatePicker = false; _dates = new List<DateOnly>(); }">
								取消
							</MButton>
							<MButton Text
							         Color="primary"
							         OnClick="() => { _isShowDatePicker = false; }">
								确认
							</MButton>
						</MDatePicker>
					</ChildContent>
				</MMenu>
			</MCol>

		</MRow>
		<MRow Class="ma-2 d-flex align-start justify-end">
			<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;"
			         Class="customer-button blue ml-4" OnClick="InitDataList">
				搜索
			</MButton>
			@if (_hasDeletePatientRecordPermission)
			{
				<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;" Class="amber ml-4" Rounded
				         OnClick="() => AddExportRecordAsync()">
					批量导出
				</MButton>
				<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;" Class="amber ml-4" Rounded
				         OnClick="ShowTaskDetail">
					导出任务
				</MButton>
			}

		</MRow>
	</MForm>
	<MDataTable Style="height: 84%;" OnOptionsUpdate="@HandleOnOptionsUpdate"
	            TItem="PatientRecordModel"
	            Headers="_headers"
	            SingleSelect="false"
	            ShowSelect
	            ItemKey="r => r.Id"
	            Items="PatientRecordModels"
	            Stripe

	            @bind-Selected="_selected"
	            DisableSort="true"
	            ItemsPerPage="_options.ItemsPerPage"
	            Loading="_loading"
	            FooterProps="@(props =>
	                         {
		                         props.ShowFirstLastPage = false;
		                         props.ShowCurrentPage = false;
		                         props.PageText = @"共{2}条数据";
	                         })"
	            ServerItemsLength="@_total">
		<HeaderColContent Context="header">
			<MLabel Style="font-weight: 500;
font-size: 1.05rem;
color: #28333E;">@header.Text</MLabel>
		</HeaderColContent>
		<ItemColContent Context="item">
			@if (item.Header.Value == "actions")
			{
				@RenderActions(item.Item)
			}
			else
			{
				<div style="display: flex;align-items: center;justify-content: center;min-width: 2rem;">
					<MLabel Style="font-size: 1rem;"> @item.Value</MLabel>
				</div>
			}
		</ItemColContent>
	</MDataTable>
</div>


@*导出任务列表*@
@if (_isShowTaskDialog)
{
	<PModal Width="942"
	        Persistent="true"
	        @bind-Value="_isShowTaskDialog">
		<TitleContent>
			<div style="flex-grow: 1;text-align: right">导出任务</div>
		</TitleContent>
		<ChildContent>
			<ExportRecordDialog @ref="exportRecordDialog" StartTaskId="@StartTaskId" CloseDialog="CloseTaskDetail">
			</ExportRecordDialog>
		</ChildContent>
	</PModal>
}


@*检测记录详情*@
<PModal Width="700"
        @bind-Value="_isShowDetailDialog">
	<TitleContent>
		<div style="flex-grow: 1;text-align: right">@RecordModelData.Patient?.Name</div>
	</TitleContent>
	<ChildContent>
		<MRow>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">检测开始时间:</MLabel>
				<MLabel>@RecordModelData.CollectStartTime</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">检测结束时间:</MLabel>
				<MLabel>@RecordModelData.CollectEndTime</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">病历/门诊号:</MLabel>
				<MLabel>@RecordModelData.Patient?.OutpatientNumberString</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">身份证号:</MLabel>
				<MLabel>@RecordModelData.Patient?.CardId</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">年龄:</MLabel>
				<MLabel>@RecordModelData.Patient?.Age 岁</MLabel>
			</MCol>
			@*体重*@
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">体重:</MLabel>
				<MLabel>@RecordModelData.Patient?.Weight KG</MLabel>
			</MCol>

			<MCol Cols="6">
				<MLabel Class="font-weight-bold">性别:</MLabel>
				<MLabel>@RecordModelData.Patient?.Sex.ToString()</MLabel>
			</MCol>
			@*终端*@
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">终端:</MLabel>
				<MLabel>@RecordModelData.DeviceId</MLabel>
			</MCol>
			@*报告编号*@
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">报告编号:</MLabel>
				<MLabel>@RecordModelData.RecordCode</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">患者来源:</MLabel>
				<MLabel>@RecordModelData.Patient?.Source</MLabel>
			</MCol>
		</MRow>
	</ChildContent>
</PModal>

@code {
	[Inject] private IJSRuntime js { get; set; }
	private bool _isShowTaskDialog;
	private bool _isShowDetailDialog;
	private int _total;


	private List<DateOnly> _dates = new()
	{
	};


	private IEnumerable<string> _selected = new List<string>();

	public DateOnly  StartTime { get; set; }
	public DateOnly  EndTime { get; set; }

	private string FormatDate()
	{
		if (_dates.Count >= 2)
		{
			return _dates[0].ToString("yyyy-MM-dd") + "至" + _dates[1].ToString("yyyy-MM-dd");
		}
	  
		return string.Empty;
	}


	private bool _loading = false;
	private bool isShowAddPatient = false;
	private PatientRecordModel RecordModelData { get; set; } = new();

	[CascadingParameter(Name = "PopupService")]
	IPopupService PopupService { get; set; }

	private Sex[] Sexes { get; } = new[]
	{
		Sex.男,
		Sex.女
	};

	private DataOptions _options = new(1, 10);
	private bool _isShowDatePicker;
	private ExportRecordDialog exportRecordDialog;
	private List<PatientRecordModel> PatientRecordModels { get; set; } = new();

	private List<DataTableHeader<PatientRecordModel>> _headers => new()
	{
		new DataTableHeader<PatientRecordModel>
		{
			Text = "检测时间",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.CollectStartTime), CellRender = (e) => e.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss")
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "结束时间",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.CollectEndTime), CellRender = (e) => e.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss")
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "姓名", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.Name),
			CellRender = (e) => e.Patient?.Name
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "门诊/住院号",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.OutpatientNumberString),
			CellRender = (e) => e.Patient?.OutpatientNumberString
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "身份证号", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.CardId),
			CellRender = (e) => e.Patient?.CardId
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "年龄",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.Age),
			CellRender = (e) => e.Patient?.Age?.ToString() ?? ""
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "性别", Align = DataTableHeaderAlign.Center,
			CellRender = (e) => e.Patient?.Sex?.ToString()
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "来源", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.Source),
			CellRender = (e) => e.Patient?.Source
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "报告编号", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.RecordCode),
			CellRender = (e) => e.RecordCode
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "报告状态", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.BuildReportStatusName),
			CellRender = (e) => e.BuildReportStatus.GetDisplayName()
		},
		new DataTableHeader<PatientRecordModel>
		{
			Text = "操作",
			Value = "actions",
			Sortable = false,
			Align = DataTableHeaderAlign.Center
		}
	};


	protected RenderFragment RenderActions(PatientRecordModel record)
	{
		return @<div class="d-flex align-center justify-center">
			@if (record.BuildReportStatus == BuildReportStatus.Completed)
			{
				<MButton Style="font-size: 1.25rem;padding: 0" OnClick="() => PrintReport(record.Id)"
				         Color="blue" Class="text-decoration-underline" Plain>
					查看报告
				</MButton>
			}
			<MButton Style="font-size: 1.25rem;padding: 0"
			         OnClick="() => { RecordModelData = record; ShowRecordDetail(); }" Color="blue"
			         Class="text-decoration-underline" Plain>
				详情
			</MButton>
			@if (_hasDeletePatientRecordPermission)
			{
				<MButton Style="font-size: 1.25rem;padding: 0"
				         OnClick="async () => { await AddExportRecordAsync(record.Id); }" Color="blue"
				         Class="text-decoration-underline" Plain>
					导出
				</MButton>
				<MButton Style="font-size: 1.25rem;padding: 0"
				         OnClick="async () => await DeleteConfirmAsync(record.Id)" Color="red"
				         Class="text-decoration-underline" Plain>
					删除
				</MButton>
			}
		</div>;
	}

	private async Task HandleOnOptionsUpdate(DataOptions options)
	{
		_options = options;
		await InitDataList();
	}

	private PatientModel _queryPatientModel { get; set; } = new();
	private PatientRecordModel _queryPatientRecordModel { get; set; } = new();
	[Inject] InternalHttpClientService _internalHttpClientService { get; set; }
	[Inject] StateService _stateService { get; set; }
	private string StartTaskId { get; set; }

	/// <summary>
	/// 路由数据
	/// </summary>
	[CascadingParameter]
	public AuthRouteData AuthRouteData { get; set; }

	private bool _hasDeletePatientRecordPermission { get; set; }
	private bool _hasExportPermission { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await InitDataList();
		_hasDeletePatientRecordPermission = AuthRouteData.CurrentModule
			?.Functions.Any(m => m.Name == "心率变异性数据管理接口/删除") ?? false;
		await base.OnInitializedAsync();
	}

	/// <summary>
	/// 初始化列表数据
	/// </summary>
	/// <returns></returns>
	private async Task InitDataList()
	{
		_loading = true;
		var queryParam = new Dictionary<string, string>
		{
			{ nameof(PatientModel.Age), _queryPatientModel.ToString() },
			{ nameof(PatientModel.Name), _queryPatientModel.Name },
			{ nameof(PatientModel.CardId), _queryPatientModel.CardId },
			{ nameof(PatientModel.Source), _queryPatientModel.Source },
			{ nameof(PatientModel.OutpatientNumberString), _queryPatientModel.OutpatientNumberString },
			{ nameof(PatientModel.Sex), _queryPatientModel.Sex.ToString() },
			{nameof(RecordModelData.CollectStartTime), _dates.Count > 0 ? _dates[0].ToString("yyyy-MM-dd") : ""},
			{nameof(RecordModelData.CollectEndTime), _dates.Count > 0 ? _dates[1].ToString("yyyy-MM-dd") : ""},
			{ "page", _options.Page.ToString() },
			{ "pageSize", _options.ItemsPerPage.ToString() }
		};
		var result = await _internalHttpClientService.GetPatientRecordModelListByParams(queryParam);
		PatientRecordModels = result?.Data?.Select(m => m.Data)?.ToList();
		_total = result.Page?.TotalCount ?? 0;
		_loading = false;
	}

	/// <summary>
	/// 删除
	/// </summary>
	private async Task DeleteConfirmAsync(string id)
	{
		var confirmed = await PopupService.ConfirmAsync(
			"警告",
			"将永久删除，无法找回，确认删除?",
			AlertTypes.Warning);
		if (confirmed)
		{
			var res = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/HRV_HX/PatientRecord/Delete?id={id}");
			var deleteResult = res.Success && res.Data;
			await PopupService.EnqueueSnackbarAsync(deleteResult ? "删除成功！" : "删除失败！", AlertTypes.Success);
			await InitDataList();
		}
	}

	private void ShowTaskDetail()
	{
		StartTaskId = string.Empty;
		_isShowTaskDialog = true;
	}

	private void CloseTaskDetail()
	{
		_isShowTaskDialog = false;
	}

	private void ShowRecordDetail()
	{
		_isShowDetailDialog = true;
	}

	private void CloseRecordDetail()
	{
		_isShowDetailDialog = false;
	}

	/// <summary>
	///  添加导出记录
	/// </summary>
	/// <returns></returns>
	private async Task AddExportRecordAsync(string id = null)
	{
		var selected = !string.IsNullOrEmpty(id) ? new List<string> { id } : _selected.ToList();

		if (!selected.Any())
		{
			await PopupService.EnqueueSnackbarAsync("请选择需要导出的记录", AlertTypes.Warning);
			return;
		}

		var export = new HxExportTaskModel()
		{
			ExportCount = selected?.Count() ?? 0,
			ExportStatus = EnumExportTaskStatus.UnStart,
			Progress = 0,
			PatientRecordIds = selected?.ToList()
		};
		    //新增
        var result = await _stateService
            .PostAsJsonAsync<DataModel<HxExportTaskModel>>("/api/v2.0/HRV_HX/HxExportTask/Add",
				export);
		if (result.Success)
		{
			PopupService.ShowProgressCircular();
			ShowTaskDetail();
			await PopupService.EnqueueSnackbarAsync("添加导出任务成功", AlertTypes.Success, true, 500);
			PopupService.HideProgressCircular();
		}
		else
		{
			await PopupService.EnqueueSnackbarAsync($"添加导出任务失败,{result.Message}", AlertTypes.Error);
		}
	}

	private void PrintReport(string id)
	{
		_stateService.NavigationManager.NavigateTo($"/client/report/report-detail/{id}");
	}

}