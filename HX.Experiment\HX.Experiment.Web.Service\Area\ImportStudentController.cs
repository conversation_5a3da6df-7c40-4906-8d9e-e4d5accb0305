// using HX.Experiment.Shared.Model;
// using HX.Experiment.Shared.Services;
// using Microsoft.AspNetCore.Mvc;
// using UFU.CoreFX.Models;
// using UFU.CoreFX.Utils;

// namespace HX.Experiment.Web.Service.Area;

// /// <summary>
// /// 学生Excel导入控制器
// /// </summary>
// [ApiController]
// [Route("api/v2/hx_experiment/[controller]")]
// public class ImportStudentController : ControllerBase
// {
//     private readonly IImportStudentService _importStudentService;
//     private readonly IDataContext _context;

//     public ImportStudentController(IImportStudentService importStudentService, IDataContext context)
//     {
//         _importStudentService = importStudentService;
//         _context = context;
//     }

//     /// <summary>
//     /// 验证Excel文件格式并预览数据
//     /// </summary>
//     /// <param name="file">Excel文件</param>
//     /// <returns>验证结果</returns>
//     [HttpPost("validate")]
//     [Permission("学生管理接口/Excel导入验证", IsAPI = true)]
//     public async Task<Result<ValidationResult>> ValidateExcelFile(IFormFile file)
//     {
//         var result = new Result<ValidationResult>();

//         try
//         {
//             if (file == null || file.Length == 0)
//             {
//                 result.AddError("请选择要上传的Excel文件");
//                 return result;
//             }

//             if (file.Length > 10 * 1024 * 1024) // 10MB限制
//             {
//                 result.AddError("文件大小不能超过10MB");
//                 return result;
//             }

//             using var stream = file.OpenReadStream();
//             var validationResult = await _importStudentService.ValidateExcelFormatAsync(stream, file.FileName);

//             result.Data = validationResult;
//             result.Success = true;
//         }
//         catch (Exception ex)
//         {
//             result.AddError($"验证文件时发生错误: {ex.Message}");
//         }

//         return result;
//     }

//     /// <summary>
//     /// 导入Excel文件中的学生信息
//     /// </summary>
//     /// <param name="file">Excel文件</param>
//     /// <param name="classId">班级ID</param>
//     /// <returns>导入结果</returns>
//     [HttpPost("import")]
//     [Permission("学生管理接口/Excel导入", IsAPI = true)]
//     public async Task<Result<ImportResult>> ImportStudentsFromExcel(IFormFile file, [FromForm] string classId)
//     {
//         var result = new Result<ImportResult>();

//         try
//         {
//             if (file == null || file.Length == 0)
//             {
//                 result.AddError("请选择要上传的Excel文件");
//                 return result;
//             }

//             if (string.IsNullOrEmpty(classId))
//             {
//                 result.AddError("请选择班级");
//                 return result;
//             }

//             // 验证班级是否存在
//             var classExists = await _context.Query<ClassInfo>().AnyAsync(x => x.Id == classId);
//             if (!classExists)
//             {
//                 result.AddError("指定的班级不存在");
//                 return result;
//             }

//             if (file.Length > 10 * 1024 * 1024) // 10MB限制
//             {
//                 result.AddError("文件大小不能超过10MB");
//                 return result;
//             }

//             using var stream = file.OpenReadStream();
//             var importResult = await _importStudentService.ImportStudentsFromExcelAsync(stream, file.FileName, classId);

//             result.Data = importResult;
//             result.Success = true;
//         }
//         catch (Exception ex)
//         {
//             result.AddError($"导入文件时发生错误: {ex.Message}");
//         }

//         return result;
//     }

//     /// <summary>
//     /// 下载Excel模板文件
//     /// </summary>
//     /// <returns>Excel模板文件</returns>
//     [HttpGet("template")]
//     [Permission("学生管理接口/下载Excel模板", IsAPI = true)]
//     public async Task<IActionResult> DownloadTemplate()
//     {
//         try
//         {
//             var templateStream = await _importStudentService.GetExcelTemplateAsync();
//             var fileName = $"学生信息导入模板_{DateTime.Now:yyyyMMdd}.xlsx";
            
//             return File(templateStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
//         }
//         catch (Exception ex)
//         {
//             return BadRequest($"生成模板文件时发生错误: {ex.Message}");
//         }
//     }

//     /// <summary>
//     /// 获取班级列表（用于导入时选择班级）
//     /// </summary>
//     /// <returns>班级列表</returns>
//     [HttpGet("classes")]
//     [Permission("学生管理接口/获取班级列表", IsAPI = true)]
//     public async Task<Result<List<DataModel<ClassInfo>>>> GetClassList()
//     {
//         var result = new Result<List<DataModel<ClassInfo>>>();

//         try
//         {
//             var classes = await _context.Query<ClassInfo>()
//                 .OrderBy(x => x.Data.Year)
//                 .ThenBy(x => x.Data.Name)
//                 .ToListAsync();

//             result.Data = classes;
//             result.Success = true;
//         }
//         catch (Exception ex)
//         {
//             result.AddError($"获取班级列表时发生错误: {ex.Message}");
//         }

//         return result;
//     }

//     /// <summary>
//     /// 批量删除导入失败后需要清理的学生记录
//     /// </summary>
//     /// <param name="studentIds">学生ID列表</param>
//     /// <returns>删除结果</returns>
//     [HttpPost("cleanup")]
//     [Permission("学生管理接口/清理导入数据", IsAPI = true)]
//     public async Task<Result<bool>> CleanupImportedStudents([FromBody] List<string> studentIds)
//     {
//         var result = new Result<bool>();

//         try
//         {
//             if (studentIds == null || !studentIds.Any())
//             {
//                 result.AddError("请提供要清理的学生ID列表");
//                 return result;
//             }

//             var studentsToDelete = await _context.Query<StudentInfo>()
//                 .Where(x => studentIds.Contains(x.Id))
//                 .ToListAsync();

//             if (studentsToDelete.Any())
//             {
//                 _context.RemoveRange(studentsToDelete);
//                 await _context.SaveChangesAsync();
//             }

//             result.Data = true;
//             result.Success = true;
//         }
//         catch (Exception ex)
//         {
//             result.AddError($"清理数据时发生错误: {ex.Message}");
//         }

//         return result;
//     }
// }
