﻿@using HX.HRV.SCI.Shared.Models
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Pages.Client.Dialog
@using HX.HRV.Shared.Pages.Client.DeviceStatus
@using Masa.Blazor
@using Masa.Blazor.Presets
@using HX.HRV.Shared.Pages.Client.Components
@inherits HX.HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringComponents
<MRow>
    <MCol Cols="12">
        <div class="d-flex flex-wrap">
            @foreach (var data in DeviceDataList)
            {
                <div style="box-shadow: 0 2px 48px 3px rgba(101,162,231,0.24);
        height:calc((100vh - (6rem + 2rem)) / 4);
        padding: 0.25%; flex:  0 0 calc((100vw) / 4) ">
                    <MCard Style="height: 100%;" Class="rounded-lg">
                        <MCardTitle Class="pa-0 d-flex  align-center justify-space-between"
                                    Style="background-color: #bbd4f0; height:2rem;border-radius: 16px 16px 0px 0px !important;">
                            <DeviceStatusCardHead IsCharge="@(true)"
                                                  DeviceStatusData="data"
                                                  LinkType="1">
                             
                            </DeviceStatusCardHead>
                        </MCardTitle>
                        <MCardText Style="height:90%;" Class="pa-1">
                            @if (data.DeviceStatus == EnumDeviceStatus.检测中)
                            {
                                <MCardText Style="background-color: #e0ecfa;height:90%;" Class="pa-1">
                                    <RealTimeMonitoringCard DeviceStatusData="@data"/>
                                </MCardText>
                            }
                            else if (data.DeviceStatus == EnumDeviceStatus.空闲)
                            {
                                <div Style="height: 100%;" class="d-flex align-center justify-center">
                                    <img src="/images/icon/free_fill.png" style="width:38px; " alt=""/>
                                    <MLabel Style="font-size:2rem">空闲</MLabel>
                                </div>
                            }
                            else
                            {
                                <div style="height: 100%;" class="d-flex align-center justify-center">
                                    <img src="/images/icon/<EMAIL>" style="width:38px" alt=""/>
                                    <MLabel Style="font-size: 2rem"> 离线</MLabel>
                                </div>
                            }
                        </MCardText>
                    </MCard>
                </div>
            }
        </div>
    </MCol>
</MRow>

@if (isShowDeviceStatusDialog)
{
    <PModal
        HeaderClass=" grey text-center lighten-2"
        Class="deviceStatusDialog"
        ContentStyle="background-color: #c2d9f3;"
        MaxWidth="1200"
        @bind-Value="isShowDeviceStatusDialog">
        <TitleContent>
            <div style="width: 90%">
                设备编号:@_SelectedDeviceStatusData.Device?.Name
            </div>
        </TitleContent>
        <ChildContent>
            <HX.HRV.SCI.Shared.Pages.Client.Dialog.DeviceStatusDialog
                @bind-DeviceStatusData="_SelectedDeviceStatusData"
                CloseDialogEvent="OnCloserDialogCallback">
            </HX.HRV.SCI.Shared.Pages.Client.Dialog.DeviceStatusDialog>
        </ChildContent>
    </PModal>
}

@code
{
    private int _page = 1;
    private List<DataModel<EventTagModel>> _tags;
    private List<DeviceStatusViewModel> PageData => DeviceDataList.Skip((_page - 1) * 10).Take(10).ToList();
    [Inject] private StateService StateService { get; set; }

    [CascadingParameter(Name = "SystemConfig")]
    private HxSystemConfigModel SystemConfig { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        await InitListAsync();
        await base.OnInitializedAsync();
    }

    private async Task InitListAsync()
    {
        var url = $"/api/v2/SCI/EventTag/List";
        var result = await StateService.GetAsJsonAsync<List<DataModel<EventTagModel>>>(url);
        if (result != null)
        {
            _tags = result.Data?.Take(5)?.ToList();
        }
    }

    private async Task Callback(string id, string mark)
    {
        await StateService.PostAsJsonAsync<bool>("api/v2/SCI/EventTag/Mark", null, new Dictionary<string, string>()
        {
            { "deviceIds", id }
        });
    }
}
