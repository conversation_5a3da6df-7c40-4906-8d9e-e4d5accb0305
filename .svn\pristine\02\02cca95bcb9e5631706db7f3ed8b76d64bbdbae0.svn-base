﻿@page "/client/patient/list"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Pages.Client.Dialog
@using HX.HRV.Shared.Pages.Client.PatientRecord
@using UFU.CoreFX.Shared.Models
@layout HRVDefaultLayout
@attribute [Permission("心率变异性/患者管理", MenuName = "患者管理", Icon = "huanzhe", MenuOrder = "3", IsMenu = true)]
<div class="pa-2">
    <MForm Class="ma-8">
        <MRow Class="search-row">
            <MCol Cols="3" Class="d-flex align-center justify-center">
                <MTextField Clearable Class="customer-input" @bind-Value="_queryModel.CardId"
                            Solo
                            Dense>
                    <PrependContent>
                        <MLabel Class="mr-2">身份证号:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
            <MCol Cols="3" Class="d-flex align-center justify-center">
                <MTextField Clearable Solo
                            Dense Class="customer-input" @bind-Value="_queryModel.OutpatientNumberString">
                    <PrependContent>
                        <MLabel Class="mr-2">门诊/住院号:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
            <MCol Cols="3" Class="d-flex align-center justify-center">
                <MTextField Clearable Solo
                            Dense Class="customer-input" @bind-Value="_queryModel.Name">
                    <PrependContent>
                        <MLabel Class="mr-2">姓名:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
            <MCol Cols="1" Class="d-flex align-start justify-space-between">
                <MButton Class="customer-button" Style="    height: 3rem;
    max-width: 9rem;
    width: 8rem;
    font-size: x-large;" OnClick="InitDataList">搜索
                </MButton>
                <MButton Class="customer-button" Style="    height: 3rem;
    max-width: 9rem;
    width: 8rem;
    font-size: x-large;" OnClick="() => HandleAddOpenDialog(new PatientModel())">新增患者
                </MButton>
                @* <MButton Class="customer-button">批量导入</MButton> *@
            </MCol>

        </MRow>

    </MForm>
    <MDataTable
        Loading="_loading"
        OnOptionsUpdate="@HandleOnOptionsUpdate"
        TItem="PatientModel"
        Stripe
        Style="font-size: 1.25rem;"
        Headers="_headers"
        Items="PatientList"
        ServerItemsLength="_total"
        FooterProps="@(props =>
                     {
                         props.ShowFirstLastPage = false;
                         props.ShowCurrentPage = false;
                         props.PageText = @"共{2}条数据";
                     })"
        ItemsPerPage="@_options.ItemsPerPage">
        <HeaderColContent Context="header">
            <MLabel Style="font-weight: 400;font-size: 1.25rem;color: #28333E;">@header.Text</MLabel>
        </HeaderColContent>
        <ItemColContent Context="item">
            @if (item.Header.Value == "actions")
            {
                <div class="d-flex align-center justify-center">

                    @if (_hasPatientRecordPermission)
                    {
                        <MButton Style="font-size: 1.25rem;" Color="blue" Class="text-decoration-underline"
                                 OnClick="() => { _isShowRecordList = true; _clickedPatient = item.Item; }" Plain>
                            检测记录
                        </MButton>
                    }

                    <MButton Style="font-size: 1.25rem;"
                             OnClick="() => HandleEditOpenDialog(item.Item)"
                             Color="blue"
                             Class="text-decoration-underline"
                             Plain>
                        编辑信息
                    </MButton>
                    @* <MButton Style="font-size: 1.25rem;" Color="blue" Class="text-decoration-underline" Plain> *@
                    @*     历次检测 *@
                    @* </MButton> *@
                    <MButton Style="font-size: 1.25rem;" OnClick="async () => await DeleteConfirmAsync(item.Item.Id)"
                             Color="red" Class="text-decoration-underline" Plain>
                        删除
                    </MButton>
                </div>
            }
            else
            {
                <MLabel Style="font-size: 1.25rem;color: #28333E;"> @item.Value</MLabel>
            }
        </ItemColContent>
    </MDataTable>
    @if (isShowAddPatient)
    {
        <PModal Width="942" Persistent="false" @bind-Value="isShowAddPatient">
            <TitleContent>
                <div style="flex-grow: 1;text-align: right;font-size: 1.5rem">新增患者</div>
            </TitleContent>
            <ChildContent>
                <PatientDialog DialogTitle="@PatientDialogTitle" IsOnlyAdd="true"
                               CloseDialog="async () => { isShowAddPatient = false; await InitDataList(); }"
                               PatientModel="DialogData">
                </PatientDialog>
            </ChildContent>
        </PModal>
    }

    @if (_isShowRecordList)
    {
        <PModal @bind-Value="_isShowRecordList">
            <TitleContent>
                <div style="flex-grow: 1;text-align: right;font-size: 1.5rem">检测记录</div>
            </TitleContent>
            <ChildContent>
                <RecordList PatientId="@_clickedPatient?.Id">
                </RecordList>
            </ChildContent>
        </PModal>
    }
</div>

@code {
    private bool isShowAddPatient = false;
    private bool _isShowRecordList = false;
    private PatientModel _clickedPatient = new PatientModel();
    [Inject] IPopupService PopupService { get; set; }
    [Inject] StateService _stateService { get; set; }
    private PatientModel DialogData { get; set; } = new();
    private bool _loading = false;

    public string PatientDialogTitle { get; set; }

    private Sex[] Sexes { get; } = new[]
    {
        Sex.男,
        Sex.女
    };

    private DataOptions _options = new(1, 10);
    private List<PatientModel> PatientList { get; set; } = new();

    private List<DataTableHeader<PatientModel>> _headers => new()
    {
        new()
        {
            Text = "姓名",
            Align = DataTableHeaderAlign.Start,
            Sortable = false,
            Value = nameof(PatientModel.Name)
        },
        new() { Text = "门诊/住院号", Value = nameof(PatientModel.OutpatientNumberString) },
        new() { Text = "身份证号", Value = nameof(PatientModel.CardId) },
        new() { Text = "年龄", Value = nameof(PatientModel.Age) },
        new() { Text = "性别", Value = nameof(PatientModel.Sex) },
        new() { Text = "身高", Value = nameof(PatientModel.Height) },
        new() { Text = "体重", Value = nameof(PatientModel.Weight) },
        new() { Text = "患者来源", Value = nameof(PatientModel.Source) },
        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Width = "100px",
            Align = DataTableHeaderAlign.Center,
        }
    };

    private void HandleEditOpenDialog(PatientModel item)
    {
        DialogData = item;
        PatientDialogTitle = "编辑患者";
        isShowAddPatient = true;
    }

    private void HandleAddOpenDialog(PatientModel item)
    {
        DialogData = item;
        PatientDialogTitle = "新增患者";
        isShowAddPatient = true;
    }

    private async Task HandleOnOptionsUpdate(DataOptions options)
    {
        _options = options;
        await InitDataList();
    }

    private int _total;
    private bool _hasPatientRecordPermission;
    private PatientModel _queryModel { get; set; } = new();
    [Inject] InternalHttpClientService _internalHttpClientService { get; set; }

    /// <summary>
    /// 路由数据
    /// </summary>
    [CascadingParameter]
    public AuthRouteData AuthRouteData { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _hasPatientRecordPermission = AuthRouteData.CurrentModule
            ?.Functions.Any(m => m.Name == "心率变异性数据管理接口/编辑") ?? false;
        DialogData = new PatientModel
        {
            Id = string.Empty,
            CardId = string.Empty,
            DoctorsAdvice = string.Empty,
            Age = 0,
            Area = string.Empty,
            Height = 0,
            Weight = 0,
            Name = string.Empty,
            OutpatientNumberString = string.Empty,
        };
        _options.ItemsPerPage = 15;
        await InitDataList();
        await base.OnInitializedAsync();
    }


    private async Task InitDataList()
    {
        _loading = true;
        var queryParam = new Dictionary<string, string>
        {
            { nameof(_queryModel.Age), _queryModel.Age.ToString() },
            { nameof(_queryModel.Name), _queryModel.Name },
            { nameof(_queryModel.CardId), _queryModel.CardId },
            { nameof(_queryModel.Source), _queryModel.Source },
            { nameof(_queryModel.OutpatientNumberString), _queryModel.OutpatientNumberString },
            { nameof(_queryModel.Sex), _queryModel.Sex.ToString() },
            { "page", _options?.Page.ToString() },
            { "pageSize", _options.ItemsPerPage.ToString() }
        };
        var result = await _internalHttpClientService.GetPatientModelList(queryParam);
        PatientList = result.Data?.Select(m => m.Data)?.ToList() ?? null;
        _total = result.Page?.TotalCount ?? 0;
        _loading = false;
    }

    private async Task DeleteConfirmAsync(string id)
    {
        var confirmed = await PopupService.ConfirmAsync(
            "警告",
            "删除后将无法恢复，确认删除吗?",
            AlertTypes.Warning);
        if (confirmed)
        {
            var deleteResult = await _internalHttpClientService.DeletePatientAsync(id);
            await PopupService.EnqueueSnackbarAsync(deleteResult ? "删除成功！" : "删除失败！", deleteResult ? AlertTypes.Success : AlertTypes.Error);
            await InitDataList();
        }
    }

}