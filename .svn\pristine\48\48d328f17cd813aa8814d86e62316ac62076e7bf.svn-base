using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;
using UFU.IoT.Services;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
///
/// </summary>
public static class ByteAnalysisServices
{
	/// <summary>
	/// Initializes the data analysis process by subscribing to device message events.
	/// </summary>
	public static void AnalysisData()
	{
		BinaryIoTService.OnReceivedDeviceMsg += ReceiveDeviceMsgTask;
		BinaryIoTService.OnDeviceOnline += OnDeviceOnline;
		BinaryIoTService.OnDeviceOffline += OnDeviceOffline;
	}

	/// <summary>
	///   前置处理PPG数据
	/// </summary>
	public static Func<Dictionary<string, List<uint>>,Dictionary<string, object>> OnBeforeSavePPGHandler;

	private static void ReceiveDeviceMsgTask(IConnect connect, string msg)
	{
		Task.Run(async () => { await ReceiveDeviceMsg(connect, msg); });
	}

	private static async Task ReceiveDeviceMsg(IConnect connect, string msg)
	{

		var jsonNode = JsonNode.Parse(msg);

		if (jsonNode == null)
		{
			LogTool.Logger.Information(
				$"【ByteAnalysisServices】 jsonNode is Null ,msg: {msg} ");
		}

		var deviceSn = jsonNode?["Device"]?["SN"]?.ToString();
		var type = jsonNode?["Data"]?["Type"]?.GetValue<int>();
		var cmd = jsonNode?["CMD"]?.GetValue<int>();
		var msgId = jsonNode?["MsgId"]?.GetValue<int>();
		var device = DeviceStatusDataService.GetDeviceStatusData(deviceSn);
		var collectModeStr = jsonNode?["Data"]?["CollectMode"];
		var toPageJson = new JsonObject();
		toPageJson.Add("SN", deviceSn);
		var timeSpan = jsonNode!["Data"]?["Time"]?.GetValue<long>();
		LogTool.Logger.Information(
			$@"-------------------------ReceiveDeviceMsgStart--------------------------
                        SN:{deviceSn} 
                        CMD:{cmd}  
                        MsgId:{jsonNode?["MsgId"]}
                        Type:{type?.ToString()}
                        CollectMode:{collectModeStr ?? "null"} 
                        timeSpan:{timeSpan}
                                    "
		);

		if (device == null) return;

		if (collectModeStr == null)
		{
			if (cmd == (int)BinaryCMD.Ack)
			{
				//开始检测
				if (msgId == 11111 && device.DeviceStatus != EnumDeviceStatus.检测中 && !device.IsReading)
				{
					device.IsStoping = false;
					device.IsReading = true;
					await using var db = new DataRepository();
					var deviceRecord = await db.Query<PatientRecordModel>()
						.OrderByDescending(m => m.AddTime)
						.FirstOrDefaultAsync(m =>
							m.Data.DeviceId == device.Device.Id
							&& m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Readying
						);
					if (deviceRecord != null)
					{
						device.StartTime = timeSpan * 1000;
						await DeviceStatusDataService.SetPatientRecordBegin(
							deviceSn,
							deviceRecord.Data
						);
						toPageJson.Add("Status", JsonValue.Create(device.DeviceStatus));
						toPageJson.Add("DeviceRecord", JsonValue.Create(toPageJson));
						toPageJson.Add("From_CollectMode", JsonValue.Create(1));
						await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
						return;
					}

					device.IsReading = false;
				}

				//结束检测
				if (msgId == 22222)
				{
					var json = jsonNode.ToJsonString();
					await StopCheck(device, timeSpan, toPageJson);
					return;
				}
			}
			else
			{
				if (device is
					{
						DeviceStatus: EnumDeviceStatus.检测中, RecordModel: not null
					})
				{
					await DeviceStatusDataService.SendBeganMsgToDevice(device);
					return;
				}

				if (device is { DeviceStatus: EnumDeviceStatus.空闲 })
				{
					device.IsStoping = false;
					device.IsReading = true;
					await using var db = new DataRepository();
					var deviceRecord = await db.Query<PatientRecordModel>()
						.OrderByDescending(m => m.AddTime)
						.FirstOrDefaultAsync(m =>
							m.Data.DeviceId == device.Device.Id
							&& m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Readying
						);
					if (deviceRecord != null)
					{
						await DeviceStatusDataService.SendBeganMsgToDevice(device);
						device.StartTime = timeSpan * 1000;
						await DeviceStatusDataService.SetPatientRecordBegin(
							deviceSn,
							deviceRecord.Data
						);
						toPageJson.Add("Status", JsonValue.Create(device.DeviceStatus));
						toPageJson.Add("DeviceRecord", JsonValue.Create(toPageJson));
						toPageJson.Add("From_CollectMode", JsonValue.Create(1));
						await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
						return;
					}
					device.IsReading = false;
					return;
				}
			}
		}

		var isParseCollectMode = int.TryParse(collectModeStr?.ToString(), out var collectMode);
		if (
			isParseCollectMode
			&& collectMode == 1
			&& device.DeviceStatus != EnumDeviceStatus.检测中
			&& !device.IsReading
		)
		{
			await using var db = new DataRepository();

			var deviceRecord = await db.Query<PatientRecordModel>()
				.OrderByDescending(m => m.AddTime)
				.FirstOrDefaultAsync(m =>
					m.Data.DeviceId == device.Device.Id
					&& m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Readying
				);
			if (deviceRecord != null)
			{
				device.StartTime = timeSpan * 1000;
				await DeviceStatusDataService.SetPatientRecordBegin(
					deviceSn,
					deviceRecord.Data
				);
				toPageJson.Add("Status", JsonValue.Create(device.DeviceStatus));
				toPageJson.Add("DeviceRecord", JsonValue.Create(toPageJson));
				toPageJson.Add("From_CollectMode", JsonValue.Create(1));
				await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
				return;
			}
		}

		if (type >= 0 && collectMode == 1 && device is { RecordModel: not null })
		{
			JsonNode dataJsonNode = null;
			try
			{
				switch (type)
				{
					case (int)HXDataType.PPG:
						{
							dataJsonNode = RecicePPGDataHandler(jsonNode, device, timeSpan, toPageJson);
							break;
						}
					case (int)HXDataType.HRSPO2:
						{
							dataJsonNode = ReciveSPODataHandler(jsonNode, deviceSn, device, timeSpan, toPageJson);
							break;
						}
					case (int)HXDataType.IMU:
						{
							await ReviceIMUDataHandler(jsonNode, deviceSn, device, timeSpan, toPageJson);
							break;
						}
					case (int)HXDataType.TEMPSKIN:
						{
							dataJsonNode =
								await ReciveSkinTempDataHandler(jsonNode, deviceSn, toPageJson, device, timeSpan);
							break;
						}
					case (int)HXDataType.EDA:
						{
							dataJsonNode = await ReceiveEdaDataHandler(jsonNode, deviceSn, toPageJson, device, timeSpan);
							break;
						}
					case (int)HXDataType.LIGHT:
					case (int)HXDataType.TEMPAIR:
					case (int)HXDataType.AP:
						{
							var dataType = (HXDataType)type;
							dataJsonNode = jsonNode["Data"][dataType.ToString()];
							if (dataJsonNode == null) return;
							var data = JsonTool.Deserialize<List<float>>(dataJsonNode.ToJsonString());
							await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
							if (device.RecordModel != null)
							{
								FileDataHelper.SaveDataToCsv(
									device.RecordModel.GetRecordDirectoryPath(),
									timeSpan.Value,
									dataType.ToString(),
									new Dictionary<string, List<float>>() { { dataType.ToString(), data } }
								);
							}

							break;
						}
					default:
						break;
				}
			}
			catch (Exception e)
			{
				LogTool.Logger.Error(
					e,
					$"---------------------数据解析错误--------------------------:{dataJsonNode.ToJsonString()}"
				);
			}
		}

		LogTool.Logger.Information(
			$@"-------------------------ReceiveDeviceMsgEnd--------------------------"
		);
	}

	#region 接收数据处理

	private static async Task<JsonNode> ReceiveEdaDataHandler(JsonNode jsonNode, string deviceSn, JsonObject toPageJson,
		DeviceStatusData device, long? timeSpan)
	{
		JsonNode dataJsonNode = jsonNode["Data"]["EDA"]; ;
		var data = JsonTool.Deserialize<List<float>>(dataJsonNode.ToJsonString());
		var viewData = data.Select(m => m > 0 ? 1000f / m : 0.0f).ToList();
		DeviceStatusDataService.OnReceiveEDAData(viewData, deviceSn);
		toPageJson.Add("EDA", JsonValue.Create(viewData));
		toPageJson.Add("From_EDA", JsonValue.Create(1));
		if (device.RecordModel != null)
		{
			FileDataHelper.SaveDataToCsv(
				device.RecordModel.GetRecordDirectoryPath(),
				timeSpan.Value,
				HXDataType.EDA.ToString(),
				new Dictionary<string, List<float>>() { { "EDA", viewData } },
				markValue:device.MarkedValue,
				afterMarked: () =>
				{
					if (!device.IsMarked) return;
					_=AlgWebSocketClient.SendTickMsgToAlg(
						device.RecordModel.Id,
						timeSpan??0,
						algorithm: "GSRSeparated",
						tickTag: device.MarkedValue 
					);
					device.MarkedValue = string.Empty;
					device.IsMarked = false;
				}
			);
		}
		try
		{
			_ = SendGsrMsgToAlg(
				device.RecordModel.Id,
				viewData.ToArray(),
				timeSpan ?? DateTime.Now.ToUnixMs(),
				device.StartTime ?? device.RecordModel.CollectStartTime.ToUnixMs(),
				DateTime.Now>=device.RecordModel.CollectEndTime
			);
		}
		catch (Exception e)
		{
			LogTool.Logger.Error(e, "SendGsrMsgToAlg");
		}

		await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
		return dataJsonNode;
	}

	private static async Task<JsonNode> ReciveSkinTempDataHandler(JsonNode jsonNode, string deviceSn,
		JsonObject toPageJson,
		DeviceStatusData device, long? timeSpan)
	{
		JsonNode dataJsonNode;
		if (jsonNode == null)
		{
			return null;
		}

		dataJsonNode = jsonNode["Data"]["TEMPSKIN"];
		var data = JsonTool.Deserialize<List<float>>(dataJsonNode.ToJsonString());
		var skintTemp = data.Select(m => m);
		DeviceStatusDataService.OnReceiveSKINTEMPData(data, deviceSn);
		toPageJson.Add("SKINTEMP", JsonValue.Create(skintTemp));
		toPageJson.Add("From_SKINTEMP", JsonValue.Create(1));
		await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());

		if (device.RecordModel != null)
		{
			FileDataHelper.SaveDataToCsv(
				device.RecordModel.GetRecordDirectoryPath(),
				timeSpan.Value,
				HXDataType.TEMPSKIN.ToString(),
				new Dictionary<string, List<float>>() { { HXDataType.TEMPSKIN.ToString(), data } }
			);
		}

		return dataJsonNode;
	}

	private static async Task ReviceIMUDataHandler(JsonNode jsonNode, string deviceSn, DeviceStatusData device,
		long? timeSpan,
		JsonObject toPageJson)
	{
		var GYRO_X = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["GYRO-X"].ToJsonString()
		);
		var GYRO_Y = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["GYRO-Y"].ToJsonString()
		);

		var GYRO_Z = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["GYRO-Z"].ToJsonString()
		);

		var ACC_X = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["ACC-X"].ToJsonString()
		);

		var ACC_Y = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["ACC-Y"].ToJsonString()
		);

		var ACC_Z = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["ACC-Z"].ToJsonString()
		);

		var GEO_X = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["GEO-X"].ToJsonString()
		);

		var GEO_Y = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["GEO-Y"].ToJsonString()
		);

		var GEO_Z = JsonTool.Deserialize<List<short>>(
			jsonNode["Data"]["IMU"]["GEO-Z"].ToJsonString()
		);
		DeviceStatusDataService.OnReceiveIMuData(GYRO_X, deviceSn);
		if (device.RecordModel != null)
		{
			FileDataHelper.SaveDataToCsv(
				device.RecordModel.GetRecordDirectoryPath(),
				timeSpan.Value,
				"IMU",
				new Dictionary<string, List<short>>()
				{
					{ "GYRO-X", GYRO_X },
					{ "GYRO-Y", GYRO_Y },
					{ "GYRO-Z", GYRO_Z },
					{ "ACC-X", ACC_X },
					{ "ACC-Y", ACC_Y },
					{ "ACC-Z", ACC_Z },
					{ "GEO-X", GEO_X },
					{ "GEO-Y", GEO_Y },
					{ "GEO-Z", GEO_Z }
				}
			);
		}

		toPageJson.Add("IMU", JsonValue.Create(GYRO_X));
		toPageJson.Add("From_IMU", JsonValue.Create(1));
		await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
	}

	private static JsonNode ReciveSPODataHandler(JsonNode jsonNode, string deviceSn, DeviceStatusData device,
		long? timeSpan,
		JsonObject toPageJson)
	{
		JsonNode dataJsonNode;
		var spo2Data = jsonNode["Data"]["SPO2"];
		var bmpData = jsonNode["Data"]["Bmp"];
		var batteryData = jsonNode["Data"]["Battery"];
		dataJsonNode = jsonNode["Data"]["SPO2"];
		var spo2 = JsonTool.Deserialize<uint>(spo2Data.ToJsonString());
		DeviceStatusDataService.OnReceiveSPO2Data(spo2, deviceSn);

		var bmp = JsonTool.Deserialize<uint>(bmpData.ToJsonString());
		DeviceStatusDataService.OnReceiveBmpData(bmp, deviceSn);

		var battery = JsonTool.Deserialize<uint>(batteryData.ToJsonString());
		DeviceStatusDataService.OnReceiveBatteryData(battery, deviceSn);
		if (device.RecordModel != null)
		{
			FileDataHelper.SaveDataToCsv(
				device.RecordModel.GetRecordDirectoryPath(),
				timeSpan.Value,
				HXDataType.HRSPO2.ToString(),
				new Dictionary<string, List<uint>>()
				{
					{
						"SPO2",
						new List<uint> { spo2 }
					},
					{
						"bpm",
						new List<uint> { bmp }
					},
					{
						"Battery",
						new List<uint> { battery }
					}
				}
			);
		}

		toPageJson.Add("SPO2", spo2);
		//toPageJson.Add("Bmp", bmp);
		toPageJson.Add("Battery", battery);
		toPageJson.Add("From_Battery", JsonValue.Create(1));
		_ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
		return dataJsonNode;
	}

	private static JsonNode RecicePPGDataHandler(JsonNode jsonNode, DeviceStatusData device, long? timeSpan,
		JsonObject toPageJson)
	{
		JsonNode dataJsonNode;
		dataJsonNode = jsonNode["Data"]["PPG_G"];
		var ppg_g_data = JsonTool.Deserialize<List<uint>>(
			jsonNode["Data"]["PPG_G"].ToJsonString()
		);
		var ppg_r_data = JsonTool.Deserialize<List<uint>>(
			jsonNode["Data"]["PPG_R"].ToJsonString()
		);
		var ppg_i_data = JsonTool.Deserialize<List<uint>>(
			jsonNode["Data"]["PPG_I"].ToJsonString()
		);
		try
		{
			var data = new Dictionary<string, List<uint>>()
			{
				{ "PPG_G", ppg_g_data },
				{ "PPG_R", ppg_r_data },
				{ "PPG_I", ppg_i_data },
			};
			device.StartTime ??= timeSpan * 1000;
			dataJsonNode = jsonNode["Data"]["PPG_G"];
			if (device.StartTime != null)
			{
				try
				{
					var ppaRate = jsonNode["Data"]["PPGRate"].GetValue<int>();
					_ = Task.Run(async () =>
					{
						await AlgWebSocketClient.SendMsgToAlg(
							device.RecordModel.Id,
							data,
							ppaRate,
							device.StartTime.Value,
							false
						);
					});
				}
				catch (Exception e)
				{
					LogTool.Logger.Error(e, "发送消息到算法平台错误");
				}
			}

			if (device.RecordModel != null)
			{
				DeviceStatusDataService.OnReceiveorgPPGData(device, ppg_g_data);
				toPageJson.Add("ORGPPG", JsonValue.Create(ppg_g_data));
				toPageJson.Add("From_PPG", JsonValue.Create(1));
				_ = Task.Run(() =>
				{
					if (OnBeforeSavePPGHandler != null)
					{
						OnBeforeSavePPGHandler?.Invoke(data);
					}
					
					FileDataHelper.SaveDataToCsv(
						device.RecordModel.GetRecordDirectoryPath(),
						timeSpan.Value,
						HXDataType.PPG.ToString(),
						data,
						device.MarkedValue,
						afterMarked: () =>
						{
							
						}
					);
				});
				_ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
			}
		}
		catch (Exception e)
		{
			LogTool.Logger.Error(e, "保存PPG数据出现错误");
		}
		return dataJsonNode;
	}

	#endregion

	private static async Task StopCheck(
		DeviceStatusData device,
		long? timeSpan,
		JsonObject toPageJson
	)
	{
		var deviceData = DeviceStatusDataService.GetDeviceStatusData(device.Device.DeviceSN);
		if (deviceData.RecordModel is { EnumPatientCheckStatus: EnumPatientCheckStatus.Checked })
		{
			//检测结束给算法网关发送消息
			try
			{
				if (device.StartTime.HasValue)
				{
					if (!device.IsSendEnd)
					{
						//await SendEndMsgToAlg(deviceData.RecordModel.Id, device.StartTime.Value, timeSpan ?? 0,true);
						device.IsSendEnd = true;
					}

					await DeviceStatusDataService.SetPatientRecordEnd(device);
				}
			}
			catch (Exception e)
			{
				LogTool.Logger.Information($"发送数据到算法网关失败：" + e);
			}
		}
	}

	private static void OnDeviceOnline(ConnectDevice device)
	{
		var deviceData = DeviceStatusDataService.GetDeviceData(device.Device.Id);
		if (deviceData == null)
		{
			DeviceStatusDataService.AddDeviceStatusData(device.Device.DeviceSN);
		}
		if (deviceData is { DeviceStatus: EnumDeviceStatus.离线 })
		{
			Task.Run(async () =>
			{
				DeviceStatusDataService.SetDeviceOnline(device.Device.Id);
				await HXAdminWebSocket.SendMsgToAdmin(
					JsonTool.SerializeIgnoreNull(
						new
						{
							SN = device.Device.DeviceSN,
							Status = deviceData.DeviceStatus,
							From_Online = 1
						}
					)
				);
			});
		}
	}

	private static void OnDeviceOffline(ConnectDevice device)
	{
		LogTool.Logger.Information($"OnDeviceOffline:{device?.Device.DeviceSN}");
		var deviceData = DeviceStatusDataService.GetDeviceStatusData(device.Device.DeviceSN);
		DeviceStatusDataService.SetDeviceOffline(device.DeviceId);
		_ = HXAdminWebSocket.SendMsgToAdmin(
			JsonTool.SerializeIgnoreNull(
				new
				{
					SN = device.Device.DeviceSN,
					Status = deviceData.DeviceStatus,
					From_Offline = 1
				}
			)
		);
	}

	private static async Task SendGsrMsgToAlg(
		string recordId,
		float[] gsr_g_data,
		long timeSpan,
		long StartTime,
		bool isEnd
	)
	{
		byte[] databytes = new byte[gsr_g_data.Count() * sizeof(float)];
		Buffer.BlockCopy(gsr_g_data.ToArray(), 0, databytes, 0, databytes.Length);
		var jsonData = new
		{
			MessageType = 3,
			ClientId = recordId,
			Algorithm = "GSRSeparated",
			IsEnd = isEnd,
			StartTime,
			Time = timeSpan * 1000,
			Data = new { gsr_Length = databytes.Length, }
		};
		var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
		await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, databytes, recordId);
	}
}