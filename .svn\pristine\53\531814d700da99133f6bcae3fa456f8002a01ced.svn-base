{
  "Logging": {
    "LogLevel": {
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "Microsoft.AspNetCore.Authentication": "Warning"
      }
    }
  },
  "AllowedHosts": "*",

  "Cors": {
    "Origins": "http://localhost:8080",
    "AllowCredentials": true
  },
  "Permission": {
    "EnableDataPermission": false,
    "EnableFunctionPermission": true,
    "EnableVersionPermission": false
  },
  "ConnectionStrings": {
    "DefaultDbContext": "User ID=postgres;Password=******************;Host=*************;Port=5432;Database=HX.HRV.SCI;Pooling=true;"
  },
  "Redis": {
    "Enable": false,
    "ConnectionString": "127.0.0.1:6379,password=xBarvRgVJEEmBbe2"
  },
  "Blazor": {
    "Enable": true,
    "Layout": "_HRVLayout",
    "RenderMode": "Server" //WebAssembly, WebAssemblyPrerendered, Server, ServerPrerendered, Auto
    // "RenderMode": "WebAssembly" //WebAssembly, WebAssemblyPrerendered, Server, ServerPrerendered, Auto
  },
  "DefaultUrl": {
    "Home": "/client/device-status",
    "Login": "/client/login",
    "Register": "/",
    "Domain": "https://linglongtu.ufutech.com"
    //"Domain": "https://linglongtu.ufutech.com:6002"
  },
  "MQTT": {
    "Port": 5168
  },

  "Version": "v1.0.0",
  "AlgorithmProxy": {
    "Host": "hx.linglongtu.cn",
    "Port": 5220
  },
    "HrvVariable": {
        "SysName": "多参数生理及行为研究平台",
        "ReportReturnUrl": "/client/sci/patient-record/list",
        "IsShowExport": true
    }
}
