﻿@page "/IoT/VirtualDevice/Edit/{Id}"
@using UFU.IoT.Models;
@inject StateService state
@inject MessageService _message
@attribute [Permission("产品管理接口/编辑", IsMenu = false)]

<div class="VirtualDeviceDetail">
    <ul class="form">
        @if (!string.IsNullOrEmpty(Device.Id))
        {
            <li>
                <label class="label">设备编号：</label>
                <div class="con">
                    @(Device.Id)
                </div>
            </li>
        }
       
        <li>
            <label class="label">设备名称：</label>
            <div class="con">
                <Input Class="input" Placeholder="设备名称" @bind-Value="@(Device.Data.Name)" />
            </div>
        </li>
        <li>
            <label class="label">设备类型：</label>
            <div class="con">
                <Select Class="input" TItem="SelectItemModel"
                        TItemValue="string"
                        DataSource="@DeviceTypeList"
                        @bind-Value="@(Device.Data.TypeId)"
                        LabelName="@nameof(SelectItemModel.Label)"
                        ValueName="@nameof(SelectItemModel.Value)"
                        Placeholder="选择设备类型">
                </Select>
            </div>
        </li>
    </ul>
    <div class="mappingList">
        <div class="tit">
            <span class="t">数据项映射</span>
            <div class="r">
                <span class="add"> <Button Type="@ButtonType.Primary" @onclick="HandleMappingShowBatch">批量修改</Button></span>
                <span class="add"> <Button Type="@ButtonType.Primary" @onclick="HandleMappingShow">添加映射</Button></span>
            </div>
           
        </div>
        <div class="table">
            <div class="thead">
                <ul class="item-list">
                    <li class="item item-0">
                        <input type="checkbox" class="c_checkbox" checked="@(IsCheckAll())" @onchange="handleCheckAll" />
                    </li>
                    <li class="item item-1">数据项</li>
                    <li class="item item-2">映射设备</li>
                    <li class="item item-3">映射数据项</li>
                    <li class="item item-4">自动映射</li>
                    <li class="item item-5">转换系数</li>
                    <li class="item item-6">操作</li>
                </ul>
            </div>
            <div class="tbody">
                @if (Device.Data.VirtualDeviceMappings != null && Device.Data.VirtualDeviceMappings.Count > 0)
                {
                    @for (int i = 0; i < Device.Data.VirtualDeviceMappings.Count; i++)
                    {
                        var index = i;
                        var item = Device.Data.VirtualDeviceMappings[i];
                        var id = item.VirtualDataItem + item.RealDeviceId + item.RealDataItem;
                        <div class="tr" @key="index">
                            <ul class="item-list">
                                    <li class="item item-0">
                                        <input type="checkbox" class="c_checkbox" checked="@IsCheck(id)" @onchange="(e) => handleCheck(e,id)"  />
                                    </li>
                                <li class="item item-1">@(item.VirtualDataItem)</li>
                                <li class="item item-2">@(GetReadDeviceName(item.RealDeviceId))</li>
                                <li class="item item-3">@(item.RealDataItem)</li>
                                <li class="item item-4">@(item.AutoMapping == true ? "是" : "否")</li>
                                <li class="item item-5">@(item.Coefficient)</li>
                                <li class="item item-6">
                                    <div class="opt">
                                        <span class="btn" @onclick="()=>HandleMappingEdit(index,item)">编辑</span>
                                        <span class="btn" @onclick="()=>HandleMappingDel(index,item)">移除</span>
                                        <span class="btn" @onclick="()=>HandleMoveUp(index)">向上移</span>
                                        <span class="btn" @onclick="()=>HandleMoveDown(index)">向下移</span>
                                       
                                    </div>
                                </li>
                            </ul>
                        </div>
                    }
                }
                else
                {
                    <Empty Simple />
                }

            </div>
        </div>
    </div>
    
    <div class="formFoot">
        <span class="item">
            <Button Type="@ButtonType.Primary" @onclick="HandeleSubmit">提交保存</Button>
        </span>
        <span class="item">
            <Button Type="@ButtonType.Dashed" @onclick="HandeleCancel">取消</Button>
        </span>
    </div>
</div>

<Modal Title="数据映射"
       Visible="@_visible"
       OnOk="@HandleMappingOk"
       OnCancel="@HandleMappingCancel">
    <div class="mappingModal">
        <ul class="form">
            <li>
                <label class="label">虚拟数据项：</label>
                <div class="con">
                    <Select Class="input" TItem="SelectItemModel"
                            OnSelectedItemChanged="OnSelectedItemChangedVirtualDataItem"
                            @bind-Value="@(VirtualDeviceMappingModal.VirtualDataItem)"
                            TItemValue="string"
                            DataSource="@DeviceTypeDataItemList"
                            LabelName="@nameof(SelectItemModel.Label)"
                            ValueName="@nameof(SelectItemModel.Value)"
                            Placeholder="请选择">
                    </Select>
                </div>
            </li>
            <li>
                <label class="label">映射设备：</label>
                <div class="con">
                    <div class="inputSelect">
                        <input class="inputReadonly" type="text" readonly value="@(GetDevicesName(VirtualDeviceMappingModal.RealDeviceId))" />
                        <span class="selectBtn" @onclick="HandleShowDevicesModel">选择</span>
                    </div>
                  @*  <Select Class="input" TItem="SelectItemModel"
                            TItemValue="string"
                            DataSource="@ReadDeviceList"
                            @bind-Value="@(VirtualDeviceMappingModal.RealDeviceId)"
                            LabelName="@nameof(SelectItemModel.Label)"
                            ValueName="@nameof(SelectItemModel.Value)"
                            Placeholder="请选择">
                    </Select>*@
                </div>
            </li>
            <li>
                <label class="label">映射数据项：</label>
                <div class="con">
                    <Select Class="input" TItem="SelectItemModel"
                            TItemValue="string"
                            DataSource="@ReadDeviceTypeDataItemList2"
                            @bind-Value="@(VirtualDeviceMappingModal.RealDataItem)"
                            LabelName="@nameof(SelectItemModel.Label)"
                            ValueName="@nameof(SelectItemModel.Value)"
                            Placeholder="选择设备类型">
                    </Select>
                </div>
            </li>
            <li>
                <label class="label">自动映射：</label>
                <div class="con">
                    <Switch @bind-Checked="VirtualDeviceMappingModal.AutoMapping" OnChange="HandleChangeAutoMapping" />
                </div>
            </li>
            @if (DeviceTypeDataItem != null && (DeviceTypeDataItem.DataType == DataItemType.Float || DeviceTypeDataItem.DataType == DataItemType.Int))
            {
                <li>
                    <label class="label">转换系数：</label>
                    <div class="con">
                        <Input Class="input" Placeholder="转换系数" @bind-Value="@(VirtualDeviceMappingModal.Coefficient)" />
                    </div>
                </li>
            }
           
        </ul>
        @if (!VirtualDeviceMappingModal.AutoMapping && !string.IsNullOrEmpty(VirtualDeviceMappingModal.VirtualDataItem))
        {
            <div class="mappingList2">
                <div class="tit">
                    <span class="t">值映射</span>
                </div>
                <div class="table">
                    <div class="thead">
                        <ul class="item-list">
                            <li class="item item-1">虚拟值</li>
                            <li class="item item-2">映射方向</li>
                            <li class="item item-3">映射值</li>
                            <li class="item item-4">
                                <span class="addbtn" @onclick="handleAddDataMappings">
                                    添加
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="tbody">
                        @if (VirtualDeviceMappingModal.DataMappings != null)
                        {
                            @for (int i = 0; i < VirtualDeviceMappingModal.DataMappings.Count; i++)
                            {
                                var item = VirtualDeviceMappingModal.DataMappings[i];
                                <div class="tr">
                                    <ul class="item-list">
                                        <li class="item item-1">
                                            @if (SelectDeviceEnumValues != null && SelectDeviceEnumValues.Count > 0)
                                            {
                                                <Select Class="input" TItem="SelectItemModel"
                                                        TItemValue="string"
                                                        DataSource="@SelectDeviceEnumValues"
                                                @bind-Value="@(item.VirtualValue)"
                                                        LabelName="@nameof(SelectItemModel.Label)"
                                                        ValueName="@nameof(SelectItemModel.Value)"
                                                        Placeholder="请选择">
                                                </Select>
                                            }
                                            else
                                            {
                                                <Input Placeholder="" @bind-Value="@(item.VirtualValue)" />
                                            }

                                        </li>
                                        <li class="item item-2">
                                            <Select Class="input" TItem="SelectItemModel2"
                                                    TItemValue="RWType?"
                                                    DataSource="@RWTypeList"
                                            @bind-Value="@(item.RWType)"
                                                    LabelName="@nameof(SelectItemModel2.Label)"
                                                    ValueName="@nameof(SelectItemModel2.Value)"
                                                    Placeholder="请选择">
                                            </Select>

                                        </li>
                                        <li class="item item-3">
                                            <Input Placeholder="" @bind-Value="@(item.RealValue)" />
                                        </li>
                                        <li class="item item-4">
                                            <span class="delbtn" @onclick="()=>handleDelDataMappings(item)">
                                                删除
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            }
                        }
                        else
                        {
                            <Empty Simple />
                        }
                    </div>
                </div>

            </div>
        }
       
    </div>
</Modal>
<Modal Title="批量修改映射"
       Visible="@_visiblebBatch"
       OnOk="@HandleMappingOkBatch"
       OnCancel="@HandleMappingCancelBatch">
    <div class="mappingModal">
        <ul class="form">
            <li>
                <label class="label">映射设备：</label>
                <div class="con">
                    <div class="inputSelect">
                        <input class="inputReadonly" type="text" readonly value="@(GetDevicesName(ReadDeviceId))" />
                        <span class="selectBtn" @onclick="HandleShowDevicesModel">选择</span>
                    </div>
                </div>
            </li>
          @*  <li>
                <label class="label">映射数据项：</label>
                <div class="con">
                    <Select Class="input" TItem="SelectItemModel"
                            TItemValue="string"
                            DataSource="@ReadDeviceTypeDataItemList4"
                    @bind-Value="@(ReadDeviceItemData)"
                            LabelName="@nameof(SelectItemModel.Label)"
                            ValueName="@nameof(SelectItemModel.Value)"
                            Placeholder="选择设备类型">
                    </Select>
                </div>
            </li>*@

        </ul>
       

    </div>
</Modal>

@if (_visibleSelectDevices)
{
    <DevicesModel OnClose="HandleCloseDevicesModel" OnSelect="HandleSelectDevice" />
}

@code {
    private bool _visibleSelectDevices { get; set; } = false;
    /// <summary>
    /// 关闭选择设备弹窗
    /// </summary>
    private void HandleCloseDevicesModel()
    {
        _visibleSelectDevices = false;
        StateHasChanged();
    }
    /// <summary>
    /// 选择设备回调
    /// </summary>
    private void HandleSelectDevice(string deviceId)
    {
        VirtualDeviceMappingModal.RealDeviceId = deviceId;
        ReadDeviceId = deviceId;
        StateHasChanged();
    }
    /// <summary>
    /// 选择设备
    /// </summary>
    private void HandleShowDevicesModel()
    {
        _visibleSelectDevices = true;
        StateHasChanged();
    }
    /// <summary>
    /// 真实设备名称
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    private string GetDevicesName(string id)
    {
        if (string.IsNullOrEmpty(id))        
        {
            return null;     
        }

        var item = ReadDevices.FirstOrDefault(m => m.Id == id);
        if(item != null)
        {
            return  item.Name;
        }
        else
        {
            return id;
        }
    }

    private List<string> checkedList = new List<string>();
    /// <summary>
    /// 单选
    /// </summary>
    /// <param name="e"></param>
    /// <param name="v"></param>
    void handleCheck(ChangeEventArgs e,string v)
    {
        if ((bool)e.Value == true)
        {
            checkedList.Add(v);
        }
        else
        {
            checkedList.Remove(v);
        }
        checkedList = checkedList.Distinct().ToList();
        StateHasChanged();
    }
    //// <summary>
    /// 全选
    /// </summary>
    /// <param name="e"></param>
    void handleCheckAll(ChangeEventArgs e)
    {
        if ((bool)e.Value == true)
        {
            checkedList = Device.Data.VirtualDeviceMappings.Select(m => m.VirtualDataItem+m.RealDeviceId+m.RealDataItem).ToList();
        }
        else
        {
            checkedList = new List<string>();
        }
        checkedList = checkedList.Distinct().ToList();
        StateHasChanged();
    }
    /// <summary>
    /// 全选
    /// </summary>
    /// <returns></returns>
    private bool IsCheckAll()
    {
        if (Device.Data.VirtualDeviceMappings != null && Device.Data.VirtualDeviceMappings.Count > 0 && checkedList.Count == Device.Data.VirtualDeviceMappings.Count)
        {
            return true;
        }
        else
        {
            return false;
        }

    }
    /// <summary>
    /// 是否选择
    /// </summary>
    /// <returns></returns>
    private bool IsCheck(string v)
    {
        if(checkedList.FirstOrDefault(m => m == v) != null)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /// <summary>
    /// 向上移动
    /// </summary>
    private void HandleMoveUp(int index)
    {
        if(index  == 0)
        {
            _message.Warn("已经是最顶部");
            return;
        }
        var temp = Device.Data.VirtualDeviceMappings[index];
        var temp2 = Device.Data.VirtualDeviceMappings[index-1];
        Device.Data.VirtualDeviceMappings[index - 1] = temp;
        Device.Data.VirtualDeviceMappings[index] = temp2;
    }
    /// <summary>
    /// 向下移动
    /// </summary>
    private void HandleMoveDown(int index)
    {
        if (index == (Device.Data.VirtualDeviceMappings.Count-1))
        {
            _message.Warn("已经是最低部");
            return;
        }
        var temp = Device.Data.VirtualDeviceMappings[index];
        var temp2 = Device.Data.VirtualDeviceMappings[index + 1];
        Device.Data.VirtualDeviceMappings[index + 1] = temp;
        Device.Data.VirtualDeviceMappings[index] = temp2;
    }


    private bool _visible { get; set; } = false;

    private bool _visiblebBatch { get; set; } = false;



    /// <summary>
    /// 编辑数据项映射下标
    /// </summary>
    private int? DeviceMappingIndex { get; set; } = null;

    /// <summary>
    /// ID
    /// </summary>
    [Parameter]
    public string? Id { get; set; }
    /// <summary>
    /// 是否复制
    /// </summary>
    [Parameter]
    [SupplyParameterFromQuery]
    public string? isCopy { get; set; }

    public class SelectItemModel
    {
        /// <summary>
        /// 显示文本
        /// </summary>
        public string? Label { get; set; }
        /// <summary>
        /// 下拉值
        /// </summary>
        public string? Value { get; set; }
    }


    private DataModel<DeviceModel> Device { get; set; } = new DataModel<DeviceModel> { Data = new DeviceModel() };

    /// <summary>
    /// 设备类型列表
    /// </summary>
    public List<DeviceTypeModel> DeviceTypes { get; set; } = new List<DeviceTypeModel>();

    /// <summary>
    /// 设备类型列表
    /// </summary>
    private List<SelectItemModel> DeviceTypeList
    {
        get
        {
            return DeviceTypes.Select(m => new SelectItemModel { Label = m.Name, Value = m.Id }).ToList();
        }
    }
    /// <summary>
    /// 选中的设备类型
    /// </summary>
    private DeviceTypeModel SelectDeviceType
    {
        get
        {
            return DeviceTypes.FirstOrDefault(m => m.Id == Device.Data.TypeId);
        }
    }
    /// <summary>
    /// 选中的设备类型的数据项(虚拟设备)
    /// </summary>
    private List<SelectItemModel> DeviceTypeDataItemList
    {
        get
        {
            if (SelectDeviceType == null)
            {
                return new List<SelectItemModel>();
            }
            else
            {
                return SelectDeviceType.DataItemList.Select(m => new SelectItemModel { Label = m.ShowName, Value = m.Key }).ToList();
            }

        }
    }
    /// <summary>
    /// 选中的设备类型的数据项(虚拟设备)
    /// </summary>
    private DataItem DeviceTypeDataItem
    {
        get
        {
            if (string.IsNullOrEmpty(VirtualDeviceMappingModal.VirtualDataItem))
            {
                return null;
            }
            else
            {
                var item = SelectDeviceType.DataItemList.FirstOrDefault(m => m.Key == VirtualDeviceMappingModal.VirtualDataItem);

                return item;
            }

        }
    }

    // <summary>
    /// 选中的数据项的映射(虚拟设备)
    /// </summary>
    private List<SelectItemModel> SelectDeviceEnumValues
    {
        get
        {
            if (string.IsNullOrEmpty(VirtualDeviceMappingModal.VirtualDataItem))
            {
                return new List<SelectItemModel>();
            }
            else
            {
                var item = SelectDeviceType.DataItemList.FirstOrDefault(m => m.Key == VirtualDeviceMappingModal.VirtualDataItem);
                if (item != null && item.EnumValues != null)
                {
                    return item.EnumValues.Select( m => new SelectItemModel { Label = m.ShowName,Value = m.Value.ToString() }).ToList();
                }

                return new List<SelectItemModel>();
            }

        }
    }

    /// <summary>
    /// 真实设备列表
    /// </summary>
    public List<DeviceModel> ReadDevices { get; set; } = new List<DeviceModel>();

    /// <summary>
    /// 真实设备列表
    /// </summary>
    private List<SelectItemModel> ReadDeviceList
    {
        get
        {
            return ReadDevices.Select(m => new SelectItemModel { Label = m.Name, Value = m.Id }).ToList();
        }
    }
    /// <summary>
    /// 选中的设备类型的数据项列表(真实映射设备)
    /// </summary>
    private List<DataItem>ReadDeviceTypeDataItemList
    {
        get
        {
            if (string.IsNullOrEmpty(VirtualDeviceMappingModal.RealDeviceId))
            {
                return new List<DataItem>();
            }
            else
            {
                var readDevice = ReadDevices.FirstOrDefault(m => m.Id == VirtualDeviceMappingModal.RealDeviceId);
                var typeId = readDevice.TypeId;
                return DeviceTypes.FirstOrDefault(m => m.Id == typeId).DataItemList;
            }

        }
    }

    /// <summary>
    /// 选中的设备类型的数据项列表(真实映射设备)
    /// </summary>
    private List<SelectItemModel> ReadDeviceTypeDataItemList2
    {
        get
        {
            if (ReadDeviceTypeDataItemList.Count == 0)
            {
                return new List<SelectItemModel>();
            }
            else
            {
                return ReadDeviceTypeDataItemList.Select(m => new SelectItemModel { Label = m.ShowName, Value = m.Key }).ToList();
            }

        }
    }
    /// <summary>
    /// 选中的设备类型的数据项列表(真实映射设备)
    /// </summary>
    private List<DataItem> ReadDeviceTypeDataItemList3
    {
        get
        {
            if (string.IsNullOrEmpty(ReadDeviceId))
            {
                return new List<DataItem>();
            }
            else
            {
                var readDevice = ReadDevices.FirstOrDefault(m => m.Id == ReadDeviceId);
                var typeId = readDevice.TypeId;
                return DeviceTypes.FirstOrDefault(m => m.Id == typeId).DataItemList;
            }

        }
    }

    /// <summary>
    /// 选中的设备类型的数据项列表(真实映射设备)
    /// </summary>
    private List<SelectItemModel> ReadDeviceTypeDataItemList4
    {
        get
        {
            if (ReadDeviceTypeDataItemList3.Count == 0)
            {
                return new List<SelectItemModel>();
            }
            else
            {
                return ReadDeviceTypeDataItemList3.Select(m => new SelectItemModel { Label = m.ShowName, Value = m.Key }).ToList();
            }

        }
    }

    public class SelectItemModel2
    {
        /// <summary>
        /// 显示文本
        /// </summary>
        public string? Label { get; set; }
        /// <summary>
        /// 下拉值
        /// </summary>
        public RWType? Value { get; set; }
    }
    private List<SelectItemModel2> RWTypeList = new List<SelectItemModel2> { 
        new SelectItemModel2 { Label = "只读", Value = RWType.ReadOnly },
        new SelectItemModel2 { Label = "可读可写", Value = RWType.ReadWrite },
        new SelectItemModel2 { Label = "只写", Value = RWType.WriteOnly }
    };

    /// <summary>
    /// 获取真实设备名称
    /// </summary>
    /// <returns></returns>
    private string GetReadDeviceName(string id)
    {
        var item = ReadDevices.FirstOrDefault(m => m.Id == id);
        if(item != null)
        {
            return item.Name;
        }
        else
        {
            return id;
        }
    }

    /// <summary>
    /// 弹窗映射初始值
    /// </summary>
    private VirtualDeviceMapping VirtualDeviceMappingModal = new VirtualDeviceMapping();

    /// <summary>
    /// 批量修改映射真实设备ID
    /// </summary>
    private string ReadDeviceId { get; set; }
    /// <summary>
    /// 批量修改映射数据项
    /// </summary>
    private string ReadDeviceItemData { get; set; }

    /// <summary>
    /// 设备类型列表
    /// </summary>
    /// <returns></returns>
    private async Task GetDeviceTypes()
    {
        var res = await state.GetAsJsonAsync<List<DeviceTypeModel>>($"/api/v2/IoT/VirtualDevice/GetDeviceTypes");
        if (res.Success)
        {
            DeviceTypes = res.Data;
            StateHasChanged();
        }
    }
    /// <summary>
    /// 真实设备列表
    /// </summary>
    /// <returns></returns>
    private async Task GetReadDevices()
    {
        var res = await state.GetAsJsonAsync<List<DeviceModel>>($"/api/v2/IoT/VirtualDevice/GetReadDevices");
        if (res.Success)
        {
            ReadDevices = res.Data;
            StateHasChanged();
        }
    }
    /// <summary>
    /// 获取详情
    /// </summary>
    /// <returns></returns>
    private async Task GetDetail()
    {
        if (!string.IsNullOrEmpty(Id) && Id != "01")
        {
            var res = await state.GetAsJsonAsync<DataModel<DeviceModel>>($"/api/v2/IoT/VirtualDevice/GetDeviceDetail?id={Id}");
            if (res.Success && res.Data != null)
            {
                Device = res.Data;

                if (!string.IsNullOrEmpty(isCopy) && isCopy == "true")
                {
                    Device.Id = null;
                    Device.Data.Id = null;
                    Device.Data.Name = Device.Data.Name+"副本";
                }

                StateHasChanged();
            }
        }
    }
    /// <summary>
    /// 选择虚拟数据项
    /// </summary>
    private void OnSelectedItemChangedVirtualDataItem(SelectItemModel value)    
    {
        if (value != null && VirtualDeviceMappingModal.VirtualDataItem != value.Value)
        {
           @* VirtualDeviceMappingModal.DataMappings = null;
            VirtualDeviceMappingModal.Coefficient = null;
            StateHasChanged();*@
        }      

    }
    /// <summary>
    /// 选择虚拟数据项
    /// </summary>
    private void OnChangedVirtualDataItem(string value)
    {
        VirtualDeviceMappingModal.VirtualDataItem = value;
        VirtualDeviceMappingModal.DataMappings = null;
        VirtualDeviceMappingModal.Coefficient = null;
        StateHasChanged();
    }
    /// <summary>
    /// 自动映射开/关
    /// </summary>
    /// <param name="value"></param>
    private void HandleChangeAutoMapping(bool value)
    {
        VirtualDeviceMappingModal.DataMappings = null;
        StateHasChanged();
    }

    /// <summary>
    /// 提交数据映射弹窗
    /// </summary>
    private void HandleMappingOk()
    {
        if (string.IsNullOrEmpty(VirtualDeviceMappingModal.VirtualDataItem))
        {
            _message.Error("请选择虚拟数据项");
            return;
        }
        if (string.IsNullOrEmpty(VirtualDeviceMappingModal.RealDeviceId))
        {
            _message.Error("请选择映射设备");
            return;
        }
        if (string.IsNullOrEmpty(VirtualDeviceMappingModal.RealDataItem))
        {
            _message.Error("请选择映射数据项");
            return;
        }

        if (DeviceMappingIndex != null)
        {
            //编辑
            var index = (int)DeviceMappingIndex;
            Device.Data.VirtualDeviceMappings[index] = VirtualDeviceMappingModal;
        }
        else
        {
            if (Device.Data.VirtualDeviceMappings != null)
            {
                Device.Data.VirtualDeviceMappings.Add(VirtualDeviceMappingModal);
            }
            else
            {
                Device.Data.VirtualDeviceMappings = new List<VirtualDeviceMapping> { VirtualDeviceMappingModal };
            }
        }       
        checkedList = new List<string>();
        this.HandleMappingCancel();
    }
    /// <summary>
    /// 取消数据映射弹窗
    /// </summary>
    private void HandleMappingCancel()
    {
        _visible = false;
        DeviceMappingIndex = null;
        VirtualDeviceMappingModal = new VirtualDeviceMapping();

        StateHasChanged();
    }
    /// <summary>
    /// 显示数据映射弹窗
    /// </summary>
    private void HandleMappingShow()
    {

        if (string.IsNullOrEmpty(Device.Data.TypeId))
        {
            _message.Error("请先选择数据类型");
            return;
        }
        VirtualDeviceMappingModal = new VirtualDeviceMapping();
        _visible = true;
        StateHasChanged();
    }
    /// <summary>
    /// 显示批量修改数据映射弹窗
    /// </summary>
    private void HandleMappingShowBatch()
    {
        if(checkedList.Count == 0)
        {
            _message.Error("至少选择一个数据映射项");
            return;
        }
        _visiblebBatch = true;
        ReadDeviceId = null;
        ReadDeviceItemData = null;
        StateHasChanged();
    }
    /// <summary>
    /// 批量修改数据提交
    /// </summary>
    private void HandleMappingOkBatch()
    {
        if (string.IsNullOrEmpty(ReadDeviceId))
        {
            _message.Error("请选择映射设备");
            return;
        }
        @*if (string.IsNullOrEmpty(ReadDeviceItemData))
        {
            _message.Error("请选择映射数据项");
            return;
        }*@
        foreach (var item in Device.Data.VirtualDeviceMappings)
        {
            var val = item.VirtualDataItem + item.RealDeviceId + item.RealDataItem;
            if (checkedList.Contains(val))
            {
                item.RealDeviceId = ReadDeviceId;
                //item.RealDataItem = ReadDeviceItemData;
            }
        }
        checkedList = new List<string>();
        HandleMappingCancelBatch();

    }
    /// <summary>
    /// 取消批量修改数据映射弹窗
    /// </summary>
    private void HandleMappingCancelBatch()
    {
        _visiblebBatch = false;
        ReadDeviceId = null;
        ReadDeviceItemData = null;
        StateHasChanged();
    }
    /// <summary>
    /// 编辑数据映射弹窗
    /// </summary>
    private void HandleMappingEdit(int index, VirtualDeviceMapping model)
    {
        DeviceMappingIndex = index;
        VirtualDeviceMappingModal = model;
        _visible = true;
        StateHasChanged();
    }
    /// <summary>
    /// 移除数据映射
    /// </summary>
    /// <param name="index"></param>
    /// <param name="model"></param>
    private void HandleMappingDel(int index, VirtualDeviceMapping model)
    {
        Device.Data.VirtualDeviceMappings.Remove(model);
        checkedList = new List<string>();
        StateHasChanged();
    }

    /// <summary>
    /// 添加映射
    /// </summary>
    private void  handleAddDataMappings()
    {
        if (VirtualDeviceMappingModal.DataMappings != null)
        {
            VirtualDeviceMappingModal.DataMappings.Add(new VirtualDeviceDataMapping());
        }
        else
        {
            VirtualDeviceMappingModal.DataMappings = new List<VirtualDeviceDataMapping>{new VirtualDeviceDataMapping()};
        }
        StateHasChanged();
    }
    /// <summary>
    /// 移除映射
    /// </summary>
    /// <param name="index"></param>
    private void handleDelDataMappings(VirtualDeviceDataMapping val)
    {
        VirtualDeviceMappingModal.DataMappings.Remove(val);
        StateHasChanged();
    }
    private void handleChangeRWType(RWType e,int index)    
    {
        Console.WriteLine(e);
    }
    /// <summary>
    /// 向上移动
    /// </summary>
    private void handleMoveUp()    
    {
        
    }

    protected override async void OnInitialized()
    {
        await GetDeviceTypes();
        await GetReadDevices();
        await GetDetail();
        base.OnInitialized();
    }
    /// <summary>
    /// 返回
    /// </summary>
    private void HandeleCancel()
    {
        state.NavigationManager.NavigateTo("/IoT/VirtualDevice/List");
    }
    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    private async Task HandeleSubmit()
    {
        if (string.IsNullOrEmpty(Device.Id) || isCopy == "true")
        {
            //新增   
            var res = await state.PostAsJsonAsync<DataModel<DeviceModel>>($"/api/v2/IoT/VirtualDevice/Add", Device);
            if (res.Success)
            {
                _ = _message.Success("保存成功");
                HandeleCancel();
            }
        }
        else
        {
            //编辑
            var res = await state.PostAsJsonAsync<DataModel<DeviceModel>>($"/api/v2/IoT/VirtualDevice/Edit", Device);
            if (res.Success)
            {
                _ = _message.Success("保存成功");
                HandeleCancel();
            }
        }
    }
}
