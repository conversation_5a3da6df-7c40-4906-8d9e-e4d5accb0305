﻿using HX.HRV.Shared.Pages;
using HX.HRV.Web.Services;
using Makaretu.Dns;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web
{
    public class Module : BaseModule
    {
        public override string Name { get; set; } = "心率变异性系统";
        public override string Url { get; set; } = "/client/login";
        public override string Icon { get; set; } = "iconfont icon-admin";
        public override string Description { get; set; } = "";
        public override string Override { get; set; } = "心率变异性系统共享";
        public override async void Configure(IServiceCollection services)
        {
            ConfigureItems.Add(new ConfigureItem
            {
                Name = "心率变异性系统",
                Order = 901,
                Services = async (services) =>
                {
                    ByteAnalysisServices.AnalysisData();
                    services.AddHostedService<GenerateReportService>();
                    services.AddHostedService<GenerateEmotionReportService>();
                    services.AddHostedService<FileExportService>();
                    
                    
                    await AlgWebSocketClient.InitAsync();
                    BlazorApp.SetModuleLayout("心率变异性系统", typeof(HRVDefaultLayout));
      //              _=Task.Run(() =>
      //              {
	                    
	     //               // string hostName = Dns.GetHostName();
	     //               // IPAddress[] ipAddresses = Dns.GetHostAddresses(hostName);
	     //               // foreach (IPAddress ipAddress in ipAddresses)
	     //               // {
		    //               //  Console.WriteLine(ipAddress.ToString());
	     //               // }
	                    
	     //               NetworkInterface[] networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
	     //               foreach (NetworkInterface networkInterface in networkInterfaces)
	     //               {
		    //                IPInterfaceProperties ipInterfaceProperties = networkInterface.GetIPProperties();
		    //                IPAddress[] ipAddresses = ipInterfaceProperties.UnicastAddresses.Select(ua => ua.Address).ToArray();
		    //                foreach (IPAddress ipAddress in ipAddresses)
		    //                {
			   //                 Console.WriteLine(ipAddress.ToString());
		    //                }
	     //               }
	                    
	     //               LogTool.Logger.Information($"开始获取网卡");
      //                  var ips = new List<IPAddress>();
						//foreach (NetworkInterface networkInterface in NetworkInterface.GetAllNetworkInterfaces())
						//{
						//	过滤未启用的网卡或虚拟网卡（如 Docker、VMware）
						//	 if (networkInterface.OperationalStatus != OperationalStatus.Up ||
						//	 	networkInterface.NetworkInterfaceType == NetworkInterfaceType.Loopback ||
						//	 	networkInterface.NetworkInterfaceType == NetworkInterfaceType.Tunnel ||
						//	 	networkInterface.NetworkInterfaceType == NetworkInterfaceType.VeryHighSpeedDsl)
						//	{
						//		continue;
						//	}

						//	// 获取网卡的 IP 配置
						//	IPInterfaceProperties ipProperties = networkInterface.GetIPProperties();
						//	foreach (UnicastIPAddressInformation ip in ipProperties.UnicastAddresses)
						//	{
						//		LogTool.Logger.Information($"IP地址：{ip.Address}");
						//		if (ip.Address.AddressFamily == AddressFamily.InterNetwork &&
						//			!ip.Address.IsIPv6LinkLocal &&
						//			!IPAddress.IsLoopback(ip.Address)
						//			&&ip.Address.ToString().StartsWith("192.168"))
						//		{
						//			ips.Add(ip.Address);
						//			LogTool.Logger.Information($"IP地址：{ip.Address}");
						//		}
						//	} 

						//}

						//if (ips.Count <=0)
						//{
						//	LogTool.Logger.Information($"未发现IP地址");
      //                      return;
						//}
      //                  var bindIp = ips[0];
						//var mdns = new MulticastService() { UseIpv6 = false };
      //                  var sd = new ServiceDiscovery(mdns);
      //                  //医疗版
      //                  var ms = new ServiceProfile("ms", "_hrv.huixin._tcp", 5219,
      //                      addresses: new List<IPAddress>
      //                      {
						//	   bindIp
						//	});
      //                  sd.Advertise(ms);
      //                  mdns.Start();
      //              });
                }
            });
        }
    }
}