﻿using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using HX.HRV.Shared.Models;
using Masa.Blazor;
using ReactiveUI;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;

public class PatientListViewModel : ReactiveObject
{
    private readonly StateService _stateService;


    private ObservableCollection<PatientModel> _patientList;

    public ObservableCollection<PatientModel> PatientList
    {
        get => _patientList;
        set => this.RaiseAndSetIfChanged(ref _patientList, value);
    }

    private int _total;

    public int Total
    {
        get => _total;
        set => this.RaiseAndSetIfChanged(ref _total, value);
    }

    public string Name { get; set; } = "被试编号";


    private bool _loading;

    public bool Loading
    {
        get => _loading;
        set => this.RaiseAndSetIfChanged(ref _loading, value);
    }


    private bool _isShowAddPatient;

    public bool IsShowAddPatient
    {
        get => _isShowAddPatient;
        set => this.RaiseAndSetIfChanged(ref _isShowAddPatient, value);
    }


    private PatientModel _queryModel;

    public PatientModel QueryModel
    {
        get => _queryModel;
        set => this.RaiseAndSetIfChanged(ref _queryModel, value);
    }


    private DataOptions _options;

    public DataOptions Options
    {
        get => _options;
        set => this.RaiseAndSetIfChanged(ref _options, value);
    }

    public ReactiveCommand<Unit, Unit> SearchCommand { get; }
    public ReactiveCommand<Unit, Unit> CloseAddDialogCommand { get; }


    private bool _isShowRecordList;

    public bool IsShowRecordList
    {
        get => _isShowRecordList;
        set => this.RaiseAndSetIfChanged(ref _isShowRecordList, value);
    }
    
    private string _clickedId;
    public string ClickedId
    {
        get => _clickedId;
        set => this.RaiseAndSetIfChanged(ref _clickedId, value);
    }
    
    
    
    public PatientListViewModel(StateService stateService)
    {
        QueryModel = new PatientModel();
        Options = new(1, 5);
        _stateService = stateService ?? throw new ArgumentNullException(nameof(stateService));
        SearchCommand = ReactiveCommand.CreateFromTask(ExecuteSearchAsync);
        CloseAddDialogCommand = ReactiveCommand.CreateFromTask(() =>
        {
            IsShowAddPatient = false;
            DialogData = new();
            return Task.CompletedTask;
        });

        SetOptionsCommand = ReactiveCommand.CreateFromTask<DataOptions>((op) =>
        {
            Options = new DataOptions(op.Page, op.ItemsPerPage);
            return Task.CompletedTask;
        });
        DeleteCommand = ReactiveCommand.CreateFromTask<string>(DeleteExecute);
        ConfirmDeleteInteraction = new Interaction<string, bool>();
        ShowSnackbarInteraction = new Interaction<(string, bool), Unit>();
        OpenAddDialogCommand = ReactiveCommand.CreateFromTask(() =>
        {
            IsShowAddPatient = true;
            return Task.CompletedTask;
        });
        SearchCommand.ThrownExceptions
            .Subscribe(ex => { Console.WriteLine($"Search failed: {ex.Message}"); })
            ;
        SearchCommand.IsExecuting
            .BindTo(this, x => x.Loading)
            ; 
        
        OpenAddDialogCommand = ReactiveCommand.Create(() =>
        {
            PatientDialogTitle = "新增被试";
            DialogData = new PatientModel();
            IsShowAddPatient = true;
        });

        OpenEditDialogCommand = ReactiveCommand.Create<PatientModel>(patient =>
        {
            PatientDialogTitle = "编辑被试";
            DialogData = patient;
            IsShowAddPatient = true;
        });


        OpenRecordListCommand = ReactiveCommand.Create<string>(id =>
        {
            ClickedId = id;
            IsShowRecordList = true;
        });
        
        
        this.WhenAnyValue(x => x.Options)
            .Skip(1) 
            .Where(options => options is { Page: > 0 })
            .Do(m => Console.WriteLine("PatientListViewModel.Options - A real change occurred!")) // 您可以修改日志来确认
            .Throttle(TimeSpan.FromMilliseconds(100))
            .Select(_ => Unit.Default)
            .InvokeCommand(SearchCommand)
           ;
        this.WhenAnyValue(x => x.IsShowAddPatient)
            .Where(isShow => !isShow)
            .Select(_ => Unit.Default)
            .InvokeCommand(SearchCommand);
    }

    public ReactiveCommand<string, Unit> OpenRecordListCommand { get; set; }

    public ReactiveCommand<PatientModel, Unit> OpenEditDialogCommand { get; set; }

    public PatientModel DialogData { get; set; }

    public string PatientDialogTitle { get; set; }

    public ReactiveCommand<string, Unit> DeleteCommand { get; }

    public Interaction<(string, bool), Unit> ShowSnackbarInteraction { get; }

    public Interaction<string, bool> ConfirmDeleteInteraction { get; }

    public ReactiveCommand<DataOptions, Unit> SetOptionsCommand { get; }

    public ReactiveCommand<Unit, Unit> OpenAddDialogCommand { get; }

    private async Task ExecuteSearchAsync()
    {
        var queryParam = new Dictionary<string, string>
        {
            { nameof(QueryModel.Age), QueryModel.Age.ToString() },
            { nameof(QueryModel.Name), QueryModel.Name },
            { nameof(QueryModel.CardId), QueryModel.CardId },
            { nameof(QueryModel.Source), QueryModel.Source },
            { nameof(QueryModel.OutpatientNumberString), QueryModel.OutpatientNumberString },
            { nameof(QueryModel.Sex), QueryModel.Sex.ToString() },
            { "page", Options?.Page.ToString() },
            { "pageSize", Options?.ItemsPerPage.ToString() }
        };
        var result = await _stateService
            .GetAsJsonAsync<List<DataModel<PatientModel>>>("/api/v2.0/HRV_HX/Patient/GetPatientList", queryParam);
        PatientList =
            new ObservableCollection<PatientModel>(result.Data?.Select(m => m.Data) ??
                                                   Enumerable.Empty<PatientModel>());
        Total = result.Page?.TotalCount ?? 0;
        Console.WriteLine("PatientListViewModel.ExecuteSearchAsync");
    }

    private async Task DeleteExecute(string id)
    {
        var confirmed = await ConfirmDeleteInteraction.Handle("删除后将无法恢复，确认删除吗?");
        if (!confirmed) return;
        var res = await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/HRV_HX/Patient/Delete?id={id}");
        if (res.Success && res.Data)
        {
            await ShowSnackbarInteraction.Handle(("删除成功！", true));
            await ExecuteSearchAsync();
        }
        else
        {
            await ShowSnackbarInteraction.Handle(("删除失败！", false));
        }
    }


    public ViewModelActivator Activator { get; } = new ViewModelActivator();
}