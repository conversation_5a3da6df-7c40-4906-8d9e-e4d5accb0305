﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using System.Threading.Tasks;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Services;

namespace UFU.IoT.Areas.IoT.Controllers.V1
{
    /// <summary>
    /// 数据管理
    /// </summary>
    [Area("IoT")]
    [EnableCors("any")]
    public class DatasController : Controller
    {
        private readonly DataRepository _context;
        private readonly CoreDbContext _coreContext;
        private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

        /// <summary>
        /// 数据管理
        /// </summary>
        /// <param name="context"></param>
        /// <param name="coreContext"></param>
        public DatasController(DataRepository context, CoreDbContext coreContext)
        {
            _context = context;
            _coreContext = coreContext;

            //支持中文编码
            OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
            //使用PascalCase格式
            OptionsIgnoreNull.PropertyNamingPolicy = null;
            //忽略null值
            OptionsIgnoreNull.IgnoreNullValues = true;
        }

        /// <summary>
        /// 设备最新数据重置
        /// </summary>
        /// <param name="deviceId"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("数据管理接口/重置设备最新数据", IsAPI = true, AllowAllUser = true)]
        public async Task ResetLatestData(string deviceId)
        {
            var data = await _context.Query<LatestDataModel>().Where(m => m.Data.DeviceId == deviceId).FirstOrDefaultAsync();
            if (data != null)
            {
                data.Data.GroupData["Data"] = new System.Collections.Concurrent.ConcurrentDictionary<string, LatestDataItem>();
                _context.Update(data);
                await _context.SaveChangesAsync();

                //更新缓存
                if (IoTService.Devices.TryGetValue(deviceId, out var connectDevice) && connectDevice?.Device?.LatestData != null)
                {
                    connectDevice.Device.LatestData.GroupData["Data"] = data.Data.Data;
                }
            }


        }
        /// <summary>
        /// 数据管理接口/最新数据
        /// </summary>
        /// <param name="placeId">场地编号</param>
        /// <param name="typeId">类型编号</param>
        /// <param name="deviceIds">设备编号,逗号隔开</param>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="count">最大数据条数(不超过10000)</param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("数据管理接口/最新数据", IsAPI = true, AllowAllUser = true)]
        public async Task<IActionResult> Latest(string placeId, string typeId, string deviceIds, DateTime? start, DateTime? end, int count, int? page, int? pageSize = null, string orderBy = null)
        {
            UserInfo userInfo = ViewBag.User;
            //筛选出有权限的场地或者关联的用户场地
            //List<string> organs = await OrganService.GetMyOrganIdsAsync(userInfo, null, null, _coreContext);



            var allDeviceIds = deviceIds.Split(",", StringSplitOptions.RemoveEmptyEntries);
            var query = _context.Query<DataModel>().Where(m => allDeviceIds.Contains(m.Data.DeviceId));
            if (!string.IsNullOrWhiteSpace(typeId))
            {
                query = query.Where(m => m.Data.TypeId == typeId);
            }
            if (!string.IsNullOrWhiteSpace(deviceIds))
            {
                string[] ids = deviceIds.Split(',');
                query = query.Where(m => ids.Contains(m.Data.DeviceId));
            }
            if (start.HasValue || end.HasValue)
            {
                if (start.HasValue && (end < start || !end.HasValue))
                {
                    end = start.Value.AddHours(24);
                }
                else if (end.HasValue && (start > end || !start.HasValue))
                {
                    start = end.Value.AddHours(-24);
                }
            }
            else
            {
                end = DateTime.Now;
                start = end.Value.AddDays(-1);
            }
            query = query.Where(m => m.Data.Time >= start && m.Data.Time <= end);

            if (count > 10000)
            {
                count = 10000;
            }
            if (count < 0)
            {
                count = 1;
            }
            var result = await query.OrderByDescending(model => model.Data.Time).Take(count).ToPageListAsync(page, pageSize, orderBy);
            result.AddPageInfo(Response);
            return Json(result, OptionsIgnoreNull);
        }

        /// <summary>
        /// 数据管理接口/历史数据
        /// </summary>
        /// <param name="placeId">场地编号</param>
        /// <param name="typeId">设备类型编号</param>
        /// <param name="deviceIds">设备编号,逗号隔开</param>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("数据管理接口/历史数据", IsAPI = true, AllowAllUser = true)]
        public async Task<IActionResult> History(string placeId, string typeId, string deviceIds, DateTime? start, DateTime? end, int? page, int? pageSize = null, string orderBy = null)
        {

            UserInfo userInfo = ViewBag.User;
            //筛选出有权限的场地或者关联的用户场地
                                                                            //List<string> organs = await OrganService.GetMyOrganIdsAsync(userInfo, null, null, _coreContext);
            var query = _context.Query<DataModel>().AsNoTracking();
            if (!string.IsNullOrWhiteSpace(typeId))
            {
                query = query.Where(m => m.Data.TypeId == typeId);
            }
            if (!string.IsNullOrWhiteSpace(deviceIds))
            {
                string[] ids = deviceIds.Split(',');
                query = query.Where(m => ids.Contains(m.Data.DeviceId));
            }
            if (start.HasValue || end.HasValue)
            {
                if (start.HasValue && (end < start || !end.HasValue))
                {
                    end = start.Value.AddDays(1);
                }
                else if (end.HasValue && (start > end || !start.HasValue))
                {
                    start = end.Value.AddDays(-1);
                }
            }
            else
            {
                end = DateTime.Now;
                start = end.Value.AddDays(-1);
            }
            query = query.Where(m => m.Data.Time >= start && m.Data.Time <= end);

            var list = await query.OrderByDescending(model => model.Id).ToPageListAsync(page, pageSize, orderBy);
            list.AddPageInfo(Response);
            return Json(list, OptionsIgnoreNull);
        }

        /// <summary>
        /// 统计数据计算接口/计算状态
        /// </summary>
        /// <param name="deviceId">设备编号</param>
        /// <param name="start">数据开始时间</param>
        /// <param name="end">数据结束数据</param>
        /// <param name="cycle">统计周期</param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("统计数据计算接口/计算任务列表", IsAPI = true)]
        public PageList<DataStatisticsTask> StatisticsTaskList(string deviceId, DateTime? start, DateTime? end, int? cycle, int? page, int? pageSize = null, string orderBy = null)
        {
            var query = DataAnalysisService.DataStatisticsTasks.Values.AsQueryable();
            if (!string.IsNullOrWhiteSpace(deviceId))
            {
                query = query.Where(m => m.DeviceId.Contains(deviceId));
            }
            if (start != null)
            {
                query = query.Where(m => m.CycleStartTime >= start);
            }
            if (end != null)
            {
                query = query.Where(m => m.CycleStartTime <= end);
            }
            if (cycle != null)
            {
                query = query.Where(m => m.Cycle == cycle);
            }
            var list = query.OrderBy(model => model.CycleStartTime).ToPageList(page, pageSize, orderBy).AddPageInfo(Response);
            return list;
        }

        /// <summary>
        /// 统计数据计算接口/清除
        /// </summary>
        /// <param name="deviceId">设备编号</param>
        /// <param name="start">数据开始时间</param>
        /// <param name="end">数据结束数据</param>
        /// <param name="cycle">统计周期</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("统计数据计算接口/清除计算任务", IsAPI = true)]
        public IActionResult StatisticsTaskClear(string deviceId, DateTime? start, DateTime? end, int? cycle)
        {
            var query = DataAnalysisService.DataStatisticsTasks.AsQueryable();
            if (!string.IsNullOrWhiteSpace(deviceId))
            {
                query = query.Where(m => m.Value.DeviceId.Contains(deviceId));
            }
            if (start != null)
            {
                query = query.Where(m => m.Value.CycleStartTime >= start);
            }
            if (end != null)
            {
                query = query.Where(m => m.Value.CycleStartTime <= end);
            }
            if (cycle != null)
            {
                query = query.Where(m => m.Value.Cycle == cycle);
            }
            var keys = query.Select(m => m.Key);
            foreach (var item in keys)
            {
                DataAnalysisService.DataStatisticsTasks.TryRemove(item, out var data);
            }
            return Ok();
        }
    }
}