@import '../../style/themes/index';
@import '../../style/mixins/index';

@skeleton-prefix-cls: ~'@{ant-prefix}-skeleton';
@skeleton-avatar-prefix-cls: ~'@{skeleton-prefix-cls}-avatar';
@skeleton-title-prefix-cls: ~'@{skeleton-prefix-cls}-title';
@skeleton-paragraph-prefix-cls: ~'@{skeleton-prefix-cls}-paragraph';
@skeleton-button-prefix-cls: ~'@{skeleton-prefix-cls}-button';
@skeleton-input-prefix-cls: ~'@{skeleton-prefix-cls}-input';
@skeleton-image-prefix-cls: ~'@{skeleton-prefix-cls}-image';
@skeleton-block-radius: @border-radius-base;

.@{skeleton-prefix-cls} {
  display: table;
  width: 100%;

  &-header {
    display: table-cell;
    padding-right: @padding-md;
    vertical-align: top;

    // Avatar
    .@{skeleton-avatar-prefix-cls} {
      .skeleton-element-avatar();
    }
  }

  &-content {
    display: table-cell;
    width: 100%;
    vertical-align: top;

    // Title
    .@{skeleton-title-prefix-cls} {
      width: 100%;
      height: @skeleton-title-height;
      background: @skeleton-color;
      border-radius: @skeleton-block-radius;

      + .@{skeleton-paragraph-prefix-cls} {
        margin-top: @skeleton-title-paragraph-margin-top;
      }
    }

    // paragraph
    .@{skeleton-paragraph-prefix-cls} {
      padding: 0;

      > li {
        width: 100%;
        height: @skeleton-paragraph-li-height;
        list-style: none;
        background: @skeleton-color;
        border-radius: @skeleton-block-radius;

        &:last-child:not(:first-child):not(:nth-child(2)) {
          width: 61%;
        }

        + li {
          margin-top: @skeleton-paragraph-li-margin-top;
        }
      }
    }
  }

  &-with-avatar &-content {
    // Title
    .@{skeleton-title-prefix-cls} {
      margin-top: @margin-sm;

      + .@{skeleton-paragraph-prefix-cls} {
        margin-top: @skeleton-paragraph-margin-top;
      }
    }
  }

  &-round &-content {
    .@{skeleton-title-prefix-cls},
    .@{skeleton-paragraph-prefix-cls} > li {
      border-radius: 100px;
    }
  }

  // With active animation
  &-active {
    .@{skeleton-title-prefix-cls},
    .@{skeleton-paragraph-prefix-cls} > li,
    .@{skeleton-avatar-prefix-cls},
    .@{skeleton-button-prefix-cls},
    .@{skeleton-input-prefix-cls},
    .@{skeleton-image-prefix-cls} {
      .skeleton-color();
    }
  }

  // Skeleton Block Button, Input
  &.@{skeleton-prefix-cls}-block {
    width: 100%;

    .@{skeleton-button-prefix-cls} {
      width: 100%;
    }

    .@{skeleton-input-prefix-cls} {
      width: 100%;
    }
  }

  // Skeleton element
  &-element {
    display: inline-block;
    width: auto;

    .@{skeleton-button-prefix-cls} {
      .skeleton-element-button();
    }

    .@{skeleton-avatar-prefix-cls} {
      .skeleton-element-avatar();
    }

    .@{skeleton-input-prefix-cls} {
      .skeleton-element-input();
    }

    .@{skeleton-image-prefix-cls} {
      .skeleton-element-image();
    }
  }
}
// Button
.skeleton-element-button() {
  display: inline-block;
  vertical-align: top;
  background: @skeleton-color;
  border-radius: @border-radius-base;

  .skeleton-element-button-size(@btn-height-base);

  &-lg {
    .skeleton-element-button-size(@btn-height-lg);
  }

  &-sm {
    .skeleton-element-button-size(@btn-height-sm);
  }
}
// Avatar
.skeleton-element-avatar() {
  display: inline-block;
  vertical-align: top;
  background: @skeleton-color;

  .skeleton-element-avatar-size(@avatar-size-base);

  &-lg {
    .skeleton-element-avatar-size(@avatar-size-lg);
  }

  &-sm {
    .skeleton-element-avatar-size(@avatar-size-sm);
  }
}

// Input
.skeleton-element-input() {
  display: inline-block;
  vertical-align: top;
  background: @skeleton-color;

  .skeleton-element-input-size(@input-height-base);

  &-lg {
    .skeleton-element-input-size(@input-height-lg);
  }

  &-sm {
    .skeleton-element-input-size(@input-height-sm);
  }
}

// Image
.skeleton-element-image() {
  display: flex;
  align-items: center;
  justify-content: center;
  vertical-align: top;
  background: @skeleton-color;

  .skeleton-element-image-size(@image-size-base*2);

  &-path {
    fill: #bfbfbf;
  }

  &-svg {
    .skeleton-element-image-size(@image-size-base);
    max-width: @image-size-base * 4;
    max-height: @image-size-base * 4;
  }
}

.skeleton-element-avatar-size(@size) {
  width: @size;
  .skeleton-element-common-size(@size);

  &.@{skeleton-avatar-prefix-cls}-circle {
    border-radius: 50%;
  }
}

.skeleton-element-button-size(@size) {
  width: @size * 2;
  min-width: @size * 2;
  .skeleton-element-common-size(@size);

  &.@{skeleton-button-prefix-cls}-square {
    width: @size;
    min-width: @size;
  }

  &.@{skeleton-button-prefix-cls}-circle {
    width: @size;
    min-width: @size;
    border-radius: 50%;
  }

  &.@{skeleton-button-prefix-cls}-round {
    border-radius: @size;
  }
}

.skeleton-element-input-size(@size) {
  width: @size * 5;
  min-width: @size * 5;
  .skeleton-element-common-size(@size);
}

.skeleton-element-image-size(@size) {
  width: @size;
  .skeleton-element-common-size(@size);

  &.@{skeleton-image-prefix-cls}-circle {
    border-radius: 50%;
  }
}

.skeleton-element-common-size(@size) {
  height: @size;
  line-height: @size;
}

.skeleton-color() {
  position: relative;
  // fix https://github.com/ant-design/ant-design/issues/36444
  // https://monshin.github.io/202109/css/safari-border-radius-overflow-hidden/
  /* stylelint-disable-next-line property-no-vendor-prefix,value-no-vendor-prefix */
  z-index: 0;
  overflow: hidden;
  background: transparent;

  &::after {
    position: absolute;
    top: 0;
    right: -150%;
    bottom: 0;
    left: -150%;
    background: linear-gradient(
      90deg,
      @skeleton-color 25%,
      @skeleton-to-color 37%,
      @skeleton-color 63%
    );
    animation: ~'@{skeleton-prefix-cls}-loading' 1.4s ease infinite;
    content: '';
  }
}

@keyframes ~"@{skeleton-prefix-cls}-loading" {
  0% {
    transform: translateX(-37.5%);
  }

  100% {
    transform: translateX(37.5%);
  }
}

@import './rtl';
