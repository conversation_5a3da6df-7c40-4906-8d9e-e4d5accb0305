{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["D7ra44TbRYrGnK4PL9F7iybHSeQZi41yWV5T+8uf1+Q=", "DNUT/6saQmGlSCUzgrVOoTFOOsVoNKdKw/mOYNOFDIA=", "7yIWrFRybBK2dTt6mTH2pWAf1A+CHyIUx5svdLJN8ao=", "ujPuO1z51GmxMm6ooww3tOjXrUsPF/RHxG2kFpM27RI=", "Mlh5VOL0seaqySkMqokH7Dtyf0w9hRgeiYi82YbZstc=", "xCcu7E2iFVOv05UbsutB4OXTCMhrLaPUHuoEQTzuQ84=", "6GGEPu5XDFMSbVyK8LKsaFgaQ2k1s2HL2xZrfOZAi5Q=", "FKWcNWqqSWWm5+TplucPxuzFRW/VD9pWplD3+s469J0=", "8qhi+Tlpk1MPkUkwxYBYgkfQRKT+XdPVOiw0k/5GyH0=", "Asn0iuBzaF+YykjkMCS3L8Dzuf9W6baLZ741SVLQowc=", "d5aPTXhndDTil1qa7PRv9L04KT8V2KQd+i5EbWBYwg4=", "I7mm3AasGLpEt7zbRnkklxbeAI0vBJwGnkxwg6dVAak=", "SszESJharpISJqgKq576LTDUZFoDDa0s8BpmFl2Ircc=", "pkXNcsD8GRDdTIiTN/bl56XhJJMi2PiQG/8wNp5gN14=", "ObnGoeYdu5ODUbJ6CCFI85giEfx4sxY8PU0L4R5RVGI=", "FY4wqYf8gsuo/oXOTSqZxDmmMn3p/XUdA6tAhwRZuRA=", "WwhtAF9nStEqUEV9t2TKUdMCmD+3QcuQPkXIegIWQS4=", "EuKmCqb1hP7CY4IBFZfegp2LCySiTc/Jpkk/ARuVoVg=", "+vTxKgicZbgF7Y4pO1LJt89h3P6R1pYUzWQsJzLAtuk=", "eiG10veelErwm333mFe55A8detx4OgtUH0A0cKIGBmg=", "lY8XBjNB9vVuxFTjGcxQviNLL4PJ52P8m21SgeyJFBA=", "6Ufy7V2o1Kjc0DaijPEl/P5lKeiXeS3NjG1dWLUwqxY=", "6DPDKauty5HiDEieW4sGgJBs6QNt0PekwDW2Hub48+Q=", "vkTtbnRfLCdz5kujMQKtR1E4BJC6fYHc17kJJoz3syQ=", "93yPePExbJhFZiJZVRaXEPc4KSisHjU3Odrnx2LAwyg=", "NVVFGG0kcLNiytZWKoLTrHH0cTM5bi94sE8FKX4lapc=", "PLP+0tSJ7stP9VcdUZ0dT7unGg02wTlOv56qP8yQkP8=", "n18W4J8dZgeEkEfSdjh6LHfeaEyBNRVYC1fwCcredWg=", "YpdguIIER3LjPhRbfreoeF/th0dtxmcsquoVahIvPsE=", "B8ZKUygLsAOgru/MlFf1fLC3w7E+lstYkXRDkiW6Eq8=", "T03LaXOwLAOgDnZ36JvzH8yEzw1MtcMsbj1wFaa7xSo=", "tKZTp2FWn+kRJxv7Tqnm3088woYSWVV3Npi42iTWXyI=", "GrLOVSsq9HUO+v2cPCNfFpcoNa82gxWspKzzfSmQaUo=", "NXuzfEldhSSZ9AYP4VdWm7QtPDcLdENDJJJ0kP4R5sE=", "KaE1iBx/vxVsaNVaw8blOSJ7Ka0HwO+oM4Jw0r+MokQ=", "GYEI1x/2amXAMvInd620U1o3WTNDiEhaYefxGX4T3RI=", "OaO1l+vdWmuU65O55vpho+BPXGznvpUD3Z9oLjM9XbA=", "KscN+QlFyix2atMKqhSpwjJKgKIeXTwJpt0JF6awmDY=", "nn6vrxnv5I2UvyhdQankvv5Pjvb7+iZBPS9nidbKPdU=", "s0IteB+17t+aSN3zLj14Y+LP4T1U/qSpygezKwU+Yz4=", "T0EqWsPjE8BSy0Tt83rN7ZF3lTI7auVyCMb1UrHSKZg=", "p0mZukUVisRo6OB58kqilGWxLZOJWkNls6ridZ3kYU8=", "FYeCGXZTsVew716ZBzvwC9YVN4rDVF+8H1GK35+KdMo=", "eWQtJSuubrC2TtiyRLFGaZhQkAD18on0JnlJy78pBHg=", "yvPwyGuTo6AxMUNilWXuzOGMFNJIMNCtij4S1e/aPRc="], "CachedAssets": {"D7ra44TbRYrGnK4PL9F7iybHSeQZi41yWV5T+8uf1+Q=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\6q525j3tlr-less8k5dp3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.aliyun.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e402i885od", "Integrity": "4mBwQ/MqaMqYqTYb2HFPmMMltxsnU8sHyzi7sq+P5n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.css", "FileLength": 75127, "LastWriteTime": "2025-08-05T09:14:58.8759728+00:00"}, "DNUT/6saQmGlSCUzgrVOoTFOOsVoNKdKw/mOYNOFDIA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\34blflduah-vhh2jufqs3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.aliyun.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfry0ibibp", "Integrity": "YXftugZCMhIfHCBMODujtXIpw3wgk4M+NelcU37rZYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.min.css", "FileLength": 71500, "LastWriteTime": "2025-08-05T09:14:58.8710649+00:00"}, "7yIWrFRybBK2dTt6mTH2pWAf1A+CHyIUx5svdLJN8ao=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\1zsqth3rcz-x67401ppqt.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.compact.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0c5yxzlw3", "Integrity": "8T0v0lIgEXCx0/8hv5A4IOc3DJG1hrSfxrIYfDFbKPM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.css", "FileLength": 75109, "LastWriteTime": "2025-08-05T09:14:58.8715944+00:00"}, "ujPuO1z51GmxMm6ooww3tOjXrUsPF/RHxG2kFpM27RI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\vyboxlqsxg-82f7tjxk3h.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.compact.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8w5f9zha1b", "Integrity": "aI8eX8SgkFjsvq09UBplk+Oz2beVaNyFxzQY9IjAOwM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.min.css", "FileLength": 71608, "LastWriteTime": "2025-08-05T09:14:58.8754397+00:00"}, "Mlh5VOL0seaqySkMqokH7Dtyf0w9hRgeiYi82YbZstc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\5ozbnmqx4c-less8k5dp3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e402i885od", "Integrity": "4mBwQ/MqaMqYqTYb2HFPmMMltxsnU8sHyzi7sq+P5n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.css", "FileLength": 75127, "LastWriteTime": "2025-08-05T09:14:58.8691612+00:00"}, "xCcu7E2iFVOv05UbsutB4OXTCMhrLaPUHuoEQTzuQ84=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\sqjvzvz5ns-0i9c2pqcbz.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f73vv6kcvr", "Integrity": "+7hT3MIQEl4CF/AwyoUxvp1BGa56glRh+VZ3Obj9XIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.css", "FileLength": 76595, "LastWriteTime": "2025-08-05T09:14:58.8773246+00:00"}, "6GGEPu5XDFMSbVyK8LKsaFgaQ2k1s2HL2xZrfOZAi5Q=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\zb5s6wfjbh-l551dbsw1z.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.dark.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ha881xsv8", "Integrity": "9VwzILe8TJfU+97Qu7p2EgzMtbaAMV3/IRjPcV4VC9I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.min.css", "FileLength": 73010, "LastWriteTime": "2025-08-05T09:14:58.8073301+00:00"}, "FKWcNWqqSWWm5+TplucPxuzFRW/VD9pWplD3+s469J0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\vivduzjdd9-vhh2jufqs3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfry0ibibp", "Integrity": "YXftugZCMhIfHCBMODujtXIpw3wgk4M+NelcU37rZYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.min.css", "FileLength": 71500, "LastWriteTime": "2025-08-05T09:14:58.8676768+00:00"}, "8qhi+Tlpk1MPkUkwxYBYgkfQRKT+XdPVOiw0k/5GyH0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\qwz8zeekkt-koqihchfaq.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.variable.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "atlazl7hc1", "Integrity": "7rPj7qxQWVl+Rd3Xro7bn4U/VJdDRoxTT9ouP0L0gIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.css", "FileLength": 75707, "LastWriteTime": "2025-08-05T09:14:58.8811123+00:00"}, "Asn0iuBzaF+YykjkMCS3L8Dzuf9W6baLZ741SVLQowc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\s2vzjyofgz-pvc8ota2an.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.variable.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9sxdqkycsq", "Integrity": "t8l3Yu1GR6nXUHCp8c8aX4BA70UOGbOvC0rHtpEznc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.min.css", "FileLength": 72059, "LastWriteTime": "2025-08-05T09:14:58.8681823+00:00"}, "d5aPTXhndDTil1qa7PRv9L04KT8V2KQd+i5EbWBYwg4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\1xpyc92csh-2jzru2q90w.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "js/ant-design-blazor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktyn9s7183", "Integrity": "+KaSx0H99B28TLdnTl+uqeU2PhlSUf0jb37ZnvY5SLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js", "FileLength": 20880, "LastWriteTime": "2025-08-05T09:14:58.8469948+00:00"}, "I7mm3AasGLpEt7zbRnkklxbeAI0vBJwGnkxwg6dVAak=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\9prptnwetl-islnrq0c31.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "js/ant-design-blazor.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4scx56kee", "Integrity": "opGGUImPQDDEVCzW3cI4J2Z5t3xVcLTlicJ8Hos8mcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js.map", "FileLength": 69608, "LastWriteTime": "2025-08-05T09:14:58.8671715+00:00"}, "SszESJharpISJqgKq576LTDUZFoDDa0s8BpmFl2Ircc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\7ftge31yl6-rnlyxgo5ri.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "css/masa-blazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\css\\masa-blazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "455w9za9rg", "Integrity": "+CjM5jnt+ynk2vn9ShZ13cfyLf20rRqcew48PUX/HqM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\css\\masa-blazor.min.css", "FileLength": 68230, "LastWriteTime": "2025-08-05T09:14:58.8058253+00:00"}, "pkXNcsD8GRDdTIiTN/bl56XhJJMi2PiQG/8wNp5gN14=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\tzep23ns6m-ggo88y3uma.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/EventType-63cda6c3.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\EventType-63cda6c3.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k0tt3a5qfs", "Integrity": "3Kzm28ujq+2FSLX3BMZjM6My00XT/veGRaEVOqxJ07c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\EventType-63cda6c3.js", "FileLength": 674, "LastWriteTime": "2025-08-05T09:14:58.8562339+00:00"}, "ObnGoeYdu5ODUbJ6CCFI85giEfx4sxY8PU0L4R5RVGI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\gntkt48ehx-hmh33oi7ee.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/helper-6d386307.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\helper-6d386307.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ry6ls2vdr", "Integrity": "cfp5KyvmmFJFa8dc5jPLW4FWwc8+JhvkwuoRVeDYYfQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\helper-6d386307.js", "FileLength": 893, "LastWriteTime": "2025-08-05T09:14:58.8475048+00:00"}, "FY4wqYf8gsuo/oXOTSqZxDmmMn3p/XUdA6tAhwRZuRA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\b77ayb31m4-yvkv6cxmvi.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/index-cef005e4.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\index-cef005e4.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9d6mbeufpk", "Integrity": "vUwBTnedlwtDCKJhp7M0xSiNAINfGCpMCVnuMz9ueF8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\index-cef005e4.js", "FileLength": 262, "LastWriteTime": "2025-08-05T09:14:58.8537225+00:00"}, "WwhtAF9nStEqUEV9t2TKUdMCmD+3QcuQPkXIegIWQS4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\u85skt7njs-c85iknmnh6.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/touch-5a32c5ea.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\touch-5a32c5ea.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mfw1wthdd", "Integrity": "8Lei0I5bJOnMMBnZF4uHm59a9J3p63mVyRFqTGsROh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\touch-5a32c5ea.js", "FileLength": 817, "LastWriteTime": "2025-08-05T09:14:58.8592524+00:00"}, "EuKmCqb1hP7CY4IBFZfegp2LCySiTc/Jpkk/ARuVoVg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\98s27ux9m1-n1s3lxfdbi.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/tslib.es6-68144fbe.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\tslib.es6-68144fbe.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "blpraqr6js", "Integrity": "T9ynExXpyDNiXTONHuaB6eqhVCUr82wxiYztR9cb9V4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\tslib.es6-68144fbe.js", "FileLength": 668, "LastWriteTime": "2025-08-05T09:14:58.8592524+00:00"}, "+vTxKgicZbgF7Y4pO1LJt89h3P6R1pYUzWQsJzLAtuk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\xi6ba9wok3-y1br3qhvs0.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/input/index-b92d32d0.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\input\\index-b92d32d0.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ysmcnyzp66", "Integrity": "cq1BwAcYbNxkjeR5o5QS4i9myL/p2gbLanClVLFQdqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\input\\index-b92d32d0.js", "FileLength": 959, "LastWriteTime": "2025-08-05T09:14:58.7936859+00:00"}, "eiG10veelErwm333mFe55A8detx4OgtUH0A0cKIGBmg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\y33gtmb7m2-3eyeiubupr.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/navigation-drawer/touch-47e3a6be.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\navigation-drawer\\touch-47e3a6be.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wd0h0sayy4", "Integrity": "zNPsrNk2zv26qiptYybmPnr7DeuoDLB3m6GqdLbAOFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\navigation-drawer\\touch-47e3a6be.js", "FileLength": 1323, "LastWriteTime": "2025-08-05T09:14:58.8426778+00:00"}, "lY8XBjNB9vVuxFTjGcxQviNLL4PJ52P8m21SgeyJFBA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\ypc3d9zz7v-ex5yhgml6j.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/overlay/scroll-strategy-dc362cab.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\overlay\\scroll-strategy-dc362cab.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23rtf25e8y", "Integrity": "XrXdCa1CQeIUGmkUA919xGyXkb1WaUDXTPJJpAx+zNg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\overlay\\scroll-strategy-dc362cab.js", "FileLength": 911, "LastWriteTime": "2025-08-05T09:14:58.8426778+00:00"}, "6Ufy7V2o1Kjc0DaijPEl/P5lKeiXeS3NjG1dWLUwqxY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\vl9avoc1ur-ftbbm70hxr.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/page-stack/index-3d21987e.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\index-3d21987e.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rjro896mc5", "Integrity": "d01iPvO0VEYkKZNovQSok151B5/79S5h4YfSTXmpeHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\index-3d21987e.js", "FileLength": 858, "LastWriteTime": "2025-08-05T09:14:58.8442+00:00"}, "6DPDKauty5HiDEieW4sGgJBs6QNt0PekwDW2Hub48+Q=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\jsex7tijp6-suojt78vcy.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/page-stack/touch-ecbec91c.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\touch-ecbec91c.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "25kgip8y45", "Integrity": "CuDHBtWJcxjKb5rRkvvElLMK1UNOcwfZxaYjHUmruRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\touch-ecbec91c.js", "FileLength": 1236, "LastWriteTime": "2025-08-05T09:14:58.8403279+00:00"}, "vkTtbnRfLCdz5kujMQKtR1E4BJC6fYHc17kJJoz3syQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\j9ep1wbucs-b97pdmzs4h.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/scroll-to-target/index-1c14c8ac.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\scroll-to-target\\index-1c14c8ac.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0zqct6d9qr", "Integrity": "jDjazV2/z9YsW+k0eXv2gVBAVu6/GWoa4zszcKjNN6E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\scroll-to-target\\index-1c14c8ac.js", "FileLength": 646, "LastWriteTime": "2025-08-05T09:14:58.8431842+00:00"}, "93yPePExbJhFZiJZVRaXEPc4KSisHjU3Odrnx2LAwyg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\3pkgqoc23x-4e7su8ls4e.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/transition/index-339f8848.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\transition\\index-339f8848.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xa69rj864q", "Integrity": "11Dx/zDK1wqOfNdG6SdOhnTuQiTEP0hdrIPFiWeHGTo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\transition\\index-339f8848.js", "FileLength": 535, "LastWriteTime": "2025-08-05T09:14:58.8469948+00:00"}, "NVVFGG0kcLNiytZWKoLTrHH0cTM5bi94sE8FKX4lapc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\3v9sscxtw3-apt2gw0td9.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/window/touch-f9d2ba92.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\window\\touch-f9d2ba92.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8lbtn87yfj", "Integrity": "FJmPLVM/LvWVrEFZlHtvWYNLfvzws9pcp1JAFF6hDCk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\window\\touch-f9d2ba92.js", "FileLength": 778, "LastWriteTime": "2025-08-05T09:14:58.8442+00:00"}, "PLP+0tSJ7stP9VcdUZ0dT7unGg02wTlOv56qP8yQkP8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\zp1f6whp0t-oye2tnu4h3.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/manifest.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "id3hozlj8z", "Integrity": "qkApSCQzAEaLgT3YThD/ZiFdBmBkyH5l8d7Z6pJCeuE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\manifest.json", "FileLength": 303, "LastWriteTime": "2025-08-05T09:14:58.7936859+00:00"}, "n18W4J8dZgeEkEfSdjh6LHfeaEyBNRVYC1fwCcredWg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\s682nmenk5-hoaxe64hrl.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/masa-blazor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\masa-blazor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cs2dspasl3", "Integrity": "XI+X7DR/0bdqR/Sx/H0ajA2Fva9TiU+vxVqGJl+iXcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\masa-blazor.js", "FileLength": 11243, "LastWriteTime": "2025-08-05T09:14:58.8442+00:00"}, "YpdguIIER3LjPhRbfreoeF/th0dtxmcsquoVahIvPsE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\hve6ogtdoi-cy2ms3wvwg.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/activatable/index-82cb7376.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\activatable\\index-82cb7376.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mruxdhhzkg", "Integrity": "AvsAKkuQD4RUCTELqVQrXlKKjwX5an9kOAzx+qf8lbQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\activatable\\index-82cb7376.js", "FileLength": 1107, "LastWriteTime": "2025-08-05T09:14:58.8431842+00:00"}, "B8ZKUygLsAOgru/MlFf1fLC3w7E+lstYkXRDkiW6Eq8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\j1jr3esqjc-hrfj977lv2.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/intersect/index-f360c115.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\intersect\\index-f360c115.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qcbwbfaf9e", "Integrity": "eui9pF+IOIo0dxsca7AcSr5OMqM0rxa6JZSfWcNU9EA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\intersect\\index-f360c115.js", "FileLength": 832, "LastWriteTime": "2025-08-05T09:14:58.8442+00:00"}, "T03LaXOwLAOgDnZ36JvzH8yEzw1MtcMsbj1wFaa7xSo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\xp8j0y02uu-4x1zod59k2.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/outside-click/index-47d0ce8d.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\outside-click\\index-47d0ce8d.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cyikztnmig", "Integrity": "9CNqMXMC+gNsEApcIF7kFe0hM3ODIHE/uvHMOw/8svw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\outside-click\\index-47d0ce8d.js", "FileLength": 477, "LastWriteTime": "2025-08-05T09:14:58.8475048+00:00"}, "tKZTp2FWn+kRJxv7Tqnm3088woYSWVV3Npi42iTWXyI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\jbtdqpyjb0-pv848xjna4.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/resize/index-07a0c3f6.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\resize\\index-07a0c3f6.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgwkr94iae", "Integrity": "r58+lK9sRpuQIKwCN2j+E2z4+nfGZX50Mzlgw+vJt8w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\resize\\index-07a0c3f6.js", "FileLength": 625, "LastWriteTime": "2025-08-05T09:14:58.8442+00:00"}, "GrLOVSsq9HUO+v2cPCNfFpcoNa82gxWspKzzfSmQaUo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\96ra3xu8vv-6tqei36j6p.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/baidumap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\baidumap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "29vmszk5jx", "Integrity": "SjAy1Wpt/hGvnNlW0wc7X1dAi+aY3zn9CJJaUWVezWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\baidumap.js", "FileLength": 1367, "LastWriteTime": "2025-08-05T09:14:58.8469948+00:00"}, "NXuzfEldhSSZ9AYP4VdWm7QtPDcLdENDJJJ0kP4R5sE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\55df3tpy1w-ta6451bhu3.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/drawflow-proxy.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\drawflow-proxy.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2kco54gs04", "Integrity": "kgqs1qBmtng2MrgA7/CKRzI+Y9ZL+wKrXO1Z8TiPNOw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\drawflow-proxy.js", "FileLength": 1059, "LastWriteTime": "2025-08-05T09:14:58.8453522+00:00"}, "KaE1iBx/vxVsaNVaw8blOSJ7Ka0HwO+oM4Jw0r+MokQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\s9f7hgutfm-uyjj58hnqg.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/echarts.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\echarts.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "djlglgpaj5", "Integrity": "96mBMLNeg8qyHL0wHj39KpDyT7FVhsKJIwY6aaq6mXs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\echarts.js", "FileLength": 827, "LastWriteTime": "2025-08-05T09:14:58.8414826+00:00"}, "GYEI1x/2amXAMvInd620U1o3WTNDiEhaYefxGX4T3RI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\srs9i3hsfa-u9u24q7mqc.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/monaco-editor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\monaco-editor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "djwsqql9jw", "Integrity": "uSUzep4ZTZpcg4Vj/cBM9EgDKvX8/EAi5X8e2GXMbxw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\monaco-editor.js", "FileLength": 442, "LastWriteTime": "2025-08-05T09:14:58.8431842+00:00"}, "OaO1l+vdWmuU65O55vpho+BPXGznvpUD3Z9oLjM9XbA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\ypalxvgq99-466423quza.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/quill/quill-helper.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\quill\\quill-helper.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i2uxsfwtxn", "Integrity": "hnGeO4xEzzjjJWtMcULdk7rbfBsoP2Z/Yhg7+n45Czg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\quill\\quill-helper.js", "FileLength": 2174, "LastWriteTime": "2025-08-05T09:14:58.8442+00:00"}, "KscN+QlFyix2atMKqhSpwjJKgKIeXTwJpt0JF6awmDY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\hphvn5lkuo-6jrkf04sja.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/sortable.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\sortable.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zj6jkca6b", "Integrity": "oa1VpY5nlEYLa+ydPDbVVOCbKyJhZN2LMIeuEgoBVHY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\sortable.js", "FileLength": 13145, "LastWriteTime": "2025-08-05T09:14:58.8469948+00:00"}, "nn6vrxnv5I2UvyhdQankvv5Pjvb7+iZBPS9nidbKPdU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\m9sr6dry09-2mtw4tr229.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/vditor/vditor-helper.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\vditor\\vditor-helper.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hll8ofix8h", "Integrity": "q8xRbxT9J7u4qu+DRb79V6/nV443sYJUsGxsONBEEyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\vditor\\vditor-helper.js", "FileLength": 835, "LastWriteTime": "2025-08-05T09:14:58.8453522+00:00"}, "s0IteB+17t+aSN3zLj14Y+LP4T1U/qSpygezKwU+Yz4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\rxxrt4efv6-xex8mncx4b.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "Presets/EllipsisText/EllipsisText.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\Presets\\EllipsisText\\EllipsisText.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o81ulju45i", "Integrity": "yKJDVltm+2o2KHwm6zT8qLZ+Z+6vKInNfXsKscm3r/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\Presets\\EllipsisText\\EllipsisText.razor.js", "FileLength": 191, "LastWriteTime": "2025-08-05T09:14:58.8459871+00:00"}, "T0EqWsPjE8BSy0Tt83rN7ZF3lTI7auVyCMb1UrHSKZg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\vz9ebaqy17-umtkqu2ib7.gz", "SourceId": "HX.Experiment.MAUI", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=umtkqu2ib7}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z91pbg30fq", "Integrity": "ty3q6cShC+0c4wwzsYmoue82vf1PufPlSVtelCSF8rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\css\\app.css", "FileLength": 1520, "LastWriteTime": "2025-08-05T09:14:58.8459871+00:00"}, "p0mZukUVisRo6OB58kqilGWxLZOJWkNls6ridZ3kYU8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\8286u7uofv-61n19gt1b8.gz", "SourceId": "HX.Experiment.MAUI", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lir1p715ud", "Integrity": "jcY0RXFOlcp4VNTCcYQtWVZZBlr+ZY+AOv6/0S5dnF8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\favicon.ico", "FileLength": 2432, "LastWriteTime": "2025-08-05T09:14:58.8469948+00:00"}, "FYeCGXZTsVew716ZBzvwC9YVN4rDVF+8H1GK35+KdMo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\4ay3ztng19-q06nhyzej9.gz", "SourceId": "HX.Experiment.MAUI", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "images/check-circle-outline#[.{fingerprint=q06nhyzej9}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\images\\check-circle-outline.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rqiee5gl7s", "Integrity": "nWdmvNm7ma+0v4ADYhJ3I/KHPUbCsVwqtZhaRLIE1ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\images\\check-circle-outline.svg", "FileLength": 186, "LastWriteTime": "2025-08-05T09:14:58.8459871+00:00"}, "eWQtJSuubrC2TtiyRLFGaZhQkAD18on0JnlJy78pBHg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\gart0pz622-l7rw681l30.gz", "SourceId": "HX.Experiment.MAUI", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=l7rw681l30}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mit8w7ybed", "Integrity": "kjz80XosWRLFUqo367tXlmQju1z1m5/Mp1OxSYd5/uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\wwwroot\\index.html", "FileLength": 729, "LastWriteTime": "2025-08-05T09:14:58.8527155+00:00"}, "yvPwyGuTo6AxMUNilWXuzOGMFNJIMNCtij4S1e/aPRc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\cehasc43bm-5vcnc68aiq.gz", "SourceId": "HX.Experiment.MAUI", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "HX.Experiment.MAUI#[.{fingerprint=5vcnc68aiq}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\scopedcss\\bundle\\HX.Experiment.MAUI.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nm2i8wt94p", "Integrity": "z3ULiFzgTSAkKdaHvgr5QpKDf7b+OWFNOtELx3shmJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.MAUI\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\scopedcss\\bundle\\HX.Experiment.MAUI.styles.css", "FileLength": 141, "LastWriteTime": "2025-08-05T09:14:58.8459871+00:00"}}, "CachedCopyCandidates": {}}