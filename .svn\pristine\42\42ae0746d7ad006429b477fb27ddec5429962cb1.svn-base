﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Pages.Client.Components
@using HX.HRV.Shared.Pages.Client.Dialog
@using Masa.Blazor
@using UFU.CoreFX.Models
@using UFU.IoT.Shared.Utils
@inherits HRV.Shared.Pages.Client.Dialog.PatientDialog
<style>
    .dialog-button.confirm {
        width: 300px;
        background: linear-gradient(0deg, #008ef7, #1e9fff);
        box-shadow: 0px 8px 16px 0px rgb(48 138 239 / 83%);
        border-radius: 28px;
        color: white;
        font-size: 2rem;
    }

    .dialog-button.cancel {
        width: 300px;
        border-radius: 28px;
        box-shadow: 0px 8px 16px 0px rgb(177 213 253 / 83%);
        font-size: 2rem;
    }

    .customer-input .m-input__prepend-outer .m-label {
        width: 140px;
        font-size: 1.25rem;
    }

    .patient-dialog .m-text-field__slot {
        font-size: 1.625rem;
    }
    .patient-dialog .customer-text {
          font-size: 1.625rem;
        font-weight: 400;
        position: unset !important;
        color: black;
      }
</style>

<MForm Model="PatientModel" Context="selectedDialogContext">
    <MRow>
        <MCardText Class="d-flex justify-center flex-column align-center">
            <MForm Model="PatientModel">
                <MRow Class="mt-2">
                    @if (IsOnlyAdd)
                    {
                        
                        <MCol Cols="6">
                            <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                        @bind-Value="PatientModel.OutpatientNumberString">
                                <PrependContent>
                                    <MLabel >被试编号:<label style="color:red">*</label></MLabel>
                                </PrependContent>
                            </MTextField>
                        </MCol>
                    }
                    else
                    {
                        <MCol Cols="6">
                            <MAutocomplete
                                Class="customer-input"
                                TItem="PatientModel"
                                TItemValue="string"
                                TValue="string"
                                OnChange="s => PatientModel.OutpatientNumberString = s"
                                Items="@Entries"
                                Dense
                                Solo
                                Chips
                                Clearable
                                Loading="_isIdCardLoading"
                                OnSelect="(item) => { PatientModel = item.Item; }"
                                OnSearchInputUpdate="UpdateSearchOptionInputAsync"
                                @bind-Value="PatientModel.OutpatientNumberString"
                                ItemText="r => { return r.Name + ' ' + r.OutpatientNumberString; }"
                                ItemValue="r => r.OutpatientNumberString">
                                <PrependContent>
                                    <MLabel Class="mr-2">被试编号:<label style="color:red">*</label></MLabel>
                                </PrependContent>
                                <SelectionContent Context="ItemContext">
                                    @ItemContext.Item.OutpatientNumberString
                                </SelectionContent>
                                <LabelContent>
                                    <MLabel Class="customer-text"> @PatientModel.OutpatientNumberString</MLabel>
                                </LabelContent>
                            </MAutocomplete>
                        </MCol>
                    }
                    <MCol Cols="6">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Name">
                            <PrependContent>
                                <MLabel Class="mr-2">姓名:<label style="color:red">*</label></MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                   
                    <MCol Cols="6">
                        <MTextField Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.CardId">
                            <PrependContent>
                                <MLabel Class="mr-2">身份证号:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="4">
                        <MSelect Disabled="IsDisable" Class="customer-input" Id="sex" Items="Sexes"
                                 @bind-Value="@PatientModel.Sex"
                                 ItemText="u => u.ToString()"
                                 SingleLine
                                 Clearable="true"
                                 Dense
                                 ItemValue="u => u" Solo>
                            <PrependContent>
                                <MLabel Class="mr-2">性别:</MLabel>
                            </PrependContent>
                        </MSelect>
                    </MCol>
                    <MCol Cols="4">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Age">
                            <PrependContent>
                                <MLabel Class="mr-2">年龄:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="4">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Height">
                            <PrependContent>
                                <MLabel Class="mr-2">身高(CM):</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="4">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Weight">
                            <PrependContent>
                                <MLabel Class="mr-2">体重(KG):</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="4">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Email">
                            <PrependContent>
                                <MLabel Class="mr-2">邮箱:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="4">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.PhoneNumber">
                            <PrependContent>
                                <MLabel Class="mr-2">电话:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                    <MCol Cols="4">
                        <MSelect Disabled="IsDisable" Class="customer-input"  Items="EducationalLevels"
                                 @bind-Value="@PatientModel.EducationalLevel"
                                 ItemText="u =>  u.GetDisplayName()"
                                 SingleLine
                                 Clearable="true"
                                 Dense
                                 ItemValue="u => u" Solo>
                            <PrependContent>
                                <MLabel Class="mr-2">受教育程度:</MLabel>
                            </PrependContent>
                        </MSelect>
                    </MCol>
                    <MCol Cols="12" Class="d-flex  justify-start align-center">
                        <MTextField Disabled="IsDisable" Class="customer-input" Dense Solo
                                    @bind-Value="PatientModel.Work">
                            <PrependContent>
                                <MLabel Class="mr-2">工作:</MLabel>
                            </PrependContent>
                        </MTextField>
                    </MCol>
                     <MCol Cols="12" Class="d-flex  justify-start align-start">
                        <MLabel Class="mr-2" Style="width: 120px;text-align: end;font-size: 1.25rem;">其他说明:</MLabel>
                        <MTextarea
                                   Dense
                                   Solo
                                   NoResize
                                   Placeholder="输入其他说明"
                                   @bind-Value="@PatientModel.Description"
                                   Rows="7">
                        </MTextarea>
                    </MCol>
                    @if (!IsOnlyAdd)
                    {
                        <MCol Cols="12" Class="d-flex  justify-start align-center">
                            <MLabel Class="mr-2" Style="width: 120px;text-align: end;font-size: 1.25rem;">检测时长:</MLabel>
                            <HRVCheckTimeButton CheckTimeValue="@CheckTimeValue"
                                                CheckTimeValueChanged="@(value=> CheckTimeValue = value)"
                                                >
                            </HRVCheckTimeButton>
                        </MCol>
                    }
                </MRow>
            </MForm>
        </MCardText>
    </MRow>
    <MRow Justify="JustifyTypes.Center">
        <MCol Cols="4">
            <MButton Height="56" Class="dialog-button cancel" Color="#fff" OnClick="CancelAsync">取消</MButton>
        </MCol>
        <MCol Cols="4">
            <MButton Height="56" Class="dialog-button confirm" OnClick="HandleSubmit">
                确认
            </MButton>
        </MCol>
    </MRow>
</MForm>
@if (!IsOnlyAdd&&SelectedDialog)
{
    <MDialog Width="500" @bind-Value="SelectedDialog"    Persistent="true">
        <ChildContent>
            <SelectedDeviceDialog DeviceId="@DeviceStatusData.Device.Id"
                                  PatientModel="PatientModel"
                                  CheckTimeValue="@CheckTimeValue"
                                  CloseDialogEvent="() => { 
                                      SelectedDialog = false;
                                      CloseDialog.InvokeAsync();  
                                      PatientModel=new PatientModel();
                                      CheckTimeValue=0;}">
            </SelectedDeviceDialog>
        </ChildContent>
    </MDialog>
}

@code {
    private EducationalLevel[] EducationalLevels => Enum.GetValues<EducationalLevel>();
  
    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    public new  async  Task HandleSubmit()
    {
        try
        {
            Console.WriteLine("提交保存");
            if (string.IsNullOrEmpty(PatientModel.OutpatientNumberString))
            {
                await PopupService.EnqueueSnackbarAsync("被试编号不能为空", AlertTypes.Error);
                return;
            }

            if (string.IsNullOrEmpty(PatientModel.Name))
            {
                await PopupService.EnqueueSnackbarAsync("姓名不能为空", AlertTypes.Error);
                return;
            }

            if (!IsOnlyAdd && CheckTimeValue <= 0)
            {
                await PopupService.EnqueueSnackbarAsync("检测时长不能为空", AlertTypes.Error);
                return;
            }

            if (!IsOnlyAdd && PatientModel.Id != null)
            {
                var isExist = DeviceDataList?.Any(m => m.RecordModel?.Id == PatientModel.Id) ?? false;
                if (isExist)
                {
                    await PopupService.EnqueueSnackbarAsync("该患者已经在检测中", AlertTypes.Error);
                    return;
                }
            }

            var result = await InternalHttpClientService.AddOrEditPatientAsync(PatientModel);
            if (IsOnlyAdd)
            {
                if (result.Success)
                {
                    await PopupService.EnqueueSnackbarAsync("保存成功！", AlertTypes.Success);
                    await CloseDialog.InvokeAsync();
                }

                if (!result.Success && !string.IsNullOrEmpty(result.Message))
                {
                    await PopupService.EnqueueSnackbarAsync(result.Message, AlertTypes.Error);
                    return;
                }
            }

            if (result.Success)
            {
                PatientModel = result.Data?.Data;
                SelectedDialog = true;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(" 提交保存失败{0}", e.StackTrace?.ToString());
            throw;
        }
    }
    
    public async Task UpdateSearchOptionInputAsync(string val)
    {
        _isIdCardLoading = true;
        var result = await StateService
            .GetAsJsonAsync<List<DataModel<PatientModel>>>("/api/v2.0/HRV_HX/Patient/GetPatientList",
                new Dictionary<string, string>()
                {
                    {
                        nameof(PatientModel.OutpatientNumberString), val
                    }
                });
        Entries = result.Data?.Select(m => m.Data)?.ToList();
        _isIdCardLoading = false;
    }
}