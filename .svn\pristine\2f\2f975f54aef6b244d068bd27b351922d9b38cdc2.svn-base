{"Files": [{"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\airkiss.html", "PackagePath": "staticwebassets\\airkiss.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.html", "PackagePath": "staticwebassets\\index.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.min.html", "PackagePath": "staticwebassets\\index.min.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\iotmsg.html", "PackagePath": "staticwebassets\\iotmsg.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfig301.html", "PackagePath": "staticwebassets\\netconfig301.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfigindex.html", "PackagePath": "staticwebassets\\netconfigindex.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\websocket.html", "PackagePath": "staticwebassets\\websocket.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.html", "PackagePath": "staticwebassets\\wificonfig.html"}, {"Id": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.min.html", "PackagePath": "staticwebassets\\wificonfig.min.html"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.UFU.IoT.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.UFU.IoT.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.UFU.IoT.props", "PackagePath": "build\\UFU.IoT.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.UFU.IoT.props", "PackagePath": "buildMultiTargeting\\UFU.IoT.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.UFU.IoT.props", "PackagePath": "buildTransitive\\UFU.IoT.props"}], "ElementsToRemove": []}