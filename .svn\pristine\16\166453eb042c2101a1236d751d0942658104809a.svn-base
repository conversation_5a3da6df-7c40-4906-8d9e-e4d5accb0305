{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "Microsoft.AspNetCore.Authentication": "Warning"
      }
    }
  },
  "AllowedHosts": "*",

  "Cors": {
    "Origins": "http://localhost:8080",
    "AllowCredentials": true
  },
  "Permission": {
    "EnableDataPermission": true,
    "EnableFunctionPermission": true,
    "EnableVersionPermission": false
  },
  "ConnectionStrings": {
    "DefaultDbContext": "User ID=postgres;Password=*******************;Host=cc.linglongtu.cn;Port=7432;Database=LingLongTu;Pooling=true;"
  },
  "Redis": {
    "Enable": false,
    "ConnectionString": "127.0.0.1:6379,password=xBarvRgVJEEmBbe2"
  },
  "Blazor": {
    "Enable": true,
    "Layout": "_HRVLayout",
    "RenderMode": "Server" //WebAssembly, WebAssemblyPrerendered, Server, ServerPrerendered, Auto
    // "RenderMode": "WebAssembly" //WebAssembly, WebAssemblyPrerendered, Server, ServerPrerendered, Auto
  },
  "DefaultUrl": {
    "Home": "/",
    "Login": "/Core/Auth/Login",
    "Register": "/",
    "Domain": "https://linglongtu.ufutech.com"
    //"Domain": "https://linglongtu.ufutech.com:6002"
  },
  "MQTT": {
    "Port": 5168
  }
}
