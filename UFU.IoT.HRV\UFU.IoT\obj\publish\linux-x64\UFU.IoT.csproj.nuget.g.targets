﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.runtime.compilerservices.unsafe\6.0.0\buildTransitive\netcoreapp2.0\System.Runtime.CompilerServices.Unsafe.targets" Condition="Exists('$(NuGetPackageRoot)system.runtime.compilerservices.unsafe\6.0.0\buildTransitive\netcoreapp2.0\System.Runtime.CompilerServices.Unsafe.targets')" />
    <Import Project="$(NuGetPackageRoot)system.text.encoding.codepages\6.0.0\buildTransitive\netcoreapp2.0\System.Text.Encoding.CodePages.targets" Condition="Exists('$(NuGetPackageRoot)system.text.encoding.codepages\6.0.0\buildTransitive\netcoreapp2.0\System.Text.Encoding.CodePages.targets')" />
    <Import Project="$(NuGetPackageRoot)system.security.accesscontrol\6.0.0\buildTransitive\netcoreapp2.0\System.Security.AccessControl.targets" Condition="Exists('$(NuGetPackageRoot)system.security.accesscontrol\6.0.0\buildTransitive\netcoreapp2.0\System.Security.AccessControl.targets')" />
    <Import Project="$(NuGetPackageRoot)system.security.permissions\6.0.0\buildTransitive\netcoreapp2.0\System.Security.Permissions.targets" Condition="Exists('$(NuGetPackageRoot)system.security.permissions\6.0.0\buildTransitive\netcoreapp2.0\System.Security.Permissions.targets')" />
    <Import Project="$(NuGetPackageRoot)system.formats.asn1\6.0.0\buildTransitive\netcoreapp2.0\System.Formats.Asn1.targets" Condition="Exists('$(NuGetPackageRoot)system.formats.asn1\6.0.0\buildTransitive\netcoreapp2.0\System.Formats.Asn1.targets')" />
    <Import Project="$(NuGetPackageRoot)system.security.cryptography.pkcs\6.0.1\buildTransitive\netcoreapp2.0\System.Security.Cryptography.Pkcs.targets" Condition="Exists('$(NuGetPackageRoot)system.security.cryptography.pkcs\6.0.1\buildTransitive\netcoreapp2.0\System.Security.Cryptography.Pkcs.targets')" />
    <Import Project="$(NuGetPackageRoot)system.security.cryptography.xml\6.0.1\buildTransitive\netcoreapp2.0\System.Security.Cryptography.Xml.targets" Condition="Exists('$(NuGetPackageRoot)system.security.cryptography.xml\6.0.1\buildTransitive\netcoreapp2.0\System.Security.Cryptography.Xml.targets')" />
    <Import Project="$(NuGetPackageRoot)system.security.cryptography.protecteddata\6.0.0\buildTransitive\netcoreapp2.0\System.Security.Cryptography.ProtectedData.targets" Condition="Exists('$(NuGetPackageRoot)system.security.cryptography.protecteddata\6.0.0\buildTransitive\netcoreapp2.0\System.Security.Cryptography.ProtectedData.targets')" />
    <Import Project="$(NuGetPackageRoot)system.configuration.configurationmanager\6.0.0\buildTransitive\netcoreapp2.0\System.Configuration.ConfigurationManager.targets" Condition="Exists('$(NuGetPackageRoot)system.configuration.configurationmanager\6.0.0\buildTransitive\netcoreapp2.0\System.Configuration.ConfigurationManager.targets')" />
    <Import Project="$(NuGetPackageRoot)netstandard.library\2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('$(NuGetPackageRoot)netstandard.library\2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.netcore.app\2.2.8\build\netcoreapp2.2\Microsoft.NETCore.App.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.netcore.app\2.2.8\build\netcoreapp2.2\Microsoft.NETCore.App.targets')" />
  </ImportGroup>
</Project>