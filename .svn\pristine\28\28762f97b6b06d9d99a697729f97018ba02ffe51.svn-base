﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace UFU.IoT.Models
{
    /// <summary>
    /// 数据
    /// </summary>
    [DataEntity("2307180100000007")]
    public class DataModel
    {
        /// <summary>
        /// 数据编号
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// MAC地址
        /// </summary>
        [NotMapped]
        public string Mac { get; set; }

        /// <summary>
        /// 设备类型编号
        /// </summary>
        public string TypeId { get; set; }

        /// <summary>
        /// 数据时间
        /// </summary>
        public DateTime Time { get; set; }

        /// <summary>
        /// 设备时间
        /// </summary>
        public DateTime DeviceTime { get; set; }

        /// <summary>
        /// 服务器时间
        /// </summary>
        public DateTime ServerTime { get; set; }

        /// <summary>
        /// 消息编号
        /// </summary>
        public int MsgId { get; set; }

        // /// <summary>
        // /// 设备数据
        // /// </summary>
        public Dictionary<string, Dictionary<string,dynamic>> Data { get; set; }

        /// <summary>
        /// 设备数据
        /// </summary>
        [JsonIgnore]
        public Dictionary<string, dynamic> DataDic => Data["Data"];

        /// <summary>
        /// 是否是历史数据
        /// </summary>
        [NotMapped]
        public bool? IsHistoryData { get; set; }
    }
}
