@page "/experiment"
@using HX.Experiment.Shared.Model
@using System.Text.Json
@using System.Net
@using Masa.Blazor.Components.Form
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
@using UFU.CoreFX.Utils
@using UFU.IoT.Shared.Utils

<MCard Class="pa-6" Style="background:#f5f5f5;margin:auto;">
	<!-- 页面头部 -->
	<MRow Align="@AlignTypes.Center" Justify="@JustifyTypes.SpaceBetween" Class="mb-6">
		<MCol>
			<div class="d-flex align-center">
				<MIcon Size="36" Color="primary" Class="mr-3">mdi-flask</MIcon>
				<div>
					<h2 class="font-weight-bold mb-0">实验管理</h2>
					<p class="text-caption grey--text mb-0">创建和管理各类心理学实验任务</p>
				</div>
			</div>
		</MCol>
		<MCol Class="d-flex justify-end">
			<MButton
				Color="primary"
				OnClick="ShowCreateTaskDialog"
				Elevation="2"
				Large>
				<MIcon Left>mdi-plus</MIcon>
				新建实验
			</MButton>
		</MCol>
	</MRow>

	<!-- 统计信息卡片 -->
	<MRow Class="mb-6">
		<MCol Cols="2">
			<MCard Class="elevation-2 text-center pa-4" Color="primary" Dark>
				<MIcon Size="48" Class="mb-2">mdi-flask-outline</MIcon>
				<div class="text-h4 font-weight-bold">@experimentCards.Count</div>
				<div class="text-subtitle-1">总实验数</div>
			</MCard>
		</MCol>

		@foreach (var type in taskTypeEnums)
		{
			
			<MCol Cols="2">
				<MCard Class="elevation-2 text-center pa-4" Color="primary" Dark>
					<MIcon Size="48" Class="mb-2">@GetTaskTypeIcon(type)</MIcon>
					<div class="text-h4 font-weight-bold">@experimentCards?.Where(m=>m.Data.TaskType==type).Count()</div>
					<div class="text-subtitle-1">@type.GetDisplayName()</div>
				</MCard>
			</MCol>
		}

	</MRow>

	<!-- 筛选和搜索 -->
	<MRow Class="mb-4">
		<MCol Cols="6">
			<MTextField
				TValue="string"
				@bind-Value="searchText"
				Label="搜索实验"
				PrependInnerIcon="mdi-magnify"
				Outlined
				Dense
				Clearable
				Placeholder="输入实验名称或描述进行搜索...">
			</MTextField>
		</MCol>
		<MCol Cols="3">
			<MSelect
				@bind-Value="selectedTaskType"
				Items="taskTypeFilterOptions"
				ItemText="item => item.GetDisplayName()"
				ItemValue="item => item"
				Label="实验类型筛选"
				Outlined
				Dense
			>
			</MSelect>
		</MCol>
		<MCol Cols="3" Class="d-flex align-center">
			<MButtonGroup>
				<MButton
					Icon="@(viewMode == "grid")"
					Color="@(viewMode == "grid" ? "primary" : "default")"
					OnClick="@(() => viewMode = "grid")">
					<MIcon>mdi-view-grid</MIcon>
				</MButton>
				<MButton
					Icon="@(viewMode == "list")"
					Color="@(viewMode == "list" ? "primary" : "default")"
					OnClick="@(() => viewMode = "list")">
					<MIcon>mdi-view-list</MIcon>
				</MButton>
			</MButtonGroup>
		</MCol>
	</MRow>
	<!-- 实验卡片列表 -->
	@if (filteredExperiments.Any())
	{
		@if (viewMode == "grid")
		{
			<MRow Align="@AlignTypes.Center" Justify="@JustifyTypes.Start" Class="mb-4">
				@foreach (var card in filteredExperiments)
				{
					<MCol xs="12" sm="6" md="4" Xl="3" Lg="3">
						<MCard Class="experiment-card elevation-4 rounded-lg" Style="height: 200px;">
							<!-- 卡片头部 -->
							<div class="card-header pa-4"
							     style="background: linear-gradient(135deg, @GetTaskTypeColor(card.Data.TaskType) 0%, @GetTaskTypeColorDark(card.Data.TaskType) 100%);">
								<div class="d-flex justify-space-between align-center">
									<MChip Small Dark Class="white--text">
										<MIcon Left Size="16">@GetTaskTypeIcon(card.Data.TaskType)</MIcon>
										@card.Data.TaskType.GetDisplayName()
									</MChip>
									<MMenu>
										<ActivatorContent>
											<MButton Icon Color="white" Size="small">
												<MIcon>mdi-dots-vertical</MIcon>
											</MButton>
										</ActivatorContent>
										<ChildContent>
											<MList Dense>
												<MListItem OnClick="() => EditExperiment(card)">
													<MListItemIcon>
														<MIcon>mdi-pencil</MIcon>
													</MListItemIcon>
													<MListItemContent>
														<MListItemTitle>编辑</MListItemTitle>
													</MListItemContent>
												</MListItem>
												<MListItem OnClick="() => DeleteExperiment(card)">
													<MListItemIcon>
														<MIcon Color="error">mdi-delete</MIcon>
													</MListItemIcon>
													<MListItemContent>
														<MListItemTitle>删除</MListItemTitle>
													</MListItemContent>
												</MListItem>
											</MList>
										</ChildContent>
									</MMenu>
								</div>
							</div>

							<!-- 卡片内容 -->
							<MCardText Class="pa-4 flex-grow-1 d-flex flex-column">
								<div class="flex-grow-1">
									<h3 class="text-h6 font-weight-bold mb-2 text-truncate">
										@card.Data.Name
									</h3>
									<p class="text-body-2 grey--text text--darken-1 mb-3"
									   style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;">
										@(string.IsNullOrEmpty(card.Data.Description) ? "暂无描述" : card.Data.Description)
									</p>
								</div>

								<!-- 卡片底部信息 -->
								<div class="d-flex justify-space-between align-center mt-auto">
									<div class="d-flex align-center">
										<MIcon Size="16" Color="grey" Class="mr-1">mdi-clock-outline</MIcon>
										<span class="text-caption grey--text">
											@card.AddTime.ToString("MM-dd HH:mm")
										</span>
									</div>
									<MButton
										Color="primary"
										Small
										Href="@card.Data.Href"
										Elevation="2">
										<MIcon Left Size="16">mdi-play</MIcon>
										开始实验
									</MButton>
								</div>
							</MCardText>
						</MCard>
					</MCol>
				}
			</MRow>
		}
		else
		{
			<MCard Class="elevation-4 rounded-lg">
				<MList>
					@foreach (var card in filteredExperiments)
					{
						<MListItem Class="pa-4">
							<MListItemIcon>
								<MAvatar Color="@GetTaskTypeColor(card.Data.TaskType)" Size="48">
									<MIcon Color="white">@GetTaskTypeIcon(card.Data.TaskType)</MIcon>
								</MAvatar>
							</MListItemIcon>

							<MListItemContent>
								<MListItemTitle Class="text-h6 font-weight-medium">
									@card.Data.Name
								</MListItemTitle>
								<MListItemSubtitle Class="text-body-2 mt-1">
									@(string.IsNullOrEmpty(card.Data.Description) ? "暂无描述" : card.Data.Description)
								</MListItemSubtitle>
								<div class="d-flex align-center mt-2">
									<MChip Small Color="@GetTaskTypeColor(card.Data.TaskType)" Class="mr-2">
										@card.Data.TaskType.GetDisplayName()
									</MChip>
									<MIcon Size="16" Color="grey" Class="mr-1">mdi-clock-outline</MIcon>
									<span class="text-caption grey--text">
										@card.AddTime.ToString("yyyy-MM-dd HH:mm")
									</span>
								</div>
							</MListItemContent>

							<MListItemAction>
								<div class="d-flex align-center">
									<MTooltip Bottom>
										<ActivatorContent>
											<MButton Icon Color="primary" Class="mr-2"
											         OnClick="() => EditExperiment(card)">
												<MIcon>mdi-pencil</MIcon>
											</MButton>
										</ActivatorContent>
										<ChildContent>
											<span>编辑实验</span>
										</ChildContent>
									</MTooltip>

									<MTooltip Bottom>
										<ActivatorContent>
											<MButton Icon Color="error" Class="mr-2"
											         OnClick="() => DeleteExperiment(card)">
												<MIcon>mdi-delete</MIcon>
											</MButton>
										</ActivatorContent>
										<ChildContent>
											<span>删除实验</span>
										</ChildContent>
									</MTooltip>

									<MButton
										Color="primary"
										Href="@card.Data.Href"
										Elevation="2">
										<MIcon Left>mdi-play</MIcon>
										开始实验
									</MButton>
								</div>
							</MListItemAction>
						</MListItem>

						@if (card != filteredExperiments.Last())
						{
							<MDivider></MDivider>
						}
					}
				</MList>
			</MCard>
		}
	}
	else
	{
		<MCard Class="elevation-4 rounded-lg">
			<MCardText Class="pa-12 text-center">
				<MIcon Size="96" Color="grey lighten-1">mdi-flask-empty-outline</MIcon>
				<p class="text-h5 grey--text mt-6 mb-2">
					@(string.IsNullOrEmpty(searchText) ? "暂无实验" : "未找到匹配的实验")
				</p>
				<p class="text-body-1 grey--text mb-6">
					@(string.IsNullOrEmpty(searchText) ? "还没有创建任何实验，点击下方按钮开始创建" : "尝试调整搜索条件或筛选器")
				</p>
				@if (string.IsNullOrEmpty(searchText))
				{
					<MButton
						Color="primary"
						Large
						OnClick="ShowCreateTaskDialog">
						<MIcon Left>mdi-plus</MIcon>
						创建第一个实验
					</MButton>
				}
				else
				{
					<MButton
						Color="grey"
						OnClick="ClearSearch">
						<MIcon Left>mdi-close</MIcon>
						清除搜索
					</MButton>
				}
			</MCardText>
		</MCard>
	}
</MCard>

<!-- 新建/编辑实验对话框 -->
<MDialog MaxWidth="700" @bind-Value="@_isShowDialog" Persistent>
	<MCard Class="elevation-8">
		<MCardTitle Class="d-flex align-center pa-6 primary white--text">
			<MIcon Class="mr-3"
			       Color="white">@(string.IsNullOrEmpty(_editingTask?.Id) ? "mdi-plus" : "mdi-pencil")</MIcon>
			<span class="text-h5 font-weight-medium">
				@(string.IsNullOrEmpty(_editingTask?.Id) ? "新建实验" : "编辑实验")
			</span>
		</MCardTitle>

		<MCardText Class="pa-6">
			<MRow>
				<MCol Cols="12">
					<MAlert Type="AlertTypes.Info" Class="mb-4" Dense>
						<MIcon Left>mdi-information</MIcon>
						请填写实验的基本信息，实验名称和类型为必填项
					</MAlert>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12">
					<MTextField
						Required
						Label="实验名称"
						TValue="string"
						@bind-Value="@_editingTask.Data.Name"
						Outlined
						Dense
						PrependInnerIcon="mdi-flask"
						Placeholder="请输入实验名称"
						Rules="@(new List<Func<string, StringBoolean>> { value => !string.IsNullOrEmpty(value) ? true : "实验名称不能为空" })"
						Class="mb-3"/>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="8">
					<MTextField
						Label="实验路径"
						TValue="string"
						@bind-Value="@_editingTask.Data.Href"
						Outlined
						Dense
						PrependInnerIcon="mdi-link"
						Placeholder="例如：/Eye/Calibration"
						Hint="实验页面的访问路径"
						PersistentHint
						Class="mb-3"/>
				</MCol>
				<MCol Cols="4">
					<MSelect
						Items="taskTypeEnums"
						@bind-Value="@_editingTask.Data.TaskType"
						ItemText="u => u.GetDisplayName()"
						ItemValue="u => u"
						Label="实验类型"
						Clearable
						Dense
						Outlined
						PrependInnerIcon="mdi-tag"
						Class="mb-3">
					</MSelect>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12">
					<MTextarea
						Label="实验描述"
						TValue="string"
						@bind-Value="@_editingTask.Data.Description"
						Outlined
						Dense
						PrependInnerIcon="mdi-text"
						Placeholder="请输入实验的详细描述..."
						Rows="4"
						Class="mb-3"/>
				</MCol>
			</MRow>

			<!-- 实验类型预览 -->
			@if (_editingTask.Data.TaskType != default)
			{
				<MRow>
					<MCol Cols="12">
						<MCard Class="mb-4" Outlined>
							<MCardText Class="py-3">
								<div class="d-flex align-center">
									<MAvatar Color="@GetTaskTypeColor(_editingTask.Data.TaskType)" Size="32"
									         Class="mr-3">
										<MIcon Color="white"
										       Size="20">@GetTaskTypeIcon(_editingTask.Data.TaskType)</MIcon>
									</MAvatar>
									<div>
										<div
											class="font-weight-medium">@_editingTask.Data.TaskType.GetDisplayName()</div>
										<div
											class="text-caption grey--text">@GetTaskTypeDescription(_editingTask.Data.TaskType)</div>
									</div>
								</div>
							</MCardText>
						</MCard>
					</MCol>
				</MRow>
			}
		</MCardText>

		<MDivider></MDivider>

		<MCardActions Class="pa-6">
			<MSpacer/>
			<MButton
				Color="grey"
				Text
				OnClick="() => _isShowDialog = false"
				Class="mr-3">
				<MIcon Left>mdi-close</MIcon>
				取消
			</MButton>
			<MButton
				Color="primary"
				OnClick="HandleCreateTask"
				Elevation="2"
				Loading="@_isSaving">
				<MIcon Left>mdi-content-save</MIcon>
				@(string.IsNullOrEmpty(_editingTask?.Id) ? "创建实验" : "保存修改")
			</MButton>
		</MCardActions>
	</MCard>
</MDialog>

@code {
	private bool _isShowDialog;
	private bool _isSaving;
	private string searchText = string.Empty;
	private string viewMode = "grid";
	private TaskTypeEnum? selectedTaskType = null;

	// 筛选选项
	private List<TaskTypeEnum> taskTypeFilterOptions =
		Enum.GetValues<TaskTypeEnum>().ToList();

	// 过滤后的实验列表
	private List<DataModel<ExperimentTask>> filteredExperiments =>
		experimentCards.Where(x =>
			(string.IsNullOrEmpty(searchText) ||
			 x.Data.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
			 x.Data.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) == true) &&
			(selectedTaskType == null || x.Data.TaskType == selectedTaskType)
		).ToList();

	private void OnValidating(List<FieldValidationResult> results)
	{
		var result = results.FirstOrDefault();
		if (result is not null)
		{
			_ = PopupService.EnqueueSnackbarAsync($"Validation failed: {result.ErrorMessages.First()}", AlertTypes.Error);
		}
	}

	private List<TaskTypeEnum> taskTypeEnums = Enum.GetValues<TaskTypeEnum>().ToList();

	private List<DataModel<ExperimentTask>> experimentCards = new()
	{
	};

	[Inject] StateService state { get; set; } = null!;

	[Inject] IPopupService PopupService { get; set; } = null!;

	private DataModel<ExperimentTask> _editingTask = new()
	{
		Data = new ExperimentTask()
		{
		}
	};

	private void ShowCreateTaskDialog(MouseEventArgs args)
	{
		_isShowDialog = true;
		_editingTask = new()
		{
			Data = new()
			{
				Href = "/Eye/Calibration"
			}
		};
	}


	private string _formId = "2507290600000001";

	private async Task HandleCreateTask()
	{
		_isSaving = true;
		try
		{
			var classJson = JsonTool.Serialize(_editingTask.Data);
			var dataJson = JsonDocument.Parse(classJson);
			var data = new DataModel<JsonDocument>
			{
				FormId = _formId,
				OrganId = state.Permissions?.User?.Organ?.Id ?? "01",
				UserId = state.Permissions?.User?.Id ?? "01",
				Data = dataJson
			};
			var httpContent = JsonContent.Create(data);
			HttpResponseMessage res;
			if (!string.IsNullOrEmpty(_editingTask.Id))
			{
				var url = "/api/Core/FormData/Edit";
				res = await state.PostAsync(url, httpContent,
					new Dictionary<string, string> { { "formId", _formId } });
			}
			else
			{
				var url = "/api/Core/FormData/Add";
				res = await state.PostAsync(url, httpContent,
					new Dictionary<string, string> { { "formId", _formId } });
			}

			if (res.IsSuccessStatusCode)
			{
				await InitListAsync();
				await PopupService.EnqueueSnackbarAsync(
					string.IsNullOrEmpty(_editingTask.Id) ? "实验创建成功" : "实验更新成功",
					AlertTypes.Success);
				_isShowDialog = false;
			}
			else
			{
				await PopupService.EnqueueSnackbarAsync("操作失败", AlertTypes.Error);
			}
		}
		finally
		{
			_isSaving = false;
		}
	}

	// 搜索和筛选方法
	private void OnSearchInput(ChangeEventArgs e)
	{
		searchText = e.Value?.ToString() ?? string.Empty;
	}

	private void OnTaskTypeFilterChanged(TaskTypeEnum value)
	{
		selectedTaskType = value;
	}

	private void ClearSearch()
	{
		searchText = string.Empty;
		selectedTaskType = null;
	}

	// 实验操作方法
	private void EditExperiment(DataModel<ExperimentTask> experiment)
	{
		_editingTask = new DataModel<ExperimentTask>
		{
			Id = experiment.Id,
			Data = new ExperimentTask
			{
				Name = experiment.Data.Name,
				Description = experiment.Data.Description,
				Href = experiment.Data.Href,
				TaskType = experiment.Data.TaskType
			}
		};
		_isShowDialog = true;
	}

	private async Task DeleteExperiment(DataModel<ExperimentTask> experiment)
	{
		var confirmed = await PopupService.ConfirmAsync("确认删除", $"确定要删除实验 \"{experiment.Data.Name}\" 吗？此操作不可撤销。");
		if (confirmed)
		{
			// 这里应该调用删除API
			await PopupService.EnqueueSnackbarAsync("删除功能待实现", AlertTypes.Info);
		}
	}

	// 获取任务类型相关信息的方法
	private string GetTaskTypeColor(TaskTypeEnum taskType)
	{
		return taskType switch
		{
			TaskTypeEnum.Eye => "#2196F3",
			TaskTypeEnum.Brain => "#FF9800",
			TaskTypeEnum.Behavior => "#4CAF50",
			TaskTypeEnum.Face => "#E91E63",
			TaskTypeEnum.HRV => "#C2185B",
			_ => "#9E9E9E"
		};
	}

	private string GetTaskTypeColorDark(TaskTypeEnum taskType)
	{
		return taskType switch
		{
			TaskTypeEnum.Eye => "#1976D2",
			TaskTypeEnum.Brain => "#F57C00",
			TaskTypeEnum.Behavior => "#388E3C",
			TaskTypeEnum.Face => "#C2185B",
			_ => "#757575"
		};
	}

	private string GetTaskTypeIcon(TaskTypeEnum taskType)
	{
		return taskType switch
		{
			TaskTypeEnum.Eye => "mdi-eye",
			TaskTypeEnum.Brain => "mdi-brain",
			TaskTypeEnum.Behavior => "mdi-account-group",
			TaskTypeEnum.Face => "mdi-face-recognition",
			_ => "mdi-flask"
		};
	}

	private string GetTaskTypeDescription(TaskTypeEnum taskType)
	{
		return taskType switch
		{
			TaskTypeEnum.Eye => "追踪眼球运动轨迹的实验",
			TaskTypeEnum.Brain => "记录大脑电活动的实验",
			TaskTypeEnum.Behavior => "观察行为反应的实验",
			TaskTypeEnum.Face => "分析面部表情的实验",
			_ => "其他类型的实验"
		};
	}

	protected async override Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await InitListAsync();
	}

	private async Task InitListAsync()
	{
		var url = "/api/Core/FormData/List";
		var res = await state.GetAsync(url, new Dictionary<string, string>()
		{
			{ "formId", _formId },
		});
		if (res.StatusCode == HttpStatusCode.OK)
		{
			var content = await res.Content.ReadAsStringAsync();
			var data = JsonTool.Deserialize<List<DataModel<ExperimentTask>>>(content);
			experimentCards = data;
		}
	}

}

<style>
	.experiment-card {
		transition: all 0.3s ease;
		border-radius: 12px !important;
	}

	.experiment-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
	}

	.card-header {
		border-radius: 12px 12px 0 0 !important;
	}

	.elevation-4 {
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08) !important;
	}

	.elevation-8 {
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1) !important;
	}

	.rounded-lg {
		border-radius: 12px !important;
	}

	.v-list-item:hover {
		background-color: rgba(0, 0, 0, 0.04) !important;
	}

	.primary.white--text .v-card__title {
		background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
	}

	.text-truncate {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.flex-grow-1 {
		flex-grow: 1;
	}

	.v-card--outlined {
		border: 1px solid rgba(0, 0, 0, 0.12) !important;
	}
</style>