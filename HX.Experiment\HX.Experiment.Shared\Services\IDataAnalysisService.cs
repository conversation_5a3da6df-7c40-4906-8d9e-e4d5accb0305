using HX.Experiment.Shared.Model;

namespace HX.Experiment.Shared.Services;

/// <summary>
/// 数据分析服务接口
/// </summary>
public interface IDataAnalysisService
{
    /// <summary>
    /// 开始数据分析
    /// </summary>
    /// <param name="recordId">检测记录ID</param>
    /// <param name="startTime">分析开始时间</param>
    /// <param name="endTime">分析结束时间</param>
    /// <param name="analysisName">分析名称</param>
    /// <returns>分析记录ID</returns>
    Task<string> StartAnalysisAsync(string recordId, DateTime startTime, DateTime endTime, string analysisName);

    /// <summary>
    /// 获取分析结果
    /// </summary>
    /// <param name="analysisId">分析记录ID</param>
    /// <returns></returns>
    Task<ExperimentAnalysisResult?> GetAnalysisResultAsync(string analysisId);

    /// <summary>
    /// 获取检测记录的时间段数据
    /// </summary>
    /// <param name="recordId">检测记录ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns></returns>
    Task<ExperimentSegmentData?> GetSegmentDataAsync(string recordId, DateTime startTime, DateTime endTime);

    /// <summary>
    /// 导出分析结果
    /// </summary>
    /// <param name="analysisId">分析记录ID</param>
    /// <param name="exportPath">导出路径</param>
    /// <returns></returns>
    Task<bool> ExportAnalysisResultAsync(string analysisId, string exportPath);

    /// <summary>
    /// 分析进度事件
    /// </summary>
    event EventHandler<DataAnalysisProgressEventArgs> AnalysisProgressChanged;

    /// <summary>
    /// 分析完成事件
    /// </summary>
    event EventHandler<DataAnalysisCompletedEventArgs> AnalysisCompleted;
}

/// <summary>
/// 数据分析进度事件参数
/// </summary>
public class DataAnalysisProgressEventArgs : EventArgs
{
    public string AnalysisId { get; set; } = string.Empty;
    public int Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Stage { get; set; } = string.Empty; // 分析阶段：数据准备、心率分析、皮肤电分析等
}

/// <summary>
/// 数据分析完成事件参数
/// </summary>
public class DataAnalysisCompletedEventArgs : EventArgs
{
    public string AnalysisId { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public ExperimentAnalysisResult? Result { get; set; }
}

/// <summary>
/// 实验分析结果
/// </summary>
public class ExperimentAnalysisResult
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string RecordId { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public DateTime AnalysisTime { get; set; }

    // 心率变异性分析结果
    public Dictionary<string, object>? HrvStatistics { get; set; }
    public List<int>? HrData { get; set; }
    public string? HrImageUrl { get; set; }

    // 皮肤电分析结果
    public List<EdaTimeDomainResult>? EdaTimeDomain { get; set; }
    public List<EdaScrResult>? EdaScrResults { get; set; }
    public List<EdaEventResult>? EdaEventResults { get; set; }
    public string? EdaImageUrl { get; set; }
    public string? ScrImageUrl { get; set; }

    // 情绪分析结果
    public EmotionAnalysisResult? EmotionResult { get; set; }
}

/// <summary>
/// 实验时间段数据
/// </summary>
public class ExperimentSegmentData
{
    public List<ExperimentDataPoint>? PpgData { get; set; }
    public List<ExperimentDataPoint>? EdaData { get; set; }
    public List<ExperimentDataPoint>? ImuData { get; set; }
    public List<ExperimentDataPoint>? HrData { get; set; }
}

/// <summary>
/// 实验数据点
/// </summary>
public class ExperimentDataPoint
{
    public DateTime Time { get; set; }
    public float Value { get; set; }
    public string? Label { get; set; } // 用于标记事件
}

/// <summary>
/// EDA时域分析结果
/// </summary>
public class EdaTimeDomainResult
{
    public string Name { get; set; } = string.Empty;
    public float Mean { get; set; }
    public float Max { get; set; }
    public float Min { get; set; }
    public float StandardDeviation { get; set; }
    public float Variance { get; set; }
}

/// <summary>
/// EDA SCR分析结果
/// </summary>
public class EdaScrResult
{
    public string OnsetTime { get; set; } = string.Empty;
    public string PeakTime { get; set; } = string.Empty;
    public float RiseTime { get; set; }
    public float Amplitude { get; set; }
    public float RecoveryTime { get; set; }
}

/// <summary>
/// EDA事件相关分析结果
/// </summary>
public class EdaEventResult
{
    public string EventId { get; set; } = string.Empty;
    public string EventLabel { get; set; } = string.Empty;
    public string EventOnsetTime { get; set; } = string.Empty;
    public int NScrCount { get; set; }
    public float Latency { get; set; }
    public float AmplitudeSummary { get; set; }
    public float AverageScl { get; set; }
    public float AverageRiseTime { get; set; }
    public float AverageRecoveryTime { get; set; }
}

/// <summary>
/// 情绪分析结果
/// </summary>
public class EmotionAnalysisResult
{
    public bool DepressPrediction { get; set; }
    public float DepressConfidence { get; set; }
    public int DepressLevelPrediction { get; set; }
    public float DepressLevelConfidence { get; set; }
    public float ValenceScore { get; set; }
    public int ValenceScoreType { get; set; }
    public float ArousalScore { get; set; }
    public int ArousalScoreType { get; set; }
}
