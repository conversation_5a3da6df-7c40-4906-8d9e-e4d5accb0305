﻿using System.ComponentModel.DataAnnotations;

namespace HX.HRV.Shared.Models;

public enum EducationalLevel
{
    /// <summary>
    /// Primary School
    /// </summary>
    [Display(Name = "小学")]
    PrimarySchool = 1,

    /// <summary>
    /// Junior High School
    /// </summary>
    [Display(Name = "初中")]
    JuniorHighSchool,

    /// <summary>
    /// Senior High School
    /// </summary>
    [Display(Name = "高中")]
    SeniorHighSchool,

    /// <summary>
    /// Associate Degree
    /// </summary>
    [Display(Name = "专科")]
    AssociateDegree,

    /// <summary>
    /// Bachelor's Degree
    /// </summary>
    [Display(Name = "本科")]
    BachelorDegree,

    /// <summary>
    /// Master's Degree
    /// </summary>
    [Display(Name = "研究生")]
    MasterDegree,

    /// <summary>
    /// Doctoral Degree
    /// </summary>
    [Display(Name = "博士")]
    DoctoralDegree
}