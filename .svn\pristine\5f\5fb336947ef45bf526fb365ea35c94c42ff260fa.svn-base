﻿// using DynamicData;
// using DynamicData.Binding;
// using ReactiveUI;
// using System.Collections.ObjectModel;
// using System.Reactive;
// using System.Reactive.Linq;
//
// namespace HX.HRV.Shared.Models.ViewModel
// {
//     public class DeviceStatusRazorViewModel : ReactiveObject, IActivatableViewModel
//     {
//         // IActivatableViewModel 的标准实现
//         public ViewModelActivator Activator { get; } = new();
//
//         // 服务通过构造函数注入
//         private readonly DeviceCollectionService _deviceCollectionService;
//
//         // 私有的只读集合，由 DynamicData 填充
//         private readonly ReadOnlyObservableCollection<DeviceStatusReactiveViewModel> _devices;
//         
//         // 公开的属性，供 View 绑定
//         public ReadOnlyObservableCollection<DeviceStatusReactiveViewModel> Devices => _devices;
//
//         // UI状态属性
//         private bool _isDialogVisible;
//         public bool IsDialogVisible
//         {
//             get => _isDialogVisible;
//             set => this.RaiseAndSetIfChanged(ref _isDialogVisible, value);
//         }
//
//         private DeviceStatusReactiveViewModel _selectedDevice;
//         public DeviceStatusReactiveViewModel SelectedDevice
//         {
//             get => _selectedDevice;
//             set => this.RaiseAndSetIfChanged(ref _selectedDevice, value);
//         }
//
//         // 命令 (ReactiveUI 的方式，比普通方法更强大)
//         public ReactiveCommand<DeviceStatusReactiveViewModel, Unit> OpenDialogCommand { get; }
//         public ReactiveCommand<Unit, Unit> CloseDialogCommand { get; }
//
//         public DeviceStatusRazorViewModel(DeviceCollectionService deviceCollectionService)
//         {
//             _deviceCollectionService = deviceCollectionService;
//             _deviceCollectionService.Connect()
//                 .Sort(SortExpressionComparer<DeviceStatusReactiveViewModel>.Ascending(d =>
//                     int.TryParse(d.Device.Name, out var num) ? num : int.MaxValue))
//                 .Bind(out _devices)
//                 .Subscribe();
//             OpenDialogCommand = ReactiveCommand.Create<DeviceStatusReactiveViewModel>(device =>
//             {
//                 SelectedDevice = device;
//                 IsDialogVisible = true;
//             });
//             
//             CloseDialogCommand = ReactiveCommand.Create(() =>
//             {
//                 IsDialogVisible = false;
//                 SelectedDevice = null;
//             });
//         }
//     }
// }