﻿using HX.HRV.Shared.Models;
using System.Runtime.InteropServices;
using System.Text.Json.Nodes;

namespace HX.HRV.Web.Services
{
	public class ImportFileDataAnalysisService
	{


		public static async Task AnalysisBinDirectory(string deviceSn, string directory)
		{
			var files = Directory.GetFiles(directory, "*.bin");
			var sortedFiles = files.OrderBy(file =>
			{
				string fileName = Path.GetFileNameWithoutExtension(file);
				string[] parts = fileName.Split('-');
				var timestampString = parts.ElementAt(parts.Length - 1);
				return long.Parse(timestampString);
			}).ToArray();
			var tickFiles = Directory.GetFiles(directory, "*tick*.bin");
			var ticks = new List<string>();
			foreach (var tickFile in tickFiles)
			{
				var fileContent = await System.IO.File.ReadAllBytesAsync(tickFile);
				var ticksTimes = ParseTickData(fileContent);
				ticks.AddRange(ticksTimes);
			}

			var index = 0;
			foreach (var file in sortedFiles)
			{
				var fileContent = await System.IO.File.ReadAllBytesAsync(file);
				var (startTime, frequency, fileType, timestamp) = ExtractTimestampAndFrequencyFromFileName(file);
				List<string> parsedData = ParseFileByType(fileType, fileContent, startTime, frequency);
				fileType = fileType.Replace("-", "").Replace("_", "");
				var outputFilePath = Path.Combine(directory, $"{fileType}_{timestamp}.csv");
				if (Directory.GetFiles(directory, $"*{fileType}*.csv").Length <= 0)
				{
					switch (fileType)
					{
						case "PPG":
							await System.IO.File.WriteAllLinesAsync(outputFilePath,
								new List<string>() { "Time,PPG_R,PPG_I,PPG_G,Event" });
							break;
						case "HRSPO2":
							await System.IO.File.WriteAllLinesAsync(outputFilePath, new List<string>() { "Time,HR,SPO2,Battery,Event" });
							break;
						case "IMU":
							await System.IO.File.WriteAllLinesAsync(outputFilePath,
								new List<string>() { "Time,GYRO-X,GYRO-Y,GYRO-Z,ACC-X,ACC-Y,ACC-Z,GEO-X,GEO-Y,GEO-Z,Event" });
							break;
						case "EDA":
						case "TEMPSKIN":
						case "TEMPAIR":
						case "AP":
						case "LIGHT":
							await System.IO.File.WriteAllLinesAsync(outputFilePath,
								new List<string>() { $"Time,{fileType},Event" });
							break;
						case "TICK":
							//await System.IO.File.WriteAllLinesAsync(outputFilePath, new List<string>() { "Time" });
							break;
					}
				}
				var isTicksData = parsedData.Select((str, index) =>
					new
					{
						str,
						index,
						isTicks = ticks.Any(m => str.StartsWith(
							m))
					}
				).Where(m => m.isTicks)?.ToList();
				if (isTicksData != null)
				{
					foreach (var data in isTicksData)
					{
						parsedData[data.index] += $",0";
					}
				}
				index++;
				if (index % 5 == 0)
				{
					var progress = index++ / (float)sortedFiles.Length;
					var toPageJson = new JsonObject
					{
						{ "SN", deviceSn },
						{ "Status", (int)EnumDeviceStatus.USB传输中 },
						{ "Progress", progress*100 }
					};
					_ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToString());
				}
				await System.IO.File.AppendAllLinesAsync(outputFilePath, parsedData);
			}
			var endtoPageJson = new JsonObject
				{
					{ "SN", deviceSn },
					{ "Status", (int)EnumDeviceStatus.导出完成 }
				};
			_ = HXAdminWebSocket.SendMsgToAdmin(endtoPageJson.ToString());
		}

		public
			static (DateTime time, int frequency, string fileType, long timestamp)
			ExtractTimestampAndFrequencyFromFileName(
				string fileName)
		{
			var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
			if (GetFileNameMessage(fileNameWithoutExtension, out var extractTimestampAndFrequencyFromFileName))
				return extractTimestampAndFrequencyFromFileName;
			throw new Exception("文件名中未包含有效的时间戳或频率");
		}

		public static bool GetFileNameMessage(string fileNameWithoutExtension,
			out (DateTime time, int frequency, string fileType, long timestamp) extractTimestampAndFrequencyFromFileName)
		{
			var parts = fileNameWithoutExtension.Split('-').ToList();
			if (parts.Count >= 4)
			{
				// 提取时间戳 最后一个
				var timestampString = parts[parts.Count - 1];
				var timestamp = long.Parse(timestampString);
				var time = DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime;
				// 提取频率 倒数第三个
				var frequencyString = parts[parts.Count - 3].Replace("Hz", "");
				var fileTypes = parts.Skip(1).Take(parts.Count - 3 - 1);
				if (int.TryParse(frequencyString, out var frequency))
				{
					extractTimestampAndFrequencyFromFileName =
						(time, frequency, string.Join("-", fileTypes).ToUpper(), timestamp);
					return true;
				}
			}

			throw new Exception("解析文件信息失败");
		}

		// 根据文件类型解析内容
		public static List<string> ParseFileByType(string fileType, byte[] bytes, DateTime startTime, int frequency)
		{
			switch (fileType)
			{
				case "PPG":
					var ppgData = ParsePPGDataByDataType(bytes);
					return FormatPPGDataWithTimestamp(ppgData, startTime, frequency);
				case "HR_SPO2":
					return ParseSPO2DataByDataType(bytes, startTime, frequency);
				case "EDA":
					return ParseEDAData(bytes, startTime, frequency);
				case "IMU":
					return ParseIMUDataByDataType(bytes, startTime, frequency);
				case "TEMP-SKIN":
				case "TEMP-AIR":
				case "AP":
				case "LIGHT":
					return ParseByData(bytes, startTime, frequency);
				case "TICK":
					return ParseTickData(bytes, startTime, frequency);
				default:
					return new List<string>();
			}
		}

		public static List<string> ParseTickData(byte[] bytes, DateTime startTime, int frequency)
		{
			var data = MemoryMarshal.Cast<byte, uint>(bytes)
				.ToArray();
			var formatted = new List<string>();
			foreach (var item in data)
			{
				var dateTime = DateTimeExtensions.ParseUnix(item);
				formatted.Add($"{dateTime:yyyy-MM-dd HH:mm:ss}");
			}
			return formatted;
		}

		public static List<string> ParseTickData(byte[] bytes)
		{
			var data = MemoryMarshal.Cast<byte, uint>(bytes)
				.ToArray();
			var formatted = new List<string>();
			foreach (var item in data)
			{
				var dateTime = DateTimeExtensions.ParseUnix(item);
				formatted.Add(dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
			}
			return formatted;
		}


		public static List<string> FormatPPGDataWithTimestamp(Dictionary<string, List<uint>> ppgData,
			DateTime startTime, int frequency)
		{
			// 格式化PPG数据
			var formatted = new List<string>();
			var time = startTime;
			for (var i = 0; i < ppgData["PPG-R"].Count; i++)
			{
				if (i > 0)
				{
					// 根据频率计算时间
					time = time.AddSeconds(1.0 / frequency);
				}

				var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
				formatted.Add($"{timestamp},{ppgData["PPG-R"][i]},{ppgData["PPG-I"][i]},{ppgData["PPG-G"][i]}");
			}

			return formatted;
		}

		/// <summary>
		/// 9个字节一组, 一组三个数据 ，每个数据三个字节
		/// //红光;//红外光//绿光
		/// </summary>
		/// <param name="bytes"></param>
		/// <returns></returns>
		private static Dictionary<string, List<uint>> ParsePPGDataByDataType(byte[] bytes)
		{
			var data = new Dictionary<string, List<uint>>()
			{
				["PPG-R"] = new List<uint>(),
				["PPG-I"] = new List<uint>(),
				["PPG-G"] = new List<uint>(),
			};
			// 遍历每9个字节，分别解析PPG-R, PPG-I, 和 PPG-G数据
			for (var i = 0; i < bytes.Length / 9; i++)
			{
				try
				{
					var rbytes = bytes[(i * 9 + 0)..(i * 9 + 3)];
					Array.Reverse(rbytes);
					data["PPG-R"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));

					rbytes = bytes[(i * 9 + 3)..(i * 9 + 6)];
					Array.Reverse(rbytes);
					data["PPG-I"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));

					rbytes = bytes[(i * 9 + 6)..(i * 9 + 9)];
					Array.Reverse(rbytes);
					data["PPG-G"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));
				}
				catch (System.Exception ex)
				{
					Console.WriteLine("解析数据异常：" + ex.Message);
				}
			}

			return data;
		}

		/// <summary>
		/// 解析数据类型中的心率、血氧和电量数据。
		/// </summary>
		/// <param name="bytes">要解析的字节数组。</param>
		/// <returns>包含心率、血氧和电量的元组。</returns>
		private static List<string> ParseSPO2DataByDataType(byte[] bytes, DateTime startTime, int frequency)
		{
			var result = new List<string>();
			var time = startTime;
			for (var i = 0; i < bytes.Length; i = i + 3)
			{
				// 根据频率计算时间
				time = time.AddSeconds(1.0 / frequency);
				try
				{
					result.Add($"{time.ToString("yyyy-MM-dd HH:mm:ss.fff")},{bytes[i]},{bytes[i + 1]},{bytes[i + 2]}");
				}
				catch (System.Exception ex)
				{
					Console.WriteLine("解析数据异常：" + ex.Message);
				}
			}

			return result;
		}

		private static List<string> ParseEDAData(byte[] bytes, DateTime startTime, int frequency)
		{
			var data = MemoryMarshal.Cast<byte, float>(bytes)
				.ToArray();
			var result = new List<string>();
			// 格式化数据
			var time = startTime;
			for (var i = 0; i < data.Length; i++)
			{
				if (i > 0)
				{
					// 根据频率计算时间
					time = time.AddSeconds(1.0 / frequency);
				}

				var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
				result.Add($"{timestamp},{(data[i] > 0 ? 1000f / data[i] : 0.0f)}");
			}

			return result;
		}


		private static List<string> ParseIMUDataByDataType(byte[] bytes, DateTime startTime, int frequency)
		{
			// 每次采样的数据长度为 18 个字节（9 方向，每方向 2 字节）
			int frameSize = 18;
			// 检查输入字节数组的长度是否为 18 的倍数
			if (bytes.Length % frameSize != 0)
			{
				throw new ArgumentException("数据长度不符合传感器格式！");
			}

			var time = startTime;
			var list = new List<string>();

			var count = bytes.Length / frameSize;
			// 遍历每一帧数据
			for (int i = 0; i < count; i++)
			{
				try
				{
					if (i > 0)
					{
						// 根据频率计算时间
						time = time.AddSeconds(1.0 / frequency);
					}
					var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
					// 解析陀螺仪数据（小端模式）
					var GYRO_X = BitConverter.ToInt16(bytes, i * frameSize + 0); // C1C2 陀螺仪 X 轴
					var GYRO_Y = BitConverter.ToInt16(bytes, i * frameSize + 2); // B1B2 陀螺仪 Y 轴
					var GYRO_Z = BitConverter.ToInt16(bytes, i * frameSize + 4); // A1A2 陀螺仪 Z 轴

					var ACC_X = BitConverter.ToInt16(bytes, i * frameSize + 6); // F1F2 加速度计 X 轴
					var ACC_Y = BitConverter.ToInt16(bytes, i * frameSize + 8); // E1E2 加速度计 Y 轴
					var ACC_Z = BitConverter.ToInt16(bytes, i * frameSize + 10); // D1D2 加速度计 Z 轴

					var GEO_X = BitConverter.ToInt16(bytes, i * frameSize + 12); // G1G2 地磁传感器 X 轴
					var GEO_Y = BitConverter.ToInt16(bytes, i * frameSize + 14); // H1H2 地磁传感器 Y 轴
					var GEO_Z = BitConverter.ToInt16(bytes, i * frameSize + 16); // I1I2 地磁传感器 Z 轴
					var str = $"{timestamp},{GYRO_X},{GYRO_Y},{GYRO_Z},{ACC_X},{ACC_Y},{ACC_Z},{GEO_X},{GEO_Y},{GEO_Z}";
					list.Add(str);
				}
				catch (Exception ex)
				{
					Console.WriteLine("解析数据异常：" + ex.Message);
				}
			}
			return list;
		}

		private static List<string> ParseByData(byte[] bytes, DateTime startTime, int frequency)
		{
			var data = MemoryMarshal.Cast<byte, float>(bytes)
				.ToArray();
			var result = new List<string>();
			// 格式化数据
			var time = startTime;
			for (var i = 0; i < data.Length; i++)
			{
				if (i > 0)
				{
					// 根据频率计算时间
					time = time.AddSeconds(1.0 / frequency);
				}

				var timestamp = time.ToString("yyyy-MM-dd HH:mm:ss.fff");
				result.Add($"{timestamp},{data[i]}");
			}
			return result;
		}


	}
}
