@using System.Collections.Concurrent
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Services
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Shared.Models
@inject IJSRuntime JS
<style>
    .hrv-report-fields div:last-child .hrv-report-label, .hrv-report-fields div:last-child .hrv-report-value {
        border-right: thin solid rgba(0, 0, 0, .12);
    }

    .hrv-report-value {
        background-color: white;
        text-align: center;
        border: thin solid rgba(0, 0, 0, .12);
        border-right: none;
        color: black;
    }

    .hrv-report-label {
        background-color: #e2edfa;
        text-align: center;
        border: thin solid rgba(0, 0, 0, .12);
        border-bottom: none;
        border-right: none;
    }

    .hrv-report-label, .hrv-report-value {
        height: 2em;
        line-height: 2em;
    }

    .hrv-report-fields {
        margin-top: 0.25em;
        display: flex;
        align-items: center;
        padding-left: 0.5em;
        padding-right: 0.5em;
    }

    .hrv-report-container {
        width: 210mm;
    
        overflow: auto;
        margin: 0 auto 0;
        transform-origin: top;
    }

    .hrv-report-echart-container {
        margin-top: 0.2em;
        display: flex;

    }

    table {
        width: 80mm;
        border-collapse: collapse;
        text-align: center;
    }

    .data-analysis-table {

    }

    .data-analysis-table tr
    {
       height: 30px;
    }
    .data-analysis-table th,
    .data-analysis-table td {
        border: 1px solid #ddd;
        padding: 4px;
    }

    .data-analysis-table th {
        background-color: #e1ebf6; /* 蓝色背景 */
        color: #333;
    }

    .data-analysis-table th[colspan="4"] {
        background-color: #d9eaf9;
        font-size: 18px;
        font-weight: bold;
    }

    .data-analysis-table tr:nth-child(even) {
        background-color: #f9f9f9; /* 条纹背景 */
    }


    .note-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #1a73e8; /* 蓝色背景 */
        color: white; /* 白色字体 */
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 20px;
    }

    .note-content {
        flex: 1;
        padding-right: 20px;
    }

    .note-content p {
        margin-bottom: 10px; /* 为段落添加间距 */
    }

    .note-icon img {
        width: 50px; /* 根据需要调整图标大小 */
        height: 50px;
    }

    #bottom_version {
        display: none;
    }

    .hrv-report-container {
        font-size: 14px;
        height: 100%;
    }

</style>
<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<div class="hrv-report-container">
    <div style="background-color: #ffffff ; margin:0 auto; font-size: 14px;" id="report-detail">
        <div
            style="background-color: #3380d6;color: #ffffff;width: 100%;text-align: center; display: flex;flex-direction: column;justify-content: center;">
            <h1 style="text-align: center;font-size:2.625em;color: white; display: inline-flex;justify-content: center;align-items: center;">
                @* <img src="images/logo_white.png" style="height: 1.25em"/> *@
                心率变异性分析报告
               @if (!IsHideActionButton)
                {
                    <MButton OnClick="Download" Id="btnDownload">下载</MButton>
                    <MButton OnClick="Print" Class="ml-2" Id="btnPrint">打印</MButton>
                    <MButton OnClick="Goback" Class="ml-2" Id="btnBack">返回</MButton>
                }
            </h1>
            <div style="margin-top:0.125em;text-align: center;font-size:1em;">
                编号：@patientRecordModel.RecordCode
            </div>
        </div>
        <div style="padding: 0 1mm ">
            <div class="hrv-report-fields" style="">
                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        姓名
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Name
                    </div>
                </div>
                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        性别
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Sex
                    </div>
                </div>

                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        年龄
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Age
                    </div>
                </div>

                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        门诊/住院号
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.OutpatientNumberString
                    </div>
                </div>


                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        病区
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Source
                    </div>
                </div>

                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        身高(cm)
                    </div>
                    <div class="hrv-report-value">
                        @(patientModel.Height)
                    </div>
                </div>
                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        体重(kg)
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Weight
                    </div>
                </div>


                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        BMI
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.BMI?.ToString("F2")
                    </div>
                </div>


            </div>
            @if (Statistics.StatisticsDictionary is { Count: > 0 })
            {
                <div class="hrv-report-fields">
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            平均心率(bpm)
                        </div>
                        <div class="hrv-report-value">
                            @Statistics.StatisticsDictionary["hr_mean"]
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            最低心率(bpm)
                        </div>
                        <div class="hrv-report-value">
                            @(Statistics?.HRList?.Where(m=>m>0)?.Min()??0)
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            最高心率(bpm)
                        </div>
                        <div class="hrv-report-value">
                            @(Statistics?.HRList?.Where(m=>m>0)?.Max()??0)
                        </div>
                    </div>

                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            @("心率占比(<60)")
                        </div>
                        <div class="hrv-report-value">
                            @(Statistics.StatisticsDictionary["hr_lt_60_percentage"])%
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            检测开始时间
                        </div>
                        <div class="hrv-report-value">
                            @patientRecordModel.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss")
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            检测结束时间
                        </div>
                        <div class="hrv-report-value">
                            @patientRecordModel.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss")
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            检测时长(min)
                        </div>
                        <div class="hrv-report-value">
                            @patientRecordModel.CheckTime
                        </div>
                    </div>

                </div>
            }


            <div class="hrv-report-echart-container">
                <div style="flex-grow: 1;">
                    @if (bmpChartOption!=null) {
                        <MECharts Option="bmpChartOption" Height="140" Width="360">
                        </MECharts>
                    }

                </div>
                <div style="flex-grow: 1;">

                    @if (hrChartOption != null) {
                    
                        <MECharts Option="hrChartOption" Height="140" Width="360">
                        </MECharts>
                    }

                </div>
            </div>
            <div class="hrv-report-echart-container">
                <div style="flex-grow: 1;">
                @if (frequencyChartOption != null) {

                    <MECharts
                        Option="frequencyChartOption"
                        Height="170" Width="360">
                    </MECharts>
                }
                </div>
                <div style="flex-grow: 1;">
                    @if (ScatterChartOption != null) {

                        <MECharts Option="@ScatterChartOption" Height="170"
                                  Width="360">
                        </MECharts>
                    }
                </div>
            </div>
            <div style="display: flex;align-items: flex-end;" class="data-analysis-table">

                <div style="flex-grow: 1">
                    <div style="flex-grow: 1;margin: 0 auto;display: flex;justify-content: center;">
                      
                        @if (HZBarChartOption!=null) {
                            <MECharts Option="@HZBarChartOption" Height="300" Width="360">
                            </MECharts>
                        }
                    </div>
                    <div style="flex-grow: 1;margin: 0 auto;display: flex;justify-content: center;">
                        <table class="frequency-analysis">
                            <thead>
                            <tr>
                                <th colspan="4">时域分析</th>
                            </tr>
                            <tr>
                                <th>参数</th>
                                <th>单位</th>
                                <th>数值</th>

                            </tr>
                            </thead>
                            <tbody>
                            @foreach (var reportDataModel in ReportDataModel.ShowTimeDomain)
                            {
                                <tr>
                                    <td>@reportDataModel.Name</td>
                                    <td>@reportDataModel.Unit</td>
                                    <td>@patientRecordModel.Statistics?.StatisticsDictionary.GetValueOrDefault(reportDataModel.Key)</td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                    
                </div>

                <div style="flex-grow: 1">
                    <div style="flex-grow: 1;display: flex;">
                        <table class="frequency-analysis">
                            <thead>
                            <tr>
                                <th colspan="4">频域分析</th>
                            </tr>
                            <tr>
                                <th>参数</th>
                                <th>单位</th>
                                <th>数值</th>

                            </tr>
                            </thead>
                            <tbody>
                            @foreach (var reportData in ReportDataModel.ShowFrequencyDomain)
                            {
                                <tr>
                                    <td>@reportData.Name</td>
                                    <td>@reportData.Unit</td>
                                    <td>@patientRecordModel.Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key)</td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                    <div style="flex-grow: 1;display: flex;padding-top: 0.5em">
                        <table class="frequency-analysis">
                            <thead>
                            <tr>
                                <th colspan="4">非线性分析</th>
                            </tr>
                            <tr >
                                <th>参数</th>
                                <th>单位</th>
                                <th>数值</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach (var reportData in ReportDataModel.ShowNonlinearAnalysis)
                            {
                                <tr >
                                    <td>@reportData.Name</td>
                                    <td>@reportData.Unit</td>
                                    <td>@patientRecordModel.Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key)</td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @* <MECharts Option="@GenerateBarChartOption()" Height="200"> *@
            @* </MECharts> *@
            @* <MECharts Option="@GenerateLineChartOption()" Height="300"> *@
            @* </MECharts> *@
            @* <div class="note-box" style="display: flex;"> *@
            @*     <div class="note-content"> *@
            @*         <p>◇ 您的心率在正常范围。</p> *@
            @*         <p>◇ 您的自主神经稳定性一般，生理压力指数、心理压力指数、抗压能力均属一般水平，警觉性较高。</p> *@
            @*         <p>◇ 您平时偶尔感觉到轻度疲劳感，疲劳指数正常。</p> *@
            @*     </div> *@
            @*     <div class="note-icon"> *@
            @*         <img src="/images/check-bold.png"> *@
            @*     </div> *@
            @* </div> *@
            <div style="display: flex;justify-content: space-around;margin-top: 0.5em">
                <div>
                    <span>检查医师：@userModel?.Name</span>
                </div>
                <div>
                    <span>打印报告日期：@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</span>
                    <span></span>
                </div>
            </div>
            <div style="display: flex;justify-content: space-around;height: 1em;">
                <div>
                    以上结果供医生参考，最终的诊断由专业医生负责。
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public string Id { get; set; }
    [Parameter] public bool IsHideActionButton { get; set; }
    private PatientModel patientModel = new PatientModel();

    [CascadingParameter(Name = "PopupService")]
    IPopupService PopupService { get; set; }

    private ReportDataStatistics Statistics = new ReportDataStatistics();
    private PatientRecordModel patientRecordModel = new PatientRecordModel();
    private UserInfo userModel = new UserInfo();
    private object bmpChartOption;
    private object hrChartOption;
    private object frequencyChartOption;
    private object ScatterChartOption;
    private object HZBarChartOption;
    

    private object _option { get; set; }
    [Inject] StateService StateService { get; set; }
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await InitRecordDataAsync();
        bmpChartOption =GenerateChartOption("心率", Statistics.HRList);
        
        hrChartOption =GenerateHRBarChartOption("直方图", Statistics.NniList);
        
        frequencyChartOption =GenerateFrequencyBarChartOption("频谱", Statistics.FrequencyList, Statistics.PsdList);

        ScatterChartOption = GenerateScatterChartOption("散点图", Statistics.NniList);
        
        HZBarChartOption=GenerateHZBarChartOption();
        
        await base.OnInitializedAsync();
    }

    private async Task InitRecordDataAsync()
    {
        var res = await StateService
            .GetAsJsonAsync<DataModel<PatientRecordModel>>(
                "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string>()
                    {
                    { "id", Id }
                    });
        patientRecordModel = res.Data.Data;
        userModel = res.Data.User;
        patientModel = patientRecordModel.Patient;
        Statistics = patientRecordModel.Statistics ?? new ReportDataStatistics();
    }


    private async Task PrintContent()
    {
        await JS.InvokeVoidAsync("window.printInIframe", "printableArea");
    }

    private ConcurrentQueue<int> FakeData { get; set; } = new ConcurrentQueue<int>();


    private object GenerateChartOption(string title, List<int> data)
    {
        return new
        {
            Animation = false,
            Title = new
            {
                text = title,
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                top = "20%",
                bottom = "3%",
                containLabel = true
            },
            xAxis = new
            {
                type = "category",
                Show = false,
            },
            yAxis = new
            {
                type = "value"
            },
            series = new[]
            {
                new
                {
                    data = data,
                    type = "line",
                    ShowSymbol = false,
                }
            }
        };
    }


    /// <summary>
    /// 柱状图
    /// </summary>
    /// <returns></returns>
    private object GenerateHRBarChartOption(string title, List<int> data)
    {
        if (data == null) return new { };
        // data = data.Where(m => m <= 1000).ToList();
        if (data == null || data.Count == 0) return new object();
        int step = 50;
        int maxRange = (data.Max() / step + 1) * step;
        var ranges = Enumerable 
            .Range(0, maxRange / step + 1)
            .Select(i => i * step).ToList();
        var result = ranges.ToDictionary(
            range => range,
            range => data.Count(n => n > range && n <= range + step)
        );
        return new
        {
            Animation = false,
            Title = new
            {
                text = title,
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            Renderer = "svg",
            xAxis = new
            {
                type = "category",
                data = result.Keys
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                top = "20%",
                bottom = "3%",
                containLabel = true
            },
            yAxis = new
            {
                type = "value"
            },
            series = new
            {
                data = result.Values,
                type = "bar",
                barGap = "0",
                barCategoryGap = "0"
            }
        };
    }

    
    /// <summary>
    /// 频率直方图
    /// </summary>
    /// <returns></returns>
    private object GenerateHZBarChartOption()
    {
        var freqDataKey = new[] {"TP_power","VLF_power","LF_power","HF_power" };
        var freqData = Statistics.StatisticsDictionary
            .Where(m=>freqDataKey.Contains(m.Key))
            .OrderBy(m=>m.Key);
        return new
        {
            Animation = false,
            Title = new
            {
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            Renderer = "svg",
            xAxis = new
            {
                type = "category",
                data =new [] {"总能量","极低频","低频","高频"},
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                bottom = "3%",
                top = "30",
                containLabel = true
            },
            yAxis = new
            {
                type = "value"
            },
            series = new
            {
                data = freqData,
                type = "bar",
                barGap = "20",
                barCategoryGap = "20"
            }
        };
    }

    
    
    private object GenerateFrequencyBarChartOption(string title, List<int> xdata, List<int> psdData)
    {
        if (xdata == null || xdata.Count == 0) return new object();
        var frequency = xdata.Select(m => m / 10000.0).ToList();
        var vlfIndex = frequency.Select((source, index) => new
        {
            source,
            index
        }).LastOrDefault(m => m.source < 0.04)?.index??0;

        var lfIndex = frequency.Select((source, index) => new
        {
            source,
            index
        }).LastOrDefault(m => m.source < 0.15)?.index??0;

        var hf = frequency.Select((source, index) => new
        {
            source,
            index
        }).LastOrDefault(m => m.source < 0.4)?.index??0;

        return new
        {
            Animation = false,
            Title = new
            {
                Text = title,
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            Renderer = "svg",
            xAxis = new
            {
                type = "category",
                data = frequency
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                top = "20%",
                bottom = "3%",
                containLabel = true
            },
            tooltip = new { trigger = "axis", axisPointer = new { type = "shadow" } },
            yAxis = new
            {
                type = "value",
            },
            series = new[]
            {
                new
                {
                    data = psdData,
                    type = "line",
                    ShowSymbol = false,
                    MarkLine = new
                    {
                        Symbol = new[] { "none" },
                        Label = new
                        {
                            Show = true,
                        },
                        Data = new[]
                        {
                            new
                            {
                                xAxis = vlfIndex,
                                Label = new
                                {
                                    Formatter = "VLF",
                                    Position = "end",
                                    Padding = new[] { 0, 0, 0, 0 }
                                }
                            },
                            new
                            {
                                xAxis = lfIndex,
                                Label = new
                                {
                                    Formatter = "LF",
                                    Position = "end",
                                    Padding = new[] { 0, 20, 0, 0 }
                                }
                            },
                            new
                            {
                                xAxis = hf, Label = new
                                {
                                    Formatter = "HF",
                                    Position = "end",
                                    Padding = new[] { 0, 0, 0, 0 }
                                }
                            }
                        },
                        LineStyle = new
                        {
                            Type = "solid"
                        }
                    }
                }
            }
        };
    }

    /// <summary>
    /// 多折线
    /// </summary>
    private object GenerateScatterChartOption(string title, List<int> numbers)
    {
        if (numbers == null || numbers.Count == 0) return new object();
        // 按每两个元素分组，并转换为二维数组
        var grouped = numbers
            .Select((value, index) => new { value, index }) // 添加索引
            .GroupBy(x => x.index / 2) // 每两个元素分一组
            .Select(g => g.Select(x => x.value).ToArray()) // 转换每组为数组
            .ToArray();
        return new
        {
            Animation = false,
            Title = new
            {
                text = title,
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            xAxis = new
            {
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                top = "20%",
                bottom = "3%",
                containLabel = true
            },
            yAxis = new
            {
            },
            series = new[]
            {
                new
                {
                    Type = "scatter",
                    Data = grouped,
                    SymbolSize = 4,
                }
            }
        };
    }

    public class Dessert
    {
        /// <summary>
        /// 参数
        /// </summary>
        public string ParameterName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public int Unit { get; set; }

        /// <summary>
        /// 数值
        /// </summary>
        public double NumerValue { get; set; }

        /// <summary>
        /// 参考范围
        /// </summary>
        public double Referencevalue { get; set; }
    }
    private async Task Download()
    {
        await JS.InvokeVoidAsync("exportHtml2PdfForPreview");
    }
    private async Task Print()
    {
        await JS.InvokeVoidAsync("exportHtml2PdfForPreview",true);
        PopupService.ShowProgressCircular();
        await Task.Delay(4000);
        PopupService.HideProgressCircular();
    }


    /// <summary>
    /// 路由数据
    /// </summary>
    [CascadingParameter]
    public AuthRouteData AuthRouteData { get; set; }

    [Inject] private IConfiguration Configuration { get; set; }
    private void Goback()
    {
        StateService.NavigationManager.NavigateTo(Configuration.GetSection("HrvVariable:ReportReturnUrl").Value);
    }

}