﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HX.HRV.MAUI
{
	public static class Unit
	{
		public static string ToHex(this byte[] value, string separator = " ", string format = "X2")
		{
			return string.Join(separator, value.Select(m => m.ToString(format)));
		}
		
		public static long ToUnix(this DateTime dateTime)
		{
			TimeSpan ts = dateTime.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
			return Convert.ToInt64(ts.TotalSeconds);
		}

		/// <summary>  
		/// 获取当前本地时间戳(精确到毫秒)
		/// </summary>  
		/// <returns></returns>        
		public static long ToUnixMs(this DateTime dateTime)
		{
			TimeSpan ts = dateTime.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
			return Convert.ToInt64(ts.TotalMilliseconds);
		}


		public static void Log(string message)
		{
			var file = Path.GetDirectoryName(Environment.ProcessPath);
			File.AppendAllText(Path.Combine(file,"log.txt"), $"{DateTime.Now} {Environment.NewLine}:"+ message);
		}
	}
}
