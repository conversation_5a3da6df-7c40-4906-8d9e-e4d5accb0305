﻿@page "/client/event-tag"
@using HX.HRV.SCI.Shared.Models
@using Masa.Blazor
@attribute [Permission("系统配置/事件标签", MenuOrder = "6", Icon = "mdi-forum|xitong", IsMenu = true)]
@inject StateService StateService
@inject IPopupService PopupService
<MRow Justify="@JustifyTypes.Center" Class="mt-5">
    <MCol Cols="2">
        <div class="d-flex">
            <MTextField Dense Solo Label="标签名称" @bind-Value="_tagName">
            </MTextField>
            <MButton OnClick="OnAddClickAsync" Class="ml-2" Color="blue">添加</MButton>
        </div>
    </MCol>
    <MCol Cols="3">
        <MList Dense Subheader ThreeLine>
            <MListItem>
                <ItemContent>
                    <MListItemContent>
                        <div style="
                            display: flex;
                            align-content: center;
                            justify-content: flex-start;
                            align-items: center;">
                            <MLabel Style="flex: 1">默认标签</MLabel>
                            <MLabel Style="flex: 1">标签名称</MLabel>
                            <MLabel Style="flex: 1">操作</MLabel>
                        </div>
                    </MListItemContent>
                </ItemContent>
            </MListItem>
            @foreach (var tag in _tags)
            {
                <MListItem>
                    <ItemContent>
                        <MListItemContent>
                            <div style="
                            display: flex;
                            align-content: center;
                            justify-content: flex-start;
                            align-items: center;">
                                <MLabel Style="flex: 1">
                                    <MCheckbox TValue="bool" Value="@(_activeTagName == tag.Data?.Name)"
                                               ValueChanged="@(e => OnEventTagChange(e, tag?.Data?.Name))">
                                    </MCheckbox>
                                </MLabel>

                                <MLabel Style="flex: 1">
                                    <MChip Color="primary">
                                        @tag.Data?.Name
                                    </MChip>
                                </MLabel>
                                <MLabel Style="flex: 1">
                                    <MButton OnClick="async () => await OnDeleteClickAsync(tag.Id)" Color="red"
                                             Class="text-decoration-underline" Plain>
                                        删除
                                    </MButton>
                                </MLabel>
                                
                            </div>
                        </MListItemContent>
                    </ItemContent>
                </MListItem>
            }
        </MList>
    </MCol>
</MRow>



@code {

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await InitListAsync();
    }

    private string _activeTagName = "";
    private List<DataModel<EventTagModel>> _tags = new();
    private string _tagName;

    private async Task InitListAsync()
    {
        var url = $"/api/v2/SCI/EventTag/List";
        var result = await StateService.GetAsJsonAsync<List<DataModel<EventTagModel>>>(url);
        if (result != null)
        {
            _tags = result.Data;
            _activeTagName= _tags.FirstOrDefault(t => t.Data?.IsDefault??false)?.Data?.Name;
        }
    }

    private async Task OnAddClickAsync()
    {
        var url = $"/api/v2/SCI/EventTag/Add";
        var model = new EventTagModel
        {
            Name = _tagName
        };
        var result = await StateService.PostAsJsonAsync<DataModel<EventTagModel>>(url, model);
        if (result?.Success ?? false)
        {
            await PopupService.EnqueueSnackbarAsync("添加成功", AlertTypes.Success);
            await InitListAsync();
        }
        else
        {
            await PopupService.EnqueueSnackbarAsync(result?.Message, AlertTypes.Error);
        }
    }
    private async Task OnDeleteClickAsync(string id)
    {
        var confirmed = await PopupService.ConfirmAsync(
            "警告",
            "删除后将无法恢复，确认删除吗?",
            AlertTypes.Warning);
        if (confirmed)
        {
            var url = $"/api/v2/SCI/EventTag/Delete/{id}";
            var result = await StateService.PostAsJsonAsync<bool>(url);
            if (result.Success && result.Data)
            {
                await PopupService.EnqueueSnackbarAsync("删除成功", AlertTypes.Success);
                await InitListAsync();
            }
            else
            {
                await PopupService.EnqueueSnackbarAsync(result?.Message ?? "删除失败", AlertTypes.Error);
            }
        }
    }

    private async Task OnEventTagChange(bool isChecked, string tagName)
    {
        if (isChecked)
        {
            _activeTagName = tagName;
        }
        var id = _tags.FirstOrDefault(t => t.Data?.Name == tagName)?.Id;
        PopupService.ShowProgressCircular();
        var url = $"/api/v2/SCI/EventTag/SetIsDefault/?id={id}";
       var res = await StateService.PostAsJsonAsync<DataModel<EventTagModel>>(url);
       if (res?.Success ?? false)
       {
           await PopupService.EnqueueSnackbarAsync("设置成功", AlertTypes.Success);
       }
       else
       {
           await PopupService.EnqueueSnackbarAsync(res?.Message ?? "设置失败", AlertTypes.Error);
       }
       PopupService.HideProgressCircular();
    }

}