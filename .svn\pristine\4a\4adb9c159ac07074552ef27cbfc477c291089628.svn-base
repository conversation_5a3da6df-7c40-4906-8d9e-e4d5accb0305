﻿@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
<DeviceStatusCardHead
    IsCharge="@(true)"
    DeviceStatusData="DeviceStatusData"
    LinkType="1">
</DeviceStatusCardHead>
@if (DeviceStatusData?.DeviceStatus != EnumDeviceStatus.离线||DeviceStatusData?.IsUsbLinked==true)
{
    <DeviceStatusCardContent DeviceStatusData="DeviceStatusData"/>
}
else
{
    <div style="height: 80%;" class="d-flex align-center justify-center">
        <img src="/images/icon/<EMAIL>" style="width:2rem"/>
        <MLabel Style="font-size: 2rem">离线</MLabel>
    </div>
}

@code {
    public DeviceStatusViewModel DeviceStatusData => DeviceDataList.FirstOrDefault(x => x.Device.Id == DeviceId);

    [CascadingParameter(Name = "DeviceDataList")]
    private List<DeviceStatusViewModel> DeviceDataList { get; set; } = new();

    [Parameter] public string DeviceId { get; set; }
    private System.Threading.Timer _timer;

    private void Refresh()
    {
        InvokeAsync(StateHasChanged);
    }

}