<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UFU.IoT</name>
    </assembly>
    <members>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController">
            <summary>
            配置项管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            配置项管理
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.List(System.String)">
            <summary>
            配置项管理
            </summary>
            <param name="typeId">设备类型编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.Exists(System.String,System.String)">
            <summary>
            配置项管理接口/存在
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.Get(System.String,System.String)">
            <summary>
            配置项管理接口/详情
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.Add(System.String,UFU.IoT.Models.DataItem)">
            <summary>
            配置项管理接口/添加
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="dataItem">配置项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.Edit(System.String,System.String,UFU.IoT.Models.DataItem)">
            <summary>
            配置项管理接口/编辑
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">原变量名</param>
            <param name="dataItem">配置项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.Delete(System.String,System.String)">
            <summary>
            配置项管理接口/删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.ConfigItemsController.BatchDelete(System.String,System.String[])">
            <summary>
            配置项管理接口/批量删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="keys">变量名</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController">
            <summary>
            数据项管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            数据项
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.List(System.String)">
            <summary>
            数据项管理
            </summary>
            <param name="typeId">设备类型编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.Exists(System.String,System.String)">
            <summary>
            数据项管理接口/存在
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.Get(System.String,System.String)">
            <summary>
            数据项管理接口/详情
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.Add(System.String,UFU.IoT.Models.DataItem)">
            <summary>
            数据项管理接口/添加
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="dataItem">数据项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.Edit(System.String,System.String,UFU.IoT.Models.DataItem)">
            <summary>
            数据项管理接口/编辑
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">原变量名</param>
            <param name="dataItem">数据项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.Delete(System.String,System.String)">
            <summary>
            数据项管理接口/删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataItemsController.BatchDelete(System.String,System.String[])">
            <summary>
            数据项管理接口/批量删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="keys">变量名</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController">
            <summary>
            数据映射
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            数据映射管理
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.List(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            数据映射管理接口/列表
            </summary>
            <param name="ids">设备编号,逗号隔开</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.Get(System.String)">
            <summary>
            数据映射管理接口/详情
            </summary>
            <param name="id">编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.Add(UFU.IoT.Models.DataMappingModel)">
            <summary>
            数据映射管理接口/添加
            </summary>
            <param name="model">映射数据</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.Edit(System.String,UFU.IoT.Models.DataMappingModel)">
            <summary>
            数据映射管理接口/编辑
            </summary>
            <param name="id">设备编号</param>
            <param name="model">映射数据</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.Delete(System.String)">
            <summary>
            数据映射管理接口/删除
            </summary>
            <param name="id">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DataMappingController.BatchDelete(System.String[])">
            <summary>
            数据映射管理接口/批量删除
            </summary>
            <param name="ids">设备编号</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.DatasController">
            <summary>
            数据管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DatasController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            数据管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DatasController.ResetLatestData(System.String)">
            <summary>
            设备最新数据重置
            </summary>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DatasController.Latest(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            数据管理接口/最新数据
            </summary>
            <param name="placeId">场地编号</param>
            <param name="typeId">类型编号</param>
            <param name="deviceIds">设备编号,逗号隔开</param>
            <param name="start">开始时间</param>
            <param name="end">结束时间</param>
            <param name="count">最大数据条数(不超过10000)</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DatasController.History(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            数据管理接口/历史数据
            </summary>
            <param name="placeId">场地编号</param>
            <param name="typeId">设备类型编号</param>
            <param name="deviceIds">设备编号,逗号隔开</param>
            <param name="start">开始时间</param>
            <param name="end">结束时间</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DatasController.StatisticsTaskList(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            统计数据计算接口/计算状态
            </summary>
            <param name="deviceId">设备编号</param>
            <param name="start">数据开始时间</param>
            <param name="end">数据结束数据</param>
            <param name="cycle">统计周期</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DatasController.StatisticsTaskClear(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32})">
            <summary>
            统计数据计算接口/清除
            </summary>
            <param name="deviceId">设备编号</param>
            <param name="start">数据开始时间</param>
            <param name="end">数据结束数据</param>
            <param name="cycle">统计周期</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController">
            <summary>
            设备管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            设备管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.List(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            设备管理接口/列表
            </summary>
            <param name="placeId">场地编号</param>
            <param name="id">设备编号</param>
            <param name="ids">多个设备编号,逗号隔开</param>
            <param name="typeId">类型编号</param>
            <param name="deviceSN">设备序列号</param>
            <param name="name">设备名</param>
            <param name="chipSN">芯片序列号</param>
            <param name="mac">MAC地址</param>
            <param name="macs">多个MAC地址,逗号隔开</param>
            <param name="isOnline">是否在线</param>
            <param name="userName">用户名</param>
            <param name="tel">电话</param>
            <param name="district">行政区</param>
            <param name="address">地址</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.Count(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            设备管理接口/列表
            </summary>
            <param name="placeIds">场地编号,逗号隔开</param>
            <param name="typeIds">类型编号,逗号隔开</param>
            <param name="ids">多个设备编号,逗号隔开</param>
            <param name="deviceSN">设备序列号,模糊查询</param>
            <param name="name">设备名，模糊查询</param>
            <param name="chipSN">芯片序列号，模糊查询</param>
            <param name="mac">MAC地址，模糊查询</param>
            <param name="macs">多个MAC地址,逗号隔开</param>
            <param name="userName">用户名</param>
            <param name="tel">电话</param>
            <param name="district">行政区</param>
            <param name="address">地址</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.Get(System.String)">
            <summary>
            设备管理接口/详情
            </summary>
            <param name="id">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.Add(UFU.IoT.Models.DeviceModel)">
            <summary>
            设备管理接口/添加
            </summary>
            <param name="deviceModel">设备信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.Edit(System.String,UFU.IoT.Models.DeviceModel)">
            <summary>
            设备管理接口/编辑
            </summary>
            <param name="id">设备编号</param>
            <param name="deviceModel">设备信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.Delete(System.String)">
            <summary>
            设备管理接口/删除
            </summary>
            <param name="id">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.BatchDelete(System.String[])">
            <summary>
            设备管理接口/批量删除
            </summary>
            <param name="ids">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.CreateSN(System.DateTime,System.Int32,System.Int32)">
            <summary>
            创建设备序列号
            </summary>
            <param name="dateTime">生产日期</param>
            <param name="startNumber">开始号码</param>
            <param name="count">数量</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.QuerySN(System.String)">
            <summary>
            查询序列号
            </summary>
            <param name="sn"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.Bind(System.String,System.String)">
            <summary>
            网页绑定设备
            </summary>
            <param name="type"></param>
            <param name="sn"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DevicesController.BindDevice(System.String,System.String,System.String,System.String)">
            <summary>
            通过设备类型和序列号绑定设备
            </summary>
            <param name="id">设备编号</param>
            <param name="type">类型编号</param>
            <param name="sn">序列号</param>
            <param name="mac">设备MAC地址</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController">
            <summary>
            产品管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            产品管理
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.List(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            产品管理接口/列表
            </summary>
            <param name="id">产品（设备类型）编号</param>
            <param name="name">产品名称</param>
            <param name="manufacturer">产商</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.Get(System.String)">
            <summary>
            产品管理接口/详情
            </summary>
            <param name="id">产品详情</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.Add(UFU.IoT.Models.DeviceTypeModel)">
            <summary>
            产品管理接口/添加
            </summary>
            <param name="model">产品信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.Edit(System.String,UFU.IoT.Models.DeviceTypeModel)">
            <summary>
            产品管理接口/编辑
            </summary>
            <param name="id">编号</param>
            <param name="deviceTypeModel">产品信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.Delete(System.String)">
            <summary>
            产品管理接口/删除
            </summary>
            <param name="id">编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.DeviceTypesController.BatchDelete(System.String[])">
            <summary>
            产品管理接口/批量删除
            </summary>
            <param name="ids">产品编号</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.HomeController">
            <summary>
            首页
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.HomeController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            首页
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.HomeController.Index">
            <summary>
            首页
            </summary>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.HomeController.Info">
            <summary>
            首页
            </summary>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.OTAController">
            <summary>
            固件升级
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.OTAController.Update(System.String)">
            <summary>
            固件升级
            </summary>
            <param name="id">固件名</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController">
            <summary>
            场地设备管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            场地设备管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.List(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            场地设备管理接口/列表
            </summary>
            <param name="id">用户设备编号</param>
            <param name="userPlaceId">用户场地编号</param>
            <param name="placeIds">场地编号，逗号隔开，精确匹配</param>
            <param name="organPath">组织机构路径</param>
            <param name="name">用户设备名称或设备名称</param>
            <param name="deviceId">设备编号</param>
            <param name="deviceSN">设备序列号</param>
            <param name="mac">设备硬件编号</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.Get(System.String)">
            <summary>
            场地设备管理接口/详情
            </summary>
            <param name="id">场地设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.Add(UFU.IoT.Models.PlaceDeviceModel)">
            <summary>
            场地设备管理接口/添加
            </summary>
            <param name="model">设备信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.Edit(System.String,UFU.IoT.Models.PlaceDeviceModel)">
            <summary>
            场地设备管理接口/编辑
            </summary>
            <param name="id">场地设备编号</param>
            <param name="placeDeviceModel">设备信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.Delete(System.String)">
            <summary>
            场地设备管理接口/删除
            </summary>
            <param name="id">场地编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.BatchDelete(System.String[])">
            <summary>
            场地设备管理接口/批量删除
            </summary>
            <param name="ids">场地编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.MyList(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            我的设备列表
            </summary>
            <param name="id">用户设备编号</param>
            <param name="userPlaceId">用户场地编号</param>
            <param name="placeIds">场地编号，逗号隔开，精确匹配</param>
            <param name="organPath">组织机构路径</param>
            <param name="name">用户设备名称或设备名称</param>
            <param name="deviceId">设备编号</param>
            <param name="deviceSN">设备序列号</param>
            <param name="mac">设备硬件编号</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlaceDevicesController.MyGet(System.String)">
            <summary>
            我的设备详情
            </summary>
            <param name="id">场地设备编号</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController">
            <summary>
            场地管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            场地管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController.List(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            我的场地列表
            如果用户场地的Id为null，说明是通过其他权限获取到的用户场地，实际上并没有绑定场地
            </summary>
            <param name="placeId">场地编号，模糊查询</param>
            <param name="userPlaceId">用户场地编号，模糊查询</param>
            <param name="placeIds">场地编号，多个场地使用英文逗号隔开，完全匹配</param>
            <param name="userPlaceIds">用户场地编号，多个场地使用英文逗号隔开，完全匹配</param>
            <param name="name">用户场地或场地名</param>
            <param name="organPath">组织机构路径</param>
            <param name="userName"></param>
            <param name="tel"></param>
            <param name="district"></param>
            <param name="address"></param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController.Get(System.String)">
            <summary>
            场地管理接口/详情
            </summary>
            <param name="id">场地编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController.Add(UFU.IoT.Models.PlaceUserModel)">
            <summary>
            场地管理接口/添加
            </summary>
            <param name="model">场地信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController.Edit(System.String,UFU.IoT.Models.PlaceUserModel)">
            <summary>
            场地管理接口/编辑
            </summary>
            <param name="id">场地编号</param>
            <param name="userPlaceModel">场地信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.PlacesController.Delete(System.String[])">
            <summary>
            场地管理接口/删除
            </summary>
            <param name="ids">场地编号</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController">
            <summary>
            模板管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            模板管理
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController.Unzip(System.String)">
            <summary>
            模板管理接口/解压
            </summary>
            <param name="path">模板路径</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController.UpdateDevice(System.String,System.String)">
            <summary>
            模板管理接口/更新设备模板
            </summary>
            <param name="id">设备类型编号</param>
            <param name="path">模板路径</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController.UpdatePlace(System.String,System.String)">
            <summary>
            模板管理接口/更新场地模板
            </summary>
            <param name="id">场地编号</param>
            <param name="path">模板路径</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController.UpdateDeviceDefault(System.String)">
            <summary>
            模板管理接口/更新设备默认模板
            </summary>
            <param name="path">模板路径</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.TemplateController.UpdatePlaceDefault(System.String)">
            <summary>
            模板管理接口/更新场地默认模板
            </summary>
            <param name="path">模板路径</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController">
            <summary>
            用户场地管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            用户场地管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.List(System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            用户场地管理接口/列表
            </summary>
            <param name="id">用户场地编号</param>
            <param name="name">用户场地名</param>
            <param name="userId">用户编号</param>
            <param name="placeId">场地编号</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.Get(System.String)">
            <summary>
            用户场地管理接口/详情
            </summary>
            <param name="id">用户场地编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.Add(UFU.IoT.Models.PlaceUserModel)">
            <summary>
            用户场地管理接口/添加
            </summary>
            <param name="model">场地信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.Edit(System.String,UFU.IoT.Models.PlaceUserModel)">
            <summary>
            用户场地管理接口/编辑
            </summary>
            <param name="id">场地编号</param>
            <param name="userPlaceModel">场地信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.Delete(System.String)">
            <summary>
            用户场地管理接口/删除
            </summary>
            <param name="id">场地编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V1.UserPlacesController.BatchDelete(System.String[])">
            <summary>
            用户场地管理接口/批量删除
            </summary>
            <param name="ids">场地编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.AutoRuleController.Add(UFU.CoreFX.Models.DataModel{UFU.IoT.Shared.Models.AutoRuleModel})">
            <summary>
            添加自动规则
            </summary>
            <param name="model">自动策略数据</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.AutoRuleController.Edit(UFU.CoreFX.Models.DataModel{UFU.IoT.Shared.Models.AutoRuleModel})">
            <summary>
            编辑自动规则
            </summary>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.AutoRuleController.Delete(System.String)">
            <summary>
            删除自动规则
            </summary>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigBindingController">
            <summary>
             命令配置
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigBindingController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            ctor
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigBindingController.AddCommandConfigBinding(UFU.IoT.Shared.Models.CommandConfigBindingModel)">
            <summary>
            添加命令配置
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigBindingController.UpdateCommandConfigBinding(UFU.IoT.Shared.Models.CommandConfigBindingModel)">
            <summary>
            更新命令配置
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigBindingController.GetAllCommandConfigBinding(System.String)">
            <summary>
            设备类型列表
            </summary>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigController">
            <summary>
             命令配置
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            ctor
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigController.AddCommandConfig(UFU.IoT.Shared.Models.CommandConfigModel)">
            <summary>
            添加命令配置
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigController.UpdateCommandConfig(UFU.IoT.Shared.Models.CommandConfigModel)">
            <summary>
            更新命令配置
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.CommandConfigController.GetAllCommandConfig">
            <summary>
            设备类型列表
            </summary>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController">
            <summary>
            配置项管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            配置项管理
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.List(System.String)">
            <summary>
            配置项管理
            </summary>
            <param name="typeId">设备类型编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.Exists(System.String,System.String)">
            <summary>
            配置项管理接口/存在
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.Get(System.String,System.String)">
            <summary>
            配置项管理接口/详情
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.Add(System.String,UFU.IoT.Models.DataItem)">
            <summary>
            配置项管理接口/添加
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="dataItem">配置项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.Edit(System.String,System.String,UFU.IoT.Models.DataItem)">
            <summary>
            配置项管理接口/编辑
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">原变量名</param>
            <param name="dataItem">配置项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.Delete(System.String,System.String)">
            <summary>
            配置项管理接口/删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.ConfigItemsController.BatchDelete(System.String,System.String[])">
            <summary>
            配置项管理接口/批量删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="keys">变量名</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController">
            <summary>
            数据项管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            数据项
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.List(System.String)">
            <summary>
            数据项管理
            </summary>
            <param name="typeId">设备类型编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.Exists(System.String,System.String)">
            <summary>
            数据项管理接口/存在
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.Get(System.String,System.String,System.String)">
            <summary>
            数据项管理接口/详情
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <param name="groupName">groupName</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.Add(System.String,UFU.IoT.Models.DataItem)">
            <summary>
            数据项管理接口/添加
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="dataItem">数据项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.Edit(System.String,System.String,System.String,UFU.IoT.Models.DataItem)">
            <summary>
            数据项管理接口/编辑
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">原变量名</param>
            <param name="dataItem">数据项</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.Delete(System.String,System.String,System.String)">
            <summary>
            数据项管理接口/删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="key">变量名</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DataItemsController.BatchDelete(System.String,System.String[])">
            <summary>
            数据项管理接口/批量删除
            </summary>
            <param name="typeId">设备类型编号</param>
            <param name="keys">变量名</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.DatasController">
            <summary>
            数据管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DatasController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            数据管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DatasController.ResetLatestData(System.String)">
            <summary>
            设备最新数据重置
            </summary>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DatasController.Latest(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            数据管理接口/最新数据
            </summary>
            <param name="placeId">场地编号</param>
            <param name="typeId">类型编号</param>
            <param name="deviceIds">设备编号,逗号隔开</param>
            <param name="start">开始时间</param>
            <param name="end">结束时间</param>
            <param name="count">最大数据条数(不超过10000)</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DatasController.History(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            数据管理接口/历史数据
            </summary>
            <param name="placeId">场地编号</param>
            <param name="typeId">设备类型编号</param>
            <param name="deviceIds">设备编号,逗号隔开</param>
            <param name="start">开始时间</param>
            <param name="end">结束时间</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DatasController.StatisticsTaskList(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            统计数据计算接口/计算状态
            </summary>
            <param name="deviceId">设备编号</param>
            <param name="start">数据开始时间</param>
            <param name="end">数据结束数据</param>
            <param name="cycle">统计周期</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DatasController.StatisticsTaskClear(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32})">
            <summary>
            统计数据计算接口/清除
            </summary>
            <param name="deviceId">设备编号</param>
            <param name="start">数据开始时间</param>
            <param name="end">数据结束数据</param>
            <param name="cycle">统计周期</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController">
            <summary>
            设备管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            设备管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.List(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            设备管理接口/列表
            </summary>
            <param name="placeId">场地编号</param>
            <param name="id">设备编号</param>
            <param name="ids">多个设备编号,逗号隔开</param>
            <param name="typeId">类型编号</param>
            <param name="deviceSN">设备序列号</param>
            <param name="name">设备名</param>
            <param name="chipSN">芯片序列号</param>
            <param name="mac">MAC地址</param>
            <param name="macs">多个MAC地址,逗号隔开</param>
            <param name="isOnline">是否在线</param>
            <param name="userName">用户名</param>
            <param name="tel">电话</param>
            <param name="district">行政区</param>
            <param name="address">地址</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.Count(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            设备管理接口/列表
            </summary>
            <param name="placeIds">场地编号,逗号隔开</param>
            <param name="typeIds">类型编号,逗号隔开</param>
            <param name="ids">多个设备编号,逗号隔开</param>
            <param name="deviceSN">设备序列号,模糊查询</param>
            <param name="name">设备名，模糊查询</param>
            <param name="chipSN">芯片序列号，模糊查询</param>
            <param name="mac">MAC地址，模糊查询</param>
            <param name="macs">多个MAC地址,逗号隔开</param>
            <param name="userName">用户名</param>
            <param name="tel">电话</param>
            <param name="district">行政区</param>
            <param name="address">地址</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.Get(System.String)">
            <summary>
            设备管理接口/详情
            </summary>
            <param name="id">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.Add(UFU.IoT.Models.DeviceModel)">
            <summary>
            设备管理接口/添加
            </summary>
            <param name="deviceModel">设备信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.Edit(System.String,UFU.IoT.Models.DeviceModel)">
            <summary>
            设备管理接口/编辑
            </summary>
            <param name="id">设备编号</param>
            <param name="deviceModel">设备信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.Delete(System.String)">
            <summary>
            设备管理接口/删除
            </summary>
            <param name="id">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.BatchDelete(System.String[])">
            <summary>
            设备管理接口/批量删除
            </summary>
            <param name="ids">设备编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.CreateSN(System.DateTime,System.Int32,System.Int32)">
            <summary>
            创建设备序列号
            </summary>
            <param name="dateTime">生产日期</param>
            <param name="startNumber">开始号码</param>
            <param name="count">数量</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.QuerySN(System.String)">
            <summary>
            查询序列号
            </summary>
            <param name="sn"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.Bind(System.String,System.String)">
            <summary>
            网页绑定设备
            </summary>
            <param name="type"></param>
            <param name="sn"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DevicesController.BindDevice(System.String,System.String,System.String,System.String)">
            <summary>
            通过设备类型和序列号绑定设备
            </summary>
            <param name="id">设备编号</param>
            <param name="type">类型编号</param>
            <param name="sn">序列号</param>
            <param name="mac">设备MAC地址</param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController">
            <summary>
            产品管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.#ctor(UFU.CoreFX.Data.DataRepository)">
            <summary>
            产品管理
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.List(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            产品管理接口/列表
            </summary>
            <param name="id">产品（设备类型）编号</param>
            <param name="name">产品名称</param>
            <param name="manufacturer">产商</param>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.GetDeviceTypeModelDetail(System.String)">
            <summary>
            产品管理接口/详情
            </summary>
            <param name="id">产品详情</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.Add(UFU.CoreFX.Models.DataModel{UFU.IoT.Models.DeviceTypeModel})">
            <summary>
            产品管理接口/添加
            </summary>
            <param name="model">产品信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.Edit(System.String,UFU.CoreFX.Models.DataModel{UFU.IoT.Models.DeviceTypeModel})">
            <summary>
            产品管理接口/编辑
            </summary>
            <param name="id">编号</param>
            <param name="deviceTypeModel">产品信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.Delete(System.String)">
            <summary>
            产品管理接口/删除
            </summary>
            <param name="id">编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.BatchDelete(System.String[])">
            <summary>
            产品管理接口/批量删除
            </summary>
            <param name="ids">产品编号</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.DeviceTypesController.GetAllDeviceTypes">
            <summary>
            设备类型列表
            </summary>
        </member>
        <member name="T:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController">
            <summary>
            设备管理
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.#ctor(UFU.CoreFX.Data.DataRepository,UFU.CoreFX.Models.CoreDbContext)">
            <summary>
            设备管理
            </summary>
            <param name="context"></param>
            <param name="coreContext"></param>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.List(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Boolean)">
            <summary>
            设备管理接口/虚拟设备列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.Add(UFU.CoreFX.Models.DataModel{UFU.IoT.Models.DeviceModel})">
            <summary>
            设备管理接口/添加虚拟设备
            </summary>
            <param name="model">组织机构信息</param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.Edit(UFU.CoreFX.Models.DataModel{UFU.IoT.Models.DeviceModel})">
            <summary>
            设备管理接口/编辑虚拟设备
            </summary>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.GetDeviceTypes">
            <summary>
            设备类型列表
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.GetReadDevices(System.Boolean)">
            <summary>
            真实设备列表
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.GetDeviceDetail(System.String)">
            <summary>
            获取设备详情
            </summary>
        </member>
        <member name="M:UFU.IoT.Areas.IoT.Controllers.V2.VirtualDeviceController.DelDeviceDetail(System.String)">
            <summary>
            删除虚拟设备
            </summary>
        </member>
        <member name="T:UFU.IoT.IoTModule">
            <summary>
            物联网管理系统
            </summary>
        </member>
        <member name="P:UFU.IoT.IoTModule.Name">
            <summary>
            模块名
            </summary>
        </member>
        <member name="P:UFU.IoT.IoTModule.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:UFU.IoT.IoTModule.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="M:UFU.IoT.IoTModule.Configure(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            配置服务
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:UFU.IoT.Services.AdminWebSocket">
            <summary>
            物联网管理员WebSocket
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.AdminWebSocket.DeviceIds">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.AdminWebSocket.DeviceTypeIds">
            <summary>
            设备类型编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.AdminWebSocket.ConnectAll">
            <summary>
            连接所有设备
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.AdminWebSocket.ClientSource">
            <summary>
            0 默认 1客户端
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.AdminWebSocket.Close">
            <summary>
            关闭连接
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.AdminWebSocket.OnConnect">
            <summary>
            连接
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.AdminWebSocket.OnReciveText(System.String)">
            <summary>
            收到文本消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.AdminWebSocket.GetDevices">
            <summary>
            获取要连接的设备
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.AutoRuleService.MsgId">
            <summary>
            消息编号
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.AutoRuleService.OnSavedData(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.DataModel)">
            <summary>
            收到设备消息，会将每个消息存储到 DataModelDic 中， key 为 DeviceId， value 为 DataModel
            </summary>
            <param name="connect"></param>
            <param name="data"></param>
        </member>
        <member name="T:UFU.IoT.Services.BinaryDeviceWebSocketConnect">
            <summary>
            物联网设备WebSocket
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.BinaryDeviceWebSocketConnect.ConnectMode">
            <summary>
            连接方式
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.OnReciveText(System.String)">
            <summary>
            收到文本消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.OnReciveBinary(System.Byte[])">
            <summary>
            收到二进制数据
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.ParsePPGDataByDataType(System.Byte[])">
            <summary>
            9个字节一组, 一组三个数据 ，每个数据三个字节
            //红光;//红外光//绿光
            将数据添加到JsonNoe["data"]
            </summary>
            <param name="bytes"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.ParseSPO2DataByDataType(System.Byte[])">
            <summary>
            解析数据类型中的心率、血氧和电量数据。
            </summary>
            <param name="bytes">要解析的字节数组。</param>
            <returns>包含心率、血氧和电量的元组。</returns>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.Close">
            <summary>
            关闭连接
            
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.SendText(System.String)">
            <summary>
            发送文本消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.BinaryDeviceWebSocketConnect.SendBinary(System.Byte[])">
            <summary>
            发送二进制消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="T:UFU.IoT.Services.BinaryIoTService">
            <summary>
            IoT服务
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.BinaryIoTService.RealTimeDataQueue">
            <summary>
            实时数据保存队列
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.BinaryIoTService.HistoryDataQueue">
            <summary>
            历史数据保存队列
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.BinaryIoTService.DeviceTypes">
            <summary>
            全部设备类型
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.BinaryIoTService.Devices">
            <summary>
            全部在线设备
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.BinaryIoTService.OnDeviceOnlineEventHandler">
            <summary>
            设备上线
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.BinaryIoTService.OnDeviceOfflineEventHandler">
            <summary>
            设备离线
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.BinaryIoTService.OnReceivedDeviceMsgEventHandler">
            <summary>
            收到设备消息
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.BinaryIoTService.OnSaveDataEventHandler">
            <summary>
            准备保存数据
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.BinaryIoTService.OnReceivedUserMsgEventHandler">
            <summary>
            收到用户消息(IWebSocket在后台发出消息时会为null)
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.BinaryIoTService.OnDeviceOnline">
            <summary>
            设备上线
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.BinaryIoTService.OnDeviceOffline">
            <summary>
            设备离线
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.BinaryIoTService.OnReceivedDeviceMsg">
            <summary>
            收到设备消息
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.BinaryIoTService.OnSavingData">
            <summary>
            准备保存数据
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.BinaryIoTService.OnSavedData">
            <summary>
            数据保存完成
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.BinaryIoTService.OnReceivedUserMsg">
            <summary>
            收到用户消息
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.#cctor">
            <summary>
            启动定时任务
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.ReceiveDeviceMsg(UFU.IoT.Services.IConnect,System.String)">
            <summary>
            1.0版消息处理
            </summary>
            <param name="connect"></param>
            <param name="msg"></param>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.WriteCommandHandler(UFU.IoT.Services.IConnect,UFU.IoT.Shared.Models.BinaryDevice,System.UInt16,System.Text.Json.Nodes.JsonNode)">
            <summary>
            写命令
            </summary>
            <param name="connect"></param>
            <param name="binaryDevice"></param>
            <param name="msgId"></param>
            <param name="jsonNode"></param>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.RegCommandHandler(UFU.IoT.Services.IConnect,System.UInt16,System.Int64,UFU.IoT.Shared.Models.BinaryDevice)">
            <summary>
            设备注册
            </summary>
            <param name="connect"></param>
            <param name="msg"></param>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.SaveDeviceData(UFU.IoT.Services.ConnectDevice,System.UInt16,System.Text.Json.Nodes.JsonNode)">
            <summary>
            保存设备数据
            </summary>
            <param name="device"></param>
            <param name="msg"></param>
            
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.ReadCommandHandler(UFU.IoT.Services.IConnect,System.UInt16,System.Text.Json.Nodes.JsonNode)">
            <summary>
            从Json的DataGroup中读取所有的KEY
            </summary>
            <param name="connect"></param>
            <param name="msgId"></param>
            <param name="jsonReadNode"></param>
        </member>
        <member name="F:UFU.IoT.Services.BinaryIoTService.EmptyObjectKey">
            <summary>
            
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.GenerateResponseBytes(System.Byte[])">
            <summary>
            构建返回包结构
            </summary>
            <param name="dataContent"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.GenerateResponseBytes(System.String)">
            <summary>
             构建返回包结构
            </summary>
            <param name="jsonData"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.GenerateResponseBytes(System.Object)">
            <summary>
             构建返回包结构
            </summary>
            <param name="jsonData"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.BinaryIoTService.DeviceConnectClosed(System.String)">
            <summary>
            连接断开
            </summary>
            <param name="connectId"></param>
        </member>
        <member name="T:UFU.IoT.Services.CommandConfigWebSocketService">
            <summary>
            IoT服务
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.CommandConfigWebSocketService.CommandConfigConnect.ProxyDevice">
            <summary>
            当前网关
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.CommandConfigWebSocketService.CommandConfigConnect.CommandConfigBinding">
            <summary>
             命令配置
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.CommandConfigWebSocketService.CommandConfigConnect.ReceiveToDevice">
            <summary>
             接收到的设备
            </summary>
        </member>
        <member name="F:UFU.IoT.Services.CommandConfigWebSocketService.CommandConfigBindings">
            <summary>
            命令配置
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.CommandConfigWebSocketService.RealTimeDataQueue">
            <summary>
            实时数据保存队列
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.CommandConfigWebSocketService.QueueData.LastEnqueueDate">
            <summary>
            最后一次入队时间
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.CommandConfigWebSocketService.InitOnlineDeviceCommandConfig">
            <summary>
             获取命令配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.CommandConfigWebSocketService.BatchSendCommand">
            <summary>
            发送命令
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.CommandConfigWebSocketService.#cctor">
            <summary>
            
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.ConnectDevice">
            <summary>
            已连接设备
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.MsgId">
            <summary>
            消息编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.Connect">
            <summary>
            连接对象
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.DeviceId">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.Device">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.LastDataTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.ConnectTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.TimeOffset">
            <summary>
            时间差，单位毫秒(服务器时间-设备时间)
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.Token">
            <summary>
            登录Token
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.NeedUpdate">
            <summary>
            是否需要更新数据库
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.IsOnline">
            <summary>
            是否在线
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.FindValue">
            <summary>
            查找值
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.ConnectDevice.OTAFile">
            <summary>
            OTA文件
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.ConnectDevice.#ctor(UFU.IoT.Services.IConnect,UFU.IoT.Models.DeviceModel)">
            <summary>
            构造函数
            </summary>
            <param name="connect">连接对象</param>
            <param name="device">设备对象</param>
        </member>
        <member name="M:UFU.IoT.Services.ConnectDevice.OTAStart(System.Int64)">
            <summary>
            开始OTA
            </summary>
            <param name="offset"></param>
        </member>
        <member name="M:UFU.IoT.Services.ConnectDevice.OTAStop">
            <summary>
            停止OTA
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.ConnectDevice.IsOTAStart">
            <summary>
            OTA是否已经开始
            </summary>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Services.DataAnalysisService">
            <summary>
            数据分析
            </summary>
        </member>
        <member name="F:UFU.IoT.Services.DataAnalysisService.DataStatisticsTasks">
            <summary>
            数据统计任务列表
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.DataAnalysisService.AnalysisDataEventHandler">
            <summary>
            分析数据计算完成委托
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.DataAnalysisService.OnAnalysisDataComputed">
            <summary>
            分析数据计算完成事件
            对于历史数据来说，数据Id可能不准确
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.DataAnalysisService.OnAnalysisDataSaved">
            <summary>
            分析数据计算完成事件
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DataAnalysisService.Start">
            <summary>
            开始计算
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DataAnalysisService.CreateTask(System.Collections.Generic.List{System.String},System.String,System.DateTime,System.DateTime)">
            <summary>
            手动创建数据统计任务
            最长只能创建24小时的计算任务
            </summary>
            <param name="deviceIds"></param>
            <param name="deviceTypeId"></param>
            <param name="startTime"></param>
            <param name="endTime"></param>
        </member>
        <member name="M:UFU.IoT.Services.DataAnalysisService.CreateTask(System.String,System.String,System.DateTime,UFU.IoT.Models.DataModel)">
            <summary>
            创建数据统计任务
            </summary>
            <param name="deviceId"></param>
            <param name="deviceTypeId"></param>
            <param name="time"></param>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DataAnalysisService.GroupDataCalcAsync(System.Collections.Generic.List{UFU.IoT.Services.DataStatisticsTask})">
            <summary>
            分组数据统计计算
            </summary>
            <param name="dataTasks">相同周期、相同计算时间的统计数据计算任务</param>
        </member>
        <member name="M:UFU.IoT.Services.DataAnalysisService.DataCalc(UFU.IoT.Services.DataStatisticsTask,UFU.IoT.Models.DeviceTypeModel)">
            <summary>
            统计数据数值计算
            </summary>
            <param name="dataTask"></param>
            <param name="deviceType"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.DataAnalysisService.GetDataValue(System.Object)">
            <summary>
            数据类型转换
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Services.DataStatisticsTask">
            <summary>
            数据统计任务
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.DeviceId">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.DeviceTypeId">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.Cycle">
            <summary>
            统计周期（秒）
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.CycleStartTime">
            <summary>
            周期开始时刻
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.CycleEndTime">
            <summary>
            周期结束时刻
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.FirstTimeCalc">
            <summary>
            是否第一次计算
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DataStatisticsTask.Datas">
            <summary>
            原始数据,只用于计算最小周期
            集群环境下设备离线后应该将数据清空，防止数据不全计算错误（尚未实现）
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.DeviceTcpConnect">
            <summary>
            TCP连接
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.Start">
            <summary>
            启动服务
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DeviceTcpConnect.ConnectId">
            <summary>
            连接编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DeviceTcpConnect.ConnectMode">
            <summary>
            连接方式
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.#ctor(System.String,System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            初始化
            </summary>
            <param name="connectId"></param>
            <param name="args"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.OnReceived(System.Byte[])">
            <summary>
            接收到设备数据
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.StartRececive">
            <summary>
            接收并处理数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.SendBinary(System.Byte[])">
            <summary>
            发送二进制消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.SendText(System.String)">
            <summary>
            发送文本消息
            第一个字节为协议号，接下来2个字节为开始标志两个2，接下来2个字节为数据长度（无符号整数），最后四个字节是CRC32校验
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTcpConnect.Close">
            <summary>
            关闭网络连接
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.DeviceTokenService">
            <summary>
            跨服务器周期任务
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTokenService.#cctor">
            <summary>
            静态构造函数开启周期任务
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTokenService.CleanDeviceToken">
            <summary>
            清理设备登录Token
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceTokenService.Start">
            <summary>
            开始执行
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.DeviceWebSocketConnect">
            <summary>
            物联网设备WebSocket
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.DeviceWebSocketConnect.ConnectMode">
            <summary>
            连接方式
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceWebSocketConnect.OnReciveText(System.String)">
            <summary>
            收到文本消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceWebSocketConnect.OnReciveBinary(System.Byte[])">
            <summary>
            收到二进制数据
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceWebSocketConnect.Close">
            <summary>
            关闭连接
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.DeviceWebSocketConnect.SendText(System.String)">
            <summary>
            发送文本消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.DeviceWebSocketConnect.SendBinary(System.Byte[])">
            <summary>
            发送二进制消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="T:UFU.IoT.Services.FindWebSocket">
            <summary>
            物联网设备查找WebSocket
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.FindWebSocket.DeviceTypeId">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.FindWebSocket.OnConnect">
            <summary>
            连接
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.FindWebSocket.OnReciveText(System.String)">
            <summary>
            收到文本消息
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:UFU.IoT.Services.FindWebSocket.Close">
            <summary>
            关闭连接
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.ConnectMode">
            <summary>
            连接方式
            </summary>
        </member>
        <member name="F:UFU.IoT.Services.ConnectMode.Tcp">
            <summary>
            Tcp连接
            </summary>
        </member>
        <member name="F:UFU.IoT.Services.ConnectMode.WebSocket">
            <summary>
            WebSocket连接
            </summary>
        </member>
        <member name="F:UFU.IoT.Services.ConnectMode.Http">
            <summary>
            Http连接
            </summary>
        </member>
        <member name="F:UFU.IoT.Services.ConnectMode.SerialPort">
            <summary>
            串口连接
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IConnect">
            <summary>
            设备连接接口
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IConnect.ConnectId">
            <summary>
            连接编号
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IConnect.ConnectMode">
            <summary>
            连接方式
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IConnect.SendText(System.String)">
            <summary>
            发送文本数据
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IConnect.SendBinary(System.Byte[])">
            <summary>
            发送文本数据
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IoTService">
            <summary>
            IoT服务
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.RealTimeDataQueue">
            <summary>
            实时数据保存队列
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.HistoryDataQueue">
            <summary>
            历史数据保存队列
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.DeviceTypes">
            <summary>
            全部设备类型·
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.Devices">
            <summary>
            全部在线设备
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.Admins">
            <summary>
            全部在线管理员
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.Finds">
            <summary>
            全部在线查找设备用户
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.IoTService.FindDevices">
            <summary>
            需要绑定的设备
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IoTService.OnDeviceOnlineEventHandler">
            <summary>
            设备上线
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IoTService.OnDeviceOfflineEventHandler">
            <summary>
            设备离线
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IoTService.OnReceivedDeviceMsgEventHandler">
            <summary>
            收到设备消息
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IoTService.OnSaveDataEventHandler">
            <summary>
            准备保存数据
            </summary>
        </member>
        <member name="T:UFU.IoT.Services.IoTService.OnReceivedUserMsgEventHandler">
            <summary>
            收到用户消息(IWebSocket在后台发出消息时会为null)
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.IoTService.OnDeviceOnline">
            <summary>
            设备上线
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.IoTService.OnDeviceOffline">
            <summary>
            设备离线
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.IoTService.OnReceivedDeviceMsg">
            <summary>
            收到设备消息
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.IoTService.OnSavingData">
            <summary>
            准备保存数据
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.IoTService.OnSavedData">
            <summary>
            数据保存完成
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.IoTService.OnReceivedUserMsg">
            <summary>
            收到用户消息
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.#cctor">
            <summary>
            启动定时任务
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.DeviceOnline(UFU.IoT.Services.ConnectDevice)">
            <summary>
            设备上线
            </summary>
            <param name="device"></param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.DeviceOffline(UFU.IoT.Services.ConnectDevice)">
            <summary>
            设备下线
            </summary>
            <param name="device"></param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.DeviceConnectClosed(System.String)">
            <summary>
            连接断开
            </summary>
            <param name="connectId"></param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.ReceiveDeviceMsgV1(UFU.IoT.Services.IConnect,System.String)">
            <summary>
            1.0版消息处理
            </summary>
            <param name="connect"></param>
            <param name="msg"></param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.ReceiveDeviceRawData(UFU.IoT.Services.IConnect,System.String)">
            <summary>
            收到设备原始数据
            </summary>
            <param name="connect">设备连接</param>
            <param name="msg">原始数据</param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.Reg(UFU.IoT.Services.IConnect,UFU.IoT.Models.Msg)">
            <summary>
            设备注册
            </summary>
            <param name="connect"></param>
            <param name="msg"></param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.Login(UFU.IoT.Services.IConnect,UFU.IoT.Models.Msg)">
            <summary>
            设备登录
            </summary>
            <param name="connect"></param>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.Logout(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.Msg)">
            <summary>
            设备登出
            </summary>
            <param name="device"></param>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.Ping(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.Msg)">
            <summary>
            心跳数据
            </summary>
            <param name="device"></param>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.Find(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.Msg)">
            <summary>
            查找
            </summary>
            <param name="device"></param>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.SaveDeviceData(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.Msg)">
            <summary>
            保存设备数据
            </summary>
            <param name="device"></param>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.CalibrateTime(UFU.IoT.Services.ConnectDevice,System.DateTime)">
            <summary>
            校准数据时间
            </summary>
            <param name="device"></param>
            <param name="deviceTime"></param>
            <returns></returns>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.ReceiveUserMsg(UFU.CoreFX.Utils.IWebSocket,UFU.IoT.Models.Msg)">
            <summary>
            用户消息处理
            </summary>
            <param name="ws">用户</param>
            <param name="msg">消息</param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.ReceiveUserRawData(UFU.CoreFX.Utils.IWebSocket,System.String)">
            <summary>
            用户消息处理
            </summary>
            <param name="ws">用户Websocket</param>
            <param name="str">数据内容</param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.SendMsgToUser(UFU.IoT.Models.DeviceModel,System.String)">
            <summary>
            发送消息给用户
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.SendMsgToAdmin(UFU.IoT.Models.DeviceModel,System.String)">
            <summary>
            发送消息给管理员
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.SendFind">
            <summary>
            设备绑定任务
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.OTAInfo(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.Msg)">
            <summary>
            固件升级
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.OTA(UFU.IoT.Services.ConnectDevice,UFU.IoT.Models.Msg)">
            <summary>
            固件升级,传输文件
            </summary>
            <param name="device"></param>
            <param name="msg"></param>
        </member>
        <member name="M:UFU.IoT.Services.IoTService.UpdateDeviceTypesAsync">
            <summary>
            更新设备类型数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:UFU.IoT.Services.TcpSocketServer">
            <summary>
            TcpSocket服务端
            </summary>
        </member>
        <member name="P:UFU.IoT.Services.TcpSocketServer.IsServerStart">
            <summary>
            服务器是否已启动
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.TcpSocketServer.OnConnected">
            <summary>
            连接建立事件
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.TcpSocketServer.OnDisconnected">
            <summary>
            断开连接事件
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.TcpSocketServer.OnReceived">
            <summary>
            收到数据事件
            </summary>
        </member>
        <member name="E:UFU.IoT.Services.TcpSocketServer.OnSent">
            <summary>
            发送成功事件
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.#ctor(System.Net.IPEndPoint)">
            <summary>
            TcpSocket服务
            </summary>
            <param name="endPoint"></param>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.Start">
            <summary>
            开启Socket监听,死循环
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            执行异步发送消息
            </summary>
            <param name="sendArgs"></param>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.Close(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            关闭连接
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.ProcessAccept(System.Net.Sockets.Socket)">
            <summary>
            处理新连接
            </summary>
            <param name="client"></param>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.IOCompleted(System.Object,System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            IO处理完成事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.ProcessSend(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            发送结束处理
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:UFU.IoT.Services.TcpSocketServer.ProcessReceived(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            收到消息处理
            </summary>
            <param name="e"></param>
        </member>
        <member name="T:UFU.IoT.Services.VirtualDeviceService">
            <summary>
            虚拟设备
            </summary>
        </member>
        <member name="M:UFU.IoT.Services.VirtualDeviceService.List(System.String,System.Boolean)">
            <summary>
            虚拟设备列表（根据id查询）
            </summary>
            <param name="ids"></param>
            <param name="isVirtual"></param>
            <returns></returns>
        </member>
        <member name="T:HX.HRV.Web.Services.VirtualDeviceConnect">
            <summary>
            虚拟设备连接
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.VirtualDevices">
            <summary>
            虚拟设备
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.RealVirtualMapping">
            <summary>
            真实设备-虚拟设备映射()
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.VirtualDeviceConnects">
            <summary>
            虚拟设备连接
            </summary>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.Start">
            <summary>
            开始服务
            </summary>
        </member>
        <member name="F:HX.HRV.Web.Services.VirtualDeviceConnect._msgId">
            <summary>
            消息编号
            </summary>
        </member>
        <member name="F:HX.HRV.Web.Services.VirtualDeviceConnect.datas">
            <summary>
            数据缓存
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.ConnectId">
            <summary>
            连接编号
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.ConnectMode">
            <summary>
            连接方式
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.DeviceTypeId">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.DeviceSecret">
            <summary>
            设备秘钥
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.Mac">
            <summary>
            设备Mac地址
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.ChipSN">
            <summary>
            芯片编号
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.DeviceId">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.Token">
            <summary>
            登录Token
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.ExceptionTimes">
            <summary>
            错误次数
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.LatestDataTime">
            <summary>
            最后数据时间
            </summary>
        </member>
        <member name="P:HX.HRV.Web.Services.VirtualDeviceConnect.MsgId">
            <summary>
            消息编号
            </summary>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.#ctor(System.String,System.String,System.String)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.SendBinary(System.Byte[])">
            <summary>
            发送二进制数据到设备（收到服务器消息）
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.SendText(System.String)">
            <summary>
            发送文本数据到设备（收到服务器消息）
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.Reg">
            <summary>
            设备注册
            </summary>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.Login">
            <summary>
            设备登录
            </summary>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.SendCacheDataToServer">
            <summary>
            发送缓存数据到服务器
            </summary>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.SendDataToServer(System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.Nullable{System.DateTime})">
            <summary>
            收到设备数据
            </summary>
            <param name="data"></param>
            <param name="time"></param>
        </member>
        <member name="M:HX.HRV.Web.Services.VirtualDeviceConnect.Disconnected">
            <summary>
            设备连接断开
            </summary>
        </member>
    </members>
</doc>
