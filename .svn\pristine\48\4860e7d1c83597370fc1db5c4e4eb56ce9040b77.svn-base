// using Microsoft.AspNetCore.Builder;
// using Microsoft.AspNetCore.Mvc.ApiExplorer;
// using UFU.CoreFX;
// using UFU.CoreFX.Shared;
// //BlazorApp.AppType = typeof(UFU.CoreFX.Shared.Pages.App);
// BlazorApp.AppType = typeof(HX.HRV.Shared.App);
// BlazorApp.AppAssembly = typeof(HX.HRV.Web.Module).Assembly;
// new HRVAPP().Start(args);

//
// using UFU.CoreFX;
// using UFU.CoreFX.Shared;
// BlazorApp.AppType = typeof(HX.HRV.Shared.App);
// BlazorApp.AppAssembly = typeof(HX.HRV.Web.Module).Assembly;
// new HRVAPP().Start(args);
// public class HRVAPP : CoreApp
// {
// 	public override void Start(WebApplicationBuilder builder)
// 	{
// 		this.ConfigureBuilder(builder);
// 		WebApplication app = builder.Build();
// 		app.UseWebAssemblyDebugging();
// 		app.UseHttpsRedirection();
//
// 		app.UseBlazorFrameworkFiles();
// 		app.UseStaticFiles();
//
// 		app.UseRouting();
// 		app.MapFallbackToFile("index.html");
// 		//app.MapRazorPages();
// 		app.MapControllers();
// 		app.Run();
// 	}
// }
//
// //public class Program
// //{
// //	public static void Main(string[] args)
// //	{
// //		var builder = WebApplication.CreateBuilder(args);
//
// //		// Add services to the container.
//
// //		builder.Services.AddControllersWithViews();
// //		builder.Services.AddRazorPages();
// //		builder.Services.AddEndpointsApiExplorer();
// //		builder.Services.AddVersionedApiExplorer(delegate (ApiExplorerOptions options)
// //		{
// //			options.GroupNameFormat = "'v'VVV";
// //			options.SubstituteApiVersionInUrl = true;
// //		});
// //		var app = builder.Build();
//
// //		// Configure the HTTP request pipeline.
// //		if (app.Environment.IsDevelopment())
// //		{
// //			app.UseWebAssemblyDebugging();
// //		}
// //		else
// //		{
// //			app.UseExceptionHandler("/Error");
// //			// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
// //			app.UseHsts();
// //		}
//
// //		app.UseHttpsRedirection();
//
// //		app.UseBlazorFrameworkFiles();
// //		app.UseStaticFiles();
//
// //		app.UseRouting();
//
//
// //		app.MapRazorPages();
// //		app.MapControllers();
// //		app.MapFallbackToFile("index.html");
//
// //		app.Run();
// //	}
// //}
//
using UFU.CoreFX;
using UFU.CoreFX.Shared;


BlazorApp.AppType = typeof(UFU.CoreFX.Shared.Pages.App);
BlazorApp.AppAssembly = typeof(HX.HRV.Web.Module).Assembly;
new HXAPP().Start(args);
public class HXAPP : CoreApp
{
	/// <inheritdoc/>
	public override void Start(WebApplicationBuilder builder)
	{
		this.ConfigureBuilder(builder);
		WebApplication app = builder.Build();
		app.UseBlazorFrameworkFiles();
		this.ConfigureApp(app);
		app.UseWebAssemblyDebugging();
		// app.MapFallbackToPage("/_Host");
		app.Run();
	}
}


