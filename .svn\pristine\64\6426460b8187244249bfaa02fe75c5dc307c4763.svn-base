﻿using MathNet.Numerics;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Services;

namespace UFU.IoT.Areas.IoT.Controllers.V1
{
    /// <summary>
    /// 设备管理
    /// </summary>
    [Area("IoT")]
    [EnableCors("any")]
    public class DevicesController : Controller
    {
        private readonly DataRepository _context;
        private readonly CoreDbContext _coreContext;

        /// <summary>
        /// 设备管理
        /// </summary>
        /// <param name="context"></param>
        /// <param name="coreContext"></param>
        public DevicesController(DataRepository context, CoreDbContext coreContext)
        {
            _context = context;
            _coreContext = coreContext;
        }

        #region 接口

        /// <summary>
        /// 设备管理接口/列表
        /// </summary>
        /// <param name="placeId">场地编号</param>
        /// <param name="id">设备编号</param>
        /// <param name="ids">多个设备编号,逗号隔开</param>
        /// <param name="typeId">类型编号</param>
        /// <param name="deviceSN">设备序列号</param>
        /// <param name="name">设备名</param>
        /// <param name="chipSN">芯片序列号</param>
        /// <param name="mac">MAC地址</param>
        /// <param name="macs">多个MAC地址,逗号隔开</param>
        /// <param name="isOnline">是否在线</param>
        /// <param name="userName">用户名</param>
        /// <param name="tel">电话</param>
        /// <param name="district">行政区</param>
        /// <param name="address">地址</param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("设备管理接口/列表", IsAPI = true, AllowAllUser = true)]
        public async Task<PageList<DeviceModel>> List(string placeId, string typeId, string id, string ids, string deviceSN, string name, string chipSN, string mac, string macs, bool? isOnline, string userName, string tel, string district, string address, int? page, int? pageSize = null, string orderBy = null)
        {
            UserInfo userInfo = ViewBag.User;
            var query = _context.Query<DeviceModel>();
            if (!string.IsNullOrWhiteSpace(id))
            {
                query = query.Where(m => m.Id.Contains(id));
            }
            if (!string.IsNullOrWhiteSpace(ids))
            {
                var idlist = ids.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Id));
            }
            if (!string.IsNullOrWhiteSpace(typeId))
            {
                query = query.Where(m => m.Data.TypeId.Contains(typeId));
            }
            if (!string.IsNullOrWhiteSpace(deviceSN))
            {
                query = query.Where(m => m.Data.DeviceSN.Contains(deviceSN));
            }
            if (!string.IsNullOrWhiteSpace(name))
            {
                query = query.Where(m => m.Data.Name.Contains(name));
            }
            if (!string.IsNullOrWhiteSpace(chipSN))
            {
                query = query.Where(m => m.Data.ChipSN.Contains(chipSN));
            }
            if (!string.IsNullOrWhiteSpace(mac))
            {
                query = query.Where(m => m.Data.MAC.Contains(mac));
            }
            if (!string.IsNullOrWhiteSpace(macs))
            {
                var idlist = macs.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Data.MAC));
            }
            if (isOnline.HasValue)
            {
                var onlineIds = IoTService.Devices.Keys.ToArray();
                query = query.Where(m => onlineIds.Contains(m.Id));
            }
            if (!string.IsNullOrWhiteSpace(userName))
            {
                query = query.Where(m => m.Data.UserName.Contains(userName));
            }
            if (!string.IsNullOrWhiteSpace(tel))
            {
                query = query.Where(m => m.Data.Tel.Contains(tel));
            }
            if (!string.IsNullOrWhiteSpace(district))
            {
                query = query.Where(m => m.Data.DistrictId.Contains(district));
            }
            if (!string.IsNullOrWhiteSpace(address))
            {
                query = query.Where(m => m.Data.Address.Contains(address));
            }
            if (string.IsNullOrWhiteSpace(orderBy))
            {
                orderBy = "Id";
            }
            var list = await query.Select(m => m.Data).ToPageListAsync(page, pageSize, orderBy);
            //设备类型
            var typeIds = list.Select(m => m.TypeId).Distinct().ToList();
            var deviceTypes = await _context.Query<DeviceTypeModel>().Where(m => typeIds.Contains(m.Id)).Select(m=>m.Data).ToListAsync();
            foreach (var item in list)
            {
                item.DeviceType = deviceTypes.FirstOrDefault(m => m.Id == item.TypeId);
                item.IsOnline = BinaryIoTService.Devices.Values.Any(m => m.DeviceId == item.Id);
            }

            //最新数据
            var dataids = list.Select(m => m.Id);
            var latestData = await _context.Query<LatestDataModel>().Where(m => dataids.Contains(m.Data.DeviceId)).ToListAsync();
            foreach (var item in latestData)
            {
                list.First(m => m.Id == item.Data.DeviceId).LatestData = item.Data;
            }
            return list.AddPageInfo(Response);
        }

        /// <summary>
        /// 设备管理接口/列表
        /// </summary>
        /// <param name="placeIds">场地编号,逗号隔开</param>
        /// <param name="typeIds">类型编号,逗号隔开</param>
        /// <param name="ids">多个设备编号,逗号隔开</param>
        /// <param name="deviceSN">设备序列号,模糊查询</param>
        /// <param name="name">设备名，模糊查询</param>
        /// <param name="chipSN">芯片序列号，模糊查询</param>
        /// <param name="mac">MAC地址，模糊查询</param>
        /// <param name="macs">多个MAC地址,逗号隔开</param>
        /// <param name="userName">用户名</param>
        /// <param name="tel">电话</param>
        /// <param name="district">行政区</param>
        /// <param name="address">地址</param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("设备管理接口/数量统计", IsAPI = true, AllowAllUser = true)]
        public async Task<dynamic> Count(string placeIds, string typeIds, string ids, string name, string deviceSN, string chipSN, string mac, string macs, string userName, string tel, string district, string address)
        {
            UserInfo userInfo = ViewBag.User;
            //筛选出有权限的场地或者关联的用户场地
            //List<string> organs = await OrganService.GetMyOrganIdsAsync(userInfo, null, null, _coreContext);

            //使用LEFT JOIN 查出所有场地及场地用户
            var queryPlace = from Place in _context.Query<PlaceModel>()
                             from UserPlace in _context.Query<PlaceUserModel>(PermissionSwitch.Default).Where(m => m.Data.PlaceId == Place.Data.Id).DefaultIfEmpty()
                             select new PlaceUserModel
                             {
                                 Id = UserPlace.Id,
                                 Name = UserPlace.Data.Name,
                                 Place = Place.Data,
                                 PlaceId = Place.Id,
                                 IsAdmin = UserPlace.Data.IsAdmin,
                                 IsDefault = UserPlace.Data.IsDefault,
                                 UserId = UserPlace.UserId,
                                 AddTime = UserPlace.AddTime,
                                 UpdateTime = UserPlace.UpdateTime,
                                 Version = UserPlace.Version,
                             };
            //queryPlace = queryPlace.Where(m => organs.Contains(m.Place.OrganId) || m.UserId == userInfo.Id);
            //所有有权限的场地
            var allPlaceIds = queryPlace.Select(m => m.PlaceId).Distinct();

            var queryDevice = from Device in _context.Query<DeviceModel>()
                              from PlaceDevice in _context.Query<PlaceDeviceModel>(PermissionSwitch.Default).Where(m => m.Data.DeviceId == Device.Id).DefaultIfEmpty()
                              select new PlaceDeviceModel
                              {
                                  Id = PlaceDevice.Id,
                                  DeviceId = Device.Id,
                                  Device = Device.Data,
                                  Name = PlaceDevice.Data.Name,
                                  Place = PlaceDevice.Data.Place,
                                  PlaceId = PlaceDevice.Data.PlaceId,
                                  AddTime = PlaceDevice.AddTime,
                                  UpdateTime = PlaceDevice.UpdateTime,
                                  Version = PlaceDevice.Version,
                              };
            //queryDevice = queryDevice.Where(m => organs.Contains(m.Device.OrganId) || allPlaceIds.Contains(m.PlaceId));
            //筛选场地
            if (!string.IsNullOrWhiteSpace(placeIds))
            {
                var idlist = ids.Split(',', StringSplitOptions.RemoveEmptyEntries);
                queryDevice = queryDevice.Where(m => idlist.Contains(m.PlaceId));
            }
            var allDeviceIds = queryDevice.Select(m => m.DeviceId).Distinct();


            var query = _context.Query<DeviceModel>().Where(m => allDeviceIds.Contains(m.Id));
            if (!string.IsNullOrWhiteSpace(ids))
            {
                var idlist = ids.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Id));
            }
            if (!string.IsNullOrWhiteSpace(typeIds))
            {
                query = query.Where(m => typeIds.Contains(m.Data.TypeId));
            }
            if (!string.IsNullOrWhiteSpace(deviceSN))
            {
                query = query.Where(m => m.Data.TypeId.Contains(deviceSN));
            }
            if (!string.IsNullOrWhiteSpace(name))
            {
                query = query.Where(m => m.Data.Name.Contains(name));
            }
            if (!string.IsNullOrWhiteSpace(chipSN))
            {
                query = query.Where(m => m.Data.ChipSN.Contains(chipSN));
            }
            if (!string.IsNullOrWhiteSpace(mac))
            {
                query = query.Where(m => m.Data.MAC.Contains(mac));
            }
            if (!string.IsNullOrWhiteSpace(macs))
            {
                var idlist = macs.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Data.MAC));
            }
            if (!string.IsNullOrWhiteSpace(userName))
            {
                query = query.Where(m => m.Data.UserName.Contains(userName));
            }
            if (!string.IsNullOrWhiteSpace(tel))
            {
                query = query.Where(m => m.Data.Tel.Contains(tel));
            }
            if (!string.IsNullOrWhiteSpace(district))
            {
                query = query.Where(m => m.Data.DistrictId.Contains(district));
            }
            if (!string.IsNullOrWhiteSpace(address))
            {
                query = query.Where(m => m.Data.Address.Contains(address));
            }

            var list = await query.Select(m => m.Id).ToListAsync();

            var total = list.Count;
            var online = IoTService.Devices.Where(m => list.Contains(m.Key)).Count();
            var offline = total - online;

            return new { Total = total, Online = online, Offline = offline };
        }


        /// <summary>
        /// 设备管理接口/详情
        /// </summary>
        /// <param name="id">设备编号</param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]/{id?}")]
        [Permission("设备管理接口/详情", IsAPI = true)]
        public async Task<DeviceModel> Get(string id)
        {
            var deviceModel = await _context.Query<DeviceModel>().FirstOrDefaultAsync(m => m.Id == id);
            //最新数据
            if (deviceModel != null)
            {
                var latestData = await _context.Query<LatestDataModel>().AsNoTracking().FirstOrDefaultAsync(m => m.Data.DeviceId == deviceModel.Id);
                deviceModel.Data.LatestData = latestData?.Data ?? new LatestDataModel();
            }
            return deviceModel?.Data;
        }

        /// <summary>
        /// 设备管理接口/添加
        /// </summary>
        /// <param name="deviceModel">设备信息</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("设备管理接口/添加", IsAPI = true)]
        public async Task<IActionResult> Add([FromBody] DeviceModel deviceModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            UserInfo userInfo = ViewBag.User;
            deviceModel.Id = UId.GetNewId();
            deviceModel.AddTime = DateTime.Now;
            deviceModel.UpdateTime = DateTime.Now;
            deviceModel.Version = Guid.NewGuid().ToString();

            var temp = new DataModel<DeviceModel>()
            {
                Id = deviceModel.Id,
                Data = deviceModel,
                OrganId = userInfo.Organ.GetTopOrganId(),
                UserId = userInfo.Id,

                AddTime = deviceModel.AddTime,
                UpdateTime = deviceModel.UpdateTime,
                Version = deviceModel.Version
            };
            _context.Add(temp);
            await _context.SaveChangesAsync();
            return Ok(deviceModel);
        }

        /// <summary>
        /// 设备管理接口/编辑
        /// </summary>
        /// <param name="id">设备编号</param>
        /// <param name="deviceModel">设备信息</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]/{id?}")]
        [Permission("设备管理接口/编辑", IsAPI = true)]
        public async Task<IActionResult> Edit(string id, [FromBody] DeviceModel deviceModel)
        {
            if (id != deviceModel.Id)
            {
                return NotFound();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var model = await _context.Query<DeviceModel>().FirstOrDefaultAsync(m => m.Id == id);
            if (model == null)
            {
                return NotFound();
            }
            else if (model.Version != deviceModel.Version)
            {
                ModelState.AddModelError("", "该信息已经被其他用户修改，请刷新后重试。");
                var result = BadRequest(ModelState);
                result.StatusCode = 409;
                return result;
            }

            model.Data.Address = deviceModel.Address;
            model.Data.DeviceSN = deviceModel.DeviceSN;
            model.Data.DistrictId = deviceModel.DistrictId;
            model.Data.Tel = deviceModel.Tel;
            model.Data.UserName = deviceModel.UserName;
            model.Data.Name = deviceModel.Name;
            model.Data.AddTime = deviceModel.AddTime;
            model.Data.ChipSN = deviceModel.ChipSN;
            model.Data.Id = deviceModel.Id;
            model.Data.MAC = deviceModel.MAC;
            model.Data.Name = deviceModel.Name;
            model.Data.TypeId = deviceModel.TypeId;
            model.Data.UserId = deviceModel.UserId;
            model.Data.IsShare = deviceModel.IsShare;
            model.Data.OrganId = deviceModel.OrganId;
            model.Data.Longitude = deviceModel.Longitude;
            model.Data.Latitude = deviceModel.Latitude;
            model.Data.Altitude = deviceModel.Altitude;

            if (deviceModel.Extend != null)
            {
                model.Data.Extend = deviceModel.Extend;
            }
            model.Data.UpdateTime = DateTime.Now;
            model.Data.Version = Guid.NewGuid().ToString();
            model.UpdateTime = model.Data.UpdateTime;
            model.Version = model.Data.Version;
            _context.Update(model);
            await _context.SaveChangesAsync();
            return Ok(deviceModel);
        }

        /// <summary>
        /// 设备管理接口/删除
        /// </summary>
        /// <param name="id">设备编号</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]/{id?}")]
        [Permission("设备管理接口/删除", IsAPI = true)]
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var deviceModel = await _context.Query<DeviceModel>().FirstOrDefaultAsync(m => m.Id == id);
            if (deviceModel == null)
            {
                return NotFound();
            }

            _context.Remove(deviceModel);
            await _context.SaveChangesAsync();
            return Ok(deviceModel);
        }

        /// <summary>
        /// 设备管理接口/批量删除
        /// </summary>
        /// <param name="ids">设备编号</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("设备管理接口/批量删除", IsAPI = true)]
        public async Task<IActionResult> BatchDelete([FromBody] string[] ids)
        {
            if (ids == null || ids.Count() == 0)
            {
                return NotFound();
            }
            var list = await _context.Query<DeviceModel>().Where(m => ids.Contains(m.Id)).ToListAsync();
            _context.RemoveRange(list);
            await _context.SaveChangesAsync();
            return Ok(list.Count);
        }

        /// <summary>
        /// 创建设备序列号
        /// </summary>
        /// <param name="dateTime">生产日期</param>
        /// <param name="startNumber">开始号码</param>
        /// <param name="count">数量</param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("设备管理接口/创建设备序列号", IsAPI = true)]
        public List<string> CreateSN(DateTime dateTime, int startNumber, int count)
        {
            List<string> result = new List<string>();
            var days = (long)(dateTime - new DateTime(2019, 1, 1)).TotalDays + 1;
            var startStr = "U" + BinaryConversion.IntTo36(days).PadLeft(3, '0');
            for (int i = startNumber; i < startNumber + count; i++)
            {
                result.Add(startStr + i.ToString().PadLeft(4, '0') + VerifyCode.CreateVerifyCode(3).ToUpper());
            }
            return result;
        }

        /// <summary>
        /// 查询序列号
        /// </summary>
        /// <param name="sn"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("设备管理接口/查询序列号", IsAPI = true)]
        public string QuerySN(string sn)
        {
            if (string.IsNullOrWhiteSpace(sn) || sn.Length != 11)
            {
                return "Error";
            }

            var days = sn.Substring(1, 3);
            var date = new DateTime(2019, 01, 01).AddDays(BinaryConversion.Binary36ToInt(days) - 1);
            var number = sn.Substring(4, 4);
            return date.ToShortDateString() + "," + number;
        }

        /// <summary>
        /// 网页绑定设备
        /// </summary>
        /// <param name="type"></param>
        /// <param name="sn"></param>
        /// <returns></returns>
        [HttpGet("[area]/[controller]/[action]/{type}/{sn}")]
        public IActionResult Bind(string type, string sn)
        {
            var result = BindDevice(null, type, null, sn);
            //if(result )
            return Redirect("~/");
        }

        /// <summary>
        /// 通过设备类型和序列号绑定设备
        /// </summary>
        /// <param name="id">设备编号</param>
        /// <param name="type">类型编号</param>
        /// <param name="sn">序列号</param>
        /// <param name="mac">设备MAC地址</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        public IActionResult BindDevice(string id, string type, string mac, string sn)
        {
            //type = "1901171919190001";
            //sn = "U00H0002KDY";
            if (string.IsNullOrWhiteSpace(id) && (string.IsNullOrWhiteSpace(type) || string.IsNullOrWhiteSpace(sn) && string.IsNullOrWhiteSpace(mac)))
            {
                ModelState.AddModelError("", "设备不存在");
                return BadRequest(ModelState);
            }

            var device = _context.Query<DeviceModel>().FirstOrDefault(m => m.Id == id || m.Data.TypeId == type && (m.Data.DeviceSN == sn || m.Data.MAC == mac));
            if (device == null)
            {
                ModelState.AddModelError("", "设备不存在");
                return BadRequest(ModelState);
            }

            //查找场地设备
            var placeDevice = _context.Query<PlaceDeviceModel>().FirstOrDefault(m => m.Data.DeviceId == device.Id);

            UserInfo userInfo = (UserInfo)ViewBag.User;

            //如果没有场地设备，说明是设备第一次被绑定
            if (placeDevice == null)
            {
                //查询当前用户是否存在场地信息
                var userPlace = _context.Query<PlaceUserModel>().Where(m => m.UserId == userInfo.Id).OrderByDescending(m => m.Data.IsDefault).FirstOrDefault();
                //如果没有用户场地，说明是用户第一次使用，创建一个默认场地
                if (userPlace == null)
                {
                    //添加一个场地
                    var place = new PlaceModel();
                    place.Id = UId.GetNewId();
                    place.OrganId = userInfo.Organ.Id;
                    place.Name = (string.IsNullOrWhiteSpace(userInfo.Nikename) ? "我" : userInfo.Nikename) + "的场地";
                    place.IsShare = false;
                    place.UserName = userInfo.Nikename;
                    place.Tel = userInfo.Phone;
                    place.Template = "/IoT/Places/Default/index.html";
                    place.AddTime = DateTime.Now;
                    place.UpdateTime = DateTime.Now;
                    place.Version = Guid.NewGuid().ToString();

                    var placeData = new DataModel<PlaceModel>()
                    {
                        Id = place.Id,
                        Data = place,
                        OrganId = userInfo.Organ.GetTopOrganId(),
                        UserId = userInfo.Id,

                        AddTime = place.AddTime,
                        UpdateTime = place.UpdateTime,
                        Version = place.Version

                    };
                    _context.Add(placeData);

                    //添加一个用户场地
                    var userPlaceModel = new PlaceUserModel();
                    userPlaceModel.Id = UId.GetNewId();
                    userPlaceModel.Name = place.Name;
                    userPlaceModel.PlaceId = place.Id;
                    userPlaceModel.UserId = userInfo.Id;
                    userPlaceModel.IsAdmin = true;
                    userPlaceModel.IsDefault = true;
                    userPlaceModel.AddTime = DateTime.Now;
                    userPlaceModel.UpdateTime = DateTime.Now;
                    userPlaceModel.Version = Guid.NewGuid().ToString();
                    userPlace = new DataModel<PlaceUserModel>()
                    {
                        Id = userPlaceModel.Id,
                        Data = userPlaceModel,
                        OrganId = userInfo.Organ.GetTopOrganId(),
                        UserId = userInfo.Id,

                        AddTime = userPlaceModel.AddTime,
                        UpdateTime = userPlaceModel.UpdateTime,
                        Version = userPlaceModel.Version
                    };

                    _context.Add(userPlace);

                    //添加场地设备
                    var placeDeviceModel = new PlaceDeviceModel();
                    placeDeviceModel.Id = UId.GetNewId();
                    placeDeviceModel.PlaceId = place.Id;
                    placeDeviceModel.DeviceId = device.Id;
                    placeDeviceModel.Name = device.Data.Name;
                    placeDeviceModel.AddTime = DateTime.Now;
                    placeDeviceModel.UpdateTime = DateTime.Now;
                    placeDeviceModel.Version = Guid.NewGuid().ToString();

                    placeDevice = new DataModel<PlaceDeviceModel>()
                    {
                        Id = placeDeviceModel.Id,
                        Data = placeDeviceModel,
                        OrganId = userInfo.Organ.GetTopOrganId(),
                        UserId = userInfo.Id,

                        AddTime = placeDeviceModel.AddTime,
                        UpdateTime = placeDeviceModel.UpdateTime,
                        Version = placeDeviceModel.Version
                    };
                    _context.Add(placeDevice);
                    _context.SaveChanges();

                }
                //用户存在场地，添加到当前场地，暂时不判断是否有权限
                else
                {
                    //添加场地设备
                    var placeDeviceModel = new PlaceDeviceModel();
                    placeDeviceModel.Id = UId.GetNewId();
                    placeDeviceModel.PlaceId = userPlace.Data.PlaceId;
                    placeDeviceModel.DeviceId = device.Id;
                    placeDeviceModel.Name = device.Data.Name;
                    placeDeviceModel.AddTime = DateTime.Now;
                    placeDeviceModel.UpdateTime = DateTime.Now;
                    placeDeviceModel.Version = Guid.NewGuid().ToString();

                    placeDevice = new DataModel<PlaceDeviceModel>()
                    {
                        Id = placeDeviceModel.Id,
                        Data = placeDeviceModel,
                        OrganId = userInfo.Organ.GetTopOrganId(),
                        UserId = userInfo.Id,

                        AddTime = placeDeviceModel.AddTime,
                        UpdateTime = placeDeviceModel.UpdateTime,
                        Version = placeDeviceModel.Version
                    };
                    _context.Add(placeDevice);
                    _context.SaveChanges();
                }
                return Ok(new { UserPlaceId = userPlace.Id, DeviceId = device.Id, PlaceDeviceId = placeDevice.Id });
            }
            //如果存在场地设备
            else
            {
                //查询当前用户是否当前场地的用户
                var userPlace = _context.Query<PlaceUserModel>().Where(m => m.UserId == userInfo.Id && m.Data.PlaceId == placeDevice.Data.PlaceId).OrderByDescending(m => m.Data.IsDefault).FirstOrDefault();
                //如果用户不在此场地下，不允许直接绑定设备，需要联系当前设备的管理员
                if (userPlace == null)
                {
                    ModelState.AddModelError("", "当前设备已经被其他用户绑定");
                    return BadRequest(ModelState);

                    ////添加一个用户场地
                    //userPlace = new UserPlaceModel();
                    //userPlace.Id = UId.GetNewId();
                    //userPlace.Name = placeDeviceModel.Place.Name;
                    //userPlace.PlaceId = placeDeviceModel.PlaceId;
                    //userPlace.UserId = userInfo.Id;
                    //userPlace.IsAdmin = false;
                    //userPlace.IsDefault = true;
                    //userPlace.AddTime = DateTime.Now;
                    //userPlace.UpdateTime = DateTime.Now;
                    //userPlace.Version = Guid.NewGuid().ToString();
                    //_context.Add(userPlace);
                    //_context.SaveChanges();
                    //return Ok(new { UserPlaceId = userPlace.Id, DeviceId = device.Id, PlaceDeviceId = placeDeviceModel.Id });
                }
                //说明用户已经是场地成员，直接返回
                else
                {
                    return Ok(new { UserPlaceId = userPlace.Id, DeviceId = device.Id, PlaceDeviceId = placeDevice.Id });
                }
            }
        }

        #endregion
    }
}
