﻿using System.Management;
using System.Net;
using System.Net.Sockets;
using Makaretu.Dns;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace HX.HRV.MDns;

public class MDnsService : BackgroundService
{
    public MDnsService(ILoggerFactory loggerFactory)
    {
        Logger = loggerFactory.CreateLogger<MDnsService>();
    }

    public ILogger Logger { get; }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        Task.Run(() =>
        {
            var allips = new List<IPAddress>();
            var query = new ManagementObjectSearcher(
                "SELECT * FROM Win32_NetworkAdapter WHERE PhysicalAdapter = True");
            foreach (ManagementObject adapter in query.Get())
            {
                var adapterName = adapter["Name"].ToString();

                // Skip WSL, vEthernet and other virtual adapters
                if (adapterName.Contains("WSL") ||
                    adapterName.Contains("vEthernet") ||
                    adapterName.Contains("Virtual") ||
                    adapterName.Contains("VMware") ||
                    adapterName.Contains("VirtualBox"))
                {
                    continue;
                }

                int index = Convert.ToInt32(adapter["Index"]); // 关键字段：用于关联 IP 配置
                var configQuery =
                    new ManagementObjectSearcher(
                        $"SELECT * FROM Win32_NetworkAdapterConfiguration WHERE Index = {index}");
                foreach (ManagementObject config in configQuery.Get())
                {
                    string[] ips = (string[])config["IPAddress"] ?? new string[0];
                    foreach (var ip in ips)
                    {
                        var ipAddress = IPAddress.Parse(ip);
                        if (ip.Contains(":")
                            || !ipAddress.AddressFamily.Equals(AddressFamily.InterNetwork))
                        {
                            continue;
                        }

                        allips.Add(ipAddress);
                    }
                }
            }

            if (allips.Count <= 0)
            {
                Console.WriteLine("No network adapters found.");
                return;
            }

            Console.WriteLine($"found {allips.Count} network adapters.");
            foreach (var ipAddress in allips)
            {
                Console.WriteLine($"IP Address: {ipAddress}");
            }

            var mdns = new MulticastService() { UseIpv6 = false };
            var sd = new ServiceDiscovery(mdns);
            var sr = new ServiceProfile("sr.hrv", "_huixin._tcp",
                5222, allips);
            sd.Advertise(sr);
            var ms = new ServiceProfile("ms.hrv", "_huixin._tcp",
                5219,
                addresses: allips);
            sd.Advertise(ms);
            mdns.Start();
        }, stoppingToken);
        while (!stoppingToken.IsCancellationRequested)
        {
            Logger.LogInformation("MDns Service is doing background work.");
            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
}