{"format": 1, "restore": {"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\HX.Experiment.Window.csproj": {}}, "projects": {"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj", "projectName": "HX.Experiment.Shared", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj": {"projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj"}, "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Masa.Blazor": {"target": "Package", "version": "[1.10.3, )"}, "Masa.Blazor.MobileComponents": {"target": "Package", "version": "[1.10.2, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[9.0.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\HX.Experiment.Window.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\HX.Experiment.Window.csproj", "projectName": "HX.Experiment.Window", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\HX.Experiment.Window.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Window\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj": {"projectPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"DeviceId": {"target": "Package", "version": "[6.9.0, )"}, "EPPlus": {"target": "Package", "version": "[7.5.1, )"}, "Microsoft.AspNetCore.Components.WebView.WindowsForms": {"target": "Package", "version": "[9.0.90, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.*, )"}, "OpenCvSharp4.Extensions": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.Windows": {"target": "Package", "version": "[4.11.0.20250507, )"}, "System.Management": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj", "projectName": "UFU.IoT.Shared", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AntDesign": {"target": "Package", "version": "[0.15.5, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.10, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"AntDesign": {"target": "Package", "version": "[0.15.5, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[9.0.7, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"version": "5.0.6", "restore": {"projectUniqueName": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj", "projectName": "UFU.CoreFX.Shared", "projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AntDesign": {"target": "Package", "version": "[0.15.5, )"}, "Majorsoft.Blazor.Extensions.BrowserStorage": {"target": "Package", "version": "[1.5.0, )"}, "Microsoft.AspNetCore.Components.Authorization": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[6.0.10, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.4, )"}, "blazor-dragdrop": {"target": "Package", "version": "[2.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"AntDesign": {"target": "Package", "version": "[0.15.5, )"}, "Majorsoft.Blazor.Extensions.BrowserStorage": {"target": "Package", "version": "[1.5.0, )"}, "Microsoft.AspNetCore.Components.Authorization": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[9.0.7, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}, "blazor-dragdrop": {"target": "Package", "version": "[2.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}