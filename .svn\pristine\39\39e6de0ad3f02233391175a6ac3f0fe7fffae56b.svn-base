﻿@using System.Net.WebSockets
@using System.Text
@using System.Text.Json.Nodes
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using UFU.CoreFX.Utils
@using UFU.IoT.Shared.Models
@using UFU.IoT.Models
@using HX.HRV.Shared.Pages.Client.Dialog
@using Microsoft.Extensions.Configuration


@inject  DeviceStateService DeviceStateService

@implements IDisposable
<style>
    @@keyframes rotate {
    from {
    transform: rotate(0deg);
    }
    to {
    transform: rotate(360deg);
    }
    }

    @@keyframes steam {
    0% {
    transform: translateY(0);
    }
    50% {
    transform: translateY(-5px);
    }
    100% {
    transform: translateY(0);
    }
    }

    .add-check-card {
    width: 323px;
    height: 77px;
    border-radius: 9px;
    align-content: center;
    }
</style>

<div Style="width: 100%;height: 80%;" class="d-flex flex-column align-center justify-center">
    @if (isShowExport&& DeviceStatusData?.IsUsbLinked==true)
    {
        if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.USB传输中)
        {
            <div Style="height: 100%;" class="d-flex align-center justify-center">
                <MLabel><span class="mdi mdi-usb-flash-drive" 
                    style="font-size: 1.4rem;color:#1f88ff"> 
                    </span>
                </MLabel>
                <MLabel Style="font-size:1rem">@DeviceStatusData.DeviceStatus</MLabel>
            </div>
            <MRow Justify="JustifyTypes.Center" Style="width: 90%;height: 4rem;    display: flex;
    align-items: center; ">
                <MCol Cols="12">
                    <MProgressLinear Color="@DeviceStatusData.ColorString" Value="DeviceStatusData.Progress" 
                    BufferValue="100" BackgroundOpacity="0.2"></MProgressLinear>
                </MCol>
            </MRow>
        }
        else
        {
            <div Style="height: 100%;" class="d-flex align-center justify-center">
                <span class="mdi mdi-usb-flash-drive" style="font-size: 1.4rem;color:#1f88ff"> </span>
                <MButton OnClick="ShowExportDataDialog" OnClickStopPropagation Class="text-subtitle-1"
                Style="@("background-color:#1f88ff"+ $";box-shadow: 0px 3px 16px 0px {DeviceStatusData.ColorString}d4;")"
                Rounded Depressed>
                    导出数据
                </MButton>
            </div>
        }
    }
    else  if (DeviceStatusData?.DeviceStatus == EnumDeviceStatus.空闲)
    {
        <div Style="height: 100%;" class="d-flex align-center justify-center">
            <img src="/images/icon/free_fill.png" style="width:38px; "/>
            <MLabel Style="font-size:2rem">空闲</MLabel>
        </div>
    }
    else if (DeviceStatusData?.DeviceStatus == EnumDeviceStatus.检测中)
    {
        var labelColor = $"font-size:1rem;color: {DeviceStatusData.ColorString}";
        <div Style="height: 4rem;width: 100%;" class="d-flex align-center justify-center">
            <img src="/images/icon/jiazaizhong.png" style="width: 38px; animation: rotate 2s linear infinite;" alt=""/>
            <MLabel Style="@labelColor">检测中...</MLabel>
            <MCol Cols="3">
                <MButton OnClick="HandleStopSubmit" OnClickStopPropagation Class="text-subtitle-1"
                Style="@("color: white;background-color: " + DeviceStatusData.ColorString + $";box-shadow: 0px 3px 16px 0px {DeviceStatusData.ColorString}d4;")"
                Rounded Depressed>
                    停止检测
                </MButton>
            </MCol>
        </div>
        <DeviceStatusUserInfoCard DeviceId="@DeviceId"/>
    }
    else if (DeviceStatusData?.DeviceStatus == EnumDeviceStatus.完成)
    {
        <div Style="height: 4rem" class="d-flex align-center justify-center">
            <MRow>
                <MCol Class="text-center d-flex align-center justify-center " Cols="9">
                    <MProgressCircular Size="24" Indeterminate Color="amber"></MProgressCircular> &nbsp;
                    <MLabel Class="text-h6" Style="@("font-size:2rem;color: " + DeviceStatusData.ColorString)">
                        报告生成中...
                    </MLabel>
                </MCol>
                <MCol Cols="2">
                    <MButton OnClickStopPropagation OnClick="HandleStopSubmit" Class="text-subtitle-1"
                    Style="@("color: white;background-color: " + DeviceStatusData.ColorString + $";box-shadow: 0px 3px 16px 0px {DeviceStatusData.ColorString}d4;")"
                    Rounded Depressed>
                        结束
                    </MButton>
                </MCol>
            </MRow>
        </div>
        <DeviceStatusUserInfoCard  DeviceId="@DeviceId"/>
    }
    else if (DeviceStatusData?.DeviceStatus == EnumDeviceStatus.报警)
    {
        <div Style="height: 4rem" class="d-flex align-center justify-center">
            <img src="/images/icon/jinggao.png" style="width: 38px;"/>
            <MLabel>异常</MLabel>
        </div>
        <DeviceStatusUserInfoCard  DeviceId="@DeviceId"/>
    }

</div>
@if (_isShowDataExportDialog)
{
    <PModal
    Persistent="true"
    HeaderClass=" grey text-center lighten-2"
    Class="deviceStatusDialog"
    ContentStyle="background-color: #c2d9f3;"
    MaxWidth="1200"
    @bind-Value="_isShowDataExportDialog">
        <ChildContent>
            <ExportPatientDialog  CloseDialog="CloseDialog" DialogTitle="选择被试信息" AfterSaveClick="OnExportDataDialogConfirmClick">
            </ExportPatientDialog>
        </ChildContent>
    </PModal>
}

@code {
    private bool _isShowDataExportDialog = false;
    [Inject] IJSRuntime Js { get; set; }

    [Parameter] public string DeviceId { get; set; }
    public DeviceStatusViewModel DeviceStatusData=>DeviceStateService.GetDeviceStatusViewModel(DeviceId);
    private DeviceModel Device => DeviceStatusData.Device;
    bool dialog = false;
    private bool isShowExport;
    [Inject] StateService _stateService { get; set; }

    [Inject] IPopupService _popupService { get; set; }

    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    private async Task HandleStopSubmit()
    {
        await SendEndMsgToDevice(Device.Id);
    }

    [Inject] IConfiguration Configuration { get; set; }
    protected override Task OnInitializedAsync() {
        return base.OnInitializedAsync();
    }
    protected override void OnInitialized()
    {
        isShowExport = Configuration.GetValue<bool>("HrvVariable:IsShowExport");
        DeviceStatusData.OnDeviceStatusChange+=OnChange;
        base.OnInitialized();
    }

    public void Dispose() {
        DeviceStatusData.OnDeviceStatusChange-=OnChange;
    }
    private void OnChange() {
        InvokeAsync(StateHasChanged);
    }
    
    private async Task OnExportDataDialogConfirmClick(string id)
    {
      await Js.InvokeVoidAsync("SendExportToClient",Device.ChipSN,id);
    }

    private async Task SendEndMsgToDevice(string deviceId)
    {
        await _stateService.PostAsJsonAsync<bool>($"/api/v2.0/HRV_HX/PatientRecord/Finished?id={DeviceStatusData.RecordModel?.Id}");
    }

    private void OnGetDeviceBinDataInfo(string data)
    {
        var jsonNode = JsonNode.Parse(data);
        var count = jsonNode["count"].GetValue<int>();
        _popupService.HideProgressCircular();
        if (count > 0)
        {
            _isShowDataExportDialog = true;
        }
    } 

    private void ShowExportDataDialog()
    {
        DeviceStatusData.OnReceiveClientMessage+=OnGetDeviceBinDataInfo;
        _popupService.ShowProgressCircular();
        
        var msgJson = new
        {
            MsgId = 555555,
            action = "GetDeviceBinDataInfo",
            //时间戳
            Time = DateTime.Now.ToUnixMs(),
            Device = new
            {
                SN = Device.DeviceSN,
            },
            CMD = (int)BinaryCMD.Write,
        };
        var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
        Js.InvokeVoidAsync("SendMessageToClientByMsg",msgStr);
    }

    private void CloseDialog()
    { 
        DeviceStatusData.OnReceiveClientMessage-=OnGetDeviceBinDataInfo;
        _isShowDataExportDialog = false;
    } 

}