using System.Globalization;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.Experiment.Shared.Model;
using HX.Experiment.Shared.Services;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.CoreFX.Utils;

namespace HX.Experiment.Window.Services;

/// <summary>
/// 数据分析服务实现
/// </summary>
public class DataAnalysisService : IDataAnalysisService
{
    private readonly StateService _stateService;
    private readonly ISocketService _socketService;
    private ClientWebSocket? _analysisWebSocket;
    private readonly Dictionary<string, ExperimentAnalysisResult> _analysisResults = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    public event EventHandler<DataAnalysisProgressEventArgs>? AnalysisProgressChanged;
    public event EventHandler<DataAnalysisCompletedEventArgs>? AnalysisCompleted;

    public DataAnalysisService(StateService stateService, ISocketService socketService)
    {
        _stateService = stateService;
        _socketService = socketService;
    }

    public async Task<string> StartAnalysisAsync(string recordId, DateTime startTime, DateTime endTime, string analysisName)
    {
        try
        {
            var analysisId = Guid.NewGuid().ToString("N");
            
            OnAnalysisProgressChanged(analysisId, 0, "开始数据分析...", "初始化");

            // 创建分析记录
            var analysisRecord = new
            {
                Id = analysisId,
                Name = analysisName,
                RecordId = recordId,
                StartTime = startTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                EndTime = endTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                AnalysisTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")
            };

            // 保存分析记录到服务器
            var result = await _stateService.PostAsJsonAsync<DataModel<object>>(
                "/api/v2/hx_experiment/DataAnalysis/Add", analysisRecord);

            if (!result.Success)
            {
                OnAnalysisCompleted(analysisId, false, result.Message);
                return string.Empty;
            }

            OnAnalysisProgressChanged(analysisId, 10, "分析记录已创建", "数据准备");

            // 连接到分析WebSocket
            await ConnectAnalysisWebSocketAsync();

            // 发送分析请求
            var analysisRequest = new
            {
                Function = "ToALG",
                Data = new
                {
                    RecordId = recordId,
                    StartTime = startTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    EndTime = endTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    Name = analysisName,
                    AnalysisId = analysisId
                }
            };

            var msgStr = JsonTool.SerializeIgnoreNull(analysisRequest);
            var msgBytes = Encoding.UTF8.GetBytes(msgStr);

            if (_analysisWebSocket?.State == WebSocketState.Open)
            {
                await _analysisWebSocket.SendAsync(
                    new ArraySegment<byte>(msgBytes), 
                    WebSocketMessageType.Text, 
                    true, 
                    _cancellationTokenSource.Token);

                OnAnalysisProgressChanged(analysisId, 20, "分析请求已发送", "数据处理");
            }
            else
            {
                OnAnalysisCompleted(analysisId, false, "WebSocket连接失败");
                return string.Empty;
            }

            return analysisId;
        }
        catch (Exception ex)
        {
            OnAnalysisCompleted(string.Empty, false, ex.Message);
            return string.Empty;
        }
    }

    public async Task<ExperimentAnalysisResult?> GetAnalysisResultAsync(string analysisId)
    {
        try
        {
            if (_analysisResults.TryGetValue(analysisId, out var cachedResult))
            {
                return cachedResult;
            }

            var result = await _stateService.GetAsJsonAsync<DataModel<ExperimentAnalysisResult>>(
                "/api/v2/hx_experiment/DataAnalysis/Get",
                new Dictionary<string, string> { { "id", analysisId } });

            if (result.Success && result.Data?.Data != null)
            {
                _analysisResults[analysisId] = result.Data.Data;
                return result.Data.Data;
            }

            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取分析结果失败: {ex.Message}");
            return null;
        }
    }

    public async Task<ExperimentSegmentData?> GetSegmentDataAsync(string recordId, DateTime startTime, DateTime endTime)
    {
        try
        {
            var parameters = new Dictionary<string, string>
            {
                { "recordId", recordId },
                { "startTime", startTime.ToString("yyyy-MM-dd HH:mm:ss.fff") },
                { "endTime", endTime.ToString("yyyy-MM-dd HH:mm:ss.fff") }
            };

            var result = await _stateService.GetAsJsonAsync<DataModel<ExperimentSegmentData>>(
                "/api/v2/hx_experiment/DataAnalysis/GetSegmentData", parameters);

            return result.Success ? result.Data?.Data : null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取时间段数据失败: {ex.Message}");
            return null;
        }
    }

    public async Task<bool> ExportAnalysisResultAsync(string analysisId, string exportPath)
    {
        try
        {
            OnAnalysisProgressChanged(analysisId, 0, "开始导出分析结果...", "导出");

            var result = await _stateService.GetAsync(
                $"/api/v2/hx_experiment/DataAnalysis/Export/{analysisId}");

            if (result.IsSuccessStatusCode)
            {
                var content = await result.Content.ReadAsByteArrayAsync();
                await File.WriteAllBytesAsync(exportPath, content);
                
                OnAnalysisProgressChanged(analysisId, 100, "导出完成", "导出");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"导出分析结果失败: {ex.Message}");
            return false;
        }
    }

    private async Task ConnectAnalysisWebSocketAsync()
    {
        try
        {
            if (_analysisWebSocket?.State == WebSocketState.Open)
                return;

            _analysisWebSocket = new ClientWebSocket();
            var currentUri = _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
            var webSocketUrl = $"ws://{currentUri.Authority}/iot/hx_experiment/analysis/v1?token={_stateService.Token}";
            var serverUri = new Uri(webSocketUrl);

            await _analysisWebSocket.ConnectAsync(serverUri, _cancellationTokenSource.Token);

            // 启动接收消息的任务
            _ = Task.Run(ReceiveAnalysisMessagesAsync, _cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接分析WebSocket失败: {ex.Message}");
            throw;
        }
    }

    private async Task ReceiveAnalysisMessagesAsync()
    {
        var buffer = new byte[8192];

        while (_analysisWebSocket?.State == WebSocketState.Open && !_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                WebSocketReceiveResult result;
                using var ms = new MemoryStream();
                
                do
                {
                    result = await _analysisWebSocket.ReceiveAsync(
                        new ArraySegment<byte>(buffer), 
                        _cancellationTokenSource.Token);
                    ms.Write(buffer, 0, result.Count);
                } while (!result.EndOfMessage);

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var message = Encoding.UTF8.GetString(ms.ToArray());
                    await ProcessAnalysisMessage(message);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收分析消息失败: {ex.Message}");
                break;
            }
        }
    }

    private async Task ProcessAnalysisMessage(string message)
    {
        try
        {
            var jsonNode = JsonNode.Parse(message);
            if (jsonNode == null) return;

            var function = jsonNode["Function"]?.ToString();
            if (function == "FromALG")
            {
                var analysisId = jsonNode["AnalysisId"]?.ToString();
                if (string.IsNullOrEmpty(analysisId)) return;

                var category = jsonNode["Category"]?.ToString();
                var progress = jsonNode["Progress"]?.GetValue<int>() ?? 100;

                if (category == "HRV")
                {
                    OnAnalysisProgressChanged(analysisId, progress, "心率变异性分析完成", "HRV分析");
                    await ProcessHrvAnalysisResult(analysisId, jsonNode);
                }
                else if (category == "EDA")
                {
                    OnAnalysisProgressChanged(analysisId, progress, "皮肤电分析完成", "EDA分析");
                    await ProcessEdaAnalysisResult(analysisId, jsonNode);
                }
                else if (category == "Emotion")
                {
                    OnAnalysisProgressChanged(analysisId, progress, "情绪分析完成", "情绪分析");
                    await ProcessEmotionAnalysisResult(analysisId, jsonNode);
                }

                // 检查是否所有分析都完成
                var result = await GetAnalysisResultAsync(analysisId);
                if (result != null)
                {
                    OnAnalysisCompleted(analysisId, true, null, result);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理分析消息失败: {ex.Message}");
        }
    }

    private async Task ProcessHrvAnalysisResult(string analysisId, JsonNode jsonNode)
    {
        // 处理心率变异性分析结果
        // 这里需要根据实际的数据格式进行解析
    }

    private async Task ProcessEdaAnalysisResult(string analysisId, JsonNode jsonNode)
    {
        // 处理皮肤电分析结果
        // 这里需要根据实际的数据格式进行解析
    }

    private async Task ProcessEmotionAnalysisResult(string analysisId, JsonNode jsonNode)
    {
        // 处理情绪分析结果
        // 这里需要根据实际的数据格式进行解析
    }

    private void OnAnalysisProgressChanged(string analysisId, int progress, string message, string stage)
    {
        AnalysisProgressChanged?.Invoke(this, new DataAnalysisProgressEventArgs
        {
            AnalysisId = analysisId,
            Progress = progress,
            Message = message,
            Stage = stage
        });
    }

    private void OnAnalysisCompleted(string analysisId, bool isSuccess, string? errorMessage, ExperimentAnalysisResult? result = null)
    {
        AnalysisCompleted?.Invoke(this, new DataAnalysisCompletedEventArgs
        {
            AnalysisId = analysisId,
            IsSuccess = isSuccess,
            ErrorMessage = errorMessage,
            Result = result
        });
    }

    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _analysisWebSocket?.CloseAsync(WebSocketCloseStatus.NormalClosure, "Service disposing", CancellationToken.None);
        _analysisWebSocket?.Dispose();
        _cancellationTokenSource.Dispose();
    }
}
