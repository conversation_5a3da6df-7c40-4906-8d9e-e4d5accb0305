﻿using System.Diagnostics;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Security.Cryptography;
using System.Text.Json;
using System.Windows;
using HX.StandtardFearmwork.Models;

namespace HX.HRV.Keygen.ViewModel
{
    public partial class MainViewModel : ObservableObject, IDisposable
    {
        [ObservableProperty] private RSA? _currentPrivateKeyInstance;

        [ObservableProperty] private string? _uniqueIdentifier;

        [ObservableProperty] private DateTime? _expiryDate;

        [ObservableProperty] private string? _generatedLicenseKey;

        [ObservableProperty] private string? _keyStatusDisplay;

        public IEnumerable<SoftwareProduct> SoftwareProductOptions { get; } = Enum.GetValues<SoftwareProduct>();
        [ObservableProperty] private SoftwareProduct _selectedSoftwareProduct;

        public IEnumerable<LicenseMode> LicenseVersionOptions { get; } = Enum.GetValues<LicenseMode>();
        [ObservableProperty] private LicenseMode _selectedLicenseVersion;

        [ObservableProperty] private int? _trialDays;

        public MainViewModel()
        {
            // 初始化
            SelectedSoftwareProduct = SoftwareProductOptions.FirstOrDefault();
            SelectedLicenseVersion = LicenseVersionOptions.FirstOrDefault();
            TrialDays = 30; // 默认试用天数
            ExpiryDate = DateTime.Today.AddYears(1); // 正式版默认一年

            UpdateKeyStatusDisplay();
        }

        partial void OnCurrentPrivateKeyInstanceChanged(RSA? oldValue, RSA? newValue)
        {
            oldValue?.Dispose();
            UpdateKeyStatusDisplay();
        }

        private void UpdateKeyStatusDisplay()
        {
            KeyStatusDisplay = CurrentPrivateKeyInstance != null ? "私钥已加载" : "未加载私钥";
        }


        [RelayCommand]
        private void GenerateLicenseKey()
        {
            DateTime actualExpiryDate;
            int? licenseTrialDays = null;
            if (SelectedLicenseVersion == LicenseMode.Trial)
            {
                if (TrialDays == null || TrialDays.Value <= 0)
                {
                    MessageBox.Show("试用版请输入有效的试用天数。", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                actualExpiryDate = DateTime.UtcNow.AddDays(TrialDays.Value);
                licenseTrialDays = TrialDays.Value;
            }
            else
            {
                if (ExpiryDate == null || ExpiryDate.Value.Date < DateTime.Today)
                {
                    MessageBox.Show("正式版请选择一个有效的到期日期（不能是过去）。", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                actualExpiryDate = ExpiryDate.Value.Date.AddDays(1).AddTicks(-1);
                actualExpiryDate = TimeZoneInfo.ConvertTimeToUtc(actualExpiryDate, TimeZoneInfo.Local);
            }

            try
            {
                var licenseData = new LicenseData
                {
                    UniqueIdentifier = this.UniqueIdentifier,
                    Product = this.SelectedSoftwareProduct,
                    LicenseMode = this.SelectedLicenseVersion,
                    IssueDate = DateTime.UtcNow,
                    ExpiryDate = actualExpiryDate,
                    TrialDays = licenseTrialDays
                };
                GeneratedLicenseKey = RsaEncryption.GenerateLicenseKey(licenseData);
                MessageBox.Show("许可证密钥已成功生成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (JsonException jsonEx)
            {
                MessageBox.Show($"序列化许可证数据时发生错误: {jsonEx.Message}\n请检查输入的数据。", "序列化错误", MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成许可证时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        [RelayCommand]
        private void CopyToClipboard()
        {
            if (string.IsNullOrWhiteSpace(GeneratedLicenseKey))
            {
                MessageBox.Show("请先生成许可证密钥。", "缺少许可证", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Clipboard.SetText(GeneratedLicenseKey);
                MessageBox.Show("许可证密钥已复制到剪贴板！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("复制到剪贴板时发生错误: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        [RelayCommand]
        private void VerifyLicenseKey()
        {
            if (string.IsNullOrWhiteSpace(GeneratedLicenseKey))
            {
                MessageBox.Show("请先生成或输入一个许可证密钥。", "缺少许可证", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var licenseData = RsaEncryption.VerifyAndParseLicenseKey(GeneratedLicenseKey);
                if (licenseData == null)
                {
                    MessageBox.Show("许可证密钥无效或已过期（或公钥不匹配）。", "验证失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                else
                {
                    string message = $"许可证验证成功。\n" +
                                     $"唯一标识: {licenseData.UniqueIdentifier}\n" +
                                     $"产品: {licenseData.Product}\n" +
                                     $"软件版本: {licenseData.Product}\n" +
                                     $"许可证类型: {licenseData.LicenseMode}\n";
                    if (licenseData.LicenseMode == LicenseMode.Trial && licenseData.TrialDays.HasValue)
                    {
                        message += $"原始试用天数: {licenseData.TrialDays.Value}\n";
                    }

                    message += $"颁发日期 (UTC): {licenseData.IssueDate:yyyy-MM-dd HH:mm:ss}\n" +
                               $"到期日期 (UTC): {licenseData.ExpiryDate:yyyy-MM-dd HH:mm:ss}";

                    MessageBox.Show(message, "验证成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("验证许可证时发生错误: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void Dispose()
        {
            CurrentPrivateKeyInstance?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}