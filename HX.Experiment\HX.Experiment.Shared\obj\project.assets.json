{"version": 3, "targets": {"net9.0": {"AntDesign/0.15.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.DataAnnotations.Validation": "3.2.0-rc1.20223.4", "Microsoft.AspNetCore.Components.Web": "7.0.0", "OneOf": "2.1.155"}, "compile": {"lib/net7.0/AntDesign.dll": {}}, "runtime": {"lib/net7.0/AntDesign.dll": {}}, "build": {"buildTransitive/AntDesign.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/AntDesign.props": {}}}, "BemIt/2.2.0": {"type": "package", "compile": {"lib/net8.0/BemIt.dll": {}}, "runtime": {"lib/net8.0/BemIt.dll": {}}}, "blazor-dragdrop/2.4.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "6.0.1", "Microsoft.AspNetCore.Components.Web": "6.0.1", "Microsoft.CSharp": "4.7.0"}, "compile": {"lib/net6.0/Plk.Blazor.DragDrop.dll": {}}, "runtime": {"lib/net6.0/Plk.Blazor.DragDrop.dll": {}}}, "DeepCloner.Core/0.1.0": {"type": "package", "compile": {"lib/net8.0/DeepCloner.Core.dll": {}}, "runtime": {"lib/net8.0/DeepCloner.Core.dll": {}}}, "FluentValidation/11.4.0": {"type": "package", "compile": {"lib/net7.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/FluentValidation.dll": {"related": ".xml"}}}, "FluentValidation.DependencyInjectionExtensions/11.4.0": {"type": "package", "dependencies": {"FluentValidation": "11.4.0", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}}, "Majorsoft.Blazor.Extensions.BrowserStorage/1.5.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "5.0.3"}, "compile": {"lib/net5.0/Majorsoft.Blazor.Extensions.BrowserStorage.dll": {}}, "runtime": {"lib/net5.0/Majorsoft.Blazor.Extensions.BrowserStorage.dll": {}}}, "Masa.Blazor/1.10.3": {"type": "package", "dependencies": {"BemIt": "2.2.0", "DeepCloner.Core": "0.1.0", "FluentValidation": "11.4.0", "FluentValidation.DependencyInjectionExtensions": "11.4.0", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "OneOf": "3.0.223", "OneOf.SourceGenerator": "3.0.223", "Util.Reflection": "1.0.3"}, "compile": {"lib/net9.0/Masa.Blazor.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Masa.Blazor.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Masa.Blazor.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Masa.Blazor.props": {}}}, "Masa.Blazor.MobileComponents/1.10.2": {"type": "package", "dependencies": {"Masa.Blazor": "1.10.2", "Microsoft.AspNetCore.Components.Web": "6.0.25"}, "compile": {"lib/net6.0/Masa.Blazor.MobileComponents.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/Masa.Blazor.MobileComponents.dll": {"related": ".pdb"}}}, "Microsoft.AspNetCore.Authorization/9.0.8": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/9.0.8": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.8", "Microsoft.AspNetCore.Components.Analyzers": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.8": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Authorization/9.0.7": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.7", "Microsoft.AspNetCore.Components": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.DataAnnotations.Validation/3.2.0-rc1.20223.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Forms": "3.1.3"}, "compile": {"lib/netstandard2.1/Microsoft.AspNetCore.Components.DataAnnotations.Validation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.Components.DataAnnotations.Validation.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Forms/9.0.8": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/9.0.8": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "9.0.8", "Microsoft.AspNetCore.Components.Forms": "9.0.8", "Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8", "Microsoft.JSInterop": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.7": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.JSInterop.WebAssembly": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net9.0/_._": {}}}, "Microsoft.AspNetCore.Metadata/9.0.8": {"type": "package", "compile": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Physical": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileSystemGlobbing": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.JSInterop/9.0.8": {"type": "package", "compile": {"lib/net9.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/9.0.7": {"type": "package", "dependencies": {"Microsoft.JSInterop": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "OneOf/3.0.223": {"type": "package", "compile": {"lib/netstandard2.0/OneOf.dll": {}}, "runtime": {"lib/netstandard2.0/OneOf.dll": {}}}, "OneOf.SourceGenerator/3.0.223": {"type": "package"}, "System.Text.Json/9.0.7": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "Util.Reflection/1.0.3": {"type": "package", "compile": {"lib/net6.0/Util.Reflection.dll": {}}, "runtime": {"lib/net6.0/Util.Reflection.dll": {}}}, "UFU.CoreFX.Shared/5.0.6": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"AntDesign": "0.15.5", "Majorsoft.Blazor.Extensions.BrowserStorage": "1.5.0", "Microsoft.AspNetCore.Components.Authorization": "9.0.7", "Microsoft.AspNetCore.Components.Web": "9.0.7", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.7", "System.Text.Json": "9.0.7", "blazor-dragdrop": "2.4.0"}, "compile": {"bin/placeholder/UFU.CoreFX.Shared.dll": {}}, "runtime": {"bin/placeholder/UFU.CoreFX.Shared.dll": {}}}, "UFU.IoT.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"AntDesign": "0.15.5", "Microsoft.AspNetCore.Components.Web": "9.0.7", "System.Text.Json": "9.0.7", "UFU.CoreFX.Shared": "5.0.6"}, "compile": {"bin/placeholder/UFU.IoT.Shared.dll": {}}, "runtime": {"bin/placeholder/UFU.IoT.Shared.dll": {}}}}}, "libraries": {"AntDesign/0.15.5": {"sha512": "ldf0sMFcKqBrnEUoiWDo9cIcK+x6yUPcgjYIjq9tD2pc64VNonWVKg1mbXmlDezG2D1CtjCwS96px9mXH/L3Ig==", "type": "package", "path": "antdesign/0.15.5", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "antdesign.0.15.5.nupkg.sha512", "antdesign.nuspec", "build/AntDesign.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/AntDesign.props", "buildTransitive/AntDesign.props", "lib/net5.0/AntDesign.dll", "lib/net6.0/AntDesign.dll", "lib/net7.0/AntDesign.dll", "lib/netstandard2.1/AntDesign.dll", "logo.png", "staticwebassets/css/ant-design-blazor.aliyun.css", "staticwebassets/css/ant-design-blazor.aliyun.min.css", "staticwebassets/css/ant-design-blazor.compact.css", "staticwebassets/css/ant-design-blazor.compact.min.css", "staticwebassets/css/ant-design-blazor.css", "staticwebassets/css/ant-design-blazor.dark.css", "staticwebassets/css/ant-design-blazor.dark.min.css", "staticwebassets/css/ant-design-blazor.min.css", "staticwebassets/css/ant-design-blazor.variable.css", "staticwebassets/css/ant-design-blazor.variable.min.css", "staticwebassets/js/ant-design-blazor.js", "staticwebassets/js/ant-design-blazor.js.map", "staticwebassets/less/affix/style/entry.less", "staticwebassets/less/affix/style/index.less", "staticwebassets/less/affix/style/patch.less", "staticwebassets/less/alert/style/entry.less", "staticwebassets/less/alert/style/index.less", "staticwebassets/less/alert/style/patch.less", "staticwebassets/less/alert/style/rtl.less", "staticwebassets/less/anchor/style/entry.less", "staticwebassets/less/anchor/style/index.less", "staticwebassets/less/anchor/style/patch.less", "staticwebassets/less/anchor/style/rtl.less", "staticwebassets/less/ant-design-blazor.aliyun.less", "staticwebassets/less/ant-design-blazor.compact.less", "staticwebassets/less/ant-design-blazor.dark.less", "staticwebassets/less/ant-design-blazor.less", "staticwebassets/less/ant-design-blazor.variable.less", "staticwebassets/less/auto-complete/style/entry.less", "staticwebassets/less/auto-complete/style/index.less", "staticwebassets/less/auto-complete/style/patch.less", "staticwebassets/less/avatar/style/entry.less", "staticwebassets/less/avatar/style/group.less", "staticwebassets/less/avatar/style/index.less", "staticwebassets/less/avatar/style/rtl.less", "staticwebassets/less/back-top/style/entry.less", "staticwebassets/less/back-top/style/index.less", "staticwebassets/less/back-top/style/responsive.less", "staticwebassets/less/badge/style/entry.less", "staticwebassets/less/badge/style/index.less", "staticwebassets/less/badge/style/patch.less", "staticwebassets/less/badge/style/ribbon.less", "staticwebassets/less/badge/style/rtl.less", "staticwebassets/less/breadcrumb/style/entry.less", "staticwebassets/less/breadcrumb/style/index.less", "staticwebassets/less/breadcrumb/style/patch.less", "staticwebassets/less/breadcrumb/style/rtl.less", "staticwebassets/less/button/style/entry.less", "staticwebassets/less/button/style/index.less", "staticwebassets/less/button/style/mixin.less", "staticwebassets/less/button/style/rtl.less", "staticwebassets/less/button/style/space-compact.less", "staticwebassets/less/calendar/style/entry.less", "staticwebassets/less/calendar/style/index.less", "staticwebassets/less/calendar/style/rtl.less", "staticwebassets/less/card/style/entry.less", "staticwebassets/less/card/style/index.less", "staticwebassets/less/card/style/patch.less", "staticwebassets/less/card/style/size.less", "staticwebassets/less/carousel/style/entry.less", "staticwebassets/less/carousel/style/index.less", "staticwebassets/less/carousel/style/patch.less", "staticwebassets/less/carousel/style/rtl.less", "staticwebassets/less/cascader/style/entry.less", "staticwebassets/less/cascader/style/index.less", "staticwebassets/less/cascader/style/patch.less", "staticwebassets/less/cascader/style/rtl.less", "staticwebassets/less/checkbox/style/entry.less", "staticwebassets/less/checkbox/style/index.less", "staticwebassets/less/checkbox/style/mixin.less", "staticwebassets/less/checkbox/style/patch.less", "staticwebassets/less/checkbox/style/rtl.less", "staticwebassets/less/collapse/style/entry.less", "staticwebassets/less/collapse/style/index.less", "staticwebassets/less/collapse/style/patch.less", "staticwebassets/less/collapse/style/rtl.less", "staticwebassets/less/comment/style/entry.less", "staticwebassets/less/comment/style/index.less", "staticwebassets/less/comment/style/patch.less", "staticwebassets/less/comment/style/rtl.less", "staticwebassets/less/components.less", "staticwebassets/less/config-provider/style/entry.less", "staticwebassets/less/config-provider/style/index.less", "staticwebassets/less/date-picker/style/Calendar.less", "staticwebassets/less/date-picker/style/DecadePanel.less", "staticwebassets/less/date-picker/style/MonthPanel.less", "staticwebassets/less/date-picker/style/MonthPicker.less", "staticwebassets/less/date-picker/style/Picker.less", "staticwebassets/less/date-picker/style/RangePicker.less", "staticwebassets/less/date-picker/style/TimePicker.less", "staticwebassets/less/date-picker/style/WeekPicker.less", "staticwebassets/less/date-picker/style/YearPanel.less", "staticwebassets/less/date-picker/style/entry.less", "staticwebassets/less/date-picker/style/index.less", "staticwebassets/less/date-picker/style/panel.less", "staticwebassets/less/date-picker/style/patch.less", "staticwebassets/less/date-picker/style/rtl.less", "staticwebassets/less/date-picker/style/status.less", "staticwebassets/less/descriptions/style/entry.less", "staticwebassets/less/descriptions/style/index.less", "staticwebassets/less/descriptions/style/patch.less", "staticwebassets/less/descriptions/style/rtl.less", "staticwebassets/less/divider/style/entry.less", "staticwebassets/less/divider/style/index.less", "staticwebassets/less/divider/style/rtl.less", "staticwebassets/less/drawer/style/customize.less", "staticwebassets/less/drawer/style/drawer.less", "staticwebassets/less/drawer/style/entry.less", "staticwebassets/less/drawer/style/index.less", "staticwebassets/less/drawer/style/motion.less", "staticwebassets/less/drawer/style/rtl.less", "staticwebassets/less/dropdown/style/entry.less", "staticwebassets/less/dropdown/style/index.less", "staticwebassets/less/dropdown/style/patch.less", "staticwebassets/less/dropdown/style/rtl.less", "staticwebassets/less/dropdown/style/status.less", "staticwebassets/less/empty/style/entry.less", "staticwebassets/less/empty/style/index.less", "staticwebassets/less/empty/style/patch.less", "staticwebassets/less/empty/style/rtl.less", "staticwebassets/less/form/style/components.less", "staticwebassets/less/form/style/entry.less", "staticwebassets/less/form/style/horizontal.less", "staticwebassets/less/form/style/index.less", "staticwebassets/less/form/style/inline.less", "staticwebassets/less/form/style/mixin.less", "staticwebassets/less/form/style/patch.less", "staticwebassets/less/form/style/rtl.less", "staticwebassets/less/form/style/status.less", "staticwebassets/less/form/style/vertical.less", "staticwebassets/less/grid/style/entry.less", "staticwebassets/less/grid/style/index.less", "staticwebassets/less/grid/style/mixin.less", "staticwebassets/less/grid/style/rtl.less", "staticwebassets/less/icon/style/entry.less", "staticwebassets/less/icon/style/index.less", "staticwebassets/less/icon/style/patch.less", "staticwebassets/less/image/style/entry.less", "staticwebassets/less/image/style/index.less", "staticwebassets/less/image/style/patch.less", "staticwebassets/less/input-number/style/affix.less", "staticwebassets/less/input-number/style/entry.less", "staticwebassets/less/input-number/style/index.less", "staticwebassets/less/input-number/style/rtl.less", "staticwebassets/less/input-number/style/status.less", "staticwebassets/less/input/style/IE11.less", "staticwebassets/less/input/style/affix.less", "staticwebassets/less/input/style/allow-clear.less", "staticwebassets/less/input/style/entry.less", "staticwebassets/less/input/style/index.less", "staticwebassets/less/input/style/mixin.less", "staticwebassets/less/input/style/patch.less", "staticwebassets/less/input/style/rtl.less", "staticwebassets/less/input/style/search-input.less", "staticwebassets/less/input/style/status.less", "staticwebassets/less/layout/style/entry.less", "staticwebassets/less/layout/style/index.less", "staticwebassets/less/layout/style/light.less", "staticwebassets/less/layout/style/patch.less", "staticwebassets/less/layout/style/rtl.less", "staticwebassets/less/list/style/bordered.less", "staticwebassets/less/list/style/customize.less", "staticwebassets/less/list/style/entry.less", "staticwebassets/less/list/style/index.less", "staticwebassets/less/list/style/patch.less", "staticwebassets/less/list/style/responsive.less", "staticwebassets/less/list/style/rtl.less", "staticwebassets/less/locale-provider/style/entry.less", "staticwebassets/less/locale-provider/style/index.less", "staticwebassets/less/mentions/style/entry.less", "staticwebassets/less/mentions/style/index.less", "staticwebassets/less/mentions/style/patch.less", "staticwebassets/less/mentions/style/rtl.less", "staticwebassets/less/mentions/style/status.less", "staticwebassets/less/menu/style/dark.less", "staticwebassets/less/menu/style/entry.less", "staticwebassets/less/menu/style/index.less", "staticwebassets/less/menu/style/light.less", "staticwebassets/less/menu/style/patch.less", "staticwebassets/less/menu/style/rtl.less", "staticwebassets/less/menu/style/status.less", "staticwebassets/less/message/style/entry.less", "staticwebassets/less/message/style/index.less", "staticwebassets/less/message/style/rtl.less", "staticwebassets/less/modal/style/confirm.less", "staticwebassets/less/modal/style/customize.less", "staticwebassets/less/modal/style/entry.less", "staticwebassets/less/modal/style/index.less", "staticwebassets/less/modal/style/modal.less", "staticwebassets/less/modal/style/patch.less", "staticwebassets/less/modal/style/rtl.less", "staticwebassets/less/notification/style/customize.less", "staticwebassets/less/notification/style/entry.less", "staticwebassets/less/notification/style/index.less", "staticwebassets/less/notification/style/patch.less", "staticwebassets/less/notification/style/placement.less", "staticwebassets/less/notification/style/rtl.less", "staticwebassets/less/page-header/style/entry.less", "staticwebassets/less/page-header/style/index.less", "staticwebassets/less/page-header/style/patch.less", "staticwebassets/less/page-header/style/rtl.less", "staticwebassets/less/pagination/style/entry.less", "staticwebassets/less/pagination/style/index.less", "staticwebassets/less/pagination/style/patch.less", "staticwebassets/less/pagination/style/rtl.less", "staticwebassets/less/popconfirm/style/entry.less", "staticwebassets/less/popconfirm/style/index.less", "staticwebassets/less/popconfirm/style/patch.less", "staticwebassets/less/popover/style/customize.less", "staticwebassets/less/popover/style/entry.less", "staticwebassets/less/popover/style/index.less", "staticwebassets/less/popover/style/patch.less", "staticwebassets/less/popover/style/rtl.less", "staticwebassets/less/progress/style/entry.less", "staticwebassets/less/progress/style/index.less", "staticwebassets/less/progress/style/patch.less", "staticwebassets/less/progress/style/rtl.less", "staticwebassets/less/radio/style/entry.less", "staticwebassets/less/radio/style/index.less", "staticwebassets/less/radio/style/patch.less", "staticwebassets/less/radio/style/rtl.less", "staticwebassets/less/rate/style/entry.less", "staticwebassets/less/rate/style/index.less", "staticwebassets/less/rate/style/rtl.less", "staticwebassets/less/result/style/entry.less", "staticwebassets/less/result/style/index.less", "staticwebassets/less/result/style/patch.less", "staticwebassets/less/result/style/rtl.less", "staticwebassets/less/segmented/style/entry.less", "staticwebassets/less/segmented/style/index.less", "staticwebassets/less/segmented/style/mixins.less", "staticwebassets/less/segmented/style/rtl.less", "staticwebassets/less/select/style/entry.less", "staticwebassets/less/select/style/index.less", "staticwebassets/less/select/style/multiple.less", "staticwebassets/less/select/style/patch.less", "staticwebassets/less/select/style/rtl.less", "staticwebassets/less/select/style/single.less", "staticwebassets/less/select/style/status.less", "staticwebassets/less/skeleton/style/entry.less", "staticwebassets/less/skeleton/style/index.less", "staticwebassets/less/skeleton/style/rtl.less", "staticwebassets/less/slider/style/entry.less", "staticwebassets/less/slider/style/index.less", "staticwebassets/less/slider/style/rtl.less", "staticwebassets/less/space/style/compact.less", "staticwebassets/less/space/style/entry.less", "staticwebassets/less/space/style/index.less", "staticwebassets/less/space/style/patch.less", "staticwebassets/less/space/style/rtl.less", "staticwebassets/less/spin/style/entry.less", "staticwebassets/less/spin/style/index.less", "staticwebassets/less/spin/style/patch.less", "staticwebassets/less/spin/style/rtl.less", "staticwebassets/less/statistic/style/entry.less", "staticwebassets/less/statistic/style/index.less", "staticwebassets/less/statistic/style/rtl.less", "staticwebassets/less/steps/style/compatibility.less", "staticwebassets/less/steps/style/custom-icon.less", "staticwebassets/less/steps/style/entry.less", "staticwebassets/less/steps/style/index.less", "staticwebassets/less/steps/style/label-placement.less", "staticwebassets/less/steps/style/nav.less", "staticwebassets/less/steps/style/progress-dot.less", "staticwebassets/less/steps/style/progress.less", "staticwebassets/less/steps/style/rtl.less", "staticwebassets/less/steps/style/small.less", "staticwebassets/less/steps/style/vertical.less", "staticwebassets/less/style/aliyun.less", "staticwebassets/less/style/color/bezierEasing.less", "staticwebassets/less/style/color/colorPalette.less", "staticwebassets/less/style/color/colors.less", "staticwebassets/less/style/color/tinyColor.less", "staticwebassets/less/style/compact.less", "staticwebassets/less/style/core/base.less", "staticwebassets/less/style/core/global.less", "staticwebassets/less/style/core/iconfont.less", "staticwebassets/less/style/core/index.less", "staticwebassets/less/style/core/motion.less", "staticwebassets/less/style/core/motion/fade.less", "staticwebassets/less/style/core/motion/move.less", "staticwebassets/less/style/core/motion/other.less", "staticwebassets/less/style/core/motion/slide.less", "staticwebassets/less/style/core/motion/swing.less", "staticwebassets/less/style/core/motion/zoom.less", "staticwebassets/less/style/dark.less", "staticwebassets/less/style/default.less", "staticwebassets/less/style/entry.less", "staticwebassets/less/style/index.less", "staticwebassets/less/style/mixins/box.less", "staticwebassets/less/style/mixins/clearfix.less", "staticwebassets/less/style/mixins/compact-item-vertical.less", "staticwebassets/less/style/mixins/compact-item.less", "staticwebassets/less/style/mixins/compatibility.less", "staticwebassets/less/style/mixins/customize.less", "staticwebassets/less/style/mixins/iconfont.less", "staticwebassets/less/style/mixins/index.less", "staticwebassets/less/style/mixins/modal-mask.less", "staticwebassets/less/style/mixins/motion.less", "staticwebassets/less/style/mixins/operation-unit.less", "staticwebassets/less/style/mixins/reset.less", "staticwebassets/less/style/mixins/rounded-arrow.less", "staticwebassets/less/style/mixins/size.less", "staticwebassets/less/style/mixins/typography.less", "staticwebassets/less/style/patch.less", "staticwebassets/less/style/themes/aliyun.less", "staticwebassets/less/style/themes/compact.less", "staticwebassets/less/style/themes/dark.less", "staticwebassets/less/style/themes/default.less", "staticwebassets/less/style/themes/index.less", "staticwebassets/less/style/themes/variable.less", "staticwebassets/less/style/v2-compatible-reset.less", "staticwebassets/less/style/variable.less", "staticwebassets/less/switch/style/entry.less", "staticwebassets/less/switch/style/index.less", "staticwebassets/less/switch/style/patch.less", "staticwebassets/less/switch/style/rtl.less", "staticwebassets/less/table/style/bordered.less", "staticwebassets/less/table/style/entry.less", "staticwebassets/less/table/style/index.less", "staticwebassets/less/table/style/patch.less", "staticwebassets/less/table/style/radius.less", "staticwebassets/less/table/style/rtl.less", "staticwebassets/less/table/style/size.less", "staticwebassets/less/tabs/style/card-style.less", "staticwebassets/less/tabs/style/card-style.rtl.less", "staticwebassets/less/tabs/style/card.less", "staticwebassets/less/tabs/style/dropdown.less", "staticwebassets/less/tabs/style/entry.less", "staticwebassets/less/tabs/style/index.less", "staticwebassets/less/tabs/style/patch.less", "staticwebassets/less/tabs/style/position.less", "staticwebassets/less/tabs/style/rtl.less", "staticwebassets/less/tabs/style/size.less", "staticwebassets/less/tag/style/entry.less", "staticwebassets/less/tag/style/index.less", "staticwebassets/less/tag/style/patch.less", "staticwebassets/less/tag/style/rtl.less", "staticwebassets/less/time-picker/style/entry.less", "staticwebassets/less/time-picker/style/index.less", "staticwebassets/less/timeline/style/entry.less", "staticwebassets/less/timeline/style/index.less", "staticwebassets/less/timeline/style/rtl.less", "staticwebassets/less/tooltip/style/entry.less", "staticwebassets/less/tooltip/style/index.less", "staticwebassets/less/tooltip/style/patch.less", "staticwebassets/less/tooltip/style/rtl.less", "staticwebassets/less/transfer/style/customize.less", "staticwebassets/less/transfer/style/entry.less", "staticwebassets/less/transfer/style/index.less", "staticwebassets/less/transfer/style/rtl.less", "staticwebassets/less/transfer/style/status.less", "staticwebassets/less/tree-select/style/entry.less", "staticwebassets/less/tree-select/style/index.less", "staticwebassets/less/tree-select/style/patch.less", "staticwebassets/less/tree/style/directory.less", "staticwebassets/less/tree/style/entry.less", "staticwebassets/less/tree/style/index.less", "staticwebassets/less/tree/style/mixin.less", "staticwebassets/less/tree/style/patch.less", "staticwebassets/less/tree/style/rtl.less", "staticwebassets/less/typography/style/entry.less", "staticwebassets/less/typography/style/index.less", "staticwebassets/less/typography/style/rtl.less", "staticwebassets/less/upload/style/entry.less", "staticwebassets/less/upload/style/index.less", "staticwebassets/less/upload/style/patch.less", "staticwebassets/less/upload/style/rtl.less"]}, "BemIt/2.2.0": {"sha512": "oSrFyB/9fvxKVEqwoMjHmxDSnEFiTjyxIFjdOEyXmc/4YVXoEiYhxrNhNA4B/4lAfAMQBVcOWXdllZ+bSkWNqg==", "type": "package", "path": "bemit/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "bemit.2.2.0.nupkg.sha512", "bemit.nuspec", "lib/net6.0/BemIt.dll", "lib/net7.0/BemIt.dll", "lib/net8.0/BemIt.dll"]}, "blazor-dragdrop/2.4.0": {"sha512": "gLw4kjHNV/q1x/KeNLDMvr1SHOU9XsbYuJ0k9Bta3I9DqJqqwBgiuCJPYT+YgoMVzMHPwQMlpSA7eO0Jsk+b7g==", "type": "package", "path": "blazor-dragdrop/2.4.0", "files": [".nupkg.metadata", ".signature.p7s", "blazor-dragdrop.2.4.0.nupkg.sha512", "blazor-dragdrop.nuspec", "lib/net6.0/Plk.Blazor.DragDrop.dll"]}, "DeepCloner.Core/0.1.0": {"sha512": "45ovZyLKECJUeyMqxp8QDc7D85lUhDfaSgu0d7W9o8GU1PdePKy9sAVh+4tlXqt3PyXnTRRU42Ta+iRssbLarw==", "type": "package", "path": "deepcloner.core/0.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "deepcloner.core.0.1.0.nupkg.sha512", "deepcloner.core.nuspec", "lib/net462/DeepCloner.Core.dll", "lib/net6.0/DeepCloner.Core.dll", "lib/net7.0/DeepCloner.Core.dll", "lib/net8.0/DeepCloner.Core.dll"]}, "FluentValidation/11.4.0": {"sha512": "WZJyrNf4suiaI6avVG32W9kmBLC3+V5rDTcLeRJjeU4tDmTyDIOz1459DzCKppXs34YpZg9rCUzDY3M4XxG7Ug==", "type": "package", "path": "fluentvalidation/11.4.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.4.0.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/net7.0/FluentValidation.dll", "lib/net7.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "FluentValidation.DependencyInjectionExtensions/11.4.0": {"sha512": "iZXtoDqfRnggHwIbFWnK1sFs3KlTadH5ezNDiXz411OSkUNowBRboaZBcSvx4gEot7CNZdoReUKWYJwow2+mbg==", "type": "package", "path": "fluentvalidation.dependencyinjectionextensions/11.4.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.dependencyinjectionextensions.11.4.0.nupkg.sha512", "fluentvalidation.dependencyinjectionextensions.nuspec", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.xml", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.xml"]}, "Majorsoft.Blazor.Extensions.BrowserStorage/1.5.0": {"sha512": "KNlVUlAmc/GX2T5YJG1rnZ0YDEnF656s9Tl+tInw6+7j8FAFHAnDGrMjV7/eNQJh0rSgCgRshCJbAPdOQzFRMg==", "type": "package", "path": "majorsoft.blazor.extensions.browserstorage/1.5.0", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "blazor.components.png", "lib/net5.0/Majorsoft.Blazor.Extensions.BrowserStorage.dll", "majorsoft.blazor.extensions.browserstorage.1.5.0.nupkg.sha512", "majorsoft.blazor.extensions.browserstorage.nuspec"]}, "Masa.Blazor/1.10.3": {"sha512": "Xu4fIm0MM21YgeDaS1xxr43zwBU07oW3g/MuyTX97/H3xzcp+PLBbYT4o2Nf/70liRbXEAjoJ9QIyzUwk0+QkA==", "type": "package", "path": "masa.blazor/1.10.3", "files": [".nupkg.metadata", ".signature.p7s", "Masa.Blazor96_96.png", "README.md", "build/Masa.Blazor.props", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/Masa.Blazor.props", "buildTransitive/Masa.Blazor.props", "lib/net6.0/Masa.Blazor.dll", "lib/net6.0/Masa.Blazor.pdb", "lib/net7.0/Masa.Blazor.dll", "lib/net7.0/Masa.Blazor.pdb", "lib/net8.0/Masa.Blazor.dll", "lib/net8.0/Masa.Blazor.pdb", "lib/net9.0/Masa.Blazor.dll", "lib/net9.0/Masa.Blazor.pdb", "masa.blazor.1.10.3.nupkg.sha512", "masa.blazor.nuspec", "staticwebassets/Presets/EllipsisText/EllipsisText.razor.js", "staticwebassets/css/masa-blazor.min.css", "staticwebassets/js/chunks/EventType-63cda6c3.js", "staticwebassets/js/chunks/helper-6d386307.js", "staticwebassets/js/chunks/index-cef005e4.js", "staticwebassets/js/chunks/touch-5a32c5ea.js", "staticwebassets/js/chunks/tslib.es6-68144fbe.js", "staticwebassets/js/components/input/index-b92d32d0.js", "staticwebassets/js/components/navigation-drawer/touch-47e3a6be.js", "staticwebassets/js/components/overlay/scroll-strategy-dc362cab.js", "staticwebassets/js/components/page-stack/index-3d21987e.js", "staticwebassets/js/components/page-stack/touch-ecbec91c.js", "staticwebassets/js/components/scroll-to-target/index-1c14c8ac.js", "staticwebassets/js/components/transition/index-339f8848.js", "staticwebassets/js/components/window/touch-f9d2ba92.js", "staticwebassets/js/manifest.json", "staticwebassets/js/masa-blazor.js", "staticwebassets/js/mixins/activatable/index-82cb7376.js", "staticwebassets/js/mixins/intersect/index-f360c115.js", "staticwebassets/js/mixins/outside-click/index-47d0ce8d.js", "staticwebassets/js/mixins/resize/index-07a0c3f6.js", "staticwebassets/js/wrappers/baidumap.js", "staticwebassets/js/wrappers/drawflow-proxy.js", "staticwebassets/js/wrappers/echarts.js", "staticwebassets/js/wrappers/monaco-editor.js", "staticwebassets/js/wrappers/quill/quill-helper.js", "staticwebassets/js/wrappers/sortable.js", "staticwebassets/js/wrappers/vditor/vditor-helper.js"]}, "Masa.Blazor.MobileComponents/1.10.2": {"sha512": "+lYwxXUNlnvYlqwpuOgVkhgltmp7YvTx6wX81u9AnMYuPMEFuYiIHHpvZd0gqDdovK9nJdZvNaCb4JfkrhpAeA==", "type": "package", "path": "masa.blazor.mobilecomponents/1.10.2", "files": [".nupkg.metadata", ".signature.p7s", "Masa.Blazor96_96.png", "lib/net6.0/Masa.Blazor.MobileComponents.dll", "lib/net6.0/Masa.Blazor.MobileComponents.pdb", "masa.blazor.mobilecomponents.1.10.2.nupkg.sha512", "masa.blazor.mobilecomponents.nuspec"]}, "Microsoft.AspNetCore.Authorization/9.0.8": {"sha512": "1M8ncVJtsRBEfbDc9QCa2zuwMjh/YWLu/WnRXWHG5q+KE5FDvPEsV3cJag3qy6NWL2b0H+pdushFsN9d3geemA==", "type": "package", "path": "microsoft.aspnetcore.authorization/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net9.0/Microsoft.AspNetCore.Authorization.dll", "lib/net9.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.9.0.8.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/9.0.8": {"sha512": "+s+r/l2iBoU1mnG8ft9HS/xzqjd0cGbqnsVC9PgjNa5VGm/pVbPEMUJDea58iOaVETi7VGQCDoHLG83ObmlK2A==", "type": "package", "path": "microsoft.aspnetcore.components/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.dll", "lib/net9.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.9.0.8.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/9.0.8": {"sha512": "uRx+7E0/nKIbQ0yG2/TYr6RYqeVanWwAVdwzzffGvTyI/jZBE3YT9aJ9c0DGrhgvkDZPzH1Z4ryp6LdMmBJFAQ==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "microsoft.aspnetcore.components.analyzers.9.0.8.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Authorization/9.0.7": {"sha512": "mICbg7Cic394tN3xoLsKi4aWiARw1eCS1bNMyho81WdIKdHayxCTw6Fvj6eLMPc+zgVsoIdYWTh5w2TRwVl8pQ==", "type": "package", "path": "microsoft.aspnetcore.components.authorization/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Authorization.xml", "microsoft.aspnetcore.components.authorization.9.0.7.nupkg.sha512", "microsoft.aspnetcore.components.authorization.nuspec"]}, "Microsoft.AspNetCore.Components.DataAnnotations.Validation/3.2.0-rc1.20223.4": {"sha512": "VyZAAbuXuAVrh4Vii5Aq1N841iVhSjE4mTf/lVh+AgrKinaltE0IeEWPKv889j4ineX7i9BUiAqlGP7UpZwlnQ==", "type": "package", "path": "microsoft.aspnetcore.components.dataannotations.validation/3.2.0-rc1.20223.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/netstandard2.1/Microsoft.AspNetCore.Components.DataAnnotations.Validation.dll", "lib/netstandard2.1/Microsoft.AspNetCore.Components.DataAnnotations.Validation.xml", "microsoft.aspnetcore.components.dataannotations.validation.3.2.0-rc1.20223.4.nupkg.sha512", "microsoft.aspnetcore.components.dataannotations.validation.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/9.0.8": {"sha512": "OgbzTUq4/y0jbUspgBFBQ3TJZepx05uFYLgXyB04uwTIM2JtHFcFcQvoO2lWRwXl8I2B5PtMTcTFVFt+/b5onQ==", "type": "package", "path": "microsoft.aspnetcore.components.forms/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.9.0.8.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/9.0.8": {"sha512": "YKdFoDJ36VNRDoZfoc6bf/RWEg4mR/f6Fa9CToDEiqwZId70Qc9JUamM4rgfDaKLIGY7QEeh3oS3j1cQb5AUUA==", "type": "package", "path": "microsoft.aspnetcore.components.web/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Web.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.9.0.8.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.7": {"sha512": "XQpNRCk90fdz6srrcnED2zLQrjFjZYduqCpKrdzPhBicD37kW1p7cDGfX6Smfgk937iZmZwh72xAqEDYYjU8tA==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/net9.0/Microsoft.AspNetCore.Components.WebAssembly.props", "build/net9.0/blazor.webassembly.js", "lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll", "lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.xml", "microsoft.aspnetcore.components.webassembly.9.0.7.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.nuspec"]}, "Microsoft.AspNetCore.Metadata/9.0.8": {"sha512": "sju8zleT9mRQWeEe4hu4Vh4uep6WppxULZEkirEgU/pRl6uIQQsd526EBa8u52j+LuA3J8gS8twRmTpsr3xGtA==", "type": "package", "path": "microsoft.aspnetcore.metadata/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net9.0/Microsoft.AspNetCore.Metadata.dll", "lib/net9.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.9.0.8.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration/9.0.7": {"sha512": "oxGR51+w5cXm5B9gU6XwpAB2sTiyPSmZm7hjvv0rzRnmL5o/KZzE103AuQj7sK26OBupjVzU/bZxDWvvU4nhEg==", "type": "package", "path": "microsoft.extensions.configuration/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"sha512": "lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"sha512": "ExY+zXHhU4o9KC2alp3ZdLWyVWVRSn5INqax5ABk+HEOHlAHzomhJ7ek9HHliyOMiVGoYWYaMFOGr9q59mSAGA==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.7": {"sha512": "3LVg32iMfR9ENeegXAo73L+877iOcQauLJsXlKZNVSsLA/HbPgClZdeMGdjLSkaidYw3l02XbXTlOdGYNgu91Q==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.7": {"sha512": "3HQV326liEInT9UKEc+k73f1ECwNhvDS/DJAe5WvtMKDJTJqTH2ujrUC2ZlK/j6pXyPbV9f0Ku8JB20JveGImg==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"sha512": "JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"sha512": "xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"sha512": "y9djCca1cz/oz/J8jTxtoecNiNvaiGBJeWd7XOPxonH+FnfHqcfslJMcSr5JMinmWFyS7eh3C9L6m6oURZ5lSA==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.7": {"sha512": "JYEPYrb+YBpFTCdmSBrk8cg3wAi1V4so7ccq04qbhg3FQHQqgJk28L3heEOKMXcZobOBUjTnGCFJD49Ez9kG5w==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.7": {"sha512": "5VKpTH2ME0SSs0lrtkpKgjCeHzXR5ka/H+qThPwuWi78wHubApZ/atD7w69FDt0OOM7UMV6LIbkqEQgoby4IXA==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.7": {"sha512": "fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "type": "package", "path": "microsoft.extensions.logging/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.7.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"sha512": "pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.8": {"sha512": "OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "type": "package", "path": "microsoft.extensions.options/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.8.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.8": {"sha512": "tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "type": "package", "path": "microsoft.extensions.primitives/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.8.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.JSInterop/9.0.8": {"sha512": "arGqasYOND4vG0s1RywqHAKq1NIyFrco/srpM9QDlVGnFrYdtkWZUEuiVcD5GZswfmYOGVD5CLIHVVb8S86ylQ==", "type": "package", "path": "microsoft.jsinterop/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.JSInterop.dll", "lib/net9.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.9.0.8.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Microsoft.JSInterop.WebAssembly/9.0.7": {"sha512": "MsRXwJ1VmZYp6+497VwywuQ3eD/QRJqE/mXOvTVNW84YCFefZ/VBLgy6gxB5ymJPl8V/VxEzih2Yeu4mOO6wdQ==", "type": "package", "path": "microsoft.jsinterop.webassembly/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.JSInterop.WebAssembly.dll", "lib/net9.0/Microsoft.JSInterop.WebAssembly.xml", "microsoft.jsinterop.webassembly.9.0.7.nupkg.sha512", "microsoft.jsinterop.webassembly.nuspec"]}, "OneOf/3.0.223": {"sha512": "YWabiHBFfDdKIO7/TEa72mARVVo5u5FqFnfXuTqjIupMvJlhoH8d0m4uKtczaINuT0vGR1qlXJayeU6bTw5myg==", "type": "package", "path": "oneof/3.0.223", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/OneOf.dll", "lib/net45/OneOf.dll", "lib/netstandard1.3/OneOf.dll", "lib/netstandard2.0/OneOf.dll", "oneof.3.0.223.nupkg.sha512", "oneof.nuspec"]}, "OneOf.SourceGenerator/3.0.223": {"sha512": "cEhMSGyKgs14cuBS3b1n1DGXSlMw3Tk5s2O03VGKU8hHspBud/+UdI9Sy849DQ1cygQoDmm3LUj02ArMv+FIjQ==", "type": "package", "path": "oneof.sourcegenerator/3.0.223", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/OneOf.SourceGenerator.dll", "oneof.sourcegenerator.3.0.223.nupkg.sha512", "oneof.sourcegenerator.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "System.Text.Json/9.0.7": {"sha512": "u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "type": "package", "path": "system.text.json/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.7.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "Util.Reflection/1.0.3": {"sha512": "uKQLJZ9KKXvQ6JYLbOCISyBpssmBAX4HGXhmTifRsD/kD1E4bZYyivwDqBvTleNIE1/tmj/XhapsukxKNQEK5A==", "type": "package", "path": "util.reflection/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Util.Reflection.dll", "util.reflection.1.0.3.nupkg.sha512", "util.reflection.nuspec"]}, "UFU.CoreFX.Shared/5.0.6": {"type": "project", "path": "../../../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj", "msbuildProject": "../../../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/UFU.CoreFX.Shared.csproj"}, "UFU.IoT.Shared/1.0.0": {"type": "project", "path": "../../UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj", "msbuildProject": "../../UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["Masa.Blazor >= 1.10.3", "Masa.Blazor.MobileComponents >= 1.10.2", "Microsoft.AspNetCore.Components.Web >= 9.0.*", "UFU.CoreFX.Shared >= 5.0.6", "UFU.IoT.Shared >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj", "projectName": "HX.Experiment.Shared", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\HX.Experiment.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj": {"projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj"}, "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Masa.Blazor": {"target": "Package", "version": "[1.10.3, )"}, "Masa.Blazor.MobileComponents": {"target": "Package", "version": "[1.10.2, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[9.0.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}