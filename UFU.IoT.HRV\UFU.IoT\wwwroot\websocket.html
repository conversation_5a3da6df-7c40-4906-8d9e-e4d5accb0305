﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body>
    <button onclick="send()">发送</button>
    <p>开始时间</p>
    <div id="starttime"></div>
    <p>当前时间</p>
    <div id="currenttime"></div>
    <p>当前数据</p>
    <div id="show"></div>
    <script>
        var num = 0;
        var socket;
        var uri = "ws://" + location.hostname + "/iot/admin/v1";
        //var uri = "ws://" + location.hostname + "/iot/manufacturer/v1";
        //var uri = "ws://" + location.hostname + "/iot/device/v1";
        //var uri = "ws://iot.ufusmart.com/iot/device/v1";
        function start() {
            //判断上一次的状态
            if (socket && socket.readyState != 3) {
                return;
            }
            //开始连接
            socket = new WebSocket(uri);
            //打开连接回调
            socket.onopen = function (e) {
                console.log("open");
                var time = new Date().getTime();
                document.getElementById("starttime").innerHTML = time;
            };
            //接收数据回调
            socket.onmessage = function (e) {
                console.log(e.data);
            };
            //关闭连接回调
            socket.onclose = function (e) {
                console.log("close");
                socket.close();
            };
            //错误回调
            socket.onerror = function (e) {
                console.log("error:" + e);
                socket.close();
            };
        }

        //开始连接
        start();
        //每秒定时重连
        setInterval(start, 1000);


        //发送信息
        function send() {
            socket.send(JSON.stringify({ "Ver": "1.0", "Msg": { "MsgId": 0, "CMD": 4 } }));
            //socket.send(JSON.stringify({ "Func": "Msg", "Data": { "CMD": 8, "Data": {}, "MsgId": 10001, "Device": { "Id": "1810081017100001" } } }));
            //document.getElementById("show").innerHTML = num;
            //var time = new Date().getTime();
            //document.getElementById("currenttime").innerHTML = time;
            //num++;
        }
        //关闭socket
        function close() {
            console.log("close");
            socket.close();
        }
       

    </script>
</body>
</html>