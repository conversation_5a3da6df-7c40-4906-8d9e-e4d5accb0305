﻿using System.IO.Compression;
using HX.HRV.Shared.Models;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web.Services;

/// <summary>
/// 
/// </summary>
public class FileExportService : BackgroundService
{
    private static List<DataModel<HxExportTaskModel>> _pendingTasks;
    /// <summary>
    /// 导出文件的正则
    /// </summary>
    private readonly string ExportRegex = @"*.csv";

    private readonly DataRepository _context;

    /// <summary>
	/// 
	/// </summary>
	public FileExportService( IConfiguration configuration)
    {
        ExportRegex = configuration.GetSection("HrvVariable:ExportRegex")?.Value??"*.csv";
        _context=new DataRepository();
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await Task.Delay(2000, stoppingToken);
        // 使用 dataRepository 执行你的任务
        // 从数据库中获取待处理的任务
        while (!stoppingToken.IsCancellationRequested) // 循环执行直到服务被停止
        {
            await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
            _pendingTasks = await GetPendingTasksAsync();
            try
            {
                if (_pendingTasks is not { Count: > 0 })
                {
                    continue;
                }

                for (int i = 0; i < _pendingTasks.Count; i++)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    // 执行任务
                    await ExportFilesAsync(_pendingTasks[i]);
                    _pendingTasks.Remove(_pendingTasks[i]);
                }
            }
            catch (TaskCanceledException ex)
            {
                LogTool.Logger.Error(ex, "处理任务被取消");
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, "处理任务发生错误");
            }
        }
    }

    private async Task<List<DataModel<HxExportTaskModel>>> GetPendingTasksAsync()
    {
        var query = _context.Query<HxExportTaskModel>(PermissionSwitch.Off)
            .Where(t => t.Data.ExportStatus == EnumExportTaskStatus.UnStart);
        return await query.ToListAsync();
    }

    public static async Task AddTask(string taskId)
    {
        await using var db = new DataRepository(UserInfo.System);
        var task = db.Query<HxExportTaskModel>()
            .FirstOrDefault(m => m.Id == taskId);
        if (task == null)
            return;
        _pendingTasks.Add(task);
    }
    
    
   public async Task ExportFilesAsync(DataModel<HxExportTaskModel> taskdata)
{
    await using var db = new DataRepository(UserInfo.System);
    var task = taskdata.Data;
    if (task.ExportStatus != EnumExportTaskStatus.Exporting)
    {
        task.ExportStatus = EnumExportTaskStatus.Exporting;
    }
    db.Update(taskdata);
    await db.SaveChangesAsync();

    // 根据 task.PatientRecordIds 查询所有病历记录
    var ids = task.PatientRecordIds;
    var records = await db.Query<PatientRecordModel>()
                           .Where(r => ids.Contains(r.Id))
                           .ToListAsync();

    // 定义导出 ZIP 的输出路径
    var outputDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "export");
    var outputZipPath = Path.Combine(outputDirectory, $"{task.Id}.zip");

    // 确保输出目录存在，否则创建
    if (!Directory.Exists(outputDirectory))
    {
        Directory.CreateDirectory(outputDirectory);
    }
    // 如果目标 ZIP 文件已存在，则删除
    if (File.Exists(outputZipPath))
    {
        File.Delete(outputZipPath);
    }

    // 创建 ZIP 文件
    using var zipArchive = ZipFile.Open(outputZipPath, ZipArchiveMode.Create);

    // 对每条记录分别处理，确保每个记录在 ZIP 中都拥有一个独立文件夹
    for (var i = 0; i < records.Count; i++)
    {
        var record = records[i];
        var recordFolderPath = record.Data.GetRecordDirectoryPath();

        if (!Directory.Exists(recordFolderPath))
        {
           LogTool.Logger.Information($"文件夹不存在：{recordFolderPath}");
        }
        else
        {
            // 使用 ExportRegex 过滤当前记录目录下的所有匹配文件（包括子目录）
            var files = Directory.GetFiles(recordFolderPath, ExportRegex, SearchOption.AllDirectories);
            files = files.OrderBy(f =>
            {
                var fileName = Path.GetFileNameWithoutExtension(f);
                var fileNameSplits = fileName.Split("_");
                if (fileNameSplits.Length < 2)
                {
                    return 0;
                }
                var timeStr = fileNameSplits[1];
                var tryParse = long.TryParse(timeStr, out var timestamp);
                return tryParse?timestamp:0;
            }).ToArray();
            
            // 使用 Dictionary 合并同一记录中文件名相同（以 "_" 分割取得第一个部分）的文件内容
            var fileContentsDict = new Dictionary<string, List<string>>();
            foreach (var file in files)
            {
                // 读取每个文件所有内容并在每行开头插入单引号
                var lines = await File.ReadAllLinesAsync(file);
                var processedLines = lines.Select(s => $"'{s}").ToArray();

                var fileName = Path.GetFileName(file);
                // 从文件名中提取真实文件名（按 "_" 分割，取第一个部分）
                var realFileName = fileName.Split("_")[0];

                if (!fileContentsDict.ContainsKey(realFileName))
                {
                    fileContentsDict[realFileName] = new List<string>();
                }
                fileContentsDict[realFileName].AddRange(processedLines);
            }

            // 将合并后的内容写入 ZIP 中对应的 record 文件夹下
            foreach (var kvp in fileContentsDict)
            {
                var realFileName = kvp.Key;
                var allLines = kvp.Value;
                // 定义 ZIP 项目的相对路径，格式为 "record.Id/realFileName"
                var entryPath = Path.Combine(record.Id, realFileName.TrimEnd(".csv")+".csv");
                // 为确保 ZIP 条目路径使用正斜杠，进行替换
                entryPath = entryPath.Replace("\\", "/");
                var entry = zipArchive.CreateEntry(entryPath);
                using var writer = new StreamWriter(entry.Open());
                foreach (var line in allLines)
                {
                    await writer.WriteLineAsync(line);
                }
            }
        }
        // 更新进度，假设每条记录权重相同
        task.Progress = (i + 1) / (float)records.Count * 100;
        db.Update(taskdata);
        await db.SaveChangesAsync();
        await Task.Delay(500);
    }

    // 导出全部完成后更新任务状态和文件路径信息
    task.ExportStatus = EnumExportTaskStatus.Finished;
    taskdata.UpdateTime = DateTime.Now;
    taskdata.Data.FilePath = outputZipPath;
    db.Update(taskdata);
    await db.SaveChangesAsync();
}



// public async Task ExportFilesAsync(DataModel<HxExportTaskModel> taskdata)
// {
//     await using var db = new DataRepository(UserInfo.System);
//     // LogTool.Logger.Information("开始执行导出任务,任务ID：{taskId}", taskdata.Id);
//     var task = taskdata.Data;
//     if (task.ExportStatus != EnumExportTaskStatus.Exporting)
//     {
//         task.ExportStatus = EnumExportTaskStatus.Exporting;
//     }
//
//     db.Update(taskdata);
//     await db.SaveChangesAsync();
//     var ids = task.PatientRecordIds;
//     var records = await db.Query<PatientRecordModel>().Where(r => ids.Contains(r.Id)).ToListAsync();
//     var folders = records.Select(r => r.Data.GetRecordDirectoryPath()).Distinct().ToList();
//     var outputDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "export");
//     var outputZipPath = Path.Combine(outputDirectory, $"{task.Id}.zip");
//
//     // 检查文件夹是否存在
//     if (!Directory.Exists(outputDirectory))
//     {
//         Directory.CreateDirectory(outputDirectory);
//     }
//
//     // 检查目标 ZIP 文件是否已存在，如果存在则删除
//     if (File.Exists(outputZipPath))
//     {
//         File.Delete(outputZipPath);
//     }
//
//     // 创建一个新的 ZIP 文件
//     using var zipArchive = ZipFile.Open(outputZipPath, ZipArchiveMode.Create);
//     for (var i = 0; i < folders.Count; i++)
//     {
//         var folderPath = folders[i];
//         // 检查文件夹是否存在
//         if (!Directory.Exists(folderPath))
//         {
//             Console.WriteLine($"文件夹不存在: {folderPath}");
//         }
//         if (Directory.Exists(folderPath))
//         {
//             // 获取文件夹中的所有文件
//             var files = Directory.GetFiles(folderPath, ExportRegex, SearchOption.AllDirectories);
//             var index = 1;
//             foreach (var file in files)
//             {
//                 var str = await File.ReadAllLinesAsync(file);
//                 str = str.Select(s => s.Insert(0, "'")).ToArray();
//                 var fileName = Path.GetFileName(file);
//                 var realFileName = fileName.Split("_")[0];
//                 var fileEntry = zipArchive.CreateEntry(realFileName);
//                 using var writer = new StreamWriter(fileEntry.Open());
//                 foreach (var line in str)
//                 {
//                     await writer.WriteLineAsync(line);
//                 }
//                 task.Progress = (i + 1) / folders.Count * (index / (float)files.Length) * 100;
//                 index++;
//             }
//         }
//         taskdata.Data.FilePath = outputZipPath;
//         // 更新进度
//         task.Progress = (i + 1) / folders.Count * 100;
//         db.Update(taskdata);
//         await db.SaveChangesAsync();
//         await Task.Delay(500);
//     }
//     // 导出完成
//     task.ExportStatus = EnumExportTaskStatus.Finished;
//     taskdata.UpdateTime = DateTime.Now;
//     taskdata.Data.FilePath = outputZipPath;
//     db.Update(taskdata);
//     await db.SaveChangesAsync();
// }

    // public async Task ExportFilesAsync(DataModel<HxExportTaskModel> taskdata, DataRepository db)
    // {
    //     // LogTool.Logger.Information("开始执行导出任务,任务ID：{taskId}", taskdata.Id);
    //     var task = taskdata.Data;
    //     if (task.ExportStatus != EnumExportTaskStatus.Exporting)
    //     {
    //         task.ExportStatus = EnumExportTaskStatus.Exporting;
    //     }
    //
    //   
    //     db.Update(taskdata);
    //     await db.SaveChangesAsync();
    //     var ids = task.PatientRecordIds;
    //     var records = await db.Query<PatientRecordModel>().Where(r => ids.Contains(r.Id)).ToListAsync();
    //     var folders = records.Select(r => r.Data.GetRecordDirectoryPath()).Distinct().ToList();
    //     var outputDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "export");
    //     var outputZipPath = Path.Combine(outputDirectory, $"{task.Id}.zip");
    //     // 检查文件夹是否存在
    //     if (!Directory.Exists(outputDirectory))
    //     {
    //         Directory.CreateDirectory(outputDirectory);
    //     }
    //     var tempDir = Path.Combine(outputDirectory, "temp",task.Id);
    //
    //     // 检查目标 ZIP 文件是否已存在，如果存在则删除
    //     if (File.Exists(outputZipPath))
    //     {
    //         File.Delete(outputZipPath);
    //     }
    //
    //     // 创建一个新的 ZIP 文件
    //     using var zipArchive = ZipFile.Open(outputZipPath, ZipArchiveMode.Create);
    //     for (var i = 0; i < folders.Count; i++)
    //     {
    //         var folderPath = folders[i];
    //         // 检查文件夹是否存在
    //         if (!Directory.Exists(folderPath))
    //         {
    //             Console.WriteLine($"文件夹不存在: {folderPath}");
    //         }
    //         if (Directory.Exists(folderPath))
    //         {
    //             // 获取文件夹的名称，用作 ZIP 中的根目录名称
    //             var folderName = Path.GetFileName(folderPath);
    //             // 获取文件夹中的所有文件
    //             var files = Directory.GetFiles(folderPath, ExportRegex, SearchOption.AllDirectories);
    //             var index = 1;
    //             foreach (var file in files)
    //             {
    //                 // 检查文件夹是否存在
    //                 if (!Directory.Exists(tempDir))
    //                 {
    //                     Directory.CreateDirectory(tempDir);
    //                 }
    //                 var str =await File.ReadAllLinesAsync(file);
    //                 str = str.Select(s => s.Insert(0,"'")).ToArray();
    //                 var fileName = Path.GetFileName(file);
    //                 var realFileName = fileName.Split("_")[0];
    //                 await File.AppendAllLinesAsync(Path.Combine(tempDir,$"{realFileName}.csv"), str);
    //                 task.Progress = (i + 1) / folders.Count*( ++index/(files.Length*1.0)) * 100;
    //             }
    //             // 将文件夹中的所有文件和子文件夹添加到 ZIP 文件
    //             AddFolderToZip(zipArchive, tempDir, folderName);
    //         }
    //         taskdata.Data.FilePath=outputZipPath;
    //         // 更新进度
    //         task.Progress = (i + 1) / folders.Count * 100;
    //         db.Update(taskdata);
    //         await db.SaveChangesAsync();
    //         await Task.Delay(500);
    //     }
    //     // 导出完成
    //     task.ExportStatus = EnumExportTaskStatus.Finished;
    //     taskdata.UpdateTime = DateTime.Now;
    //     taskdata.Data.FilePath = outputZipPath;
    //     db.Update(taskdata);
    //     //删除temp中的文件
    //     Directory.Delete(tempDir, true);
    //     await db.SaveChangesAsync();
    // }

    // 将文件夹中的所有文件和子文件夹递归添加到 ZIP 文件
    private static void AddFolderToZip(ZipArchive zipArchive, string folderPath, string baseFolderName)
    {
        // 获取文件夹中的所有文件
        var files = Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories);
        foreach (var file in files)
        {
            // 计算文件在 ZIP 文件中的相对路径
            var relativePath = Path.Combine(baseFolderName, Path.GetRelativePath(folderPath, file));
            // 添加文件到 ZIP
            zipArchive.CreateEntryFromFile(file, relativePath);
        }
    }

    public override async Task StopAsync(CancellationToken stoppingToken)
    {
        LogTool.Logger.Information("导出任务服务正在停止");
        await base.StopAsync(stoppingToken);
    }
}