﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <script src="/lib/jquery/dist/jquery.min.js"></script>
    <script src="http://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
</head>
<body>
    <button id="config" style="width:200px;height:30px;font-size:14px">WiFi配网</button>
    <script>
        $.ajax({
            type: "GET",
            url: "/api/Core/Weixin/GetJsApiConfig",
            contentType: "application/json",
            data: {
                url: location.href
            },
            success: function (data) {
                wx.config({
                    debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: data.appId, // 必填，公众号的唯一标识
                    timestamp: data.timestamp, // 必填，生成签名的时间戳
                    nonceStr: data.nonceStr, // 必填，生成签名的随机串
                    signature: data.signature,// 必填，签名
                    jsApiList: ['configWXDeviceWiFi'] // 必填，需要使用的JS接口列表
                });
            }
        });

        $("#config").click(function () {
            wx.invoke('configWXDeviceWiFi', {}, function (res) {
                if (res.err_msg == 'configWXDeviceWiFi:ok') {
                    alert("配网成功")
                } else if (res.err_msg == 'configWXDeviceWiFi:fail') {
                    alert("配网超时")
                } else if (res.err_msg == 'configWXDeviceWiFi:cancel') {
                    alert("用户取消")
                }
            });
        });

    </script>
</body>
</html>