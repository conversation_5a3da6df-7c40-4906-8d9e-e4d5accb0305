<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
		<DefaultAppHostRuntimeIdentifier>win-x64</DefaultAppHostRuntimeIdentifier>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<Authors>Kevin</Authors>
		<Company>悠服科技(UFUTECH)</Company>
		<Description>悠服科技物联网系统</Description>
		<Copyright>Copyright (C) 悠服科技(UFUTECH) 2023</Copyright>
		<Version>3.0.0</Version>
		<AssemblyVersion>3.0.0</AssemblyVersion>
		<FileVersion>3.0.0</FileVersion>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="NPOI" Version="2.6.0" />
		<PackageReference Include="RulesEngine" Version="5.0.3" />
		<PackageReference Include="UFU.CoreFX" Version="5.0.3" />
		<PackageReference Include="UFU.IoT.UI" Version="2.3.1" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\UFU.IoT.Shared\UFU.IoT.Shared.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Models\" />
	</ItemGroup>
</Project>
