@page "/client/device-status"
@using System.Net.WebSockets
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Pages.Client.Dialog
@using HX.HRV.Shared.Pages.Client.Components

@attribute [Permission(MenuName = "设备状态", Name = "心率变异性/设备状态", Icon = "shebeixinxi", MenuOrder = "1", IsMenu = true)]
@inject DeviceStatusDialog DeviceStatusDialog
@inject DeviceStateService DeviceStateService

@implements IDisposable

<style>
	.right-panel {
		padding: 1rem;
		background-color: #f5f8fa;
		border-radius: 10px;
	}

	.right-img {
		width: 70px;
		height: 70px;
	}

	.new-test-button {
		padding: 1rem;
		font-size: 1.2rem;
		width: 319px;
		text-align: center;
		display: flex;
		border-radius: 19px 0px 0px 19px;
		background-size: cover;
		flex-direction: column;
		justify-content: center;
	}

	.new-test-button p {
		font-size: 1.2rem;
		color: white;
	}

	.stat-card {
		background-color: #ffffff;
		padding: 2%;
		margin: 2% 0 4%;
		border-radius: 10px;
		box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
		text-align: center;
		width: 100%;
		height: 15%;
	}

	.stat-number {
		font-size: 1.625rem;
		font-weight: bold;
		color: #2874a6;
	}

	.stat-label {
		color: #666666;
		font-size: 1.625rem;
	}

	.patient-dialog.m-dialog {
		border-radius: 29px;
	}

	.device-status-dialog.m-dialog--active {
		background-color: #c2d9f3;
	}

	.device-main-body {
		font-size: 1.625rem;
		height: 100%;
	}

</style>
<MRow Class="device-main-body" Style="margin: 0;padding: 0">
	<MCol Cols="10" Style="height: 100%;padding: 0;">
		<HXDeviceLayout>
			<ChildContent Context="data">
				<MCard OnClick="() => OpenDialogAsync(data.Device.Id)" Style="height: 100%;" Class="rounded-lg">
					<DeviceStatusCard DeviceId="@data.Device.Id" />
				</MCard>
			</ChildContent>
		</HXDeviceLayout>
	</MCol>
	<MCol Cols="2" Style="padding: 0 1%;">
		<DeviceSummaryComponent />
	</MCol>
</MRow>

@if (isShowDeviceStatusDialog)
{
	<PModal HeaderClass=" grey text-center lighten-2"
	Class="deviceStatusDialog"
	ContentStyle="background-color: #c2d9f3;"
	MaxWidth="1200"
	@bind-Value="isShowDeviceStatusDialog">
		<TitleContent>
			<div style="width: 90%">
				设备编号:@_SelectedDeviceStatusData.Device?.Name
			</div>
		</TitleContent>
		<ChildContent>
			<DynamicComponent Type="DeviceStatusDialog.GetType()"
			Parameters="@(new Dictionary<string, object> {
				                              { "DeviceStatusData", _SelectedDeviceStatusData },
				                              { "CloseDialogEvent", EventCallback.Factory.Create(this, OnCloserDialogCallback) }
			                              })">
			</DynamicComponent>
		</ChildContent>
	</PModal>
}

@code
{
	public bool isShowAddPatient { get; set; } = false;
	public bool isShowDeviceStatusDialog { get; set; } = false;
	[Inject] StateService StateService { get; set; }
	private readonly ClientWebSocket _clientWebSocket = new();

	private List<DeviceStatusViewModel> DeviceDataList =>
	DeviceStateService.DeviceDataList;

	private HxSystemConfigModel SystemConfig =>
	DeviceStateService.SystemConfig;

	private int IsCheckCount => DeviceDataList?
											.Count(m => m.DeviceStatus == EnumDeviceStatus.检测中)
										?? 0;
	private DeviceStatusViewModel _SelectedDeviceStatusData { get; set; }

	[Inject] InternalHttpClientService InternalHttpClientService { get; set; }
	[Inject] IPopupService PopupService { get; set; }




	private void OnChange()
	{
		InvokeAsync(StateHasChanged);	
	}

	protected override void OnInitialized() {
		base.OnInitialized();
		//DeviceStateService.OnDeviceNumberChange+=OnChange;
	}


	/// <summary>
	///
	/// </summary>
	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		// _ = UpdateDeviceSummaryTask();
	}


	public void OpenDialogAsync(string deviceId)
	{
		_SelectedDeviceStatusData = DeviceDataList.FirstOrDefault(m => m.Device?.Id == deviceId);
		if (_SelectedDeviceStatusData?.DeviceStatus != EnumDeviceStatus.检测中)
		{
			return;
		}
		isShowDeviceStatusDialog = true;
	}

	private async Task OpenDialogAddAsync(MouseEventArgs e)
	{
		_SelectedDeviceStatusData = DeviceDataList.FirstOrDefault(m => m.DeviceStatus == EnumDeviceStatus.空闲);
		if (_SelectedDeviceStatusData == null)
		{
			await PopupService.EnqueueSnackbarAsync("暂未找到空闲设备！", AlertTypes.Warning);
			return;
		}
		isShowAddPatient = true;
	}

	private void OnCloserDialogCallback()
	{
		isShowDeviceStatusDialog = false;
		_SelectedDeviceStatusData = null;
	}

	private CancellationTokenSource cancellationToken = new CancellationTokenSource();

	public void Dispose()
	{
		cancellationToken?.Cancel();
	}

	// private Task UpdateDeviceSummaryTask()
	// {
	// 	return Task.Run(async () =>
	// 	{
	// 		while (true)
	// 		{
	// 			if (cancellationToken.IsCancellationRequested)
	// 			{
	// 				break;
	// 			}
	// 			try
	// 			{
	// 				await InitTotal();
	// 				await InvokeAsync(StateHasChanged);
	// 			}
	// 			catch (Exception e)
	// 			{
	// 				Console.WriteLine("DeviceStatus UpdateDeviceSummaryTask Error" + e.Message);
	// 			}
	// 			await Task.Delay(2000);
	// 		}
	// 	}, cancellationToken.Token);
	// }


}