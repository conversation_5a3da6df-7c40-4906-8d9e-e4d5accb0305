﻿@import 'common.css';
@import 'base.css';
@import 'base-list.css';
@import '../font/iconfont.css';

/** {
    padding: 0;
    margin: 0;
}

body {
    overflow: hidden;
}
*/
/*li {
    list-style: none;
}*/
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

.blazor-error-boundary::after {
    content: "An error has occurred."
}

.layoutContainer .side {
    height: 100%;
    z-index: 9999;
}

.layoutUserContainer {
    display: none;
    padding: 15px 0;
}

.layoutUserContainer li {
    padding-left: 15px;
    margin-bottom: 10px;
    color: #fff;
}

.layoutUserContainer li .link {
    display: block;
    margin-top: 15px;
}

@media screen and (max-width: 760px) {
    .layoutContainer {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 9999 !important;
        height: 100vh;
        transition: all .3s;
        width: 100vw;
        transform: translateX(-100%);
    }

    .layoutContainer.open {
        transform: translateX(0);
    }

    .layoutContainer .transparentContent {
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100%;
        background-color: #000;
        opacity: 0.2;
    }

    .layoutUserContainer {
        display: block;
    }
}

.layoutContainer .ant-layout-sider-children {
    overflow-y: auto;
}




.appCenterPage {
    width: 100%;
    min-height: 100%;
    background-color: #2e3c77;
}

.appCenterContainer {
    max-width: 1000px;
    margin: 0 auto;
    padding-top: 80px;
}

.appCenterContainer .title {
    text-align: center;
    font-size: 26px;
    color: #fff;
    margin-bottom: 30px;
}

.appCenterContainer .moduleList {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.appCenterContainer .moduleList li {
    margin: 0 20px 20px 0;
}

.appCenterContainer .moduleList li a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #0b65d9;
    width: 160px;
    height: 140px;
    color: #fff;
    border-radius: 6px;
    font-size: 17px;
}

.appCenterContainer .moduleList li .iconfont {
    font-size: 32px;
    margin-bottom: 15px;
}

@media screen and (max-width: 760px) {
    .appCenterContainer .moduleList{
        padding: 15px;
    }
    .appCenterContainer .moduleList li a{
        width: calc((100vw - 30px - 60px) / 4);
    }
    .appCenterContainer .moduleList li:nth-child(4n) {
        margin-right: 0;
    }
}

@media screen and (max-width: 600px) {
    .appCenterContainer .moduleList{
        padding: 15px;
    }
    .appCenterContainer .moduleList li a{
        width: calc((100vw - 30px - 40px) / 3);
    }
    .appCenterContainer .moduleList li:nth-child(4n) {
        margin-right: 20px;
    }
    .appCenterContainer .moduleList li:nth-child(3n) {
        margin-right: 0;
    }
}

@media screen and (max-width: 480px) {
    .appCenterContainer .moduleList{
        padding: 15px;
    }
    .appCenterContainer .moduleList li a{
        width: calc((100vw - 30px - 20px - 8px) / 2);
    }
    .appCenterContainer .moduleList li:nth-child(4n) {
        margin-right: 20px;
    }
    .appCenterContainer .moduleList li:nth-child(3n) {
        margin-right: 20px;
    }
    .appCenterContainer .moduleList li:nth-child(2n) {
        margin-right: 0;
    }
}