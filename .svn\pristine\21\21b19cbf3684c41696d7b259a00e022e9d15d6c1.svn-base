@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Services
@inject IJSRuntime JS
<style>
    .hrv-report-fields div:last-child .hrv-report-label, .hrv-report-fields div:last-child .hrv-report-value {
        border-right: thin solid rgba(0, 0, 0, .12);
    }

    .hrv-report-value {
        background-color: white;
        text-align: center;
        border: thin solid rgba(0, 0, 0, .12);
        border-right: none;
        color: black;
    }

    .hrv-report-label {

        background-color: #e2edfa;
        text-align: center;
        border: thin solid rgba(0, 0, 0, .12);
        border-bottom: none;
        border-right: none;
    }

    .hrv-report-label, .hrv-report-value {
        height: 2em;
        line-height: 2em;
    }

    .hrv-report-fields {
        margin-top: 0.25em;
        display: flex;
        align-items: center;
        padding-left: 0.5em;
        padding-right: 0.5em;
    }

    .hrv-report-container {
        width: 210mm;
        overflow: auto;
        margin: 0 auto 0;
        transform-origin: top;
    }


    table {
        width: 80mm;
        border-collapse: collapse;
        text-align: center;
    }

    .data-analysis-table {
    }

    .data-analysis-table tr {
        height: 30px;
    }

    .data-analysis-table th,
    .data-analysis-table td {
        border: 1px solid #ddd;
        padding: 4px;
    }

    .data-analysis-table th {
        background-color: #e1ebf6; /* 蓝色背景 */
        color: #333;
    }

    .data-analysis-table th[colspan="4"] {
        background-color: #d9eaf9;
        font-size: 18px;
        font-weight: bold;
    }

    .data-analysis-table tr:nth-child(even) {
        background-color: #f9f9f9; /* 条纹背景 */
    }


    .note-content p {
        margin-bottom: 10px; /* 为段落添加间距 */
    }

    .note-icon img {
        width: 50px; /* 根据需要调整图标大小 */
        height: 50px;
    }

    .hrv-report-container {
        font-size: 14px;
        height: 100%;
    }

    .emotion-section {
        padding: 15px;
    }

    .emotion-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
    }


    .scale-labels {
        display: flex;
        justify-content: flex-start;
        width: 100%;
        margin-top: 5px;
    }


</style>
<div id="page2el" class="hrv-report-container" style="margin-top: 20px">
    <div style="background-color: #ffffff ; margin:0 auto; font-size: 14px;" id="report-detail">
        <div
            style="background-color: #3380d6;color: #ffffff;width: 100%;text-align: center; display: flex;flex-direction: column;justify-content: center;">
            <h1 style="text-align: center;font-size:2.625em;color: white; display: inline-flex;justify-content: center;align-items: center;">
                情绪指数报告
            </h1>
            <div style="margin-top:0.125em;text-align: center;font-size:1em;">
                编号：@patientRecordModel.RecordCode
            </div>
        </div>
        <div style="padding: 0 1mm ">
            <div class="hrv-report-fields" style="">
                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        姓名
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Name
                    </div>
                </div>
                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        性别
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Sex
                    </div>
                </div>

                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        年龄
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Age
                    </div>
                </div>

                @* <div style="flex-grow: 1;"> *@
                @*     <div class="hrv-report-label"> *@
                @*         门诊/住院号 *@
                @*     </div> *@
                @*     <div class="hrv-report-value"> *@
                @*         @patientModel.OutpatientNumberString *@
                @*     </div> *@
                @* </div> *@
                @* *@
                @* *@
                @* <div style="flex-grow: 1;"> *@
                @*     <div class="hrv-report-label"> *@
                @*         病区 *@
                @*     </div> *@
                @*     <div class="hrv-report-value"> *@
                @*         @patientModel.Source *@
                @*     </div> *@
                @* </div> *@

                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        身高(cm)
                    </div>
                    <div class="hrv-report-value">
                        @(patientModel.Height)
                    </div>
                </div>
                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        体重(kg)
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.Weight
                    </div>
                </div>


                <div style="flex-grow: 1;">
                    <div class="hrv-report-label">
                        BMI
                    </div>
                    <div class="hrv-report-value">
                        @patientModel.BMI?.ToString("F2")
                    </div>
                </div>


            </div>
            @if (Statistics.StatisticsDictionary is { Count: > 0 })
            {
                <div class="hrv-report-fields">
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            平均心率(bpm)
                        </div>
                        <div class="hrv-report-value">
                            @Statistics.StatisticsDictionary["hr_mean"]
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            最低心率(bpm)
                        </div>
                        <div class="hrv-report-value">
                            @(Statistics?.HRList?.Where(m => m > 0)?.Min() ?? 0)
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            最高心率(bpm)
                        </div>
                        <div class="hrv-report-value">
                            @(Statistics?.HRList?.Where(m => m > 0)?.Max() ?? 0)
                        </div>
                    </div>

                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            @("心率占比(<60)")
                        </div>
                        <div class="hrv-report-value">
                            @(Statistics?.StatisticsDictionary["hr_lt_60_percentage"])%
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            检测开始时间
                        </div>
                        <div class="hrv-report-value">
                            @patientRecordModel.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss")
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            检测结束时间
                        </div>
                        <div class="hrv-report-value">
                            @patientRecordModel.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss")
                        </div>
                    </div>
                    <div style="flex-grow: 1;">
                        <div class="hrv-report-label">
                            检测时长(min)
                        </div>
                        <div class="hrv-report-value">
                            @patientRecordModel.CheckTime
                        </div>
                    </div>
                </div>
            }

            <div style="display: flex;flex-direction: column;justify-content: center;align-content: flex-start;"
                 class="data-analysis-table">
                <div class="emotion-section">

                    <div class="emotion-title">

                        <div class="scale-labels">
                            <div style="align-content: center;">抑郁风险：
                            </div>
                            @foreach (var item in EmotionalLevelColor)
                            {
                                <MChip Ripple="false" Class="ma-2" Color="@item.Value"
                                       Label>
                                    @item.Key
                                </MChip>
                            }

                        </div>
                    </div>
                    <div>
                        <div>
                            抑郁判定：
                            <MChip Ripple="EmotionData.DepressLevelPrediction" Class="ma-2"
                                   Color="@EmotionalLevelColor[GetDepressionRiskLevel()]"
                                   Label>
                                @GetDepressionRiskLevel()
                            </MChip>
                        </div>
                        <div>
                            <MSlider @bind-Value="EmotionData.DepressConfidence"
                                     Height="25"
                                     Readonly
                                     Color="@EmotionalLevelColor[GetDepressionRiskLevel()]"
                                     ThumbLabel="@("always")"></MSlider>
                        </div>
                    </div>

                    <div style="text-align: right; margin-top: 5px;">
                        <span>评分：@(EmotionData.DepressConfidence.ToString("F2"))分</span>
                    </div>
                </div>
                <div style="display: flex;justify-content: center;align-content: flex-start;">
                    <div
                        style="display: flex;flex-direction: column;justify-content: center;align-content: flex-start;">
                        <div class="emotion-section">

                            <div class="emotion-title">

                                <div class="scale-labels">
                                    <div style="align-content: center;">情绪效价：
                                    </div>
                                    @foreach (var item in EmotionalValenceColor)
                                    {
                                        <MChip Ripple="false" Class="ma-2" Color="@item.Value"
                                               Label>
                                            @item.Key
                                        </MChip>
                                    }
                                </div>
                            </div>

                            <div>
                                情绪效价判定：
                                <MChip Ripple="EmotionData.DepressLevelPrediction" Class="ma-2"
                                       Color="@EmotionalValenceColor[GetEmotionValueLevel(EmotionData.ValenceScoreType)]"
                                       Label>
                                    @GetEmotionValueLevel(EmotionData.ValenceScoreType)
                                </MChip>
                            </div>

                            <div style="margin-top: 20mm">
                                <MSlider @bind-Value="EmotionData.ValenceScore"
                                         LoaderHeight="25"
                                         Height="25"
                                         Readonly
                                         MinHeight="25"
                                         Color="@EmotionalValenceColor[GetEmotionValueLevel(EmotionData.ValenceScoreType)]"
                                         ThumbLabel="@("always")"></MSlider>
                            </div>

                            <div style="text-align: right; margin-top: 5px;">
                                <span>评分：@(EmotionData.ValenceScore.ToString("F2"))分</span>
                            </div>
                        </div>
                        <div class="emotion-section">

                            <div class="emotion-title">
                                <div class="scale-labels">
                                    <div style="align-content: center;">情绪唤醒度：
                                    </div>
                                    @foreach (var item in EmotionalArousalLevel)
                                    {
                                        <MChip Ripple="false" Class="ma-2" Color="@item.Value"
                                               Label>
                                            @item.Key
                                        </MChip>
                                    }
                                </div>
                            </div>
                            <div>
                                情绪唤醒度判定：
                                <MChip Ripple="EmotionData.DepressLevelPrediction" Class="ma-2"
                                       Color="@EmotionalArousalLevel[EmotionData.ArousalScoreType]"
                                       Label>
                                    @EmotionData.ArousalScoreType
                                </MChip>
                            </div>
                            <div style="margin-top: 20mm">
                                <MSlider @bind-Value="EmotionData.ArousalScore"
                                         LoaderHeight="25"
                                         Height="25"
                                         Readonly
                                         MinHeight="25"
                                         Color="@EmotionalArousalLevel[EmotionData.ArousalScoreType]"
                                         ThumbLabel="@("always")"></MSlider>
                            </div>
                            <div style="text-align: right; margin-top: 5px;">
                                <span>评分：@(EmotionData.ArousalScore.ToString("F2"))分</span>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex;justify-content: center;flex-direction: column;">
                        <img style="height: 60mm" src="images/emotion.jpg"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public string Id { get; set; }
    [Parameter] public bool IsHideActionButton { get; set; }
    private PatientModel patientModel = new PatientModel();

    [CascadingParameter(Name = "PopupService")]
    IPopupService PopupService { get; set; }

    private ReportDataStatistics Statistics = new ReportDataStatistics();
    private PatientRecordModel patientRecordModel = new PatientRecordModel();
    private UserInfo userModel = new UserInfo();

    private EmotionDataModel EmotionData => patientRecordModel.Emotion ??= new EmotionDataModel()
    {
        ArousalScoreType=1,
        ValenceScoreType = 1
    };

// 情绪值
    private Dictionary<string, string> EmotionalValenceColor =
        new()
        {
            { "非常消极", "#0070c0" },
            { "消极", "#1a8ae5" },
            { "积极", "#74c0fc" },
            { "中性", "#00b0f0" },
            { "非常积极", "#a5d8ff" },
        };

// 情绪唤醒度
    private Dictionary<int, string> EmotionalArousalLevel =
        new()
        {
            { 1, "#c8e5b3" },
            { 2, "#acd78e" },
            { 3, "#92d050" },
            { 4, "#00b050" },
            { 5, "#3b5f21" },
        };

    private Dictionary<string, string> EmotionalLevelColor =
        new()
        {
            { "无抑郁", "#f8cdac" },
            { "轻度抑郁", "#f2ba02" },
            { "中重度抑郁", "#ee822f" },
        };

    private object _option { get; set; }
    [Inject] StateService StateService { get; set; }
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await InitRecordDataAsync();
        await base.OnInitializedAsync();
    }

    private async Task InitRecordDataAsync()
    {
        var res = await StateService
            .GetAsJsonAsync<DataModel<PatientRecordModel>>(
                "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string>()
                {
                    { "id", Id }
                });
        patientRecordModel = res.Data.Data;
        userModel = res.Data.User;
        patientModel = patientRecordModel.Patient;
        Statistics = patientRecordModel.Statistics ?? new ReportDataStatistics();
    }


    private string GetDepressionRiskLevel()
    {
        if (EmotionData.EmotionValueLevel == 0) return "无抑郁";
        if (EmotionData.EmotionValueLevel == 1) return "轻度抑郁";
        return "中重度抑郁";
    }

    private string GetEmotionValueLevel(int score)
    {
        if (score == 1) return "非常消极";
        if (score == 2) return "消极";
        if (score == 3) return "中性";
        if (score == 4) return "积极";
        return "非常积极";
    }

 


}