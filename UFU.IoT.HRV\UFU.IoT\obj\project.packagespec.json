﻿"restore":{"projectUniqueName":"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj","projectName":"UFU.IoT","projectPath":"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj","outputPath":"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\","projectStyle":"PackageReference","crossTargeting":true,"fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0","net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj":{"projectPath":"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj"}}},"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj":{"projectPath":"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net6.0":{"targetAlias":"net6.0","dependencies":{"NPOI":{"target":"Package","version":"[2.6.0, )"},"RulesEngine":{"target":"Package","version":"[5.0.3, )"},"UFU.CoreFX":{"target":"Package","version":"[5.0.3, )"},"UFU.IoT.UI":{"target":"Package","version":"[2.3.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"},"net9.0":{"targetAlias":"net9.0","dependencies":{"NPOI":{"target":"Package","version":"[2.6.0, )"},"RulesEngine":{"target":"Package","version":"[5.0.3, )"},"UFU.CoreFX":{"target":"Package","version":"[5.0.3, )"},"UFU.IoT.UI":{"target":"Package","version":"[2.3.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}