{
    "configurations": [

        {
            "name": "HX.HRV.Web.SCI",
            "type": "dotnet-launch-settings",
            "workingDir": "D:\\Project\\HuiXin\\HX.HRV\\HX.HRV.SCI.Web",
            "allowParallelRun": true,
            "solutionPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.HRV.sln",
            "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.HRV.SCI.Web\\HX.HRV.SCI.Web.csproj",
            "targetFramework": null,
            "profileName": "HX.HRV.SCI.Web",
        },
        {
            "name": "HX.HRV.Web",
            "type": "dotnet-launch-settings",
            "workingDir": "D:\\Project\\HuiXin\\HX.HRV\\HX.HRV.Web",
            "allowParallelRun": true,
            "solutionPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.HRV.sln",
            "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\HX.HRV.Web\\HX.HRV.Web.csproj",
            "targetFramework": null,
            "profileName": "HX.HRV.Web"
        }
    ]
}