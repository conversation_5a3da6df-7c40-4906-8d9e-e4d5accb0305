using System;
using System.Collections.Generic;
using System.IO;
using System.Management;
using System.Threading;
using HX.Experiment.Shared.Model;

namespace HX.Experiment.Window.Services;

public class UDiskService : IDisposable
{
    private const string HuiXinKeyword = "HuiXin";
    private readonly object _lock = new();
    private ManagementEventWatcher? InsertWatcher;

    public event Action<List<DiskInfo>> OnUDiskAfterInsertedEvent;
    private ManagementEventWatcher? _removeWatcher;
    private List<DiskInfo> _diskList = new();
    private bool _disposed;

    public IReadOnlyList<DiskInfo> DiskList
    {
        get { lock (_lock) { return _diskList.AsReadOnly(); } }
    }

    public UDiskService()
    {
        UpdateDeviceList();
        StartMonitoring();
    }

    public void StartMonitoring()
    {
        InsertWatcher = new ManagementEventWatcher(
            new WqlEventQuery("SELECT * FROM __InstanceCreationEvent WITHIN 1 WHERE TargetInstance ISA 'Win32_DiskDrive'"));
        InsertWatcher.EventArrived += OnUDiskInserted;
        InsertWatcher.Start();

        _removeWatcher = new ManagementEventWatcher(
            new WqlEventQuery("SELECT * FROM __InstanceDeletionEvent WITHIN 1 WHERE TargetInstance ISA 'Win32_DiskDrive'"));
        _removeWatcher.EventArrived += OnUDiskRemoved;
        _removeWatcher.Start();
    }

    public void StopMonitoring()
    {
        InsertWatcher?.Stop();
        _removeWatcher?.Stop();
    }

    private void OnUDiskInserted(object sender, EventArrivedEventArgs e) => UpdateDeviceList();
    private void OnUDiskRemoved(object sender, EventArrivedEventArgs e) => UpdateDeviceList();

    private void UpdateDeviceList()
    {
        var diskList = new List<DiskInfo>();
        try
        {
            var diskClass = new ManagementClass("Win32_DiskDrive");
            foreach (ManagementObject disk in diskClass.GetInstances())
            {
                var diskCaption = disk["Caption"]?.ToString() ?? "Unknown";
                if (!diskCaption.Contains(HuiXinKeyword, StringComparison.OrdinalIgnoreCase)) continue;

                var partitionSearcher = new ManagementObjectSearcher(
                    $"ASSOCIATORS OF {{Win32_DiskDrive.DeviceID='{disk["DeviceID"]}'}} WHERE AssocClass = Win32_DiskDriveToDiskPartition");
                foreach (ManagementObject partition in partitionSearcher.Get())
                {
                    var logicalSearcher = new ManagementObjectSearcher(
                        $"ASSOCIATORS OF {{Win32_DiskPartition.DeviceID='{partition["DeviceID"]}'}} WHERE AssocClass = Win32_LogicalDiskToPartition");
                    foreach (ManagementObject logicalDisk in logicalSearcher.Get())
                    {
                        var logicalName = logicalDisk["Name"]?.ToString() ?? "Unknown";
                        int fileCount = 0;
                        try
                        {
                            var dataPath = Path.Combine(logicalName, "data");
                            if (Directory.Exists(dataPath))
                                fileCount = Directory.GetFiles(dataPath, "*.bin").Length;
                        }
                        catch { /* ignore file errors */ }

                        diskList.Add(new DiskInfo
                        {
                            LogicalName = logicalName,
                            Caption = diskCaption,
                            ShowName = GetShowName(diskCaption),
                            FileCount = fileCount,
                            IsParsing = false
                        });
                    }
                }
            }
            OnUDiskAfterInsertedEvent?.Invoke(diskList);
        }
        catch (Exception ex)
        {
            Console.WriteLine("更新设备列表时出错：" + ex.Message);
        }
        lock (_lock)
        {
            _diskList = diskList;
        }
    }

    private static string GetShowName(string caption)
    {
        var parts = caption.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        return parts.Length > 1 ? parts[1] : caption;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;
        if (disposing)
        {
            StopMonitoring();
            InsertWatcher?.Dispose();
            _removeWatcher?.Dispose();
        }
        _disposed = true;
    }
}