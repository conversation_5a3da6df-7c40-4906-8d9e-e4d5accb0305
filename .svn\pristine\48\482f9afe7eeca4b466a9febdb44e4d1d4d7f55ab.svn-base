﻿<Window x:Class="HX.HRV.Keygen.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:vm="clr-namespace:HX.HRV.Keygen.ViewModel"
        xmlns:converters="clr-namespace:HX.HRV.Keygen.Converters"
        xmlns:models="clr-namespace:HX.Encrypt.Models;assembly=HX.Encrypt"
        mc:Ignorable="d"
        Title="汇心注册机" Height="700" Width="650" MinWidth="650" MinHeight="600">
    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVis" />
        <converters:EnumToDisplayNameConverter x:Key="EnumDisplayConverter" />
    </Window.Resources>
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition />
        </Grid.RowDefinitions>
        <GroupBox Header="许可证信息" Grid.Row="0" Margin="5" Padding="10">
            <Grid >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" MinWidth="130" />
                    <ColumnDefinition Width="*"  />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions >
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="10" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="10" />

                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="10" />

                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="10" />

                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="10" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="10" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <Label Content="注册码:" 
                       Target="{Binding ElementName=UniqueIdentifierTextBox}" VerticalAlignment="Center" />
                <TextBox x:Name="UniqueIdentifierTextBox" Grid.Row="0" Grid.Column="1" 
                         Height="120"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility=" Auto"
                         Text="{Binding UniqueIdentifier, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                <Label Content="软件产品:" Grid.Row="2" 
                       Target="{Binding ElementName=SoftwareProductComboBox}" VerticalAlignment="Center" />
                <ComboBox Grid.Row="2" Grid.Column="1" ItemsSource="{Binding SoftwareProductOptions}"
                          SelectedItem="{Binding SelectedSoftwareProduct, Mode=TwoWay}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Converter={StaticResource EnumDisplayConverter}}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
                
                
                <Label Content="产品版本:" Grid.Row="4" 
                       Target="{Binding ElementName=SoftwareVersionComboBox}" VerticalAlignment="Center" />
                <ComboBox Grid.Row="4" Grid.Column="1" ItemsSource="{Binding SoftwareVersionOptions}"
                          SelectedItem="{Binding SelectedSoftwareVersion, Mode=TwoWay}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Converter={StaticResource EnumDisplayConverter}}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
                <Label Content="许可证类型:" Grid.Row="6" Grid.Column="0"
                       Target="{Binding ElementName=LicenseVersionComboBox}" VerticalAlignment="Center" />
                <ComboBox Grid.Row="6" Grid.Column="1" ItemsSource="{Binding LicenseVersionOptions}"
                          SelectedItem="{Binding SelectedLicenseVersion, Mode=TwoWay}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Converter={StaticResource EnumDisplayConverter}}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <Label Content="试用天数:" Grid.Row="8" Grid.Column="0" 
                       Target="{Binding ElementName=TrialDaysTextBox}"
                       VerticalAlignment="Center" />
                <TextBox x:Name="TrialDaysTextBox" Grid.Row="8" Grid.Column="1" 
                         Text="{Binding TrialDays, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, TargetNullValue=''}">
                    <TextBox.Style>
                        <Style TargetType="TextBox" BasedOn="{StaticResource {x:Type TextBox}}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding SelectedLicenseVersion}"
                                             Value="{x:Static models:LicenseMode.Trial}">
                                    <Setter Property="IsEnabled" Value="True" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
                <Label Content="激活码:" Grid.Row="10" Grid.Column="0" 
                       VerticalAlignment="Center" />
                <TextBox Grid.Row="10" Grid.Column="1" 
                         Height="200"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility=" Auto"
                         Text="{Binding GeneratedLicenseKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                </TextBox>
                <StackPanel Grid.Row="12" Grid.Column="1"  Orientation="Horizontal" VerticalAlignment="Center" >
                    <Button Content="生成激活码密钥"  Command="{Binding GenerateLicenseKeyCommand}" FontWeight="Bold" />
                    <Button Content="复制激活码"  Command="{Binding CopyToClipboardCommand}"  FontWeight="Bold" />
                </StackPanel>
            </Grid>
        </GroupBox>

        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="0,1,0,0" Margin="5,10,5,5">
            <TextBlock Text="提示: 生成的激活码将与唯一标识符、软件产品、版本和有效期绑定。"
                       TextWrapping="Wrap" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="DimGray"
                       Margin="10" />
        </Border>
    </Grid>
</Window>