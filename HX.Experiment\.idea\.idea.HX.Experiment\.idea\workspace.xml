<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile kind="Uwp">HX.Experiment.MAUI/HX.Experiment.MAUI.csproj</projectFile>
    <projectFile>HX.Experiment.Web.Client/HX.Experiment.Web.Client.csproj</projectFile>
    <projectFile kind="Docker">HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj</projectFile>
    <projectFile profileName="BlazorApp.Server">HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj</projectFile>
    <projectFile pubXmlPath="HX.Experiment.Web.Service/Properties/PublishProfiles/FolderProfile.pubxml">HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj</projectFile>
    <projectFile pubXmlPath="HX.Experiment.Web.Service/Properties/PublishProfiles/HX.HRV.Web.pubxml">HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj</projectFile>
    <projectFile>HX.Experiment.Window/HX.Experiment.Window.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e014dcc0-15c1-4665-9883-55669982c048" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/.dockerignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/NavMenu.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Models/AlgorithmShowModel.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/AlgorithmOverview.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/AlgorithmOverviewDetail.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Routes.razor" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Controllers/ProductRegisterController.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/img" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/img/JSSR" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/img/sandip.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/css" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/css/materialdesignicons.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/css/materialdesignicons.min.css.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.eot" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/materialdesign/v7.1.96/fonts/materialdesignicons-webfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/.vs" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/.vs/HX.PythonService" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/.vs/HX.PythonService/FileContentIndex" afterDir="true" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/dist" afterDir="true" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway.sln" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway.sln" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/HX.ALGGateway.Client.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/HX.ALGGateway.Client.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor.css" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Layout/MainLayout.razor.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/ALGHome.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Pages/ALGHome.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/_Imports.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.Client/_Imports.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/ALGWebSocketStaticManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/ALGWebSocketStaticManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/App.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/App.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Layout" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Pages/Home.razor" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Pages/Weather.razor" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/Routes.razor" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/_Imports.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Components/_Imports.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Controllers/AlgorithmsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Controllers/AlgorithmsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/HX.ALGGateway.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Properties/launchSettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/Properties/launchSettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/app.css" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.ALGGateway/HX.ALGGateway/HX.ALGGateway/wwwroot/app.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Database/HX.Database.Shared/Pages/DocumentDetail.razor" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Database/HX.Database.Shared/Pages/DocumentDetail.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Database/HX.Database.Shared/wwwroot/js/echart/echartsInterop.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Database/HX.Database.Shared/wwwroot/js/echart/echartsInterop.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Database/HX.Database.Web/HX.Database.Web.csproj.user" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Database/HX.Database.Web/HX.Database.Web.csproj.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.FileExport/HX.FileExport/FileExport.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.FileExport/HX.FileExport/FileExport.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Model/ClassInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Model/ClassInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Model/Student.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Model/Student.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Model/TaskTypeEnum.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Model/TaskTypeEnum.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/ClassManager.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/ClassManager.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/ClassManager.razor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/ClassManager.razor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/Experiment.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/Experiment.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/HXDevice/Detail.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/HXDevice/Detail.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/HXDevice/Detail.razor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/HXDevice/Detail.razor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/Login.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Pages/Login.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Services/IClassService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Services/IClassService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Services/IDeviceService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Services/IDeviceService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/Services/IImportStudentService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/Services/IImportStudentService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Shared/_Imports.razor" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Shared/_Imports.razor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Web.Service/Module.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Web.Service/Module.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Web.Service/Services/DeviceWebSocketConnect.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Web.Service/Services/DeviceWebSocketConnect.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Window/HX.Experiment.Window.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Window/HX.Experiment.Window.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Window/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Window/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Window/Services/DeviceService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Window/Services/DeviceService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HX.Experiment.Window/Services/ImportStudentService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HX.Experiment.Window/Services/ImportStudentService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../HX.HRV.SCI.Web/wwwroot/css" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/../HX.HRV.SCI.Web/wwwroot/images" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/../HX.HRV.SCI.Web/wwwroot/scripts" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/../HX.HRV.Web/wwwroot" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/../HX.HRV.sln" beforeDir="false" afterPath="$PROJECT_DIR$/../HX.HRV.sln" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT.Shared/UFU.IoT.Shared.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/BinaryDeviceWebSocketConnect.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/BinaryDeviceWebSocketConnect.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/BinaryIoTService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/Services/BinaryIoTService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/UFU.IoT.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/appsettings.Development.json" beforeDir="false" afterPath="$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT/appsettings.Development.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.Designer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.Designer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/HXScreenshotMainForm.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml.user" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.Screenshot/HX.Screenshot/Properties/PublishProfiles/FolderProfile.pubxml.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.WebsocketTest/HX.WebsocketTest/HX.WebsocketTest/Controllers/HomeController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.WebsocketTest/HX.WebsocketTest/HX.WebsocketTest/Controllers/HomeController.cs" afterDir="false" />
    </list>
    <list id="2d7c173d-5c60-4757-9f17-2bf63a728713" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/.dockerignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/.vs/ProjectSettings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/dist/main.exe" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/main.exe" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/main_test.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5_1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5_2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_6.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_tes.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_gsr_algorithm/ppg_gsr_algorithm_2.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/requirements.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/block.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/block.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/dataloader.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/dataloader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/dataset.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/dataset.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/emotion_calculator.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/emotion_calculator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/model_trans.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/model_trans.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/predict.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/predict.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/roc.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/roc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/sublayer.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/sublayer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/try.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/emotion/try___.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_algorithm/ppg_hrv_algorithm_5.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_gsr_algorithm/ppg_gsr_algorithm.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../HX.PythonService/src/algorithms/ppg_gsr_algorithm/ppg_gsr_algorithm.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/43/80acb6ab/MemoryCache.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/4e/31a3f304/CoreModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/d0/4efd455f/IWebSocket.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/363a9cb0787b4b5f8334385bba011c905f0c00/fd/2ac1885e/CoreApp.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6ba4aaa2780646e5ada7d20877c43f4f1b6930/4f/163dff76/JsonNode.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/621526284d3e4868b98948aff942206525763e57e194a27623b66c384466/ServiceProviderServiceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bde730fa981b6675e652989bfc8a726732d174f3455ad6b97bea38cd92b8b935/ModalBase.razor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT.Shared/Models/DeviceModel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT.Shared/Models/DeviceTypeModel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../UFU.IoT.HRV/UFU.IoT.Shared/Models/Msg.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/BlazorApp.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/DeviceInfoModel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Models/LoginRequestModel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../NetCore/UFU.CoreFX.Blazor/UFU.CoreFX.Shared/Services/StateService.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30WyJGc4uniz1hYia5LU27rA1XO" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET 启动设置配置文件.HX.Experiment.Web.Service: BlazorApp.Server.executor": "Run",
    ".NET 项目.HX.Experiment.Window.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "XThreadsFramesViewSplitterKey": "1.2185864",
    "com.codeium.enabled": "true",
    "do_not_show_Android_plugin_suggestion": "true",
    "ignore.virus.scanning.warn.message": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "http.proxy",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="RunManager" selected=".NET 项目.HX.Experiment.Window">
    <configuration name="HX.Experiment.Web.Service: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment.Web.Service/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-8968788675816963666" uuid_low="-6347839375241406708" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.Web.Service: HX.HRV.Web" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="HX.HRV.Web.pubxml" pubxml_path="$PROJECT_DIR$/HX.Experiment.Web.Service/Properties/PublishProfiles/HX.HRV.Web.pubxml" uuid_high="-8968788675816963666" uuid_low="-6347839375241406708" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.Web.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Web.Client/HX.Experiment.Web.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Window" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.Window/HX.Experiment.Window.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.Web.Service: BlazorApp.Server" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HX.Experiment.Web.Service/HX.Experiment.Web.Service.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="BlazorApp.Server" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="37cae5b9-e8b2-4949-9172-aafa37fbc09c" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HX.Experiment.MAUI" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HX.Experiment.MAUI/HX.Experiment.MAUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="HX.Experiment.Web.Service/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="hx.experiment.web.service" />
          <option name="contextFolderPath" value="D:\Project\HuiXin\HX.HRV\HX.Experiment" />
          <option name="sourceFilePath" value="HX.Experiment.Web.Service/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue=".NET 启动设置配置文件.HX.Experiment.Web.Service: BlazorApp.Server" />
      <item itemvalue=".NET 项目.HX.Experiment.Web.Client" />
      <item itemvalue=".NET 项目.HX.Experiment.Window" />
      <item itemvalue="Docker.HX.Experiment.Web.Service/Dockerfile" />
      <item itemvalue="UWP.HX.Experiment.MAUI" />
      <item itemvalue="发布到 IIS.HX.Experiment.Web.Service: FolderProfile" />
      <item itemvalue="发布到 IIS.HX.Experiment.Web.Service: HX.HRV.Web" />
    </list>
  </component>
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.ALGGateway" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Base" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Database" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Encrypt" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.FileExport" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.HRV" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin\HX.HRV\HX.Experiment" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.HRV\HX.Experiment" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.PythonService" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Screenshot" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.WebsocketTest" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.ALGGateway" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Base" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Database" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Encrypt" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.FileExport" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.HRV" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.PythonService" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.Screenshot" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Project\HuiXin" />
          <option name="myCopyRoot" value="D:\Project\HuiXin\HX.WebsocketTest" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e014dcc0-15c1-4665-9883-55669982c048" name="更改" comment="" />
      <created>1753757550161</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753757550161</updated>
      <workItem from="1753757554267" duration="1578000" />
      <workItem from="1753768778790" duration="320000" />
      <workItem from="1753775096749" duration="495000" />
      <workItem from="1753859673211" duration="1000" />
      <workItem from="1753859748036" duration="8099000" />
      <workItem from="1753927446676" duration="2943000" />
      <workItem from="1753942961916" duration="155000" />
      <workItem from="1754288181133" duration="4342000" />
      <workItem from="1754293441193" duration="15099000" />
      <workItem from="1754385273177" duration="81000" />
      <workItem from="1754385371166" duration="290000" />
      <workItem from="1754385735714" duration="242000" />
      <workItem from="1754386030944" duration="103000" />
      <workItem from="1754386155861" duration="101000" />
      <workItem from="1754386272070" duration="5762000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/HX.Experiment.Web.Service/Services/DeviceWebSocketConnect.cs</url>
          <line>83</line>
          <properties documentPath="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Web.Service\Services\DeviceWebSocketConnect.cs" containingFunctionPresentation="lambda 表达式内 方法 'OnReciveBinary'">
            <startOffsets>
              <option value="3148" />
            </startOffsets>
            <endOffsets>
              <option value="3181" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>