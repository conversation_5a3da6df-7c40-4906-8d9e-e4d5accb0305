using System.Collections.Concurrent;
using System.Text;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.ViewModel;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;
using UFU.IoT.Services;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
///
/// </summary>
public static class DeviceStatusDataService
{
    /// <summary>
    ///
    /// </summary>
    private const string DeviceTypeId = "2407160100000001";

    private static List<DeviceStatusData> _deviceDataList;

    public static DeviceStatusData GetDeviceStatusData(string sn)
    {
        return _deviceDataList?.FirstOrDefault(m => m.Device.DeviceSN == sn);
    }

    /// <summary>
    /// 设备上线
    /// </summary>
    /// <param name="deviceId"></param>
    public static void SetDeviceOnline(string deviceId)
    {
        if (_deviceDataList == null) return;
        var device = _deviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
        device.Device.IsOnline = true;
    }

    public static void AddDeviceStatusData(string deviceSn)
    {
        var cache = new CacheService(deviceSn);
        var initLock = cache.WaitLock(deviceSn, TimeSpan.FromSeconds(2));
        if (initLock == null)
        {
            return;
        }
        try
        {
            using var db = new DataRepository();
            if (_deviceDataList == null)
            {
                return;
            }
            var oldDevice = _deviceDataList
                .FirstOrDefault(m => m.Device.DeviceSN==deviceSn);
            _deviceDataList.Remove(oldDevice);
            var newDevice = db.Query<DeviceModel>()
                .AsTracking()
                .FirstOrDefault(m => m.Data.DeviceSN == deviceSn);
            _deviceDataList.Add(new DeviceStatusData
            {
                Device = newDevice.Data,
                DataItems = new ConcurrentQueue<JsonNode>(),
            });
        }
        catch (Exception e)
        {
            LogTool.Logger.Error("【AddDeviceStatusData】 出错", e);
        }
        finally
        {
            initLock.Unlock();
        }
    }

    /// <summary>
    /// 设备离线
    /// </summary>
    /// <param name="deviceId"></param>
    public static void SetDeviceOffline(string deviceId)
    {
        if (_deviceDataList != null&&_deviceDataList.Any( m => m.Device.Id == deviceId))
        {
            var device = _deviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
            if (device == null) return;
            device.Device.IsOnline = false;
        }
       
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="serviceProvider"></param>
    static DeviceStatusDataService()
    {
      _=  InitDataAsync();
    }

    private static Dictionary<string, CancellationTokenSource> _deviceTimers =
        new Dictionary<string, CancellationTokenSource>();

    private static void StartDeviceTimer(DeviceStatusData deviceStatusData)
    {
        var cts = new CancellationTokenSource();
        _deviceTimers[deviceStatusData.RecordModel.Id] = cts;
        Task.Run(async () =>
        {
            var cache = new CacheService(deviceStatusData.RecordModel.Id);
            var initLock = cache.WaitLock(deviceStatusData.RecordModel.Id, TimeSpan.FromSeconds(2));
            if (initLock == null)
            {
                return;
            }
            try
            {
                while (!cts.Token.IsCancellationRequested)
                {
                    try
                    {
                        await ProcessDeviceStatus(deviceStatusData);
                        await Task.Delay(500);
                    }
                    catch (TaskCanceledException ex)
                    {
                        LogTool.Logger.Error($"设备 {deviceStatusData.Device.DeviceSN} 定时器取消: " + ex);
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogTool.Logger.Error($"设备 {deviceStatusData.Device.DeviceSN} 定时器异常: " + ex);
                    }
               
                }
                initLock.Unlock();
            }
            catch (TaskCanceledException ex)
            {
                LogTool.Logger.Error($"设备 {deviceStatusData.Device.DeviceSN} 定时器取消: " + ex);
                initLock.Unlock();
            }
        }, cts.Token);
    }

    private static void StopDeviceTimer(string recordId)
    {
        if (_deviceTimers.TryGetValue(recordId, out var cts))
        {
            cts.Cancel();
            _deviceTimers.Remove(recordId);
        }
    }

    /// <summary>
    /// 检测进度
    /// </summary>
    /// <param name="deviceStatusData"></param>
    private static async Task ProcessDeviceStatus(DeviceStatusData deviceStatusData)
    {
        try
        {
            var recordModel = deviceStatusData.RecordModel;
            deviceStatusData.IsSendEnd = false;
            if (recordModel == null) return;
            if (recordModel.EnumPatientCheckStatus == EnumPatientCheckStatus.Readying)
            {
                return;
            }

            if ((recordModel.EnumPatientCheckStatus == EnumPatientCheckStatus.Checking
                 && recordModel.CollectEndTime < DateTime.Now))
            {
                LogTool.Logger.Information($"{deviceStatusData.Device.DeviceSN}检测结束");
                // if (!deviceStatusData.IsSendEnd)
                // {
                //     deviceStatusData.IsSendEnd = true;
                //     var startTime = deviceStatusData.StartTime??deviceStatusData.RecordModel?.CollectStartTime.ToUnixMs();
                //     await SendEndMsgToAlg(deviceStatusData.RecordModel.Id,startTime??0,0,true);
                // }
                var deviceData = new
                {
                    SN = deviceStatusData.Device.DeviceSN,
                    Status = deviceStatusData.DeviceStatus,
                    Progress = deviceStatusData.Progress,
                    CollectTime = deviceStatusData.CollectTime,
                    From_InitTimer = 1,
                };
                await HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(deviceData));
                if (!deviceStatusData.IsSendEnd && deviceStatusData.StartTime.HasValue)
                {
                    deviceStatusData.IsSendEnd = true;
                    await AlgWebSocketClient.SendEndMsgToAlg(
                        recordModel.Id,
                        deviceStatusData.StartTime.Value,
                        deviceStatusData.StartTime.Value,
                        true
                    );
                }
                await SetPatientRecordEnd(deviceStatusData);
                StopDeviceTimer(recordModel.Id);
                return;
            }

            if (recordModel.EnumPatientCheckStatus != EnumPatientCheckStatus.Checking
                || recordModel.CollectEndTime < DateTime.Now) return;

            deviceStatusData.CollectTime = recordModel.CollectEndTime - DateTime.Now;
            deviceStatusData.Progress = 100 - deviceStatusData.CollectTime.TotalSeconds /
                TimeSpan.FromMinutes(recordModel.CheckTime).TotalSeconds * 100;
            var jsonStr = JsonTool.SerializeIgnoreNull(new
            {
                SN = deviceStatusData.Device.DeviceSN,
                Status = deviceStatusData.DeviceStatus,
                Progress = deviceStatusData.Progress,
                CollectTime = deviceStatusData.CollectTime,
                From_InitTimer = 2
            });
            await HXAdminWebSocket.SendMsgToAdmin(jsonStr);
            LogTool.Logger.Information("检测状态\r\n：{0}", jsonStr);
        }
        catch (Exception e)
        {
            LogTool.GetLogger("DeviceStatusDataService", "ProcessDeviceStatus").Error(e, "定时器异常");
        }
    }


    /// <summary>
    ///  获取所有的设备状态
    /// </summary>
    /// <returns></returns>
    public static List<DeviceStatusData> GetDeviceDataList() => _deviceDataList;

    public static DeviceStatusData GetDeviceData(string id) => _deviceDataList?.FirstOrDefault(m => m.Device.Id == id);

    /// <summary>
    /// 初始化数据
    /// </summary>
    private static async Task InitDataAsync()
    {
        await using var db = new DataRepository();
        var devices = await db.Query<DeviceModel>()
            .AsTracking()
            .Where(m => m.Data.TypeId == DeviceTypeId)
            .Select(m => m.Data)
            .ToListAsync();
        _deviceDataList = devices?
            .OrderBy(m => m.Name)
            .Select(m => new DeviceStatusData
            {
                Device = m,
                DataItems = new ConcurrentQueue<JsonNode>(),
            })
            .ToList();

        var deviceIds = _deviceDataList.Select(m => m.Device.Id).ToArray();
        var patientRecordModel = await db.Query<PatientRecordModel>().AsNoTracking()
            .Where(m => deviceIds.Contains(m.Data.DeviceId)
                        && m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Checking)
            .Select(m => m.Data)
            .OrderByDescending(m => m.Id)
            .ToListAsync();
        foreach (var record in patientRecordModel)
        {
            if (_deviceDataList == null) continue;
            var deviceData = _deviceDataList.FirstOrDefault(m => m.Device.Id == record.DeviceId);
            if (deviceData != null)
            {
                deviceData.RecordModel = record;
                StartDeviceTimer(deviceData);
            }
        }
    }

    // public static void OnReceivePPGData(IEnumerable<uint> gdata,IEnumerable<uint> rdata,IEnumerable<uint> idata, string sn)
    // {
    //     var deviceStatus = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
    //     if (deviceStatus == null) return;
    //     deviceStatus.PPGData.AddRange(gdata);
    //     deviceStatus.PPG_RData.AddRange(rdata);
    //     deviceStatus.PPG_IData.AddRange(idata);
    // }


    public static void OnReceivePPGData(DeviceStatusData deviceStatus, IEnumerable<int> data)
    {
        deviceStatus?.PPGData.AddRange(data);
    }

    public static void OnReceiveorgPPGData(DeviceStatusData deviceStatus, IEnumerable<uint> data)
    {
        deviceStatus?.OrgPPGData.AddRange(data);
    }

    public static void OnReceiveIMuData(IEnumerable<short> data, string sn)
    {
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        if (device == null) return;
        device.ImuDatas.AddRange(data);
    }

    public static void OnReceiveSKINTEMPData(IEnumerable<float> data, string sn)
    {
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        if (device == null) return;
        device.SKINTEMPs.AddRange(data);
    }

    public static void OnReceiveEDAData(IEnumerable<float> data, string sn)
    {
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        device?.Edas.AddRange(data);
    }

    public static void OnReceiveSPO2Data(uint data, string sn)
    {
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        if (device == null) return;
        device.SPO2Data.Add(data);
    }

    public static void OnReceiveBatteryData(uint data, string sn)
    {
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        if (device == null) return;
        device.Battery = data;
    }

    public static void OnReceiveBmpData(uint data, string sn)
    {
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        if (device == null) return;
        device.BMPDatas.Add(data);
    }

    /// <summary>
    /// 发送开始
    /// </summary>
    /// <param name="deviceStatus"></param>
    public static async Task SendBeganMsgToDevice(DeviceStatusData deviceStatus)
    {
        await using var db = new DataRepository();
        var systemRateConfig = await db.Query<HxSystemConfigModel>().FirstOrDefaultAsync();
        var config = systemRateConfig.Data.SystemRateConfig;
        var device = deviceStatus.Device;
        var msgJson = new
        {
            MsgId = 11111,
            //时间戳
            Time = DateTime.Now.ToUnixMs(),
            Device = new
            {
                SN = device.DeviceSN,
                Type = device.TypeId,
            },
            CMD = (int)BinaryCMD.Write,
            Data = new
            {
                //PPG
                PPGRate = config.PPGRate,
                //九轴
                IMURate = config.IMURate,
                //SKT采样率 皮肤温度
                SKTRate = config.SKTRate,
                //血氧
                SPO2Rate = config.SPO2Rate,
                //心率
                HRRate = config.HRRate,
                //气压 环境温度 光照
                ENVRate = config.ENVRate,
                //皮肤电阻
                EDARate = config.EDARate,
                //检测模式
                CollectMode = CollectMode.Continuous
            }
        };
        deviceStatus.MsgId = (uint)msgJson.MsgId;
        var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
        HXAdminWebSocket.ReceiveUserMsg(msgStr);
    }


    /// <summary>
    /// 发送结束
    /// </summary>
    /// <param name="deviceStatus"></param>
    /// <param name="config"></param>
    public static void SendEndMsgToDevice(DeviceStatusData deviceStatus)
    {
        var device = deviceStatus.Device;
        var msgJson = new
        {
            MsgId = 22222,
            //时间戳   
            Time = DateTime.Now.ToUnixMs(),
            Device = new
            {
                SN = device.DeviceSN,
                Type = device.TypeId,
            },
            CMD = (int)BinaryCMD.Write,
            Data = new
            {
                //PPG
                PPGRate = 0,
                //九轴
                IMURate = 0,
                //SKT采样率 皮肤温度
                SKTRate = 0,
                //血氧
                SPO2Rate = 0,
                //心率
                HRRate = 0,
                //气压 环境温度 光照
                ENVRate = 0,
                //皮肤电阻
                EDARate = 0,
                //检测模式
                CollectMode = CollectMode.None
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
        HXAdminWebSocket.ReceiveUserMsg(msgStr);
    }

    public static async Task SetPatientRecordBegin(string sn, PatientRecordModel recordModel)
    {
        var cache = new CacheService(recordModel.Id);
        var initLock = cache.WaitLock(recordModel.Id, TimeSpan.FromSeconds(2));
        if (initLock == null)
        {
            return;
        }
        var device = _deviceDataList.FirstOrDefault(m => m.Device.DeviceSN == sn);
        recordModel.EnumPatientCheckStatus = EnumPatientCheckStatus.Checking;
        var res = await BeganCheckAsync(recordModel.Id);
        device.RecordModel = res;
        StartDeviceTimer(device);
        initLock.Unlock();
    }

    public static async Task SetPatientRecordData(string deviceId, string recordId)
    {
        await using var db = new DataRepository();
        var device = _deviceDataList.FirstOrDefault(m => m.Device.Id == deviceId);
        LogTool.Logger.Information($"DeviceId:{deviceId},RecordId:{recordId}");
        if (device == null)
        {
            var newDevice = db.Query<DeviceModel>()
                .AsTracking()
                .FirstOrDefault(m => m.Data.Id == deviceId);
            device = new DeviceStatusData
            {
                Device = newDevice.Data,
                DataItems = new ConcurrentQueue<JsonNode>(),
            };
            _deviceDataList.Add(device);
        }
        var patient = await db.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == recordId);
        device.RecordModel = patient.Data;
    }

    /// <summary>
    ///    开始检测
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static async Task<PatientRecordModel> BeganCheckAsync(string id)
    {
        await using var db = new DataRepository();
        var patient = await db.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        patient.UpdateTime = DateTime.Now;
        patient.Data.CollectStartTime = DateTime.Now;
        patient.Data.CollectEndTime = DateTime.Now.AddMinutes(patient.Data.CheckTime);
        patient.Data.EnumPatientCheckStatus = EnumPatientCheckStatus.Checking;
        patient.Data.DeviceStartCheckTime = DateTime.Now;
        db.Update(patient);
        await db.SaveChangesAsync();
        var returnPatient = await db.Query<PatientRecordModel>().FirstOrDefaultAsync(m => m.Id == id);
        return returnPatient.Data;
    }

    /// <summary>
    ///    停止检测
    /// </summary>
    /// <returns></returns>
    public static async Task<bool> SetPatientRecordEnd(DeviceStatusData deviceStatusData,
        EnumPatientCheckStatus status = EnumPatientCheckStatus.Checked)
    {
        await using var db = new DataRepository();
        var record = await db.Query<PatientRecordModel>()
            .FirstOrDefaultAsync(m => m.Data.Id == deviceStatusData.RecordModel.Id);
        if (record == null) return false;
        record.UpdateTime = DateTime.Now;
        record.Data.EnumPatientCheckStatus = status;
        record.Data.BuildReportStatus = BuildReportStatus.Building;
        db.Update(record);
        await db.SaveChangesAsync();
        deviceStatusData.RecordModel = record.Data;
        // try
        // {
        //     LogTool.Logger.Information("SetPatientRecordEnd发送结束消息到网关");
        //     await SendEndMsgToAlg(deviceStatusData.RecordModel.Id,deviceStatusData.StartTime.Value,0,true);
        // }
        // catch (Exception e)
        // {
        //     LogTool.Logger.Information("发送结束消息到网关失败" + e.Message);
        // }
        StopDeviceTimer(record.Id);
        deviceStatusData?.ClearData();
        deviceStatusData.RecordModel = null;
        var toPageJson = new JsonObject();
        toPageJson.Add("SN", deviceStatusData.Device.DeviceSN);
        toPageJson.Add("Status", JsonValue.Create(EnumDeviceStatus.空闲));
        toPageJson.Add("From_CollectMode", JsonValue.Create(0));
        toPageJson.Add("IsChecked", JsonValue.Create(true));
        SendEndMsgToDevice(deviceStatusData);
        await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
        return true;
    }

    /// <summary>
    ///    停止检测
    /// </summary>
    /// <returns></returns>
    public static async Task<bool> SetPatientRecordToStop(string recordId)
    {
        var deviceStatusData = _deviceDataList.FirstOrDefault(m => m.RecordModel?.Id == recordId);
        return await SetPatientRecordEnd(deviceStatusData, EnumPatientCheckStatus.Stopped);
    }

    //
    // private static async Task SendEndMsgToAlg(string recordId,long StartTime,long time,bool IsEnd=false)
    // {
    //     var jsonData = new
    //     {
    //         MessageType = 3,
    //         ClientId = recordId,
    //         Algorithm = "PPGCalculator",
    //         IsEnd = IsEnd,
    //         PPGRate=200,
    //         StartTime=StartTime,
    //         Time =time,
    //         Data = new
    //         {
    //             PPG_G_Length =0,
    //             PPG_R_Length = 0,
    //             PPG_I_Length =0
    //         }
    //     };
    //     var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
    //     await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes,Array.Empty<byte>(),recordId);
    // }
}