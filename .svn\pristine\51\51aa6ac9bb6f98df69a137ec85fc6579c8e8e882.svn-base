﻿@page "/Client/SCI/DataAnalysis/{RecordId}"
@attribute [Permission("数据分析", Icon = "shuju", MenuOrder = "5")]
@using System.Globalization
@using System.Net.WebSockets
@using System.Text
@using System.Text.Json
@using System.Text.Json.Nodes
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.CSVDataModel
@using HX.HRV.Shared.Services
@using HX.Base.Shared.Components
@using HX.HRV.SCI.Shared.Models
@using HX.HRV.Shared.Units
@using UFU.CoreFX.Utils
@using UFU.IoT.Shared.Models
@using Masa.Blazor.Presets
@inject IJSRuntime JS
@implements IDisposable
@inject DeviceStateService DeviceStateService
<style>

    .hrv-report-fields {
        display: flex;
        flex-wrap: wrap; /* 支持换行布局 */
        border: 1px solid #ddd; /* 可选：边框 */
        background-color: #f9f9f9; /* 背景色 */
        padding: 0.5rem;
        font-size: 1.2rem;
        flex-flow: row;
        width: 100%;
        justify-content: space-between;
    }

    .field-item {
        display: flex;
        justify-content: center;
        align-items: center;

        flex-direction: row;
        align-content: stretch;
    }

    .hrv-report-label {
        font-weight: bold;
        color: #333;
        text-align: end;
    }

    .hrv-report-value {
        color: #555;
        text-align: left;
    }

    .data-analysis-table {

    }

    .data-analysis-table th,
    .data-analysis-table td {
        border-bottom: 1px solid #ddd;
        padding: 0.125em;
        width: 10em;
    }

    .data-analysis-table tr:first-child {
        border-top: 1px solid #ddd;
    }

    .data-analysis-table th {
        background-color: #e1ebf6;
        color: #333;
    }

    .section-title {
        font-weight: bold;
        color: #3982e3;
        text-align: left;
        font-size: 1.5rem;
        margin-top: 0.5rem;
    }

</Style>
<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<div style="display: flex;">
    <div class="hrv-report-fields">
        @if (_fields != null)
        {
            foreach (var field in _fields)
            {
                <div class="field-item">
                    <div class="hrv-report-label">@(field.Label)：</div>
                    <div class="hrv-report-value">@field.Value</div>
                </div>
            }
        }
    </div>
</div>
<MStepper Style="height: 100%;font-size: 20px" Value="_step">
    <MStepperHeader>
        <MStepperStep Step="1" Complete="_step>1">
            选择数据
        </MStepperStep>
        <MDivider></MDivider>
        <MStepperStep Step="2" Complete="_step>2">
            分析
        </MStepperStep>
    </MStepperHeader>

    <MStepperItems>
        <MStepperContent Step="1">
            <div>
                <MContainer Class="d-flex justify-center align-center">
                    <div class="d-flex justify-center align-center">
                        <MIcon Large
                               OnClick="PreCallback"
                               Color="blue darken-2">
                            mdi-chevron-left
                        </MIcon>
                        <MLabel>时间:</MLabel>
                        <PDateDigitalClockPicker @bind-Value="minDate"
                                                 Step=@TimeSpan.FromMinutes(1)
                                                 TimeFormat="TimeFormat.Hr24">
                            <PDefaultDateTimePickerActivator Filled SoloInverted
                                                             Dense
                                                             Clearable/>
                        </PDateDigitalClockPicker>
                        <MIcon Large
                               OnClick="NextCallback"
                               Color="blue darken-2">
                            mdi-chevron-right
                        </MIcon>
                    </div>
                    <div class="d-flex justify-center align-center">
                        <MLabel>间隔时长（秒）:</MLabel>
                        <MTextField HideDetails="true" @bind-Value="_spaceTime" SoloInverted
                                    Dense>
                        </MTextField>
                    </div>
                    <div class="d-flex justify-center align-center">
                        <MButton Type="primary" Style="margin-left: 10px" OnClick="OnDateClick">
                            确定
                        </MButton>

                        <MButton Color="primary" Style="margin-left: 10px;"
                                 OnClick="()=>isShowName=true">分析
                        </MButton>
                    </div>
                </MContainer>
            </div>
            <div style="display: flex">
                <div style="flex:5">
                    <div style="display: flex;padding:  0em 2rem;   ">
                        <div style="width:1200px;height: 900px">
                            <HXEchartSegmentLazyComponent
                                Title="PPG"
                                @ref="_echartSegmentComponent"
                            />
                        </div>
                        @* <div style="width:860px;height: 600px"> *@
                        @*     <HXEchartSegmentLazyComponent *@
                        @*         Title="Eda" *@
                        @*         @ref="_edaEchartSegmentComponent" *@
                        @*          *@
                        @*     /> *@
                        @* </div> *@
                    </div>
                </div>
            </div>
        </MStepperContent>
        <MStepperContent Step="2">

            @if (Statistics != null)
            {
                <MTabs
                    @bind-Value="_SelectTable"
                    Centered
                    SliderColor="1f73d2">
                    <MTab Value=@("心率变异性分析")>
                        @("心率变异性分析")
                    </MTab>
                    <MTab Value=@("皮肤电分析")>
                        @("皮肤电分析")
                    </MTab>
                </MTabs>

                <MTabsItems Value="@_SelectTable">
                    <MTabItem Value="@("心率变异性分析")">
                        <div style="display: flex">
                            <div style=" display: flex; flex-flow: row; ">
                                <MECharts Option="@GenerateChartOption("心率", Statistics.HRList)"
                                          Width="800" Height="600">
                                </MECharts>
                            </div>
                            <div style="display: flex; ">
                                <div style=" flex: 2;display: flex; ">
                                    <div style="display: flex; flex-flow: column;font-size: 18px">
                                        <div>
                                            <div class="section-title">Time Domain</div>
                                            <table class="data-analysis-table">
                                                <tr>
                                                    @foreach (var reportData in ReportDataModel.timeDomain)
                                                    {
                                                        <td style="font-weight: bold">@reportData.Name </td>
                                                    }
                                                </tr>
                                                <tr>
                                                    @foreach (var reportData in ReportDataModel.timeDomain)
                                                    {
                                                        <td>@Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key) @reportData.Unit.Replace("-", "")</td>
                                                    }
                                                </tr>
                                            </table>
                                        </div>
                                        <div>
                                            <div class="section-title">Frequency domain</div>
                                            <table class="data-analysis-table">
                                                @{
                                                    var size = 7;
                                                    var totalPage = (ReportDataModel.frequencyDomain.Length + size - 1) / size;

                                                    for (var i = 0; i < totalPage; i++)
                                                    {
                                                        <tr>
                                                            @for (var j = 0; j < size; j++)
                                                            {
                                                                var index = i * size + j;
                                                                if (index < ReportDataModel.frequencyDomain.Length)
                                                                {
                                                                    <td style="font-weight: bold">@ReportDataModel.frequencyDomain[index].Name </td>
                                                                }
                                                            }
                                                        </tr>
                                                        <tr>
                                                            @for (var j = 0; j < size; j++)
                                                            {
                                                                var index = i * size + j;
                                                                if (index < ReportDataModel.frequencyDomain.Length)
                                                                {
                                                                    <td style="font-size: 0.85rem">@(Statistics?.StatisticsDictionary.GetValueOrDefault(ReportDataModel.frequencyDomain[index].Key) + ReportDataModel.frequencyDomain[index].Unit.Replace("-", ""))</td>
                                                                }
                                                            }
                                                        </tr>
                                                    }
                                                }

                                            </table>
                                        </div>
                                        <div>
                                            <div class="section-title">Nonlinear</div>
                                            <table class="data-analysis-table">
                                                <tr>
                                                    @foreach (var reportData in ReportDataModel.nonlinearAnalysis)
                                                    {
                                                        <td style="font-weight: bold">@reportData.Name </td>
                                                    }
                                                </tr>
                                                <tr style="font-size: 0.85rem">
                                                    @foreach (var reportData in ReportDataModel.nonlinearAnalysis)
                                                    {
                                                        <td>@(Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key) + reportData.Unit.Replace("-", ""))</td>
                                                    }
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </MTabItem>
                    <MTabItem Value="@("皮肤电分析")">
                        <div style="display: flex">
                            <div style="display: flex;flex-direction: column;height: 900px">
                                @if (isShowEDA)
                                {
                                    <MECharts Option="EDAChartOption"
                                              Width="800" Height="400">
                                    </MECharts>
                                    <MECharts Option="SCRChartOption"
                                              Width="800" Height="400">
                                    </MECharts>
                                }
                            </div>
                            <div style="display: flex;font-size: 16px ">
                                <div style="display: flex;padding-top: 1rem; flex-flow: column;">
                                    <div>
                                        <div class="section-title">Time Domain</div>
                                        @if (RecordModel.EdaTimeDomain != null)
                                        {
                                            <table class="data-analysis-table">
                                                <tr>
                                                    @foreach (var item in EDATimeDomain)
                                                    {
                                                        <td style="font-weight: bold">@item </td>
                                                    }
                                                </tr>
                                                @foreach (var timeDomain in RecordModel.EdaTimeDomain)
                                                {
                                                    <tr>
                                                        <td style="font-size: 0.85rem">@timeDomain.Name</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Mean</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Max</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Min</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.SD</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Var</td>
                                                    </tr>
                                                }
                                            </table>
                                        }
                                    </div>
                                    <div>
                                        <div class="section-title">SCR</div>
                                        @if (RecordModel.EdaSCRResults != null)
                                        {
                                            <table class="data-analysis-table"
                                                   style="max-height: 240px;overflow-y:scroll; display: block;">
                                                <tr>
                                                    @foreach (var item in ScrTimeDomain)
                                                    {
                                                        <td style="font-weight: bold">@item </td>
                                                    }
                                                </tr>
                                                @foreach (var item in RecordModel.EdaSCRResults)
                                                {
                                                    <tr>
                                                        <td style="font-size: 0.85rem">@item.SCR_OnsetsTime</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_PeaksTime</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_RiseTime</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_Amplitude</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_RecoveryTime</td>
                                                    </tr>
                                                }
                                            </table>
                                        }
                                    </div>
                                    <div>
                                        <div class="section-title">Event Related</div>
                                        @if (EventKey != null)
                                        {
                                            <table class="data-analysis-table"
                                                   style="max-height: 240px;overflow-y:scroll; display: block;">
                                                <tr>
                                                    @foreach (var item in EventKey)
                                                    {
                                                        <td style="font-weight: bold">@item </td>
                                                    }
                                                </tr>
                                                @foreach (var item in RecordModel.EdaEventResults ?? new List<EdaEventResult>())
                                                {
                                                    <tr>
                                                        <td style="font-size: 0.85rem">@item.event_id</td>
                                                        <td style="font-size: 0.85rem">@item.event_label</td>
                                                        <td style="font-size: 0.85rem">@item.event_OnsetTime</td>
                                                        <td style="font-size: 0.85rem">@item.nSCR</td>
                                                        <td style="font-size: 0.85rem">@item.Latency</td>
                                                        <td style="font-size: 0.85rem">@item.Amplitude_Summary</td>
                                                        <td style="font-size: 0.85rem">@item.Average_SCL</td>
                                                        <td style="font-size: 0.85rem">@item.Average_RiseTime</td>
                                                        <td style="font-size: 0.85rem">@item.Average_RecoveryTime</td>
                                                    </tr>
                                                }
                                            </table>
                                        }
                                    </div>

                                </div>
                            </div>
                        </div>
                    </MTabItem>
                </MTabsItems>
            }
            <div style="width: 100%;display: flex; flex-flow: row;justify-content: flex-end;margin-bottom: 1rem;">
                <MButton
                    OnClick="PreviousStep">
                    上一步
                </MButton>
                <MButton Color="primary" Style="margin-left: 10px"
                         OnClick="OnExportClick">导出
                </MButton>
            </div>
        </MStepperContent>
    </MStepperItems>
</MStepper>

<PModal
    @bind-Value="isShowName"
    Persistent
    Title="请输入分析名称"
    Width="500"
    OnSave="HandleOnSave"
    OnCancel="HandleOnCancel">
    <MRow>
        <MCol Cols="12">
            <MTextField @bind-Value="Name"
                        Label="分析名称"
                        Placeholder="请输入分析名称"
                        Dense
                        Outlined/>
        </MCol>
    </MRow>
</PModal>

@code {
    private int _step = 1;

    private class Field
    {
        public string Label { get; set; }
        public string Value { get; set; }
    }

    public bool isShowName { get; set; }
    private string Name { get; set; }

    public bool isLoadPPG { get; set; } = true;
    [Parameter] public string RecordId { get; set; }

    private PatientModel patientModel = new PatientModel();
    private ReportDataStatistics Statistics { get; set; }

    private PatientRecordModel _patientRecordModel = new PatientRecordModel();
    [Inject] StateService _stateService { get; set; }
    private List<string> FileContent { get; set; }
    private List<string> SCRFileContent { get; set; }
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }


    private async Task HandleOnSave(ModalActionEventArgs args)
    {
        isShowName = true;
        await OnAnalysisButtonClick();
    }

    private void HandleOnCancel()
    {
        Name = string.Empty;
        isShowName = false;
    }


    protected override async Task OnInitializedAsync()
    {
        await InitRecordDataAsync();
        _fields = new Field[]
        {
            new Field { Label = "被试Id", Value = patientModel.Id },
            new Field { Label = "数据名称", Value = patientModel.Name },
            new Field { Label = "开始采集时间", Value = _patientRecordModel.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss") },
            new Field { Label = "结束采集时间", Value = _patientRecordModel.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss") },
            new Field { Label = "采样率", Value = _patientRecordModel.PpgRate.ToString() },
            new Field { Label = "数据长度", Value = _patientRecordModel.CollectEndTime - _patientRecordModel.CollectStartTime > TimeSpan.Zero ? (_patientRecordModel.CollectEndTime - _patientRecordModel.CollectStartTime).ToString(@"hh\:mm\:ss", CultureInfo.InvariantCulture) : "" }
        };
        var res = await _stateService
            .GetAsJsonAsync<DataModel<PatientRecordModel>>(
                "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string>()
                {
                    { "id", RecordId }
                });
        if (!res.Success)
        {
            await PopupService.EnqueueSnackbarAsync(res.Message, AlertTypes.Error);
            return;
        }


        var currentUri =
            _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
        var webSocketUrl =
            $"ws://{currentUri.Authority}/iot/hx_hrv_sci/v1?token={_stateService.Token}";
        ClientSocket = new ClientWebSocket();
        var serverUri = new Uri(webSocketUrl);
        await ClientSocket.ConnectAsync(serverUri, CancellationToken.None);
        _ = Task.Run(async () =>
        {
            while (ClientSocket.State == WebSocketState.Open)
            {
                var buffer = new byte[1024 * 10 * 10 * 10*10];
                var result = await ClientSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                if (result.MessageType == WebSocketMessageType.Close)
                {
                    await ClientSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Close", CancellationToken.None);
                    break;
                }

                try
                {
                    var realData = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    var segmentData = JsonNode.Parse(realData);
                    var type = segmentData["Type"].GetValue<string>();
                    var data = segmentData["data"].AsArray().GetValues<string>()?.ToList();
                    var isEnd = segmentData.AsObject().ContainsKey("IsEnd");
                    if (segmentData.AsObject().ContainsKey("NoData"))
                    {
                        await PopupService.EnqueueSnackbarAsync($"所选时间段无{type}数据", AlertTypes.Warning);
                    }
                    else
                    {
                        if (type == "PPG")
                        {
                            var reData = data.Select(m => m.Split(",")).ToList();
                            _echartSegmentComponent?.UpdateChartData(reData,isEnd,multiplierindex:0);
                        }
                        if (type == "EDA")
                        {
                            var reData = data.Select(m => m.Split(",")).ToList();
                            _echartSegmentComponent?.UpdateChartData(reData,isEnd,multiplierindex:1);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.StackTrace.ToString());
                }
            }
        });
        SegmentStartDate = _patientRecordModel.CollectStartTime;
        SegmentEndDate = _patientRecordModel.CollectStartTime.AddSeconds(_spaceTime);
        if (!await UpdateSegmentData(SegmentStartDate, SegmentEndDate))
        {
            await PopupService.EnqueueSnackbarAsync("获取数据失败", AlertTypes.Error);
        }
        minDate = _patientRecordModel.CollectStartTime;
        DeviceStateService.OnMessageReceived += HandlerReceiveDataFromSocket;
        await base.OnInitializedAsync();
    }

    private ClientWebSocket ClientSocket { get; set; }
    private DateTime SegmentStartDate { get; set; }
    private DateTime SegmentEndDate { get; set; } = default;
    
    
    /// <summary>GetSegmentData
    /// 更新分段数据
    /// </summary>
    /// <param name="res"></param>
    /// <returns></returns>
    private async Task<bool> UpdateSegmentData(DateTime startTime, DateTime endTime)
    {
        PopupService.ShowProgressCircular();
        var json = new
        {
            recordId = RecordId,
            startTime = startTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            endTime = endTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
        };
        var msgStr = JsonTool.SerializeIgnoreNull(json);
        await ClientSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
            CancellationToken.None);
        _echartSegmentComponent.ClearData();
        // _edaEchartSegmentComponent.ClearData();
        PopupService.HideProgressCircular();
        return true;
    }

    private CancellationTokenSource _cancellationTokenSource;

    private List<EDACSVDataModel> EDADatas = new List<EDACSVDataModel>();

    private void HandlerReceiveDataFromSocket(JsonNode algJsonNode)
    {
        if (algJsonNode["Function"]?.ToString() == "FromALG")
        {
            _cancellationTokenSource?.Cancel(); // 取消计时器
            record = algJsonNode["Record"].Deserialize<DataModel<DataAnalysisRecordModel>>();
            Statistics = record.Data.Statistics;
            PopupService.HideProgressCircular();
            isShowName = false;
            _step = 2;
            if (algJsonNode["Category"]?.ToString() == "GSR")
            {
                Task.Run(async () =>
                {
                    await PopupService.EnqueueSnackbarAsync("皮肤电分析完成", AlertTypes.Success);
                    try
                    {
                        EDADatas = await CSVDataHelper.GetDirectoryEDAData(record.Data.GetPath(record.Id));
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }

                    EdaDaCalculateModel = new EDADataCalculateModel(EDADatas.Select(m => m.EDA).ToList());

                    ScrDaCalculateModel = new EDADataCalculateModel(EDADatas.Select(m => m.SCR).ToList());

                    SclDaCalculateModel = new EDADataCalculateModel(EDADatas.Select(m => m.SCL).ToList());

                    EDADataCalculateModels = new Dictionary<string, EDADataCalculateModel>()
                    {
                        { "SC", EdaDaCalculateModel },
                        { "Phasic", ScrDaCalculateModel },
                        { "Tonic", SclDaCalculateModel }
                    };
                    GenerateEDAChartOption();
                    isShowEDA = true;
                    //await _EDAChartRef.SetOption(EDAChartOption,lazyUpdate:true);
                });
                InvokeAsync(StateHasChanged);
            }
            else
            {
                PopupService.EnqueueSnackbarAsync("心率变异性分析完成", AlertTypes.Success);
            }

            InvokeAsync(StateHasChanged);
        }
    }

    public EDADataCalculateModel SclDaCalculateModel { get; set; }

    public EDADataCalculateModel ScrDaCalculateModel { get; set; }

    public EDADataCalculateModel EdaDaCalculateModel { get; set; }

    private Dictionary<string, EDADataCalculateModel>
        EDADataCalculateModels = new Dictionary<string, EDADataCalculateModel>();

    private string[] EDATimeDomain = new[]
    {
        "Name",
        "Mean",
        "MAX",
        "MIN",
        "Standard Deviation",
        "Variance"
    };


    private string[] ScrTimeDomain = new[]
    {
        "Onset Time",
        "Peak Time",
        "Rise Time",
        "Amp",
        "Half Deacy Time",
    };


    private string[] EventKey = new[]
    {
        "Event Group",
        "Event Name ",
        //  "Event Desription",
        "Event Time",
        "nSCR",
        "Latency",
        "Amplitude Summary",
        "SCL",
        "AVerage Rise Time",
        "AVerage Half Decay Time"
    };

    private async Task InitRecordDataAsync()
    {
        var res = await InternalHttpClientService.GetPatientRecordModelDetailById(RecordId);
        _patientRecordModel = res.Data;
        patientModel = _patientRecordModel.Patient;
    }

    private IEnumerable<Field> _fields;

    private async Task HandleDataZoom((float startIndex, float endIndex) indices)
    {
        StartIndex = indices.startIndex;
        EndIndex = indices.endIndex;
        var timeSpan = _patientRecordModel.CollectEndTime - _patientRecordModel.CollectStartTime;
        var startIndex = timeSpan * indices.startIndex / 100;
        var endIndex = timeSpan * indices.endIndex / 100;

        var newStartTime = _patientRecordModel.CollectStartTime.Add(startIndex);
        var newEndTime = _patientRecordModel.CollectStartTime.Add(endIndex);
        // var res =GetExtraTimeRanges(SegmentStartDate, SegmentEndDate, newStartTime, newEndTime);
        // if (res.IsNotContained)
        // {
        //     if (res.ExtraRanges.Count > 0)
        //     {
        //         foreach (var resExtraRange in res.ExtraRanges)
        //         {
        //             await UpdateSegmentData( resExtraRange.Start, resExtraRange.End);
        //         }
        //     }
        // }
        SegmentStartDate = newStartTime;
        SegmentEndDate = newEndTime;
        await UpdateSegmentData( newStartTime, newEndTime);
    }

    private int _chartType = 1;
    private int _spaceTime = 5*60;
    private DateTime minDate = DateTime.MaxValue;
    private DataModel<DataAnalysisRecordModel> record;
    private DataAnalysisRecordModel RecordModel => record.Data;
    private StringNumber? _SelectTable = "心率变异性分析";

    private bool isShowEDA;
    private HXEchartSegmentLazyComponent _echartSegmentComponent;
    private bool isShowSCR;

    private async Task OnDateClick()
    {
        PopupService.ShowProgressCircular();
        
        var newStartTime = minDate;
        var newEndTime = minDate.AddSeconds(_spaceTime);
        // var res =GetExtraTimeRanges(SegmentStartDate, SegmentEndDate, newStartTime, newEndTime);
        // if (res.ExtraRanges.Count > 0)
        // {
        //     foreach (var resExtraRange in res.ExtraRanges)
        //     {
        //         await UpdateSegmentData( resExtraRange.Start, resExtraRange.End);
        //     }
        // }
        SegmentStartDate = newStartTime;
        SegmentEndDate = newEndTime;
        await UpdateSegmentData( newStartTime, newEndTime);
        PopupService.HideProgressCircular();
    }

    private async Task OnAnalysisButtonClick()
    {
        var data = new
        {
            Function = "ToALG",
            Data = new
            {
                RecordId = RecordId,
                StartTime = SegmentStartDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                EndTime = SegmentEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                Name
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(data);
        try
        {
            await DeviceStateService.ClientSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
                CancellationToken.None);
        }
        catch (Exception e)
        {
            await PopupService.EnqueueSnackbarAsync(e.Message, AlertTypes.Error);
        }

        PopupService.ShowProgressCircular();
        // 启动取消操作的计时器
        _cancellationTokenSource = new CancellationTokenSource();
        // 启动计时器任务
        _ = Task.Delay(1000 * 30, _cancellationTokenSource.Token).ContinueWith(t =>
        {
            if (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                PopupService.HideProgressCircular();
                PopupService.EnqueueSnackbarAsync("分析操作超时", AlertTypes.Error);
                _cancellationTokenSource.Cancel();
            }
        }, TaskContinuationOptions.OnlyOnRanToCompletion);
    }

    [Inject] IPopupService PopupService { get; set; }
    private float EndIndex { get; set; }
    private float StartIndex { get; set; }

    public void Dispose()
    {
        _cancellationTokenSource?.Cancel(); // 确保在销毁时取消计时器
        DeviceStateService.OnMessageReceived -= HandlerReceiveDataFromSocket;
    }

    [Inject] HttpClient HttpClient { get; set; }

    private async Task OnExportClick()
    {
        if (record == null)
        {
            await PopupService.EnqueueSnackbarAsync("请先进行数据分析", AlertTypes.Warning);
            return;
        }

        _ = Task.Run(async () =>
        {
            PopupService.ShowProgressCircular();
            await JS.InvokeVoidAsync("AutoPrintPdf", $"/client/DataAnalysis/ReportDetail/{record.Id}");
            await Task.Delay(3000);
            PopupService.HideProgressCircular();
        });
        await JS.InvokeVoidAsync("downloadFileFromUrl", $"api/v2.0/SCI/DataAnalysisRecord/DownloadStatic/{record.Id}");
    }

    private void NextStep(int step)
    {
        _step = 2;
    }

    private void PreviousStep()
    {
        _step = 1;
    }

    private async Task PreCallback()
    {
        minDate = minDate.Add(TimeSpan.FromMinutes(-1));
        var newStartTime = minDate;
        var newEndTime = newStartTime.Add(TimeSpan.FromSeconds(_spaceTime));
        // var res =GetExtraTimeRanges(SegmentStartDate, SegmentEndDate, newStartTime, newEndTime);
        // if (res.ExtraRanges.Count > 0)
        // {
        //     foreach (var resExtraRange in res.ExtraRanges)
        //     {
        //         await UpdateSegmentData( resExtraRange.Start, resExtraRange.End);
        //     }
        // }
        await UpdateSegmentData( newStartTime,newEndTime);
        SegmentStartDate = newStartTime;
        SegmentEndDate = newEndTime;
    }

    private async Task NextCallback()
    {
        minDate = minDate.Add(TimeSpan.FromMinutes(1));
        var newStartTime = minDate;
        var newEndTime = newStartTime.Add(TimeSpan.FromSeconds(_spaceTime));
        SegmentStartDate = newStartTime;
        SegmentEndDate = newEndTime;
         await UpdateSegmentData(newStartTime,newEndTime);
    }


    private object GenerateChartOption(string title, List<int> data)
    {
        return new
        {
            Animation = false,
            Title = new
            {
                text = title,
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                top = "20%",
                bottom = "3%",
                containLabel = true
            },
            xAxis = new
            {
                type = "category",
                Show = false,
            },
            yAxis = new
            {
                type = "value"
            },
            series = new[]
            {
                new
                {
                    data = data,
                    type = "line",
                    ShowSymbol = false,
                }
            }
        };
    }


    private Object EDAChartOption { get; set; } = new { };
    private Object SCRChartOption { get; set; } = new { };

    /// <summary>
    /// 构建EDA图表
    /// </summary>
    /// <returns></returns>
    private void GenerateEDAChartOption()
    {
        var series = new List<object>();

        //从EDADatas中生成Series

        var seriesEdaData = EDADatas.Select(m => new Object[]
        {
            m.Date.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            m.EDA
        }).ToList();


        var seriesScr = EDADatas.Select(m => new Object[]
        {
            m.Date.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            m.SCR
        }).ToList();

        var seriesScl = EDADatas.Select(m =>
            new Object[]
            {
                m.Date.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                m.SCL
            }).ToList();

        series.Add(new
        {
            Name = "EDA",
            type = "line",
            data = seriesEdaData,
            yAxisIndex = 1,
            showSymbol = false,
            markLine = new
            {
                symbol = new string[] { "none" },
                label = new
                {
                    show = true
                },
                lineStyle = new
                {
                    color = "red",
                    width = 2
                }
            }
        });
        // series.Add(new
        // {
        //     Name = "SCR",
        //     type = "line",
        //     data = seriesScr,
        //     yAxisIndex = 0,
        //     showSymbol = false,
        //     markLine = new
        //     {
        //         symbol = new string[] { "none" },
        //         label = new
        //         {
        //             show = true
        //         },
        //         lineStyle = new
        //         {
        //             color = "red",
        //             width = 2
        //         }
        //     }
        // });

        series.Add(new
        {
            Name = "SCL",
            type = "line",
            data = seriesScl,
            showSymbol = false,
            yAxisIndex = 0,
            markLine = new
            {
                symbol = new string[] { "none" },
                label = new
                {
                    show = true
                },
                lineStyle = new
                {
                    color = "red",
                    width = 2
                }
            }
        });

        EDAChartOption = new
        {
            Animation = false,
            Title = new
            {
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            tooltip = new
            {
                trigger = "axis"
            },
            legend = new
            {
                type = "plain"
            },
            xAxis = new
            {
                Type = "time",
            },
            yAxis = new object[]
            {
                new
                {
                    type = "value",
                },
                new
                {
                    type = "value",
                }
            },
            series = series
        };
        
        
        SCRChartOption = new
        {
            Animation = false,
            Title = new
            {
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            tooltip = new
            {
                trigger = "axis"
            },
            legend = new
            {
                type = "plain"
            },
            xAxis = new
            {
                Type = "time",
            },
            yAxis = new
            {
                type = "value",
            },
            series = new
            {
                Name = "SCR",
                type = "line",
                data = seriesScr,
                showSymbol = false,
                markLine = new
                {
                    symbol = new string[] { "none" },
                    label = new
                    {
                        show = true
                    },
                    lineStyle = new
                    {
                        color = "red",
                        width = 2
                    }
                }
            }
        };
    }
    public class TimeRangeResult
    {
        public bool IsNotContained { get; set; } // 新区间是否被旧区间包含
        public List<(DateTime Start, DateTime End)> ExtraRanges { get; set; } = new();
    }

    // /// <summary>
    // /// 只返回新时间区间比旧时间区间多出来的部分，如果新时间区间完全包含旧时间区间则返回多出来的两端
    // /// </summary>
    // public TimeRangeResult GetExtraTimeRanges(DateTime oldStart, DateTime oldEnd, DateTime newStart, DateTime newEnd)
    // {
    //     var result = new TimeRangeResult();

    //     // 新区间被旧区间包含
    //     result.IsNotContained = (newStart >= oldStart && newEnd <= oldEnd);

    //     // 新区间完全包含旧区间，返回两端多出来的部分
    //     if (newStart < oldStart && newEnd > oldEnd)
    //     {
    //         result.ExtraRanges.Add((newStart, oldStart));
    //         result.ExtraRanges.Add((oldEnd, newEnd));
    //     }
    //     // 新区间左侧有多余
    //     else if (newStart < oldStart && newEnd <= oldEnd)
    //     {
    //         result.ExtraRanges.Add((newStart, oldStart));
    //     }
    //     // 新区间右侧有多余
    //     else if (newStart >= oldStart && newEnd > oldEnd)
    //     {
    //         result.ExtraRanges.Add((oldEnd, newEnd));
    //     }

    //     // 新区间完全被旧区间包含或无多余部分，不返回任何区间
    //     return result;
    // }


}
