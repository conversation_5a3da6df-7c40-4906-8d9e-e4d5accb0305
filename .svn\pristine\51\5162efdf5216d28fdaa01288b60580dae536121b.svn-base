﻿using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Shared.Models.AlgDataModel;
using HX.HRV.Shared.Units;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;
using HrvVariable = HX.HRV.Web.Units.HrvVariable;

namespace HX.HRV.Web.Services
{
    /// <summary>
    ///  算法Socket通信服务
    /// </summary>
    public static class AlgWebSocketClient
    {
        private static readonly DataRepository _context;
        private static ClientWebSocket _clientWebSocket;
        private static string _proxyHost;
        private static int _proxyPort;

        static AlgWebSocketClient()
        {
            _context = new DataRepository();
            _proxyHost = Env.Configuration["AlgorithmProxy:Host"];
            _proxyPort = int.Parse(Env.Configuration["AlgorithmProxy:Port"]);
            LogTool.Logger.Information("算法网关地址:" + _proxyHost + ":" + _proxyPort);
            _ = Task.Run(async () =>
            {
                _clientWebSocket = new ClientWebSocket();
                await _clientWebSocket.ConnectAsync(
                    new Uri($"ws://{_proxyHost}:{_proxyPort}/api/Algorithms?ClientType=2"),
                    CancellationToken.None);
                await GetAlgClientWebSocketAsync();
                await Task.Delay(200);
                while (true)
                {
                    try
                    {
                        var clientWebSocket = await GetAlgClientWebSocketAsync();
                        var buffer = new ArraySegment<byte>(new byte[1024 * 1024 * 10]);
                        var result = await clientWebSocket.ReceiveAsync(buffer, CancellationToken.None);
                        if (clientWebSocket.State == WebSocketState.Connecting)
                        {
                            await Task.Delay(300);
                            continue;
                        }

                        if (result.MessageType == WebSocketMessageType.Close)
                        {
                            await clientWebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "算法网关断开连接",
                                CancellationToken.None);
                            await Task.Delay(300);
                            continue;
                        }

                        if (result.Count > 0)
                        {
                            var data = ParseCommandPacket(buffer.Array);
                            OnDataReceived?.Invoke(data);
                            InvokeCallbacks(data);
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex is WebSocketException { WebSocketErrorCode: WebSocketError.ConnectionClosedPrematurely })
                        {
                        }
                        else
                        {
                            LogTool.Logger.Error("接收算法网关数据错误：" + ex);
                            await Task.Delay(300);
                        }
                    }
                }
            });
        }

        public static Action<ParsedPacket> OnDataReceived { get; set; }


        public static ParsedPacket ParseCommandPacket(byte[] packet)
        {
            if (packet.Length < 2)
                throw new ArgumentException("Invalid packet length");
            var commandContentLength = BitConverter.ToUInt32(packet[..4]);
            byte[] commandContent = new byte[commandContentLength];
            Array.Copy(packet, 4, commandContent, 0, commandContentLength);
            string jsonString = Encoding.UTF8.GetString(commandContent);
            byte[] data = new byte[packet.Length - 4 - commandContentLength];
            Array.Copy(packet, 4 + commandContentLength, data, 0, data.Length);
            return new ParsedPacket
            {
                JsonContent = jsonString,
                BinaryData = data
            };
        }

        /// <summary>
        /// 发送二进制数据到算法网关
        /// </summary>
        /// <param name="jsonBytes"></param>
        /// <param name="bytes"></param>
        private static async Task SendToAlgAsync(byte[] jsonBytes, byte[] bytes)
        {
            var dataPacket = CreateCommandPacket(jsonBytes, bytes);
            var buffer = new ArraySegment<byte>(dataPacket);
            var clientWebSocket = await GetAlgClientWebSocketAsync();
            await clientWebSocket.SendAsync(buffer, WebSocketMessageType.Binary, true,
                CancellationToken.None);
        }


        /// <summary>
        /// 发送二进制数据到算法网关 并且注册默认的回调
        /// </summary>
        /// <param name="jsonBytes"></param>
        /// <param name="bytes"></param>
        public static async Task SendToAlgAsyncAndRegister(byte[] jsonBytes, byte[] bytes, string key,
            Action<ParsedPacket> callback = null)
        {
            await SendToAlgAsync(jsonBytes, bytes);
            if (callback != null)
            {
                RegisterCallback(key, callback);
            }
            else
            {
                RegisterCallback(key, OnReceiveCalculatorData);
            }

            //LogTool.Logger.Information($"发送数据到算法网关成功,{_proxyHost}:{_proxyPort}");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="recordId"></param>
        /// <param name="ppgData"></param>
        /// <param name="ppgRate"></param>
        /// <param name="startTime"></param>
        /// <param name="isEnd"></param>
        /// <param name="algorithm"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public static async Task SendMsgToAlg(string recordId, Dictionary<string, List<uint>> ppgData, int ppgRate,
            long startTime, bool isEnd, string algorithm = null, Action<ParsedPacket> callback = null)
        {
            algorithm ??= HrvVariable.HrvVariableInstance.PPGALG;
            var dataBytes = new Dictionary<string, byte[]>();
            var totalLength = 0;
            foreach (var kvp in ppgData)
            {
                var byteArray = new byte[kvp.Value.Count * sizeof(uint)];
                Buffer.BlockCopy(kvp.Value?.ToArray() ?? Array.Empty<uint>(), 0, byteArray, 0, byteArray.Length);
                dataBytes[kvp.Key] = byteArray;
                totalLength += byteArray.Length;
            }

            var jsonData = new
            {
                MessageType = AlgSocketMessageType.Call,
                ClientId = recordId,
                Algorithm = algorithm,
                IsEnd = isEnd,
                PPGRate = ppgRate,
                StartTime = startTime,
                Data = dataBytes.ToDictionary(kvp => kvp.Key + "_Length", kvp => kvp.Value.Length)
            };
            var combinedBytes = new byte[totalLength];
            int offset = 0;
            foreach (var byteArray in dataBytes.Values)
            {
                Buffer.BlockCopy(byteArray, 0, combinedBytes, offset, byteArray.Length);
                offset += byteArray.Length;
            }

            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            await SendToAlgAsyncAndRegister(jsonBytes, combinedBytes, recordId, callback: callback);
        }

        public static async Task SendTickMsgToAlg(
            string recordId,
            long timeSpan,
            string algorithm = null,
            string tickTag = "tick")
        {
            algorithm ??= HrvVariable.HrvVariableInstance.PPGALG;
            var jsonData = new
            {
                MessageType = AlgSocketMessageType.Call,
                ClientId = recordId,
                Algorithm = algorithm,
                AlgorithmAction = "tick",
                TickTime = timeSpan,
                TickTag = tickTag
            };
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            await SendToAlgAsync(jsonBytes, Array.Empty<byte>());
        }


        /// <summary>
        /// 创建二进制数据
        /// </summary>
        /// <param name="jsonBytes"></param>
        /// <param name="binaryData"></param>
        /// <returns></returns>
        public static byte[] CreateCommandPacket(byte[] jsonBytes, byte[] binaryData)
        {
            byte[] commandContent = new byte[4+ jsonBytes.Length + binaryData.Length];
            uint binaryDataLength = (uint)jsonBytes.Length;
            Array.Copy(BitConverter.GetBytes(binaryDataLength), 0, commandContent, 0, 4);
            Array.Copy(jsonBytes, 0, commandContent, 4, jsonBytes.Length);
            if (binaryData.Length > 0)
            {
                Array.Copy(binaryData, 0, commandContent, 4+ jsonBytes.Length, binaryData.Length);
            }

            return commandContent;
        }

        /// <summary>
        /// 获取和算法网关的连接
        /// </summary>
        /// <returns></returns>
        private static async Task<ClientWebSocket> GetAlgClientWebSocketAsync()
        {
            if (_clientWebSocket is { State: WebSocketState.Open })
            {
                return _clientWebSocket;
            }

            try
            {
                _clientWebSocket = new ClientWebSocket();
                //获取当前的URL和Host
                var webSocketUrl = $"ws://{_proxyHost}:{_proxyPort}/api/Algorithms?ClientType=2";
                var serverUri = new Uri(webSocketUrl);
                await _clientWebSocket.ConnectAsync(serverUri, CancellationToken.None);
            }
            catch (Exception e)
            {
                LogTool.Logger.Error("获取算法网关连接失败：" + e);
            }

            return _clientWebSocket;
        }


        /// <summary>
        /// PPG的算法
        /// </summary>
        /// <param name="data"></param>
        private static void OnReceiveCalculatorData(ParsedPacket data)
        {
            var jsonData = JsonNode.Parse(data.JsonContent);

            var algorithm = jsonData?["Algorithm"]?.GetValue<string>();
            if (algorithm == HrvVariable.HrvVariableInstance.PPGALG)
            {
                LogTool.Logger.Information($"OnReceiveCalculatorData：收到PPG数据{data.JsonContent}");
                ParsePpgData(data);
            }
            else if (algorithm == "GSRSeparated")
            {
                ParseGsrData(data);
            }

            if (algorithm == "EmotionCalculator")
            {
                LogTool.Logger.Information($"【ALGDataAnalysisService】 EmotionCalculator：{data.JsonContent}");
                ParseEmotionCalculatorData(data);
            }
        }

        /// <summary>
        /// 解析Gsr数据
        /// </summary>
        /// <param name="data"></param>
        private static void ParseGsrData(ParsedPacket data)
        {
            var jsonData = JsonNode.Parse(data.JsonContent);
            var clientId = jsonData?["ClientId"]?.ToString();
            if (string.IsNullOrEmpty(clientId))
            {
                LogTool.Logger.Information($"ParseGSRData：收到未知消息{data.JsonContent}");
                return;
            }

            var devices = DeviceStatusDataService.GetDeviceDataList();
            var device = devices.FirstOrDefault(m => m.RecordModel?.Id == clientId);
            var recordId = jsonData?["ClientId"]?.ToString();
            var sclLength = jsonData?["Data"]?["eda_tonic_len"]?.GetValue<int>() ?? 0;

            var scrLength = jsonData?["Data"]?["eda_phasic_len"]?.GetValue<int>() ?? 0;
            var denoisedSignal = jsonData?["Data"]?["denoised_signal_len"]?.GetValue<int>() ?? 0;
            var denoisedSignalLength = denoisedSignal * sizeof(int);
            if (scrLength == 0 || sclLength == 0) return;


            var sclData = new float[sclLength];
            var sclByteDataLength = sclLength * sizeof(float);
            var sclByteData = data.BinaryData
                .Skip(denoisedSignalLength)
                .Take(sclByteDataLength)
                .ToArray();
            using (var ms = new MemoryStream(sclByteData))
            {
                using (var br = new BinaryReader(ms))
                {
                    for (int i = 0; i < sclData.Length; i++)
                    {
                        sclData[i] = br.ReadSingle() ;
                    }
                }
            }

            var scrData = new float[scrLength];
            var scrByteDataLength = scrLength * sizeof(float);
            var scrBytes = data.BinaryData.Skip(denoisedSignalLength + sclByteDataLength)
                .Take(scrByteDataLength)
                .ToArray();
            using (var ms = new MemoryStream(scrBytes))
            {
                using (var br = new BinaryReader(ms))
                {
                    for (int i = 0; i < scrData.Length; i++)
                    {
                        scrData[i] = br.ReadSingle();
                    }
                }
            }


            if (jsonData["Data"].AsObject().ContainsKey("eda_time_domain"))
            {
                Task.Run(async () =>
                {
                    var path = "";
                    var clientId =  jsonData["ClientId"]?.ToString();
                    var time = jsonData["Data"]?["Time"]?.GetValue<long>() ?? 0;
                    if (time == 0) return;
                    var record = device?.RecordModel;
                    if (device?.RecordModel == null)
                    {
                        var patientRecordModel =
                            await _context.Query<PatientRecordModel>(PermissionSwitch.Off)
                                .FirstOrDefaultAsync(m => m.Id == recordId);
                        // record = patientRecordModel.Data;
                    }
                    // path = record.GetRecordDirectoryPath();
                    // _ = FileBufferManager.AddToBuffer(Path.Combine(path, "SCL&SCR" + ".csv"),
                    //     new Dictionary<string, List<float>>
                    //     {
                    //          { "SCL", sclData.ToList() },
                    //          { "SCR", scrData.ToList() }
                    //     },
                    //     time / 1000
                    // );
                });
            }
            else
            {
                Task.Run(async () =>
                {
                    var path = "";
                    var time = jsonData["Data"]?["Time"]?.GetValue<long>() ?? 0;
                    if (time == 0) return;
                    var record = device?.RecordModel;
                    if (device?.RecordModel == null)
                    {
                        var patientRecordModel =
                            await _context.Query<PatientRecordModel>(PermissionSwitch.Off)
                                .FirstOrDefaultAsync(m => m.Id == recordId);
                        record = patientRecordModel.Data;
                    }
                    path = record.GetRecordDirectoryPath();
                    // _ = FileBufferManager.AddToBuffer(Path.Combine(path, "SCL&SCR" + ".csv"),
                    //     new Dictionary<string, List<float>>
                    //     {
                    //         { "SCL", sclData.ToList() },
                    //         { "SCR", scrData.ToList() }
                    //     },
                    //     time / 1000
                    // );
                });
            }
            if (device is not { DeviceStatus: EnumDeviceStatus.检测中 }) return;
            var deviceData = new
            {
                SN = device.Device.DeviceSN,
                SCL = sclData,
                SCR = scrData,
                From_ParseGSRData = 1
            };
            _ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(deviceData));
        }

        /// <summary>
        /// 解析情绪
        /// </summary>
        /// <param name="data"></param>
        private static void ParseEmotionCalculatorData(ParsedPacket data)
        {
            LogTool.Logger.Information($"EmotionCalculator：收到未知消息{data.JsonContent}");
            var jsonData = JsonNode.Parse(data.JsonContent);
            var clientId = jsonData?["ClientId"]?.ToString();
            if (string.IsNullOrEmpty(clientId))
            {
                LogTool.Logger.Information($"EmotionCalculator：收到未知消息{data.JsonContent}");
                return;
            }

            var recordId = jsonData?["ClientId"]?.ToString();
            var calculatorData = jsonData?["Data"]?.AsObject();
            if (string.IsNullOrEmpty(recordId) || calculatorData is { Count: <= 0 }) return;

            
            if(calculatorData.ContainsKey("Error"))
            {
                var error = calculatorData?["Error"]?.GetValue<string>();
                LogTool.Logger.Information($"EmotionCalculator：情绪报告生成出错：\r\n{error}");
                return;
            }
            
            LogTool.Logger.Information($"EmotionCalculator：收到情绪数据{data.JsonContent}");
            var record = _context
                .Query<PatientRecordModel>(PermissionSwitch.Off)
                .FirstOrDefault(m => m.Id == clientId);
            var depress_prediction = calculatorData?["depress_prediction"]?.GetValue<int>() == 1;
            var depressConfidence = calculatorData?["depress_confidence"]?.GetValue<float>();
            //是否抑郁
            var depressLevelPrediction = calculatorData?["depressLevelPrediction"]?.GetValue<int>();
            //置信概率 
            var depressLevelConfidence = calculatorData?["depressLevelConfidence"]?.GetValue<float>();
            //情绪效价
            var valenceScore = calculatorData?["valence_score"]?.GetValue<float>() ?? 0;
            var valenceScoreType = calculatorData?["valence_score_type"]?.GetValue<int>() ?? 0;
            //情绪唤醒度
            var arousalScore = calculatorData?["arousal_score"]?.GetValue<float>() ?? 0;
            var arousalScoreType = calculatorData?["arousal_score_type"]?.GetValue<float>() ?? 0;

            if (record == null) return;
            record.Data.IsNotEmotion = false;
            record.Data.Emotion = new EmotionDataModel()
            {
                DepressPrediction = depress_prediction,
                DepressConfidence = depressConfidence ?? 0,
                DepressLevelConfidence = depressLevelConfidence ?? 0,
                DepressLevelPrediction = depressLevelPrediction ?? 0,
                ValenceScore = valenceScore,
                ArousalScore = arousalScore,
                ArousalScoreType = (int)arousalScoreType,
                ValenceScoreType = valenceScoreType
            };
            //生成了变异性报告才发送消息
            var device = DeviceStatusDataService.GetDeviceData(record.Data.DeviceId);
            if (device is not null && !string.IsNullOrEmpty(device?.Device?.DeviceSN))
            {
                SendAutoPrintPdfMessage(device.Device.DeviceSN, record.Id, "printEmotionPdf");
            } 
            _context.Update(record, PermissionSwitch.Off);
            _context.SaveChanges();
        }


        /// <summary>
        ///  解析算法传回的PPG数据
        /// </summary>
        /// <param name="data"></param>
        private static void ParsePpgData(ParsedPacket data)
        {
            var jsonData = JsonNode.Parse(data.JsonContent);
            var isDenoisedSignal = jsonData?["Data"]?.AsObject()?.ContainsKey("denoised_signal_len");
            var clientId = jsonData?["ClientId"]?.ToString();
            if (string.IsNullOrEmpty(clientId))
            {
                LogTool.Logger.Information($"OnReceivePigCalculator：收到未知消息{data.JsonContent}");
                return;
            }

            var devices = DeviceStatusDataService.GetDeviceDataList();
            var device = devices.FirstOrDefault(m => m.RecordModel?.Id == clientId);
            if ((isDenoisedSignal ?? false))
            {
                if (device?.DeviceStatus == EnumDeviceStatus.检测中)
                {
                    try
                    {
                        var bytesDenoisedSignalLen =
                            jsonData?["Data"]?["denoised_signal_len"]?.GetValue<int>() ?? 0;

                        var ppgData2 = new int[bytesDenoisedSignalLen];
                        var signalLen = bytesDenoisedSignalLen * sizeof(uint);
                        using (var ms = new MemoryStream(data.BinaryData
                                   .Take(signalLen).ToArray()))
                        {
                            using (var br = new BinaryReader(ms))
                            {
                                for (var i = 0; i < bytesDenoisedSignalLen; i++)
                                {
                                    ppgData2[i] = br.ReadInt32();
                                }
                            }
                        }

                        DeviceStatusDataService.OnReceivePPGData(device, ppgData2);
                        var toPageJson = new JsonObject();
                        toPageJson.Add("SN", device.Device.DeviceSN);
                        toPageJson.Add("PPG", JsonValue.Create(ppgData2));
                        _ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        try
                        {
                            var heartJson = new JsonObject();
                            var bmp = jsonData?["Data"]?["heart_rate"]?.GetValue<int>() ?? 0;
                            DeviceStatusDataService.OnReceiveSPO2Data(bmp > 0 ? (uint)bmp : 0, device.Device.DeviceSN);
                            if (Env.Configuration.GetValue<string>("HrvVariable:HRShowFrom") == "Alg")
                            {
                                heartJson.Add("Bmp", bmp > 0 ? (uint)bmp : 0);
                                heartJson.Add("From", "ParsePpgData");
                                heartJson.Add("SN", device.Device.DeviceSN);
                                _ = HXAdminWebSocket.SendMsgToAdmin(heartJson.ToJsonString());
                            }
                        }
                        catch (Exception e)
                        {
                            LogTool.Logger.Information(" OnReceivePigCalculator_____heart_rate错误", e);
                        }
                    }
                    catch (Exception e)
                    {
                        LogTool.Logger.Information(e, $"OnReceivePigCalculator_____isDenoisedSignal错误");
                    }
                }
            }
            else
            {
                try
                {
                    var recordData = _context
                        .Query<PatientRecordModel>(PermissionSwitch.Off)
                        .FirstOrDefault(m => m.Id == clientId);
                    if (recordData?.Data == null)
                    {
                        LogTool.Logger.Error($"不存在检测记录{clientId}");
                        return;
                    }

                    if (recordData.Data.BuildReportStatus == BuildReportStatus.Completed)
                    {
                        LogTool.Logger.Error($"{clientId}检测记录已经生成报告");
                        return;
                    }

                    var statistics = GetDataStatistics(data, jsonData);
                    recordData.Data.Statistics = statistics;
                    recordData.Data.BuildReportStatus = BuildReportStatus.Completed;
                    recordData.UpdateTime = DateTime.Now;
                    var deviceId = recordData.Data.DeviceId;
                    _context.Update(recordData, PermissionSwitch.Off);
                    _context.SaveChanges();
                    device=devices.FirstOrDefault(m => m.Device.Id == deviceId);
                    if (device != null && clientId == device.RecordModel?.Id &&
                        device.DeviceStatus == EnumDeviceStatus.检测中)
                    {
                        var deviceData = new
                        {
                            SN = device.Device.DeviceSN,
                            Status = device.DeviceStatus,
                            IsAutoPrint = true,
                            recordId = recordData.Id,
                            From_ParsePPGData = 1
                        };
                        _ = HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(deviceData));
                        //_ = DeviceStatusDataService.SetPatientRecordEnd(device);
                    }

                    if (device != null)
                    {
                        SendAutoPrintPdfMessage(device.Device.DeviceSN, recordData.Id);
                    }
                }
                catch (Exception e)
                {
                    LogTool.Logger.Error("解析算法回传数据失败" + e);
                }
            }
        }

        /// <summary>
        /// 发送打印的消息
        /// </summary>
        /// <param name="deviceSn"></param>
        /// <param name="recordId"></param>
        private static void SendAutoPrintPdfMessage(string deviceSn, string recordId, string action = "printPdf")
        {
            _ = Task.Run(() =>
            {
                try
                {
                    HXAdminWebSocket.SendMsgToAdmin(JsonTool.SerializeIgnoreNull(new
                    {
                        IsPrint = true,
                        SN = deviceSn,
                        PrintId = recordId,
                        Action = action
                    }));
                    
                }
                catch (Exception e)
                {
                    LogTool.Logger.Error(e, $"发送打印消息失败{deviceSn}");
                }
            });
        }

        /// <summary>
        /// 获取算法返回的报告的数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="jsonData"></param>
        /// <returns></returns>
        public static ReportDataStatistics GetDataStatistics(ParsedPacket data, JsonNode jsonData )
        {
            var offset = 0;
            var lengths = jsonData?["Data"]?.AsObject().Where(m => m.Key.EndsWith("_len"));
            var hrData = new List<uint>();
            var nniData = new List<uint>();
            var frequencyData = new List<uint>();
            var psdData = new List<uint>();
            var hrOriginalData = new List<uint>();
            foreach (var (key, value) in lengths)
            {
                var length = value.GetValue<int>();
                var skip = length * sizeof(uint);
                var dataBytes = data.BinaryData[offset..(offset + skip)].ToArray();
                var dataArray = new List<uint>();
                using (var ms = new MemoryStream(dataBytes))
                {
                    using (var br = new BinaryReader(ms))
                    {
                        for (int i = 0; i < length; i++)
                        {
                            dataArray.Add(br.ReadUInt32());
                        }
                    }
                }

                offset += skip;
                switch (key)
                {
                    case "hr_len":
                        hrData = dataArray;
                        break;
                    case "nni_len":
                        nniData = dataArray;
                        break;
                    case "frequency_len":
                        frequencyData = dataArray;
                        break;
                    case "psd_len":
                        psdData = dataArray;
                        break;
                    case "hr_original_len":
                        hrOriginalData = dataArray;
                        break;
                }
            }

            var statistics = new ReportDataStatistics();
            if (jsonData != null)
            {
                var dataIndex = jsonData["Data"]?["data_index"];
                var resultDictionary = new Dictionary<string, string>();
                if (dataIndex != null)
                    foreach (var (key, value) in dataIndex.AsObject())
                    {
                        string formattedValue;

                        if (value.GetValueKind() == JsonValueKind.Number)
                        {
                            formattedValue = key.Contains("hr")
                                ? value.GetValue<float>().ToString("F0")
                                : value.GetValue<float>().ToString("F2");
                        }
                        else
                        {
                            formattedValue = value.ToString();
                        }

                        resultDictionary.Add(key, formattedValue);
                    }

                statistics.StatisticsDictionary = resultDictionary;
            }

            statistics.HRList = hrOriginalData.Select(m => (int)m).ToList();
            statistics.PsdList = psdData.Select(m => (int)m).ToList();
            statistics.NniList = nniData.Select(m => (int)m).ToList();
            statistics.FrequencyList = frequencyData.Select(m => (int)m).ToList();
            return statistics;
        }


        /// <summary>
        ///  回调
        /// </summary>
        private static readonly ConcurrentDictionary<string, Action<ParsedPacket>> Callbacks = new();

        /// <summary>
        ///  注册回调
        /// </summary>
        /// <param name="key"></param>
        /// <param name="callback"></param>
        public static void RegisterCallback(string key, Action<ParsedPacket> callback)
        {
            if (Callbacks.ContainsKey(key)) return;
            Callbacks.TryAdd(key, callback);
        }

        /// <summary>
        ///  取消回调
        /// </summary>
        /// <param name="key"></param>
        private static void UnregisterCallback(string key)
        {
            Callbacks.TryRemove(key, out _);
        }

        private static void InvokeCallbacks(ParsedPacket data)
        {
            var webSocketData = JsonTool.Deserialize<AlgWebSocketData>(data.JsonContent);
            var key = webSocketData.ClientId;
            if (key == null)
            {
                LogTool.Logger.Warning("AlgWebSocketClient,InvokeCallbacks,未知消息:" + data.JsonContent);
            }

            if (Callbacks.TryGetValue(key, out var callback))
            {
                callback(data);
                // UnregisterCallback(key);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="recordId"></param>
        /// <param name="startTime"></param>
        /// <param name="time"></param>
        /// <param name="isEnd"></param>
        public static async Task SendEndMsgToAlg(
            string recordId,
            long startTime,
            long time,
            bool isEnd = false
        )
        {
            var jsonData = new
            {
                MessageType = 3,
                ClientId = recordId,
                Algorithm = HrvVariable.HrvVariableInstance.PPGALG,
                IsEnd = isEnd,
                PPGRate = 200,
                StartTime = startTime,
                Time = time,
                Data = new
                {
                    PPG_G_Length = 0,
                    PPG_R_Length = 0,
                    PPG_I_Length = 0
                }
            };
            var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
            SendToAlgAsyncAndRegister(
                jsonBytes,
                Array.Empty<byte>(),
                recordId
            );

            var gSRjsonData = new
            {
                MessageType = 3,
                ClientId = recordId,
                Algorithm = "GSRSeparated",
                IsEnd = isEnd,
                StartTime = startTime,
                Time = time,
                Data = new
                {
                    gsr_Length = 0,
                }
            };
            jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(gSRjsonData));
            SendToAlgAsyncAndRegister(
                jsonBytes,
                Array.Empty<byte>(),
                recordId
            );
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="record"></param>
        public static async Task ExeGenerateEmotionAsync(string recordId, string filePath)
        {
            try
            {
                var edaDic = FileDataHelper.ReadDataFromCsv<float>(filePath, HXDataType.EDA.ToString(),out var edaHz);
                var edaList = edaDic["EDA"];
                var spo2Dic = FileDataHelper.ReadDataFromCsv<uint>(filePath, HXDataType.HRSPO2.ToString());
                var hrList = new List<uint>();
                if (spo2Dic.TryGetValue("Bmp", out var value))
                {
                    hrList = value;
                }
                else 
                {
                    hrList = spo2Dic["HR"];
                }
                
                var imuDic = FileDataHelper.ReadDataFromCsv<int>(filePath, nameof(HXDataType.IMU),out var imuHz);
                var accXList = imuDic["ACC-X"];
                var accYList = imuDic["ACC-Y"];
                var accZList = imuDic["ACC-Z"];
                //为每一项数据生成tasks
                var edaTask = async () =>
                {
                    byte[] dataBytes = new byte[edaList.Count() * sizeof(float)];
                    var data = edaList.Cast<float>().ToArray();
                    Buffer.BlockCopy(data, 0, dataBytes, 0, dataBytes.Length);
                    var jsonData = new
                    {
                        MessageType = 3,
                        ClientId = recordId,
                        Algorithm = "EmotionCalculator",
                        IsEnd = false,
                        Data = new
                        {
                            eda_Length = dataBytes.Length
                        }
                    };
                    var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
                    await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, dataBytes, recordId);
                };
                var hrTask = async () =>
                {
                    byte[] dataBytes = new byte[hrList.Count() * sizeof(uint)];
                    var data = hrList.Cast<uint>().ToArray();
                    Buffer.BlockCopy(data, 0, dataBytes, 0, dataBytes.Length);
                    var jsonData = new
                    {
                        MessageType = 3,
                        ClientId = recordId,
                        Algorithm = "EmotionCalculator",
                        IsEnd = false,
                        Data = new
                        {
                            hr_Length = dataBytes.Length
                        }
                    };
                    var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
                    await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, dataBytes, recordId);
                };
                var accXTask = async () =>
                {
                    var dataBytes = new List<byte>();
                    var accX = accXList.Cast<int>().ToArray();
                    var accY = accYList.Cast<int>().ToArray();
                    var accZ = accZList.Cast<int>().ToArray();

                    var accXBytes = new byte[accX.Length * sizeof(int)];
                    var accYBytes = new byte[accY.Length * sizeof(int)];
                    var accZBytes = new byte[accZ.Length * sizeof(int)];
                    Buffer.BlockCopy(accX, 0, accXBytes, 0, accXBytes.Length);
                    Buffer.BlockCopy(accY, 0, accYBytes, 0, accYBytes.Length);
                    Buffer.BlockCopy(accZ, 0, accZBytes, 0, accZBytes.Length);
                    dataBytes.AddRange(accXBytes);
                    dataBytes.AddRange(accYBytes);
                    dataBytes.AddRange(accZBytes);
                    var jsonData = new
                    {
                        MessageType = 3,
                        ClientId = recordId,
                        Algorithm = "EmotionCalculator",
                        IsEnd = false,
                        Data = new
                        {
                            acc_x_Length = accXBytes.Length,
                            acc_y_Length = accYBytes.Length,
                            acc_z_Length = accZBytes.Length
                        }
                    };
                    var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
                    await SendToAlgAsyncAndRegister(jsonBytes, dataBytes.ToArray(), recordId);
                };
                await Task.WhenAll(edaTask(), hrTask(), accXTask()).ContinueWith(async (_) =>
                {
                    await Task.Delay(1000);
                    var jsonData = new
                    {
                        MessageType = 3,
                        ClientId = recordId,
                        Algorithm = "EmotionCalculator",
                        IsEnd = true,
                        Data = new
                        {
                            eda_fs= edaHz,
                            acc_fs = imuHz
                        }
                    };
                    var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
                    await SendToAlgAsyncAndRegister(jsonBytes, Array.Empty<byte>(), recordId);
                });
            }
            catch (Exception e)
            {
                LogTool.Logger.Error(e, "【ExeGenerateEmotionAsync】 发送数据到算法网关失败");
            }
        }
    }
}