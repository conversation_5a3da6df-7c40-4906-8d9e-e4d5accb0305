﻿@using System.Text.Encodings.Web
@using System.Text.Json
@using System.Text.Unicode
@using UFU.CoreFX.Models.ViewModels
@inject StateService _StateService;
@inject HttpClient _Client;
@inject  MessageService _MessageService;
<div>
    <InputFile id="fileInput" OnChange="UploadFiles" hidden/>
    <Upload Name="files" FileList="_fileList" @attributes='new Dictionary<string, object> { { "ListType", "picture" } }' ShowUploadList="true" OnRemove="OnRemove">
        <label class="ant-btn" for="fileInput">
            <Icon Type="upload"/>
            上传文件
        </label>
        <span style="font-size: 12px;font-weight: lighter">只能上传jpg/png文件，且不超过500kb</span>
    </Upload>
</div>

@code {

    private List<string> AllowExtension { get; } = new()
    {
        ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso"
    };

    [Parameter] public List<UploadFileViewModel> ImageList { get; set; } = new();
    [Parameter] public string Id { get; set; }
    [Parameter] public EventCallback<List<UploadFileViewModel>> ImageListChanged { get; set; }

    private void NotifyImageListChanged()
    {
        ImageListChanged.InvokeAsync(ImageList);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        _fileList = ImageList.Select(m => new UploadFileItem
        {
            FileName = m.name,
            State = UploadState.Success,
            Url = m.url
        })?.ToList();
    }

    private List<UploadFileItem> _fileList = new();
    private const int ChunkSize = 1 * 1024 * 1024; // 1 MB

    private async Task UploadFiles(InputFileChangeEventArgs e)
    {
        foreach (var file in e.GetMultipleFiles())
        {
            var fileItem = new UploadFileItem
            {
                Size = file.Size,
                State = UploadState.Success,
                FileName = file.Name,
                Url = string.Empty
            };
            _fileList.Add(fileItem);
            await UploadFileChunked(file);
            fileItem.State = UploadState.Success;
        }
    }

    private async Task UploadFileChunked(IBrowserFile file)
    {
        if (file == null)
        {
            await _MessageService.Error("No file data found.");
            return;
        }

        var fileName = file.Name;
        var url = string.Empty;
        try
        {
            var totalChunks = (int)Math.Ceiling((double)file.Size / ChunkSize);
            var guid = Guid.NewGuid().ToString();
            var buffer = new byte[ChunkSize];
            await using (var stream = file.OpenReadStream())
            {
                for (var chunk = 0; chunk < totalChunks; chunk++)
                {
                    var readBytes = await stream.ReadAsync(buffer, 0, ChunkSize);
                    var formData = new MultipartFormDataContent();
                    formData.Add(new ByteArrayContent(buffer, 0, readBytes), "file", fileName);
                    formData.Add(new StringContent(fileName), "name");
                    formData.Add(new StringContent(file.Size.ToString()), "size");
                    formData.Add(new StringContent(chunk.ToString()), "chunk");
                    formData.Add(new StringContent(totalChunks.ToString()), "chunks");
                    formData.Add(new StringContent(guid), "guid");
                    formData.Add(new StringContent(Id ?? "temp"), "id");
                    var jsonOption = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase, //使用PascalCase格式
                        Encoder = JavaScriptEncoder.Create(UnicodeRanges.All) //支持中文编码
                    };
                    var response = await _StateService.PostAsync("api/Core/Upload", formData);
                    var json = JsonSerializer.Deserialize<JsonResponse>(await response.Content.ReadAsStringAsync(), jsonOption);
                    url = json.Url;
                    if (!string.Equals(json.State, "success", StringComparison.InvariantCultureIgnoreCase))
                    {
                        await _MessageService.Error("File upload failed.");
                        return;
                    }
                }
            }

            ImageList.Add(new UploadFileViewModel
            {
                name = fileName,
                url = url
            });
        }
        catch (Exception ex)
        {
            _fileList.Add(new UploadFileItem
            {
                FileName = fileName,
                State = UploadState.Fail,
            });
            await _MessageService.Error($"Error: {ex.Message}");
        }
    }

    private Task<bool> OnRemove(UploadFileItem file)
    {
        _fileList.Remove(file);
        return Task.FromResult(true);
    }

    private class JsonResponse
    {
        public string State { get; set; }
        public string Msg { get; set; }
        public string Url { get; set; }
    }

}