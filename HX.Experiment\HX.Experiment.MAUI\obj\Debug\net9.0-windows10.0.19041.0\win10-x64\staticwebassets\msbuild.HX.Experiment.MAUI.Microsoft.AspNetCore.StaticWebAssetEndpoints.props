﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="css/app.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\app.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BvrB0w/3EIby9jmU\u002B70rH\u002BjRyq0p5AvDM/U3sMcjKQ0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2772"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022\u002BvrB0w/3EIby9jmU\u002B70rH\u002BjRyq0p5AvDM/U3sMcjKQ0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 18 Jul 2025 06:07:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="css/app.umtkqu2ib7.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\app.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"umtkqu2ib7"},{"Name":"integrity","Value":"sha256-\u002BvrB0w/3EIby9jmU\u002B70rH\u002BjRyq0p5AvDM/U3sMcjKQ0="},{"Name":"label","Value":"css/app.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2772"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022\u002BvrB0w/3EIby9jmU\u002B70rH\u002BjRyq0p5AvDM/U3sMcjKQ0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 18 Jul 2025 06:07:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Fri, 18 Jul 2025 06:07:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Fri, 18 Jul 2025 06:07:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="HX.Experiment.MAUI.5vcnc68aiq.styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\HX.Experiment.MAUI.styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5vcnc68aiq"},{"Name":"integrity","Value":"sha256-H/s6RUKGdzqPlKEFG4cOrwps7SP1DM6OEtIE3AdTL60="},{"Name":"label","Value":"HX.Experiment.MAUI.styles.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"251"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022H/s6RUKGdzqPlKEFG4cOrwps7SP1DM6OEtIE3AdTL60=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 09:14:58 GMT"},{"Name":"Link","Value":"\u003C_content/HX.Experiment.Shared/HX.Experiment.Shared.rr2qxwjego.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022, \u003C_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022, \u003C_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="HX.Experiment.MAUI.styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\HX.Experiment.MAUI.styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H/s6RUKGdzqPlKEFG4cOrwps7SP1DM6OEtIE3AdTL60="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"251"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022H/s6RUKGdzqPlKEFG4cOrwps7SP1DM6OEtIE3AdTL60=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 09:14:58 GMT"},{"Name":"Link","Value":"\u003C_content/HX.Experiment.Shared/HX.Experiment.Shared.rr2qxwjego.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022, \u003C_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022, \u003C_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/check-bold.ln0yay0zn2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\check-bold.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ln0yay0zn2"},{"Name":"integrity","Value":"sha256-L/abwQgX1bLGXXRPk6pizaRI7SpBzX/v2Eu3wU05Ik4="},{"Name":"label","Value":"images/check-bold.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1237"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022L/abwQgX1bLGXXRPk6pizaRI7SpBzX/v2Eu3wU05Ik4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Oct 2024 02:11:59 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/check-bold.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\check-bold.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L/abwQgX1bLGXXRPk6pizaRI7SpBzX/v2Eu3wU05Ik4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1237"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022L/abwQgX1bLGXXRPk6pizaRI7SpBzX/v2Eu3wU05Ik4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Oct 2024 02:11:59 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/check-circle-outline.q06nhyzej9.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\check-circle-outline.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q06nhyzej9"},{"Name":"integrity","Value":"sha256-Oqdrs9a8g01ROiSd6xTjQbtnG43P\u002Bpso6oHbRzyMC40="},{"Name":"label","Value":"images/check-circle-outline.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"267"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Oqdrs9a8g01ROiSd6xTjQbtnG43P\u002Bpso6oHbRzyMC40=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Oct 2024 01:54:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/check-circle-outline.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\check-circle-outline.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Oqdrs9a8g01ROiSd6xTjQbtnG43P\u002Bpso6oHbRzyMC40="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"267"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Oqdrs9a8g01ROiSd6xTjQbtnG43P\u002Bpso6oHbRzyMC40=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Oct 2024 01:54:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/emotion.0cbsdby4d0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\emotion.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0cbsdby4d0"},{"Name":"integrity","Value":"sha256-YIEMaoCYnBldcKEOLC6899q\u002BYRb0maNMKB9sef/m2lM="},{"Name":"label","Value":"images/emotion.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"42408"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022YIEMaoCYnBldcKEOLC6899q\u002BYRb0maNMKB9sef/m2lM=\u0022"},{"Name":"Last-Modified","Value":"Tue, 08 Apr 2025 23:49:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/emotion.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\emotion.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YIEMaoCYnBldcKEOLC6899q\u002BYRb0maNMKB9sef/m2lM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"42408"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022YIEMaoCYnBldcKEOLC6899q\u002BYRb0maNMKB9sef/m2lM=\u0022"},{"Name":"Last-Modified","Value":"Tue, 08 Apr 2025 23:49:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head.bllcxyn7f8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bllcxyn7f8"},{"Name":"integrity","Value":"sha256-T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA="},{"Name":"label","Value":"images/head.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"110681"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:52:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"110681"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:52:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/huanzhe.mqmzn8h04r.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\huanzhe.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mqmzn8h04r"},{"Name":"integrity","Value":"sha256-IvvjHfWeqjSsI2qFGIC3ZbV98xaraNNtdcKMrBqmBzQ="},{"Name":"label","Value":"images/head/huanzhe.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"760"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IvvjHfWeqjSsI2qFGIC3ZbV98xaraNNtdcKMrBqmBzQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/huanzhe.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\huanzhe.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IvvjHfWeqjSsI2qFGIC3ZbV98xaraNNtdcKMrBqmBzQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"760"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IvvjHfWeqjSsI2qFGIC3ZbV98xaraNNtdcKMrBqmBzQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/kaohejiance.7qtuas0pqm.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\kaohejiance.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7qtuas0pqm"},{"Name":"integrity","Value":"sha256-AZSZnTBYd8SVwpx7syFsYamsFosEoeXslfSx8Baz0GI="},{"Name":"label","Value":"images/head/kaohejiance.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"961"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AZSZnTBYd8SVwpx7syFsYamsFosEoeXslfSx8Baz0GI=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/kaohejiance.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\kaohejiance.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AZSZnTBYd8SVwpx7syFsYamsFosEoeXslfSx8Baz0GI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"961"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AZSZnTBYd8SVwpx7syFsYamsFosEoeXslfSx8Baz0GI=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/shebeixinxi.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\shebeixinxi.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kTDxtqXQjmlsELGE4Q9uuiVM9CtSxi0aEZqchXrUx\u002BQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"385"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kTDxtqXQjmlsELGE4Q9uuiVM9CtSxi0aEZqchXrUx\u002BQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/shebeixinxi.x42bh0uf8h.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\shebeixinxi.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x42bh0uf8h"},{"Name":"integrity","Value":"sha256-kTDxtqXQjmlsELGE4Q9uuiVM9CtSxi0aEZqchXrUx\u002BQ="},{"Name":"label","Value":"images/head/shebeixinxi.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"385"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kTDxtqXQjmlsELGE4Q9uuiVM9CtSxi0aEZqchXrUx\u002BQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/shuju.he8dyim5vd.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\shuju.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"he8dyim5vd"},{"Name":"integrity","Value":"sha256-NwpqYWvQ\u002BsrIXzdhSC1GK6bUES1tVqQqB\u002BLYxJY2law="},{"Name":"label","Value":"images/head/shuju.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"719"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NwpqYWvQ\u002BsrIXzdhSC1GK6bUES1tVqQqB\u002BLYxJY2law=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:38 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/shuju.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\shuju.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NwpqYWvQ\u002BsrIXzdhSC1GK6bUES1tVqQqB\u002BLYxJY2law="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"719"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NwpqYWvQ\u002BsrIXzdhSC1GK6bUES1tVqQqB\u002BLYxJY2law=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:38 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/xitong.jrhehcxaga.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\xitong.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jrhehcxaga"},{"Name":"integrity","Value":"sha256-b0KVKknJyepSRxddF1EfgjIMaEWrb44LVWGWP/qm9dk="},{"Name":"label","Value":"images/head/xitong.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"814"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022b0KVKknJyepSRxddF1EfgjIMaEWrb44LVWGWP/qm9dk=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:38 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/xitong.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\xitong.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b0KVKknJyepSRxddF1EfgjIMaEWrb44LVWGWP/qm9dk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"814"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022b0KVKknJyepSRxddF1EfgjIMaEWrb44LVWGWP/qm9dk=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:38 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/zhanghao.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\zhanghao.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RSxhG3LEA5UymXFxXpBKvuOzqQMTwyULRcvGLDtjv/s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"653"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RSxhG3LEA5UymXFxXpBKvuOzqQMTwyULRcvGLDtjv/s=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/head/zhanghao.tmtt1g5vjc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\head\zhanghao.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tmtt1g5vjc"},{"Name":"integrity","Value":"sha256-RSxhG3LEA5UymXFxXpBKvuOzqQMTwyULRcvGLDtjv/s="},{"Name":"label","Value":"images/head/zhanghao.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"653"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RSxhG3LEA5UymXFxXpBKvuOzqQMTwyULRcvGLDtjv/s=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 11:39:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/add.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\add.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G0hGms0sWBVRU8L4RpOgYD2B3CXlxt0geAGne9mPMGk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2870"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022G0hGms0sWBVRU8L4RpOgYD2B3CXlxt0geAGne9mPMGk=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/add.rchd7ayzc8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\add.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rchd7ayzc8"},{"Name":"integrity","Value":"sha256-G0hGms0sWBVRU8L4RpOgYD2B3CXlxt0geAGne9mPMGk="},{"Name":"label","Value":"images/icon/add.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2870"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022G0hGms0sWBVRU8L4RpOgYD2B3CXlxt0geAGne9mPMGk=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/electricity-full.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\electricity-full.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eNvENda8KTgVu92oHRCVCFyfcJ/1jGogorz6Qw\u002BcWGk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"330"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022eNvENda8KTgVu92oHRCVCFyfcJ/1jGogorz6Qw\u002BcWGk=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/electricity-full.szj9i35yp2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\electricity-full.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"szj9i35yp2"},{"Name":"integrity","Value":"sha256-eNvENda8KTgVu92oHRCVCFyfcJ/1jGogorz6Qw\u002BcWGk="},{"Name":"label","Value":"images/icon/electricity-full.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"330"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022eNvENda8KTgVu92oHRCVCFyfcJ/1jGogorz6Qw\u002BcWGk=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/free_fill.0ipe1d3brj.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\free_fill.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0ipe1d3brj"},{"Name":"integrity","Value":"sha256-mF2/PsmpJp1BSimGrb\u002B673ZVzA5oLl/gUUuVMXDXAZ0="},{"Name":"label","Value":"images/icon/free_fill.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"674"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mF2/PsmpJp1BSimGrb\u002B673ZVzA5oLl/gUUuVMXDXAZ0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/free_fill.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\free_fill.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mF2/PsmpJp1BSimGrb\u002B673ZVzA5oLl/gUUuVMXDXAZ0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"674"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mF2/PsmpJp1BSimGrb\u002B673ZVzA5oLl/gUUuVMXDXAZ0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/is_charge.09zzm8sby1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\is_charge.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"09zzm8sby1"},{"Name":"integrity","Value":"sha256-NBNrmfH60LpQ4yGWFSjDJbzOnCiiMJ5KneF9ru5o95g="},{"Name":"label","Value":"images/icon/is_charge.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"640"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NBNrmfH60LpQ4yGWFSjDJbzOnCiiMJ5KneF9ru5o95g=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/is_charge.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\is_charge.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NBNrmfH60LpQ4yGWFSjDJbzOnCiiMJ5KneF9ru5o95g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"640"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NBNrmfH60LpQ4yGWFSjDJbzOnCiiMJ5KneF9ru5o95g=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/jiazaizhong.pggo555lzu.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\jiazaizhong.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pggo555lzu"},{"Name":"integrity","Value":"sha256-p63H3TVC9RiaPEpcvtfXt9bnRLQjJ5d0ca3o7ovXcEQ="},{"Name":"label","Value":"images/icon/jiazaizhong.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1318"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022p63H3TVC9RiaPEpcvtfXt9bnRLQjJ5d0ca3o7ovXcEQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/jiazaizhong.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\jiazaizhong.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p63H3TVC9RiaPEpcvtfXt9bnRLQjJ5d0ca3o7ovXcEQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1318"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022p63H3TVC9RiaPEpcvtfXt9bnRLQjJ5d0ca3o7ovXcEQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/jinggao.51r8h8dof1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\jinggao.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"51r8h8dof1"},{"Name":"integrity","Value":"sha256-2dsLILgrcPZ\u002BDp4PM0Z2YqI0RnsjJPujhb\u002BL9R66Omg="},{"Name":"label","Value":"images/icon/jinggao.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"978"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222dsLILgrcPZ\u002BDp4PM0Z2YqI0RnsjJPujhb\u002BL9R66Omg=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/jinggao.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\jinggao.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2dsLILgrcPZ\u002BDp4PM0Z2YqI0RnsjJPujhb\u002BL9R66Omg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"978"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222dsLILgrcPZ\u002BDp4PM0Z2YqI0RnsjJPujhb\u002BL9R66Omg=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/lazy.d3tr8885ft.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\lazy.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d3tr8885ft"},{"Name":"integrity","Value":"sha256-CbTY3nR96oVoONTyuGR1ShM80H69ThmzYN/Ev55Eb/c="},{"Name":"label","Value":"images/icon/lazy.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5497"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CbTY3nR96oVoONTyuGR1ShM80H69ThmzYN/Ev55Eb/c=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/lazy.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\lazy.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CbTY3nR96oVoONTyuGR1ShM80H69ThmzYN/Ev55Eb/c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5497"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CbTY3nR96oVoONTyuGR1ShM80H69ThmzYN/Ev55Eb/c=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/<EMAIL>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\<EMAIL>'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l7ujb9yvl2"},{"Name":"integrity","Value":"sha256-sa6OajYscx9doMTAmDsQZyNZihUaqJmG69HWfvF3BMs="},{"Name":"label","Value":"images/icon/<EMAIL>"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3477"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sa6OajYscx9doMTAmDsQZyNZihUaqJmG69HWfvF3BMs=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/<EMAIL>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\<EMAIL>'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sa6OajYscx9doMTAmDsQZyNZihUaqJmG69HWfvF3BMs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3477"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sa6OajYscx9doMTAmDsQZyNZihUaqJmG69HWfvF3BMs=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/loading.0xjn1hvh0b.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\loading.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0xjn1hvh0b"},{"Name":"integrity","Value":"sha256-lHlgMa\u002B5tQsbHfzFESq9Vf66mOGsqe2uNxz4RPVzV0Q="},{"Name":"label","Value":"images/icon/loading.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1817"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lHlgMa\u002B5tQsbHfzFESq9Vf66mOGsqe2uNxz4RPVzV0Q=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/loading.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\loading.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lHlgMa\u002B5tQsbHfzFESq9Vf66mOGsqe2uNxz4RPVzV0Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1817"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lHlgMa\u002B5tQsbHfzFESq9Vf66mOGsqe2uNxz4RPVzV0Q=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/play.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\play.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uVpVnrXR7\u002Be8Cq4mjGl29oeE066WHRM7LKdr1A53o20="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1817"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022uVpVnrXR7\u002Be8Cq4mjGl29oeE066WHRM7LKdr1A53o20=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/play.vyylxc59yc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\play.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vyylxc59yc"},{"Name":"integrity","Value":"sha256-uVpVnrXR7\u002Be8Cq4mjGl29oeE066WHRM7LKdr1A53o20="},{"Name":"label","Value":"images/icon/play.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1817"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022uVpVnrXR7\u002Be8Cq4mjGl29oeE066WHRM7LKdr1A53o20=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/shalou.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\shalou.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a01FLNfxFQ9Ah\u002BcEhDHlY32GPoF0tdVG1zJaywY9DT4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"335"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022a01FLNfxFQ9Ah\u002BcEhDHlY32GPoF0tdVG1zJaywY9DT4=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/shalou.vvbptioo99.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\shalou.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vvbptioo99"},{"Name":"integrity","Value":"sha256-a01FLNfxFQ9Ah\u002BcEhDHlY32GPoF0tdVG1zJaywY9DT4="},{"Name":"label","Value":"images/icon/shalou.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"335"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022a01FLNfxFQ9Ah\u002BcEhDHlY32GPoF0tdVG1zJaywY9DT4=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/stack.0a26h65scb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\stack.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0a26h65scb"},{"Name":"integrity","Value":"sha256-CCoqfrqyeclohAc7XqEO5xGHABDrL5GcCY8XkvcBMVQ="},{"Name":"label","Value":"images/icon/stack.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5740"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CCoqfrqyeclohAc7XqEO5xGHABDrL5GcCY8XkvcBMVQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/stack.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\stack.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CCoqfrqyeclohAc7XqEO5xGHABDrL5GcCY8XkvcBMVQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5740"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CCoqfrqyeclohAc7XqEO5xGHABDrL5GcCY8XkvcBMVQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/today.a8arm6zg0e.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\today.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a8arm6zg0e"},{"Name":"integrity","Value":"sha256-drWNeQami\u002BTi9dqdFrHmNYBWyyfSLYvrl6uTuCoM6L4="},{"Name":"label","Value":"images/icon/today.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1921"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022drWNeQami\u002BTi9dqdFrHmNYBWyyfSLYvrl6uTuCoM6L4=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/today.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\today.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-drWNeQami\u002BTi9dqdFrHmNYBWyyfSLYvrl6uTuCoM6L4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1921"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022drWNeQami\u002BTi9dqdFrHmNYBWyyfSLYvrl6uTuCoM6L4=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:54:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/<EMAIL>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\<EMAIL>'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4x4d8q45px"},{"Name":"integrity","Value":"sha256-KIq7sZeW/Y0JdK6TwSO7CgVk7d8lGD53T5WNBMq\u002B3UE="},{"Name":"label","Value":"images/icon/<EMAIL>"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1121"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KIq7sZeW/Y0JdK6TwSO7CgVk7d8lGD53T5WNBMq\u002B3UE=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/<EMAIL>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\<EMAIL>'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KIq7sZeW/Y0JdK6TwSO7CgVk7d8lGD53T5WNBMq\u002B3UE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1121"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KIq7sZeW/Y0JdK6TwSO7CgVk7d8lGD53T5WNBMq\u002B3UE=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/yiwancheng.l5w3t41efd.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\yiwancheng.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l5w3t41efd"},{"Name":"integrity","Value":"sha256-iSJeZ\u002BtiiNw7bRw3o1pO6B3bkq4Rzwaa0VMtJ6gWZms="},{"Name":"label","Value":"images/icon/yiwancheng.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"858"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iSJeZ\u002BtiiNw7bRw3o1pO6B3bkq4Rzwaa0VMtJ6gWZms=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/icon/yiwancheng.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\icon\yiwancheng.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iSJeZ\u002BtiiNw7bRw3o1pO6B3bkq4Rzwaa0VMtJ6gWZms="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"858"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iSJeZ\u002BtiiNw7bRw3o1pO6B3bkq4Rzwaa0VMtJ6gWZms=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 01:01:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/large_button_back.jxs0d11u4k.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\large_button_back.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jxs0d11u4k"},{"Name":"integrity","Value":"sha256-iQwJnAcLK\u002BXmnsfR8ph0iqidmHR0i6FzXH7pUIvNoqY="},{"Name":"label","Value":"images/large_button_back.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20375"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iQwJnAcLK\u002BXmnsfR8ph0iqidmHR0i6FzXH7pUIvNoqY=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:35:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/large_button_back.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\large_button_back.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iQwJnAcLK\u002BXmnsfR8ph0iqidmHR0i6FzXH7pUIvNoqY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20375"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iQwJnAcLK\u002BXmnsfR8ph0iqidmHR0i6FzXH7pUIvNoqY=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 Aug 2024 10:35:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/layout_header.bllcxyn7f8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\layout_header.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bllcxyn7f8"},{"Name":"integrity","Value":"sha256-T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA="},{"Name":"label","Value":"images/layout_header.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"110681"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 09:01:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/layout_header.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\layout_header.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"110681"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022T7Gdxd1e2MU7Z9YnUqk4l5AtnjTFQB6h\u002BVP9\u002Bexv8EA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 09:01:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/login_bg.2zye9ddk19.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\login_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2zye9ddk19"},{"Name":"integrity","Value":"sha256-jnVBmDvqo\u002B0w3pyyOaU7nuO87Eanl/m3pVvh5m6NjRo="},{"Name":"label","Value":"images/login_bg.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1494984"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022jnVBmDvqo\u002B0w3pyyOaU7nuO87Eanl/m3pVvh5m6NjRo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 08:32:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/login_bg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\login_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jnVBmDvqo\u002B0w3pyyOaU7nuO87Eanl/m3pVvh5m6NjRo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1494984"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022jnVBmDvqo\u002B0w3pyyOaU7nuO87Eanl/m3pVvh5m6NjRo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 08:32:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/logo.2gjsjwvbkm.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2gjsjwvbkm"},{"Name":"integrity","Value":"sha256-jovkUKeKtFnYnezC9x4JGZPEjIraYGfgHe5BqjZQnx4="},{"Name":"label","Value":"images/logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17216"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022jovkUKeKtFnYnezC9x4JGZPEjIraYGfgHe5BqjZQnx4=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 09:40:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jovkUKeKtFnYnezC9x4JGZPEjIraYGfgHe5BqjZQnx4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"17216"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022jovkUKeKtFnYnezC9x4JGZPEjIraYGfgHe5BqjZQnx4=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 09:40:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/logo_only.kx2u7j7ilc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo_only.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kx2u7j7ilc"},{"Name":"integrity","Value":"sha256-qBLONP/u1FqqoRpZfFOaHsuf21pb5X2r\u002BXB5lI6qGrs="},{"Name":"label","Value":"images/logo_only.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3022"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qBLONP/u1FqqoRpZfFOaHsuf21pb5X2r\u002BXB5lI6qGrs=\u0022"},{"Name":"Last-Modified","Value":"Tue, 22 Oct 2024 09:14:55 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/logo_only.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo_only.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qBLONP/u1FqqoRpZfFOaHsuf21pb5X2r\u002BXB5lI6qGrs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3022"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qBLONP/u1FqqoRpZfFOaHsuf21pb5X2r\u002BXB5lI6qGrs=\u0022"},{"Name":"Last-Modified","Value":"Tue, 22 Oct 2024 09:14:55 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/logo_white.4qm3beyatb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo_white.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4qm3beyatb"},{"Name":"integrity","Value":"sha256-zBjZobaYwlFS8cuZPatyi38AbMK\u002B/TS6sIkVuLAzh8Q="},{"Name":"label","Value":"images/logo_white.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4468"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022zBjZobaYwlFS8cuZPatyi38AbMK\u002B/TS6sIkVuLAzh8Q=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 00:11:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/logo_white.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo_white.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zBjZobaYwlFS8cuZPatyi38AbMK\u002B/TS6sIkVuLAzh8Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4468"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022zBjZobaYwlFS8cuZPatyi38AbMK\u002B/TS6sIkVuLAzh8Q=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 00:11:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/main_bg.8u0fk6t6a7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\main_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8u0fk6t6a7"},{"Name":"integrity","Value":"sha256-AG77JPVQQi05H/wKqMj5P4\u002BDNii\u002BysVjTtngaLuQ6hc="},{"Name":"label","Value":"images/main_bg.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"655215"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AG77JPVQQi05H/wKqMj5P4\u002BDNii\u002BysVjTtngaLuQ6hc=\u0022"},{"Name":"Last-Modified","Value":"Fri, 11 Jul 2025 10:11:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/main_bg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\main_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AG77JPVQQi05H/wKqMj5P4\u002BDNii\u002BysVjTtngaLuQ6hc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"655215"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AG77JPVQQi05H/wKqMj5P4\u002BDNii\u002BysVjTtngaLuQ6hc=\u0022"},{"Name":"Last-Modified","Value":"Fri, 11 Jul 2025 10:11:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/mima.cjgwf6xbfq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\mima.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cjgwf6xbfq"},{"Name":"integrity","Value":"sha256-SM3zDNNVudP3Jx3n0cFmBm\u002B9v4Se0FizQTUvoJ73q3w="},{"Name":"label","Value":"images/mima.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"438"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SM3zDNNVudP3Jx3n0cFmBm\u002B9v4Se0FizQTUvoJ73q3w=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 08:54:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/mima.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\mima.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SM3zDNNVudP3Jx3n0cFmBm\u002B9v4Se0FizQTUvoJ73q3w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"438"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SM3zDNNVudP3Jx3n0cFmBm\u002B9v4Se0FizQTUvoJ73q3w=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 08:54:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/zhanghao.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\zhanghao.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kDxRMfsqKE0xlB91BOgXwy/anqNUkBzlnhRESlwqLuo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"516"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kDxRMfsqKE0xlB91BOgXwy/anqNUkBzlnhRESlwqLuo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 08:54:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="images/zhanghao.stmkg355uq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\zhanghao.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"stmkg355uq"},{"Name":"integrity","Value":"sha256-kDxRMfsqKE0xlB91BOgXwy/anqNUkBzlnhRESlwqLuo="},{"Name":"label","Value":"images/zhanghao.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"516"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kDxRMfsqKE0xlB91BOgXwy/anqNUkBzlnhRESlwqLuo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 Aug 2024 08:54:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="index.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-P3iMkfIJ2WuYUgIGcn/SNy0u/6lJVT8l6dRSEy7IsxI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2067"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022P3iMkfIJ2WuYUgIGcn/SNy0u/6lJVT8l6dRSEy7IsxI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 25 Jul 2025 08:42:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="index.l7rw681l30.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l7rw681l30"},{"Name":"integrity","Value":"sha256-P3iMkfIJ2WuYUgIGcn/SNy0u/6lJVT8l6dRSEy7IsxI="},{"Name":"label","Value":"index.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2067"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022P3iMkfIJ2WuYUgIGcn/SNy0u/6lJVT8l6dRSEy7IsxI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 25 Jul 2025 08:42:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>