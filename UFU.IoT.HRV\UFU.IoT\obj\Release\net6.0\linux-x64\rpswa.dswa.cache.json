{"GlobalPropertiesHash": "a/KKqzikHOHsutozGMEdOIqBmzQl6QhZyrjurmUaCtE=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8sosVOMxU4y89cpKosfidnApteZgMayeg5h1I+lon5k=", "Zq39xesxn7kqlpUcrmZ9czYxlldnjaFUSneWrE87zfA=", "tiSzHtvxaqU8t5GDZvgdvYHjfWrgHRs0eR6vZfwGhCk=", "7/bxxIlUBDWEEw0OApQErKgQ/U7AJOBZq2spV3ECDt0=", "mPFKvfX64uGk7n7dcHSdybpZMEIcvKpXSIpHSSFKKIE=", "AsfxjKIb10oH5VgQBZS1z/MDhT7Tvh/I5FxpTOpYUbQ=", "/gR3gclPhPwudAAgKjYs+7k9EIOXg2+KwhqR9SNvq3o=", "T3ndSQSBzNzCXwiUcezaMvQoak88BLQZiZr42wHoOT0=", "L5P2PTKD8wCBAFPrYaVruEgiyMsaC0PmBtvvpP138/8=", "7deH/BJicCwmU4ytcGhD9HSHnEs3kvlsVe9Ok45yrNo=", "D91taFCdQDD7H8/OAp+bnrh2YTQIw0NqNhyv6ciP0f8="], "CachedAssets": {"8sosVOMxU4y89cpKosfidnApteZgMayeg5h1I+lon5k=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\airkiss.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "airkiss#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dkhcs8bo8w", "Integrity": "7U8PKkWEtxh46JI8m92UvW9/sJUa8od7XPjz34F0sIo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\airkiss.html", "FileLength": 1848, "LastWriteTime": "2024-07-08T02:49:45.8248161+00:00"}, "Zq39xesxn7kqlpUcrmZ9czYxlldnjaFUSneWrE87zfA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ktkzw6ryo0", "Integrity": "KHxAMD9U6cNFShWxW2jMWJWeTsyK2Hqw2i4rZ7/Gqag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 7022, "LastWriteTime": "2024-07-08T02:49:45.8248161+00:00"}, "tiSzHtvxaqU8t5GDZvgdvYHjfWrgHRs0eR6vZfwGhCk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\index.min.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "index.min#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tfad1g4lu9", "Integrity": "GddG0nOWlVlryveJcbKKl1bvUlqNuy5lq53Z2UFrobU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.min.html", "FileLength": 6064, "LastWriteTime": "2024-07-08T02:49:45.8408211+00:00"}, "7/bxxIlUBDWEEw0OApQErKgQ/U7AJOBZq2spV3ECDt0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\iotmsg.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "iotmsg#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "teovowdu0s", "Integrity": "VXXh0Rzha8VFqrdqewwC+gB+dwC/ZBg5z9BKFU0onkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\iotmsg.html", "FileLength": 1441, "LastWriteTime": "2024-07-08T02:49:45.8408211+00:00"}, "mPFKvfX64uGk7n7dcHSdybpZMEIcvKpXSIpHSSFKKIE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfig301.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "netconfig301#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "006o1sg79w", "Integrity": "YIWSXEXzJo076tcPW3RBc1/O+I3nNtzxaeFJAOnGxvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\netconfig301.html", "FileLength": 112, "LastWriteTime": "2024-07-08T02:49:45.8418154+00:00"}, "AsfxjKIb10oH5VgQBZS1z/MDhT7Tvh/I5FxpTOpYUbQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\netconfigindex.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "netconfigindex#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ganed4i40t", "Integrity": "mLWRKobeKfXvUZws5QBUObnAPc/JwyRQj7DV94PGS1E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\netconfigindex.html", "FileLength": 11557, "LastWriteTime": "2024-07-08T02:49:45.858821+00:00"}, "/gR3gclPhPwudAAgKjYs+7k9EIOXg2+KwhqR9SNvq3o=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\websocket.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "websocket#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3n88wbcuej", "Integrity": "v0kJ9rMP7HYrCXbblS1SQFeW9SZ1qeBOSY9Af6W5UVw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websocket.html", "FileLength": 2405, "LastWriteTime": "2024-07-08T02:49:45.858821+00:00"}, "T3ndSQSBzNzCXwiUcezaMvQoak88BLQZiZr42wHoOT0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "wificonfig#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "giqexvpcp8", "Integrity": "SFVhxfZDjwjA1JSZpFTCf2GfJ5ycmcn0hx+zacrXFxY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\wificonfig.html", "FileLength": 10251, "LastWriteTime": "2024-07-08T02:49:45.8238221+00:00"}, "L5P2PTKD8wCBAFPrYaVruEgiyMsaC0PmBtvvpP138/8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\wificonfig.min.html", "SourceId": "UFU.IoT", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\wwwroot\\", "BasePath": "_content/UFU.IoT", "RelativePath": "wificonfig.min#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aown4g87o9", "Integrity": "1vbVz2qgwqG/ZY7c0xnVkNgQvQvpywUCIcFMLsKrIQk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\wificonfig.min.html", "FileLength": 5962, "LastWriteTime": "2024-07-08T02:49:45.8238221+00:00"}}, "CachedCopyCandidates": {}}