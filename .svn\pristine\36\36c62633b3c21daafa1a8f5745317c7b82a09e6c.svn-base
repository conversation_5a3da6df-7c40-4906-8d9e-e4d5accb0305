﻿@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using UFU.CoreFX.Shared
@using UFU.CoreFX.Utils

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    if (!Enum.TryParse<Microsoft.AspNetCore.Mvc.Rendering.RenderMode>(Env.Configuration["Blazor:RenderMode"], true, out var renderMode))
    {
        renderMode = Microsoft.AspNetCore.Mvc.Rendering.RenderMode.WebAssemblyPrerendered;
        string u = this.Context.Request.Headers["USER-AGENT"];
        var b = new System.Text.RegularExpressions.Regex(@"(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino", System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Multiline);
        var v = new System.Text.RegularExpressions.Regex(@"1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-", System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Multiline);
        if ((b.IsMatch(u) || v.IsMatch(u.Substring(0, 4))))
        {
            renderMode = Microsoft.AspNetCore.Mvc.Rendering.RenderMode.ServerPrerendered;
        }
    }
    string jsFile = renderMode.ToString().Contains("Server") ? "server" : "webassembly";
}
<!DOCTYPE html>
<html lang="en">
<head>
    <base href="~/" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link href="_content/AntDesign/css/ant-design-blazor.css" rel="stylesheet">
    <link href="_content/Masa.Blazor/css/masa-blazor.min.css?@DateTime.Now" rel="stylesheet" />
    <link href="_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.css" rel="stylesheet" />
    <link href="_content/UFU.CoreFX.Shared/css/app.css?@DateTime.Now" rel="stylesheet" />
 
    <link href="_content/HX.HRV.Web/css/materialdesign/v7.1.96/css/materialdesignicons.min.css?@DateTime.Now" rel="stylesheet">
    <link href="_content/HX.HRV.Web/css/hx.hrv.web.css?@DateTime.Now" rel="stylesheet" />
    <link href="/css/hx.hrv.sci.web.css?@DateTime.Now" rel="stylesheet" />
    @foreach (var name in BlazorApp.BlazorAssemblies.Select(m => m.GetName().Name))
    {
        <link href="_content/@name/@(name).bundle.scp.css" rel="stylesheet" />
    }
    <component type="typeof(HeadOutlet)" render-mode="@renderMode" />
</head>
<body>
  
    @if (BlazorApp.AppType == null || BlazorApp.AppAssembly == null)
    {
        <h3>未设置BlazorApp.AppType或BlazorApp.AppAssembly</h3>
    }
    else
    {
        <component type="BlazorApp.AppType" render-mode="@renderMode" />
        <div id="app">
            @RenderBody()
        </div>
        <div id="blazor-error-ui">
            <environment include="Staging,Production">
                An error has occurred. This application may no longer respond until reloaded.
            </environment>
            <environment include="Development">
                An unhandled exception has occurred. See browser dev tools for details.
            </environment>
            <a href="" class="reload">Reload</a>
            <a class="dismiss">🗙</a>
        </div>
    
        <script src="_content/AntDesign/js/ant-design-blazor.js"></script>
        <script src="_content/Masa.Blazor/js/masa-blazor.js?@DateTime.Now"></script>
        <script src="_framework/blazor.@(jsFile).js" autostart="false"></script>

        
        <script src="_content/UFU.CoreFX.Shared/js/cookieStore.js"></script>
        <script src="_content/UFU.CoreFX.Shared/js/utils.js"></script>
        <script src="_content/UFU.CoreFX.Shared/js/createCanvas.js"></script>
        
 
        <script src="_content/HX.HRV.Web/scripts/echarts5.5.1.js"></script>
        <script src="_content/HX.HRV.Web/scripts/echartsInterop.js"></script>
        <script src="_content/HX.HRV.Web/scripts/html2pdf.bundle.min.js"></script>
        <script src="_content/HX.HRV.Web/scripts/exportpdf.js?@DateTime.Now"></script>
        <script src="_content/HX.HRV.Web/scripts/realtime-echart.js?@DateTime.Now"></script>
        <script src="_content/HX.HRV.Web/scripts/HybridWebView.js?@DateTime.Now"></script>
        <script>
            Blazor.start({}).then(res => {
                document.getElementById("app").remove();
                //Blazor断开连接后自动重连
                Blazor.defaultReconnectionHandler._reconnectCallback = function (d) {
                    document.location.reload();
                }
            });
        </script>
    }
</body>
</html>




