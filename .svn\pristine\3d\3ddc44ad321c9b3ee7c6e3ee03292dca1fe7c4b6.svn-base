﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<!--<Nullable>enable</Nullable>-->
		<ImplicitUsings>enable</ImplicitUsings>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
		<DefaultAppHostRuntimeIdentifier>win-x64</DefaultAppHostRuntimeIdentifier>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<Nullable>enable</Nullable>
	</PropertyGroup>


	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="6.0.36" />
		<PackageReference Include="ScottPlot" Version="5.0.55" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="wwwroot\css\hx.hrv.css">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Include="..\.dockerignore">
	    <Link>.dockerignore</Link>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\HX.Encrypt\HX.Encrypt.csproj" />
	  <ProjectReference Include="..\HX.HRV.Shared\HX.HRV.Shared.csproj" />
	  <ProjectReference Include="..\HX.HRV.Web\HX.HRV.Web.csproj" />
	  <ProjectReference Include="..\HX.HRV.SCI.Shared\HX.HRV.SCI.Shared.csproj" />
	</ItemGroup>
</Project>
