﻿@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
<style>
    .customer-input .m-input__prepend-outer .m-label {
        width: 120px;
    }
</style>
<MCard Class="text-center ">
    <MCardTitle Style="background: linear-gradient(90deg, #bbd2ec, #c6ddf7);" Class="dialog-header text-center">
        <div class="hx-card-title">
            修改密码
        </div>
        <MButton OnClick="@CloseDialogEvent" Class="ml-auto" Icon>
            <MIcon>mdi-close</MIcon>
        </MButton>
    </MCardTitle>
    <MCardText Class="d-flex justify-center flex-column align-center ">
        <MRow Class="mt-2">
            <MCol Cols="12">
                <MTextField Class="customer-input" Type="password" Dense Solo
                            @bind-Value="Password" TValue="string">
                    <PrependContent>
                        <MLabel Class="mr-2">新密码:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
            <MCol Cols="12">
                <MTextField Class="customer-input" Type="password" Dense Solo
                            @bind-Value="RepPassword" TValue="string">
                    <PrependContent>
                        <MLabel Class="mr-2">确认新密码:</MLabel>
                    </PrependContent>
                </MTextField>
            </MCol>
        </MRow>

    </MCardText>
    <MCardActions Class="text-center d-flex justify-center justify-center">
        <MButton OnClick="HandleSubmit"
                 Class="blue linear-gradient customer-button"
                 Style="margin-top: 20px; width: 100px;color: white;  font-size: 1rem; border-radius: 5px;">
            确认
        </MButton>
    </MCardActions>

</MCard>


@code {
    [Parameter] public EventCallback CloseDialogEvent { get; set; }
    [Inject] private StateService state { get; set; }
    [Inject] private IPopupService PopupService { get; set; }
    public string Password { get; set; }
    public string RepPassword { get; set; }
    [Parameter] public UserInfo userData { get; set; }
    private MForm? _form;
    private bool valid;
    private List<Func<string?, StringBoolean>> valids => new() { v => RepPassword == Password ? true : "两次密码不一致" };

    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    private async Task HandleSubmit()
    {
        if (Password != RepPassword)
        {
            await PopupService.EnqueueSnackbarAsync("两次密码不一致", AlertTypes.Error);
            return;
        }


        var qs = new Dictionary<string, string> { { "userId", userData.Id }, { "password", Password } };
        var ChangeState = await state.PostAsJsonAsync<UserModel>("/api/v2/Core/Users/<USER>", null, qs);
        if (ChangeState.Success)
        {
            await PopupService.EnqueueSnackbarAsync("密码修改成功", AlertTypes.Success);
            await CloseDialogEvent.InvokeAsync();
        }
        else
        {
            await PopupService.EnqueueSnackbarAsync(ChangeState.Errors[0].Error, AlertTypes.Error);
        }
    }

}