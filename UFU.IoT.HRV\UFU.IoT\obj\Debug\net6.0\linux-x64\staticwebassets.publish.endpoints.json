{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "UFU.IoT.styles.css", "AssetFile": "UFU.IoT.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gf8oSQcWReuymcmUF8uRGF1LnSLqdYp2TrlXYDeBIwI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 06:58:33 GMT"}, {"Name": "Link", "Value": "<_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gf8oSQcWReuymcmUF8uRGF1LnSLqdYp2TrlXYDeBIwI="}]}, {"Route": "UFU.IoT.vo82sr88ek.styles.css", "AssetFile": "UFU.IoT.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gf8oSQcWReuymcmUF8uRGF1LnSLqdYp2TrlXYDeBIwI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 06:58:33 GMT"}, {"Name": "Link", "Value": "<_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vo82sr88ek"}, {"Name": "integrity", "Value": "sha256-gf8oSQcWReuymcmUF8uRGF1LnSLqdYp2TrlXYDeBIwI="}, {"Name": "label", "Value": "UFU.IoT.styles.css"}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.aliyun.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.aliyun.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "677631"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Mxkw87dSb+vr8tYuYfLp77q8lCjfzxcuK9wKLg+AQEA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mxkw87dSb+vr8tYuYfLp77q8lCjfzxcuK9wKLg+AQEA="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.aliyun.min.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.aliyun.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "554305"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Z9bQVNiWhTw2BlWjEe1+76NxTx6XfK5DiiLBZW9DB4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7Z9bQVNiWhTw2BlWjEe1+76NxTx6XfK5DiiLBZW9DB4="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.compact.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.compact.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "677551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aXHYzJIa0TVQ1fpoPap8BY9oLwkpobZpHaFkVDx+hcg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aXHYzJIa0TVQ1fpoPap8BY9oLwkpobZpHaFkVDx+hcg="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.compact.min.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.compact.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "553210"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AD+EmuN592nSv/8Yzx4kFfBe2oqGN5nKs1LtusB6owY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AD+EmuN592nSv/8Yzx4kFfBe2oqGN5nKs1LtusB6owY="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "677631"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Mxkw87dSb+vr8tYuYfLp77q8lCjfzxcuK9wKLg+AQEA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mxkw87dSb+vr8tYuYfLp77q8lCjfzxcuK9wKLg+AQEA="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.dark.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "691596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEPhAtCkdF9GKwrkDqOqso9dr3+X7u06VFRjZNTiog=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEPhAtCkdF9GKwrkDqOqso9dr3+X7u06VFRjZNTiog="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.dark.min.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.dark.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "565408"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WcntylJW/SdKXqnd+YXvvugK8lg6wBVSTb7UvEtkqdk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WcntylJW/SdKXqnd+YXvvugK8lg6wBVSTb7UvEtkqdk="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.min.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "554305"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Z9bQVNiWhTw2BlWjEe1+76NxTx6XfK5DiiLBZW9DB4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7Z9bQVNiWhTw2BlWjEe1+76NxTx6XfK5DiiLBZW9DB4="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.variable.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.variable.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "689965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bHdHU6Yv6H2wGuvxPNmSmr+hltkuQBSX3Q4mWZzb+Go=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bHdHU6Yv6H2wGuvxPNmSmr+hltkuQBSX3Q4mWZzb+Go="}]}, {"Route": "_content/AntDesign/css/ant-design-blazor.variable.min.css", "AssetFile": "_content/AntDesign/css/ant-design-blazor.variable.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "565596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y0DVnk27OQTSrds/pZtVM2nfSHHYTZgMJVQgzXoGCSU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y0DVnk27OQTSrds/pZtVM2nfSHHYTZgMJVQgzXoGCSU="}]}, {"Route": "_content/AntDesign/js/ant-design-blazor.js", "AssetFile": "_content/AntDesign/js/ant-design-blazor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "77036"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Yg2uDpRVGeT/8MCHKWyE5xiXt1YbXUThFvCZFOhlDaA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yg2uDpRVGeT/8MCHKWyE5xiXt1YbXUThFvCZFOhlDaA="}]}, {"Route": "_content/AntDesign/js/ant-design-blazor.js.map", "AssetFile": "_content/AntDesign/js/ant-design-blazor.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "272069"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7o6kalYNr+S4ecm2/aSrNvVG6V+lITMDAgfwWboGkAs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7o6kalYNr+S4ecm2/aSrNvVG6V+lITMDAgfwWboGkAs="}]}, {"Route": "_content/AntDesign/less/affix/style/entry.less", "AssetFile": "_content/AntDesign/less/affix/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE="}]}, {"Route": "_content/AntDesign/less/affix/style/index.less", "AssetFile": "_content/AntDesign/less/affix/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "107"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"mhJx8F5hbYtaIOoGYYAgRAl+E3kuL9cwtd9RqEloLI8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mhJx8F5hbYtaIOoGYYAgRAl+E3kuL9cwtd9RqEloLI8="}]}, {"Route": "_content/AntDesign/less/affix/style/patch.less", "AssetFile": "_content/AntDesign/less/affix/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/alert/style/entry.less", "AssetFile": "_content/AntDesign/less/alert/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/alert/style/index.less", "AssetFile": "_content/AntDesign/less/alert/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3247"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"s19R3heg0iwYlbp5SJMOEZvxYxvMECm0QUdWYqKYN94=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s19R3heg0iwYlbp5SJMOEZvxYxvMECm0QUdWYqKYN94="}]}, {"Route": "_content/AntDesign/less/alert/style/patch.less", "AssetFile": "_content/AntDesign/less/alert/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/alert/style/rtl.less", "AssetFile": "_content/AntDesign/less/alert/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "760"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Bo7d83alJHLyEKIL4qZIWBfzcyMbcBTsici/FRivxoU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bo7d83alJHLyEKIL4qZIWBfzcyMbcBTsici/FRivxoU="}]}, {"Route": "_content/AntDesign/less/anchor/style/entry.less", "AssetFile": "_content/AntDesign/less/anchor/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "83"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zut23CkuO4/chWDZwoi2K8uFSEPFlD3RGpYOwtGJyfE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zut23CkuO4/chWDZwoi2K8uFSEPFlD3RGpYOwtGJyfE="}]}, {"Route": "_content/AntDesign/less/anchor/style/index.less", "AssetFile": "_content/AntDesign/less/anchor/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1562"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+8eotUeSEMuaiLyPVmHdVYNyd3PCN63OZjAUhs5eJQI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8eotUeSEMuaiLyPVmHdVYNyd3PCN63OZjAUhs5eJQI="}]}, {"Route": "_content/AntDesign/less/anchor/style/patch.less", "AssetFile": "_content/AntDesign/less/anchor/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/anchor/style/rtl.less", "AssetFile": "_content/AntDesign/less/anchor/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "567"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"9kExmlSpBzoPKSxVtrWKq+6L55FvVjuDsi8swjt1veE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kExmlSpBzoPKSxVtrWKq+6L55FvVjuDsi8swjt1veE="}]}, {"Route": "_content/AntDesign/less/ant-design-blazor.aliyun.less", "AssetFile": "_content/AntDesign/less/ant-design-blazor.aliyun.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "90"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"KfkyB6xZRJZ2c/vwrKrcFnOEF7gjowDZaIikQmfDe7w=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KfkyB6xZRJZ2c/vwrKrcFnOEF7gjowDZaIikQmfDe7w="}]}, {"Route": "_content/AntDesign/less/ant-design-blazor.compact.less", "AssetFile": "_content/AntDesign/less/ant-design-blazor.compact.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/2depb2i7wa5Cb5Xfi+Uw3cCxn9pH3GsXafnSuLNdzQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/2depb2i7wa5Cb5Xfi+Uw3cCxn9pH3GsXafnSuLNdzQ="}]}, {"Route": "_content/AntDesign/less/ant-design-blazor.dark.less", "AssetFile": "_content/AntDesign/less/ant-design-blazor.dark.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "88"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"b4GgonRok+oYOwqj/J45SzuHjwo4mzC/6BHbnIXISSQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4GgonRok+oYOwqj/J45SzuHjwo4mzC/6BHbnIXISSQ="}]}, {"Route": "_content/AntDesign/less/ant-design-blazor.less", "AssetFile": "_content/AntDesign/less/ant-design-blazor.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UmIM5PP13lntHgfsarxUJ/K1mE1cUdvqZJqB+q6ko3s=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UmIM5PP13lntHgfsarxUJ/K1mE1cUdvqZJqB+q6ko3s="}]}, {"Route": "_content/AntDesign/less/ant-design-blazor.variable.less", "AssetFile": "_content/AntDesign/less/ant-design-blazor.variable.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "92"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UGwsDJbomdEAH0p7EsEdoYlLgczg/uqbN/btnSsiZxA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UGwsDJbomdEAH0p7EsEdoYlLgczg/uqbN/btnSsiZxA="}]}, {"Route": "_content/AntDesign/less/auto-complete/style/entry.less", "AssetFile": "_content/AntDesign/less/auto-complete/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "146"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VD3Dk1qibIs2E6F7O8KN+9lOa4DwEF5EmGd7+YuNM3Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VD3Dk1qibIs2E6F7O8KN+9lOa4DwEF5EmGd7+YuNM3Q="}]}, {"Route": "_content/AntDesign/less/auto-complete/style/index.less", "AssetFile": "_content/AntDesign/less/auto-complete/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "429"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6oRd22vAoQt7NFTZmF01/MjnNCuv6CcEKCSw6Vh2kSc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6oRd22vAoQt7NFTZmF01/MjnNCuv6CcEKCSw6Vh2kSc="}]}, {"Route": "_content/AntDesign/less/auto-complete/style/patch.less", "AssetFile": "_content/AntDesign/less/auto-complete/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"E4061lkn5MamURQ4LYxkiPzpJlB7JNSAsdOQUAD5BAM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E4061lkn5MamURQ4LYxkiPzpJlB7JNSAsdOQUAD5BAM="}]}, {"Route": "_content/AntDesign/less/avatar/style/entry.less", "AssetFile": "_content/AntDesign/less/avatar/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/avatar/style/group.less", "AssetFile": "_content/AntDesign/less/avatar/style/group.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "333"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Am9e9cJsAwZxKYZz08NgqEoTUay9kX6C4/V2J0GI1kw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Am9e9cJsAwZxKYZz08NgqEoTUay9kX6C4/V2J0GI1kw="}]}, {"Route": "_content/AntDesign/less/avatar/style/index.less", "AssetFile": "_content/AntDesign/less/avatar/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6xOqX+Ixjd6ba0lif5NZmcUlNIwQ0z02mhoXVrklpeg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6xOqX+Ixjd6ba0lif5NZmcUlNIwQ0z02mhoXVrklpeg="}]}, {"Route": "_content/AntDesign/less/avatar/style/rtl.less", "AssetFile": "_content/AntDesign/less/avatar/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "333"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"l7k/LO/v699BZQ1HSrSb0DY1wyyKq3wwJfL+lPnkuww=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l7k/LO/v699BZQ1HSrSb0DY1wyyKq3wwJfL+lPnkuww="}]}, {"Route": "_content/AntDesign/less/back-top/style/entry.less", "AssetFile": "_content/AntDesign/less/back-top/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/back-top/style/index.less", "AssetFile": "_content/AntDesign/less/back-top/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "800"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/kgy19ANmjWl+hnyLbPEm0OnbQwB/ukYBroAek5c79E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/kgy19ANmjWl+hnyLbPEm0OnbQwB/ukYBroAek5c79E="}]}, {"Route": "_content/AntDesign/less/back-top/style/responsive.less", "AssetFile": "_content/AntDesign/less/back-top/style/responsive.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YAIClUDbe3qh6x1iYnfo06YQK7H4H84ZxxoFey6OydQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YAIClUDbe3qh6x1iYnfo06YQK7H4H84ZxxoFey6OydQ="}]}, {"Route": "_content/AntDesign/less/badge/style/entry.less", "AssetFile": "_content/AntDesign/less/badge/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0="}]}, {"Route": "_content/AntDesign/less/badge/style/index.less", "AssetFile": "_content/AntDesign/less/badge/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5646"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"mvFPJChnL4V5OdjHPf1ei7dVR9B8mRIhSIIjwFJkx8g=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mvFPJChnL4V5OdjHPf1ei7dVR9B8mRIhSIIjwFJkx8g="}]}, {"Route": "_content/AntDesign/less/badge/style/patch.less", "AssetFile": "_content/AntDesign/less/badge/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "410"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"eHCEArJzAM7r74rgYQ4iZdEWcEMAr+R7EC9YuiDztJo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eHCEArJzAM7r74rgYQ4iZdEWcEMAr+R7EC9YuiDztJo="}]}, {"Route": "_content/AntDesign/less/badge/style/ribbon.less", "AssetFile": "_content/AntDesign/less/badge/style/ribbon.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1827"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0idQ8QyNNeBlc+7nogp4OWPxKmIDTb+YZRTUFGRDe/8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0idQ8QyNNeBlc+7nogp4OWPxKmIDTb+YZRTUFGRDe/8="}]}, {"Route": "_content/AntDesign/less/badge/style/rtl.less", "AssetFile": "_content/AntDesign/less/badge/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2141"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"mLkrgLtpHLQHsEvomYdHJht81e4ab4us3Qb6iQ4ncRw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mLkrgLtpHLQHsEvomYdHJht81e4ab4us3Qb6iQ4ncRw="}]}, {"Route": "_content/AntDesign/less/breadcrumb/style/entry.less", "AssetFile": "_content/AntDesign/less/breadcrumb/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/breadcrumb/style/index.less", "AssetFile": "_content/AntDesign/less/breadcrumb/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"duHsQx8Hkq2BNilddZtmsNNfLZ4Zm6dpdO8bTyNn6mo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duHsQx8Hkq2BNilddZtmsNNfLZ4Zm6dpdO8bTyNn6mo="}]}, {"Route": "_content/AntDesign/less/breadcrumb/style/patch.less", "AssetFile": "_content/AntDesign/less/breadcrumb/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "585"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LbGy7DloG/q7y19E5/4k4XJ2vEGrNts4/2K5kpU/LGM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LbGy7DloG/q7y19E5/4k4XJ2vEGrNts4/2K5kpU/LGM="}]}, {"Route": "_content/AntDesign/less/breadcrumb/style/rtl.less", "AssetFile": "_content/AntDesign/less/breadcrumb/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "477"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"J17Z694UIvMsO7B23Hf/vmEBlD6aD9+zEYKpHDYHP1A=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J17Z694UIvMsO7B23Hf/vmEBlD6aD9+zEYKpHDYHP1A="}]}, {"Route": "_content/AntDesign/less/button/style/entry.less", "AssetFile": "_content/AntDesign/less/button/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/button/style/index.less", "AssetFile": "_content/AntDesign/less/button/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6288"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/+UNhKYKeFyQTmDWSuz7usB580VcdeqG/ISr98URLMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/+UNhKYKeFyQTmDWSuz7usB580VcdeqG/ISr98URLMg="}]}, {"Route": "_content/AntDesign/less/button/style/mixin.less", "AssetFile": "_content/AntDesign/less/button/style/mixin.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15807"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Tz6zNqPv3TyEb5SFD3Z14N5q+vk/RroTMu1vIiNdrNU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tz6zNqPv3TyEb5SFD3Z14N5q+vk/RroTMu1vIiNdrNU="}]}, {"Route": "_content/AntDesign/less/button/style/rtl.less", "AssetFile": "_content/AntDesign/less/button/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2161"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CXaDO/C4nW79Ff47K7JpXD15q93k3eG6zsB86PkNR5A=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CXaDO/C4nW79Ff47K7JpXD15q93k3eG6zsB86PkNR5A="}]}, {"Route": "_content/AntDesign/less/button/style/space-compact.less", "AssetFile": "_content/AntDesign/less/button/style/space-compact.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2341"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"dCoLHG/NEdJi0ZHF3Q+Ichab8gao7ShA4Y4dL5CrHEc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dCoLHG/NEdJi0ZHF3Q+Ichab8gao7ShA4Y4dL5CrHEc="}]}, {"Route": "_content/AntDesign/less/calendar/style/entry.less", "AssetFile": "_content/AntDesign/less/calendar/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "127"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8PE21iAdMw3hFOORAymM12WCI9Prc+EF741bBifD5tk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8PE21iAdMw3hFOORAymM12WCI9Prc+EF741bBifD5tk="}]}, {"Route": "_content/AntDesign/less/calendar/style/index.less", "AssetFile": "_content/AntDesign/less/calendar/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4244"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fhgvsA5U+w+ee3uZ/IB/x3s41AYJHMaDAoGYBcP1P9E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhgvsA5U+w+ee3uZ/IB/x3s41AYJHMaDAoGYBcP1P9E="}]}, {"Route": "_content/AntDesign/less/calendar/style/rtl.less", "AssetFile": "_content/AntDesign/less/calendar/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "912"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fNCpLoXfrf0B4bTsYh7kRIqB6VXhHxTN58hL4GS7A0E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fNCpLoXfrf0B4bTsYh7kRIqB6VXhHxTN58hL4GS7A0E="}]}, {"Route": "_content/AntDesign/less/card/style/entry.less", "AssetFile": "_content/AntDesign/less/card/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "148"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M8ywkB9ZfQCe6iwq6MNkRowgJMumg6//Hsj6zQBwRKU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M8ywkB9ZfQCe6iwq6MNkRowgJMumg6//Hsj6zQBwRKU="}]}, {"Route": "_content/AntDesign/less/card/style/index.less", "AssetFile": "_content/AntDesign/less/card/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5750"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"rLIw6SFf0lnnLbD+78NJGS6G82xFxTieH4NrSIvQqsM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rLIw6SFf0lnnLbD+78NJGS6G82xFxTieH4NrSIvQqsM="}]}, {"Route": "_content/AntDesign/less/card/style/patch.less", "AssetFile": "_content/AntDesign/less/card/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "170"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PDxGY1qtW4R9rhs1XLl9s8RZABResFv1cwLJznbyY7Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PDxGY1qtW4R9rhs1XLl9s8RZABResFv1cwLJznbyY7Y="}]}, {"Route": "_content/AntDesign/less/card/style/size.less", "AssetFile": "_content/AntDesign/less/card/style/size.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "521"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"MYdypsPd53A/OeU3A+vOYXDddAoYjt/osqBYO32EHQw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MYdypsPd53A/OeU3A+vOYXDddAoYjt/osqBYO32EHQw="}]}, {"Route": "_content/AntDesign/less/carousel/style/entry.less", "AssetFile": "_content/AntDesign/less/carousel/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/Rz2DtIGGHuUqEdQMsvmafOVyRIxKa6tZM5TkYI5DXY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/Rz2DtIGGHuUqEdQMsvmafOVyRIxKa6tZM5TkYI5DXY="}]}, {"Route": "_content/AntDesign/less/carousel/style/index.less", "AssetFile": "_content/AntDesign/less/carousel/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5370"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QF2ELRH6hrkEoMefDxJ28nDaHjSY987U/PCBuJQ/eDo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QF2ELRH6hrkEoMefDxJ28nDaHjSY987U/PCBuJQ/eDo="}]}, {"Route": "_content/AntDesign/less/carousel/style/patch.less", "AssetFile": "_content/AntDesign/less/carousel/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "101"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"57uplBqOcvJpl6LUjqsdkGt/F0Q7Nudo9QLuy1LbHAc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-57uplBqOcvJpl6LUjqsdkGt/F0Q7Nudo9QLuy1LbHAc="}]}, {"Route": "_content/AntDesign/less/carousel/style/rtl.less", "AssetFile": "_content/AntDesign/less/carousel/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "823"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vvp6lvBZQMTgqxm6jwXlfof8ckBk4u/2SWHoW/sFTng=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vvp6lvBZQMTgqxm6jwXlfof8ckBk4u/2SWHoW/sFTng="}]}, {"Route": "_content/AntDesign/less/cascader/style/entry.less", "AssetFile": "_content/AntDesign/less/cascader/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "150"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"I5a/eIktS06Fv6c8sgNimo9o13qsW0GwRM47RIl1H0o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I5a/eIktS06Fv6c8sgNimo9o13qsW0GwRM47RIl1H0o="}]}, {"Route": "_content/AntDesign/less/cascader/style/index.less", "AssetFile": "_content/AntDesign/less/cascader/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2475"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"GZBchimmc0MG5I9F1ky0H/SlGCCy4dOacmb9l0iuhEo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GZBchimmc0MG5I9F1ky0H/SlGCCy4dOacmb9l0iuhEo="}]}, {"Route": "_content/AntDesign/less/cascader/style/patch.less", "AssetFile": "_content/AntDesign/less/cascader/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "86"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QgwrR8JwSXJBY6p+gY22uNPqBZAX8iKxez3RRNSkPoI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QgwrR8JwSXJBY6p+gY22uNPqBZAX8iKxez3RRNSkPoI="}]}, {"Route": "_content/AntDesign/less/cascader/style/rtl.less", "AssetFile": "_content/AntDesign/less/cascader/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "469"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ehgu2r5JquZcRCu0YcMq+6w6yjfLfLLLilL8FPsAUbc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ehgu2r5JquZcRCu0YcMq+6w6yjfLfLLLilL8FPsAUbc="}]}, {"Route": "_content/AntDesign/less/checkbox/style/entry.less", "AssetFile": "_content/AntDesign/less/checkbox/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/checkbox/style/index.less", "AssetFile": "_content/AntDesign/less/checkbox/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "92"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BDWzkKkxJJYlK2ZQ0K1Ux/w/U17XVt3vtpXy1Xbu6QM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BDWzkKkxJJYlK2ZQ0K1Ux/w/U17XVt3vtpXy1Xbu6QM="}]}, {"Route": "_content/AntDesign/less/checkbox/style/mixin.less", "AssetFile": "_content/AntDesign/less/checkbox/style/mixin.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5486"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"a+Gt4+qerEPPuwRVwvw+9nDAGTwwTT312zfEKUkp/FM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a+Gt4+qerEPPuwRVwvw+9nDAGTwwTT312zfEKUkp/FM="}]}, {"Route": "_content/AntDesign/less/checkbox/style/patch.less", "AssetFile": "_content/AntDesign/less/checkbox/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"9PM2GSkMDz25Y/uZC/tr33e3jiV7Jf6S0KsvpokGyvw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9PM2GSkMDz25Y/uZC/tr33e3jiV7Jf6S0KsvpokGyvw="}]}, {"Route": "_content/AntDesign/less/checkbox/style/rtl.less", "AssetFile": "_content/AntDesign/less/checkbox/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sBhu1j/xp8T/FNPcmouQN4LumUv4AfoKg1hOWv585pM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sBhu1j/xp8T/FNPcmouQN4LumUv4AfoKg1hOWv585pM="}]}, {"Route": "_content/AntDesign/less/collapse/style/entry.less", "AssetFile": "_content/AntDesign/less/collapse/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/collapse/style/index.less", "AssetFile": "_content/AntDesign/less/collapse/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3905"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EqKEBYUj0ZrAV5YYxwrdO60ZA3J5mdm1ZOR629bpvc4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EqKEBYUj0ZrAV5YYxwrdO60ZA3J5mdm1ZOR629bpvc4="}]}, {"Route": "_content/AntDesign/less/collapse/style/patch.less", "AssetFile": "_content/AntDesign/less/collapse/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/collapse/style/rtl.less", "AssetFile": "_content/AntDesign/less/collapse/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1597"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6Z/EpWr6bn3erNAWOagHOTd9h/C+xjL7S3LGKRW6L8o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6Z/EpWr6bn3erNAWOagHOTd9h/C+xjL7S3LGKRW6L8o="}]}, {"Route": "_content/AntDesign/less/comment/style/entry.less", "AssetFile": "_content/AntDesign/less/comment/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "42"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"AoEZ6wkO28zU8KBWXaNuh/W31KyPGhM1D5pG96ooaQI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AoEZ6wkO28zU8KBWXaNuh/W31KyPGhM1D5pG96ooaQI="}]}, {"Route": "_content/AntDesign/less/comment/style/index.less", "AssetFile": "_content/AntDesign/less/comment/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2052"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"uMmq5M3dPKoTogRLiHSLGbALyv9VSIzQnXKtx+5Na/A=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uMmq5M3dPKoTogRLiHSLGbALyv9VSIzQnXKtx+5Na/A="}]}, {"Route": "_content/AntDesign/less/comment/style/patch.less", "AssetFile": "_content/AntDesign/less/comment/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/comment/style/rtl.less", "AssetFile": "_content/AntDesign/less/comment/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "826"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/ETNFGww3YNXMPgvOBSZE/ryN6XTmV8cnWE0g+MPfcY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/ETNFGww3YNXMPgvOBSZE/ryN6XTmV8cnWE0g+MPfcY="}]}, {"Route": "_content/AntDesign/less/components.less", "AssetFile": "_content/AntDesign/less/components.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2393"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"q8M8p6yj7BPDoCnck0Qpytw/Qix3YqbuVnijBC1YKeA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q8M8p6yj7BPDoCnck0Qpytw/Qix3YqbuVnijBC1YKeA="}]}, {"Route": "_content/AntDesign/less/config-provider/style/entry.less", "AssetFile": "_content/AntDesign/less/config-provider/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/config-provider/style/index.less", "AssetFile": "_content/AntDesign/less/config-provider/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"k159C9m2csXfitZwmBaLeGF+T0awj5UUfXoGxMFWUfs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k159C9m2csXfitZwmBaLeGF+T0awj5UUfXoGxMFWUfs="}]}, {"Route": "_content/AntDesign/less/date-picker/style/Calendar.less", "AssetFile": "_content/AntDesign/less/date-picker/style/Calendar.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Jj/JE7aHNfCv8OCXrRMwkAf6zOvUcsrYT2QPGL1dBJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jj/JE7aHNfCv8OCXrRMwkAf6zOvUcsrYT2QPGL1dBJM="}]}, {"Route": "_content/AntDesign/less/date-picker/style/DecadePanel.less", "AssetFile": "_content/AntDesign/less/date-picker/style/DecadePanel.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1696"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+v5we1rnaTcPVCk8QryCpkgMkxtDPAYbmbbmyYQGmCo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+v5we1rnaTcPVCk8QryCpkgMkxtDPAYbmbbmyYQGmCo="}]}, {"Route": "_content/AntDesign/less/date-picker/style/MonthPanel.less", "AssetFile": "_content/AntDesign/less/date-picker/style/MonthPanel.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1807"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"9yMCLji7fnvD1wSAM4ymp6iQWMXeW37YaYkGjt2+5m8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9yMCLji7fnvD1wSAM4ymp6iQWMXeW37YaYkGjt2+5m8="}]}, {"Route": "_content/AntDesign/less/date-picker/style/MonthPicker.less", "AssetFile": "_content/AntDesign/less/date-picker/style/MonthPicker.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "239"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"GBt+X5Y5zPSSd4ZQIKKCKNqs7R/js5gf2Dnd+Tfh1No=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GBt+X5Y5zPSSd4ZQIKKCKNqs7R/js5gf2Dnd+Tfh1No="}]}, {"Route": "_content/AntDesign/less/date-picker/style/Picker.less", "AssetFile": "_content/AntDesign/less/date-picker/style/Picker.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2443"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iS2WwE6+jOxm6Ia/+qmeGfqvxBEHmknT9VdARQd2/9k=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iS2WwE6+jOxm6Ia/+qmeGfqvxBEHmknT9VdARQd2/9k="}]}, {"Route": "_content/AntDesign/less/date-picker/style/RangePicker.less", "AssetFile": "_content/AntDesign/less/date-picker/style/RangePicker.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5196"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CtRS7mZqMnYFBGwOdfRNKZnM1aIGf/ISfarJLAg59vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CtRS7mZqMnYFBGwOdfRNKZnM1aIGf/ISfarJLAg59vU="}]}, {"Route": "_content/AntDesign/less/date-picker/style/TimePicker.less", "AssetFile": "_content/AntDesign/less/date-picker/style/TimePicker.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2636"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3AYtCXbrOrJUGRAxUtnQy8SxIsN5JkwXL8Rq2o0ZOT8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3AYtCXbrOrJUGRAxUtnQy8SxIsN5JkwXL8Rq2o0ZOT8="}]}, {"Route": "_content/AntDesign/less/date-picker/style/WeekPicker.less", "AssetFile": "_content/AntDesign/less/date-picker/style/WeekPicker.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "526"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"omiiFC1oKDhxy5o0/yVAewXAQfNAmmStyMZHJKxzwzg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-omiiFC1oKDhxy5o0/yVAewXAQfNAmmStyMZHJKxzwzg="}]}, {"Route": "_content/AntDesign/less/date-picker/style/YearPanel.less", "AssetFile": "_content/AntDesign/less/date-picker/style/YearPanel.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1825"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0Lx0b4uRdzFLmY4zbm0x0emgLFgSsambR5ohuKgW6Jk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0Lx0b4uRdzFLmY4zbm0x0emgLFgSsambR5ohuKgW6Jk="}]}, {"Route": "_content/AntDesign/less/date-picker/style/entry.less", "AssetFile": "_content/AntDesign/less/date-picker/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NbhecbVIm2JNZ6LpbRH8bn1HJqNmXJu+11Ci0Uhs8mc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbhecbVIm2JNZ6LpbRH8bn1HJqNmXJu+11Ci0Uhs8mc="}]}, {"Route": "_content/AntDesign/less/date-picker/style/index.less", "AssetFile": "_content/AntDesign/less/date-picker/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8936"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"DBSuQPJ9sDpv30PLvLMs+q2UIOg3Zil/tJMCStlgDWM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DBSuQPJ9sDpv30PLvLMs+q2UIOg3Zil/tJMCStlgDWM="}]}, {"Route": "_content/AntDesign/less/date-picker/style/panel.less", "AssetFile": "_content/AntDesign/less/date-picker/style/panel.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17473"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/m9I07hB8q3DwxhwvoNo+Hz9qINMb+oIS02neNfsCjo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/m9I07hB8q3DwxhwvoNo+Hz9qINMb+oIS02neNfsCjo="}]}, {"Route": "_content/AntDesign/less/date-picker/style/patch.less", "AssetFile": "_content/AntDesign/less/date-picker/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "165"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"GiezbYSqeGg3lJTielb/Wp8cf8G2VMPrBecf/PMgGyY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GiezbYSqeGg3lJTielb/Wp8cf8G2VMPrBecf/PMgGyY="}]}, {"Route": "_content/AntDesign/less/date-picker/style/rtl.less", "AssetFile": "_content/AntDesign/less/date-picker/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6413"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"SxmyQnIvu7PupctMKPR8p5XNv/CxzPU4LVsxYH9Hb+I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SxmyQnIvu7PupctMKPR8p5XNv/CxzPU4LVsxYH9Hb+I="}]}, {"Route": "_content/AntDesign/less/date-picker/style/status.less", "AssetFile": "_content/AntDesign/less/date-picker/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "924"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O9qtzOZS3qrI8rje6/7OEJO6K8WKebVb76NsicDdWzA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O9qtzOZS3qrI8rje6/7OEJO6K8WKebVb76NsicDdWzA="}]}, {"Route": "_content/AntDesign/less/descriptions/style/entry.less", "AssetFile": "_content/AntDesign/less/descriptions/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "42"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0iy7eX5BQlxqJBth8rSmJH8dpGUN+UpKO/xiefF2cpU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0iy7eX5BQlxqJBth8rSmJH8dpGUN+UpKO/xiefF2cpU="}]}, {"Route": "_content/AntDesign/less/descriptions/style/index.less", "AssetFile": "_content/AntDesign/less/descriptions/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3506"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"nBm2AfL1QWaP60eEvT5vltvGYseYwk7Tl33pxynujug=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nBm2AfL1QWaP60eEvT5vltvGYseYwk7Tl33pxynujug="}]}, {"Route": "_content/AntDesign/less/descriptions/style/patch.less", "AssetFile": "_content/AntDesign/less/descriptions/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/descriptions/style/rtl.less", "AssetFile": "_content/AntDesign/less/descriptions/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "717"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"E3i3fOw5yyxTjwbohmVf+fvP5fRxWFnE17qa0HQ/ECQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E3i3fOw5yyxTjwbohmVf+fvP5fRxWFnE17qa0HQ/ECQ="}]}, {"Route": "_content/AntDesign/less/divider/style/entry.less", "AssetFile": "_content/AntDesign/less/divider/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/divider/style/index.less", "AssetFile": "_content/AntDesign/less/divider/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2632"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qNV1wDOMqukAyaiSzS+kkRvJH+qrE+eDvalvi80cDPE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qNV1wDOMqukAyaiSzS+kkRvJH+qrE+eDvalvi80cDPE="}]}, {"Route": "_content/AntDesign/less/divider/style/rtl.less", "AssetFile": "_content/AntDesign/less/divider/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ABhYskKnpt0nOHc9urZRRsvLsHUpfhAh4BwC6cGv81k=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ABhYskKnpt0nOHc9urZRRsvLsHUpfhAh4BwC6cGv81k="}]}, {"Route": "_content/AntDesign/less/drawer/style/customize.less", "AssetFile": "_content/AntDesign/less/drawer/style/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WK6O84LJLL6RVbIWHAh+lwyMheWqLtVgRL3iS3G6u5U=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WK6O84LJLL6RVbIWHAh+lwyMheWqLtVgRL3iS3G6u5U="}]}, {"Route": "_content/AntDesign/less/drawer/style/drawer.less", "AssetFile": "_content/AntDesign/less/drawer/style/drawer.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3098"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UI5NxKjsoWrQeAVEuMmzwoyDZc6jUSkQqLWdW/RYo5A=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UI5NxKjsoWrQeAVEuMmzwoyDZc6jUSkQqLWdW/RYo5A="}]}, {"Route": "_content/AntDesign/less/drawer/style/entry.less", "AssetFile": "_content/AntDesign/less/drawer/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/drawer/style/index.less", "AssetFile": "_content/AntDesign/less/drawer/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "194"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"TafTDrmuOTxQja/Tu7imMdOkPmvWZHagaUm8WwFAjNA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TafTDrmuOTxQja/Tu7imMdOkPmvWZHagaUm8WwFAjNA="}]}, {"Route": "_content/AntDesign/less/drawer/style/motion.less", "AssetFile": "_content/AntDesign/less/drawer/style/motion.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2244"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3w/w6a7M1Sj3nddlFcYMWnsecz4hQBiwcfPc1D+Ar4g=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3w/w6a7M1Sj3nddlFcYMWnsecz4hQBiwcfPc1D+Ar4g="}]}, {"Route": "_content/AntDesign/less/drawer/style/rtl.less", "AssetFile": "_content/AntDesign/less/drawer/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "248"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"RqDuCCfvP0bh9rGXi0gMOEWjSAM8asDA6EvI8SC6qJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RqDuCCfvP0bh9rGXi0gMOEWjSAM8asDA6EvI8SC6qJM="}]}, {"Route": "_content/AntDesign/less/dropdown/style/entry.less", "AssetFile": "_content/AntDesign/less/dropdown/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Gxj7veDOli+vA75QhMsKClDHz3o+yylJXbR0sYgojpI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gxj7veDOli+vA75QhMsKClDHz3o+yylJXbR0sYgojpI="}]}, {"Route": "_content/AntDesign/less/dropdown/style/index.less", "AssetFile": "_content/AntDesign/less/dropdown/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9859"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7eL7Oot2ylgaRt8onkJ888X2V58i1n9YEkW5HdShaJI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7eL7Oot2ylgaRt8onkJ888X2V58i1n9YEkW5HdShaJI="}]}, {"Route": "_content/AntDesign/less/dropdown/style/patch.less", "AssetFile": "_content/AntDesign/less/dropdown/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+IqziV8RNdJecnuc6XpkzfA2hD1+rFvTo456Ld/ZXXc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+IqziV8RNdJecnuc6XpkzfA2hD1+rFvTo456Ld/ZXXc="}]}, {"Route": "_content/AntDesign/less/dropdown/style/rtl.less", "AssetFile": "_content/AntDesign/less/dropdown/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1826"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"RixCtbT8TL9pZrUwdlTGliY8cagT3jALKXDYVkO+E8s=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RixCtbT8TL9pZrUwdlTGliY8cagT3jALKXDYVkO+E8s="}]}, {"Route": "_content/AntDesign/less/dropdown/style/status.less", "AssetFile": "_content/AntDesign/less/dropdown/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "273"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wlm08CrUyIeF1Cx4aMYTN8DAJWMpyfeBN5ekZPzv+B0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wlm08CrUyIeF1Cx4aMYTN8DAJWMpyfeBN5ekZPzv+B0="}]}, {"Route": "_content/AntDesign/less/empty/style/entry.less", "AssetFile": "_content/AntDesign/less/empty/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE="}]}, {"Route": "_content/AntDesign/less/empty/style/index.less", "AssetFile": "_content/AntDesign/less/empty/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2166"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bvaRvSXlpK4vSGCwT33coxHcGR+EMzLu1NWPaeFoYn4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bvaRvSXlpK4vSGCwT33coxHcGR+EMzLu1NWPaeFoYn4="}]}, {"Route": "_content/AntDesign/less/empty/style/patch.less", "AssetFile": "_content/AntDesign/less/empty/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/empty/style/rtl.less", "AssetFile": "_content/AntDesign/less/empty/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "176"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ef/ic+ZXXVpIsqLLvkLzWBWex3e16hSMWt0LOo5UXIY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ef/ic+ZXXVpIsqLLvkLzWBWex3e16hSMWt0LOo5UXIY="}]}, {"Route": "_content/AntDesign/less/form/style/components.less", "AssetFile": "_content/AntDesign/less/form/style/components.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "506"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"q2GKNgqi6pfEPDKqhD010tp+VW6waKSobApWLwXt0v8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q2GKNgqi6pfEPDKqhD010tp+VW6waKSobApWLwXt0v8="}]}, {"Route": "_content/AntDesign/less/form/style/entry.less", "AssetFile": "_content/AntDesign/less/form/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "104"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PQC6iuZ1hekQz4gS9LBssJQmzc4Ipa1QarGrkbvNICU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PQC6iuZ1hekQz4gS9LBssJQmzc4Ipa1QarGrkbvNICU="}]}, {"Route": "_content/AntDesign/less/form/style/horizontal.less", "AssetFile": "_content/AntDesign/less/form/style/horizontal.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "744"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"XO/K0wSjasjIbxOkpH7IqrGcG1RHC0HHp54LOn2Gq/I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XO/K0wSjasjIbxOkpH7IqrGcG1RHC0HHp54LOn2Gq/I="}]}, {"Route": "_content/AntDesign/less/form/style/index.less", "AssetFile": "_content/AntDesign/less/form/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7370"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"e6E06z8dCEvGEoUaRxECAom7EvzFJpB8IP96wjjQW5w=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e6E06z8dCEvGEoUaRxECAom7EvzFJpB8IP96wjjQW5w="}]}, {"Route": "_content/AntDesign/less/form/style/inline.less", "AssetFile": "_content/AntDesign/less/form/style/inline.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "753"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"byFCrOs2tBZvMT6Q18FpbGy+2Ld5rA6/wu+kVkOEQTo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-byFCrOs2tBZvMT6Q18FpbGy+2Ld5rA6/wu+kVkOEQTo="}]}, {"Route": "_content/AntDesign/less/form/style/mixin.less", "AssetFile": "_content/AntDesign/less/form/style/mixin.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1620"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YsLj7jSxdMRwJLN+rnBVCYunMs9YXd6BbmgSIPr9YdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YsLj7jSxdMRwJLN+rnBVCYunMs9YXd6BbmgSIPr9YdQ="}]}, {"Route": "_content/AntDesign/less/form/style/patch.less", "AssetFile": "_content/AntDesign/less/form/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/form/style/rtl.less", "AssetFile": "_content/AntDesign/less/form/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5251"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wSQM+NbT7uGqqoP4/UF66wlv3hbacYtemtDt///Vijs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wSQM+NbT7uGqqoP4/UF66wlv3hbacYtemtDt///Vijs="}]}, {"Route": "_content/AntDesign/less/form/style/status.less", "AssetFile": "_content/AntDesign/less/form/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3dzQ/uCnra14I6UldfLQl4cVpaDbye77ixzF9iofPnE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3dzQ/uCnra14I6UldfLQl4cVpaDbye77ixzF9iofPnE="}]}, {"Route": "_content/AntDesign/less/form/style/vertical.less", "AssetFile": "_content/AntDesign/less/form/style/vertical.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2061"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"a9caFWgzxYYxhR3EIAl5dKIMhtSboVyt448ITkMf58o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a9caFWgzxYYxhR3EIAl5dKIMhtSboVyt448ITkMf58o="}]}, {"Route": "_content/AntDesign/less/grid/style/entry.less", "AssetFile": "_content/AntDesign/less/grid/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/grid/style/index.less", "AssetFile": "_content/AntDesign/less/grid/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2084"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"gXvwP6JthZEGdzTW2QjCUjvRHuCBufKhnvFWWqXVs6Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gXvwP6JthZEGdzTW2QjCUjvRHuCBufKhnvFWWqXVs6Y="}]}, {"Route": "_content/AntDesign/less/grid/style/mixin.less", "AssetFile": "_content/AntDesign/less/grid/style/mixin.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"RHO/p5mf9xgjk2+bmCh/nRH+gCnRt1Rh5eMd+zCmUgQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RHO/p5mf9xgjk2+bmCh/nRH+gCnRt1Rh5eMd+zCmUgQ="}]}, {"Route": "_content/AntDesign/less/grid/style/rtl.less", "AssetFile": "_content/AntDesign/less/grid/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1419"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"L0sp3Reb9KFH9L0MywoB2SmZ1Chl5rlfCqsYF2F0oeI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L0sp3Reb9KFH9L0MywoB2SmZ1Chl5rlfCqsYF2F0oeI="}]}, {"Route": "_content/AntDesign/less/icon/style/entry.less", "AssetFile": "_content/AntDesign/less/icon/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/icon/style/index.less", "AssetFile": "_content/AntDesign/less/icon/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "114"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"94PGYptRzKURcJc1UdaVQlfYnsJaRIJ79RG2tP14Dkk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-94PGYptRzKURcJc1UdaVQlfYnsJaRIJ79RG2tP14Dkk="}]}, {"Route": "_content/AntDesign/less/icon/style/patch.less", "AssetFile": "_content/AntDesign/less/icon/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "164"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PXKrl2NZkocj/12q4nvJM95iWxtQAZ5Sf2yz3ZAoyrs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PXKrl2NZkocj/12q4nvJM95iWxtQAZ5Sf2yz3ZAoyrs="}]}, {"Route": "_content/AntDesign/less/image/style/entry.less", "AssetFile": "_content/AntDesign/less/image/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0="}]}, {"Route": "_content/AntDesign/less/image/style/index.less", "AssetFile": "_content/AntDesign/less/image/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"I8pq5kcYqkGRhddYhg+vYFs5gE+3InXe1d2srQ/caKA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I8pq5kcYqkGRhddYhg+vYFs5gE+3InXe1d2srQ/caKA="}]}, {"Route": "_content/AntDesign/less/image/style/patch.less", "AssetFile": "_content/AntDesign/less/image/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/input-number/style/affix.less", "AssetFile": "_content/AntDesign/less/input-number/style/affix.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1547"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"05WdlkO0Hk609jppL88LjqhzNbHdy5misoHp8DFazNM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-05WdlkO0Hk609jppL88LjqhzNbHdy5misoHp8DFazNM="}]}, {"Route": "_content/AntDesign/less/input-number/style/entry.less", "AssetFile": "_content/AntDesign/less/input-number/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/input-number/style/index.less", "AssetFile": "_content/AntDesign/less/input-number/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5423"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YjsvTwjc4m/Maofjsiuzf2+lLq5uyKj3WvaZZM56QtM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YjsvTwjc4m/Maofjsiuzf2+lLq5uyKj3WvaZZM56QtM="}]}, {"Route": "_content/AntDesign/less/input-number/style/rtl.less", "AssetFile": "_content/AntDesign/less/input-number/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1778"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EmE4ieC2QxxmC5ijZf/CgHVtras6pBlVKwWuSlR02mc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EmE4ieC2QxxmC5ijZf/CgHVtras6pBlVKwWuSlR02mc="}]}, {"Route": "_content/AntDesign/less/input-number/style/status.less", "AssetFile": "_content/AntDesign/less/input-number/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1068"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hp/kLlQk69YPn3WGQPXhDaFJ6tgAjo9FU2mh65uSERA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hp/kLlQk69YPn3WGQPXhDaFJ6tgAjo9FU2mh65uSERA="}]}, {"Route": "_content/AntDesign/less/input/style/IE11.less", "AssetFile": "_content/AntDesign/less/input/style/IE11.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "377"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cvorCpnsTi6ReYbcQ9Vd+qJ2wMCgcA7fQ+90pgE35V4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cvorCpnsTi6ReYbcQ9Vd+qJ2wMCgcA7fQ+90pgE35V4="}]}, {"Route": "_content/AntDesign/less/input/style/affix.less", "AssetFile": "_content/AntDesign/less/input/style/affix.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1220"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8MGEIPxzJddiwDK9S4A8cqIQE3q5ndc/ve/onNSHtZk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8MGEIPxzJddiwDK9S4A8cqIQE3q5ndc/ve/onNSHtZk="}]}, {"Route": "_content/AntDesign/less/input/style/allow-clear.less", "AssetFile": "_content/AntDesign/less/input/style/allow-clear.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "963"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FL1CNt4HGlz+pybyGQ5PeDl/U9mM7ZIn3h6eUl6CsS8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FL1CNt4HGlz+pybyGQ5PeDl/U9mM7ZIn3h6eUl6CsS8="}]}, {"Route": "_content/AntDesign/less/input/style/entry.less", "AssetFile": "_content/AntDesign/less/input/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/input/style/index.less", "AssetFile": "_content/AntDesign/less/input/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1845"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fA1HW5E7iWLL7QV41q4Px+r4tdDMW3I2efc20H6YVh0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fA1HW5E7iWLL7QV41q4Px+r4tdDMW3I2efc20H6YVh0="}]}, {"Route": "_content/AntDesign/less/input/style/mixin.less", "AssetFile": "_content/AntDesign/less/input/style/mixin.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11001"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"03LBKtSC6zRFJf3tWNIllL86LzeFTJAD1uzUelnMU7k=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-03LBKtSC6zRFJf3tWNIllL86LzeFTJAD1uzUelnMU7k="}]}, {"Route": "_content/AntDesign/less/input/style/patch.less", "AssetFile": "_content/AntDesign/less/input/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "774"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sThh4GUOMKHlqQ+plV+U8sJUUaYbuGUNCUOhqHf5L0E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sThh4GUOMKHlqQ+plV+U8sJUUaYbuGUNCUOhqHf5L0E="}]}, {"Route": "_content/AntDesign/less/input/style/rtl.less", "AssetFile": "_content/AntDesign/less/input/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4769"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"5mqsbSMNc9EaiK3om74U6b1PJApKAcfG6F9azZ1pWX4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5mqsbSMNc9EaiK3om74U6b1PJApKAcfG6F9azZ1pWX4="}]}, {"Route": "_content/AntDesign/less/input/style/search-input.less", "AssetFile": "_content/AntDesign/less/input/style/search-input.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2804"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LTRDKi031Yq+1dSZn6BFBJIPhNwQ2b9rYUaywmBljzQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LTRDKi031Yq+1dSZn6BFBJIPhNwQ2b9rYUaywmBljzQ="}]}, {"Route": "_content/AntDesign/less/input/style/status.less", "AssetFile": "_content/AntDesign/less/input/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1220"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zSeE+NgFt8mdukneh8WkcNX9cVBgoG53OTWuUOeglw0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zSeE+NgFt8mdukneh8WkcNX9cVBgoG53OTWuUOeglw0="}]}, {"Route": "_content/AntDesign/less/layout/style/entry.less", "AssetFile": "_content/AntDesign/less/layout/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/layout/style/index.less", "AssetFile": "_content/AntDesign/less/layout/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3312"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BPcYkTRFXC90OZxHsGaz3gWnH3yLX4luNVfLPqP7nR0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BPcYkTRFXC90OZxHsGaz3gWnH3yLX4luNVfLPqP7nR0="}]}, {"Route": "_content/AntDesign/less/layout/style/light.less", "AssetFile": "_content/AntDesign/less/layout/style/light.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "363"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"THP9KZDn/FqYQG4tt5JIw8QAkq9Jec1gO4CdIlzUdXU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-THP9KZDn/FqYQG4tt5JIw8QAkq9Jec1gO4CdIlzUdXU="}]}, {"Route": "_content/AntDesign/less/layout/style/patch.less", "AssetFile": "_content/AntDesign/less/layout/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/layout/style/rtl.less", "AssetFile": "_content/AntDesign/less/layout/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "179"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"MhGrB/7RDQHpd775q1Ca/oxi9w36VPKFwstYBSdgddk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MhGrB/7RDQHpd775q1Ca/oxi9w36VPKFwstYBSdgddk="}]}, {"Route": "_content/AntDesign/less/list/style/bordered.less", "AssetFile": "_content/AntDesign/less/list/style/bordered.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "951"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BC+ALj+6TKKTGv97xmXd7zBVfdCj4SED7JPzvXswdvc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BC+ALj+6TKKTGv97xmXd7zBVfdCj4SED7JPzvXswdvc="}]}, {"Route": "_content/AntDesign/less/list/style/customize.less", "AssetFile": "_content/AntDesign/less/list/style/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "316"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/lbIQhH8AmRgsAxgiitvdZTRqX/3lV1NQ8/hXEBOa6c=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lbIQhH8AmRgsAxgiitvdZTRqX/3lV1NQ8/hXEBOa6c="}]}, {"Route": "_content/AntDesign/less/list/style/entry.less", "AssetFile": "_content/AntDesign/less/list/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "206"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vMsq/0pQ78YZ/WSbNwtYNPNj4dK5HLlifPOdFhg9RsM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMsq/0pQ78YZ/WSbNwtYNPNj4dK5HLlifPOdFhg9RsM="}]}, {"Route": "_content/AntDesign/less/list/style/index.less", "AssetFile": "_content/AntDesign/less/list/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4530"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2tSOrjmUj8bxE4RFolkZQoUjQ4ag8IlShUkP7SvfglU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2tSOrjmUj8bxE4RFolkZQoUjQ4ag8IlShUkP7SvfglU="}]}, {"Route": "_content/AntDesign/less/list/style/patch.less", "AssetFile": "_content/AntDesign/less/list/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/list/style/responsive.less", "AssetFile": "_content/AntDesign/less/list/style/responsive.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "654"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"n9BEcuEBPiaE89NqvYl7y6DqMc/f8Q1riNl9lcEcnbo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9BEcuEBPiaE89NqvYl7y6DqMc/f8Q1riNl9lcEcnbo="}]}, {"Route": "_content/AntDesign/less/list/style/rtl.less", "AssetFile": "_content/AntDesign/less/list/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2551"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"HdkvsDaGdqQj55cVDYPJ2lOmKdj/6P6vZxfHulNq0k0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HdkvsDaGdqQj55cVDYPJ2lOmKdj/6P6vZxfHulNq0k0="}]}, {"Route": "_content/AntDesign/less/locale-provider/style/entry.less", "AssetFile": "_content/AntDesign/less/locale-provider/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/locale-provider/style/index.less", "AssetFile": "_content/AntDesign/less/locale-provider/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"k159C9m2csXfitZwmBaLeGF+T0awj5UUfXoGxMFWUfs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k159C9m2csXfitZwmBaLeGF+T0awj5UUfXoGxMFWUfs="}]}, {"Route": "_content/AntDesign/less/mentions/style/entry.less", "AssetFile": "_content/AntDesign/less/mentions/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE="}]}, {"Route": "_content/AntDesign/less/mentions/style/index.less", "AssetFile": "_content/AntDesign/less/mentions/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3938"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ot2HmOegSzlyepSGwwJrwAHrdJaxsVdcRqHl9aoLLdc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ot2HmOegSzlyepSGwwJrwAHrdJaxsVdcRqHl9aoLLdc="}]}, {"Route": "_content/AntDesign/less/mentions/style/patch.less", "AssetFile": "_content/AntDesign/less/mentions/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "259"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7sdvbe7jRXhCUA2Sy02Y22xm+sZEZO1Ab41lkxoAaH4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7sdvbe7jRXhCUA2Sy02Y22xm+sZEZO1Ab41lkxoAaH4="}]}, {"Route": "_content/AntDesign/less/mentions/style/rtl.less", "AssetFile": "_content/AntDesign/less/mentions/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "183"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qeF/VmASnSywjYXo/2jtNpnqk2oBM9E3JG04jjJbz68=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qeF/VmASnSywjYXo/2jtNpnqk2oBM9E3JG04jjJbz68="}]}, {"Route": "_content/AntDesign/less/mentions/style/status.less", "AssetFile": "_content/AntDesign/less/mentions/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "714"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"okYd3KKexglaS0iun2PxXbb+BKVaiz90RJC0sXYG7ow=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-okYd3KKexglaS0iun2PxXbb+BKVaiz90RJC0sXYG7ow="}]}, {"Route": "_content/AntDesign/less/menu/style/dark.less", "AssetFile": "_content/AntDesign/less/menu/style/dark.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3462"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wSczZjHwBP1pQJ/PsGoQKQiz6CZz00cYrQDLg4xLvok=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wSczZjHwBP1pQJ/PsGoQKQiz6CZz00cYrQDLg4xLvok="}]}, {"Route": "_content/AntDesign/less/menu/style/entry.less", "AssetFile": "_content/AntDesign/less/menu/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "138"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wXprU3eNN5Ln3+XXmZ6uf1XXhcSYBi0cRR5HiNg5vuE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wXprU3eNN5Ln3+XXmZ6uf1XXhcSYBi0cRR5HiNg5vuE="}]}, {"Route": "_content/AntDesign/less/menu/style/index.less", "AssetFile": "_content/AntDesign/less/menu/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15763"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"B0v6qepTsw3DqN4aX8hiGnTXtoTRyc8T4FnwiEmx32Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B0v6qepTsw3DqN4aX8hiGnTXtoTRyc8T4FnwiEmx32Q="}]}, {"Route": "_content/AntDesign/less/menu/style/light.less", "AssetFile": "_content/AntDesign/less/menu/style/light.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "348"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WOJXNIuqps9qVyHvwIUfilA1E8cI5dUJgHGJOWnLSy4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WOJXNIuqps9qVyHvwIUfilA1E8cI5dUJgHGJOWnLSy4="}]}, {"Route": "_content/AntDesign/less/menu/style/patch.less", "AssetFile": "_content/AntDesign/less/menu/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "400"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"G6q3xJDxqWyO93UJk6m5Ux3qONgJFm+Jma0Muqh4lRs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G6q3xJDxqWyO93UJk6m5Ux3qONgJFm+Jma0Muqh4lRs="}]}, {"Route": "_content/AntDesign/less/menu/style/rtl.less", "AssetFile": "_content/AntDesign/less/menu/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QvvD8R2lW9q4h30tQLfzia1nbshUIu049q3D+AAm/ns=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QvvD8R2lW9q4h30tQLfzia1nbshUIu049q3D+AAm/ns="}]}, {"Route": "_content/AntDesign/less/menu/style/status.less", "AssetFile": "_content/AntDesign/less/menu/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1058"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"A887p4VCYCL0nyOfaxPGrho7qHpKzZrmlS7GN72HGPU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A887p4VCYCL0nyOfaxPGrho7qHpKzZrmlS7GN72HGPU="}]}, {"Route": "_content/AntDesign/less/message/style/entry.less", "AssetFile": "_content/AntDesign/less/message/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/message/style/index.less", "AssetFile": "_content/AntDesign/less/message/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1335"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NIpBzgSaXcj56AyOrTCUftvPbBTC+MNaDwCsSoP6szA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIpBzgSaXcj56AyOrTCUftvPbBTC+MNaDwCsSoP6szA="}]}, {"Route": "_content/AntDesign/less/message/style/rtl.less", "AssetFile": "_content/AntDesign/less/message/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "280"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"OlRUrcZSMsbRX9YPoG17YLxrtGETv/BWrIeHgL9o/c4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OlRUrcZSMsbRX9YPoG17YLxrtGETv/BWrIeHgL9o/c4="}]}, {"Route": "_content/AntDesign/less/modal/style/confirm.less", "AssetFile": "_content/AntDesign/less/modal/style/confirm.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1736"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"h+9az3pp2AzgkwSgt2KN90wjxDsFoBmhVAKBjFOlGs4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h+9az3pp2AzgkwSgt2KN90wjxDsFoBmhVAKBjFOlGs4="}]}, {"Route": "_content/AntDesign/less/modal/style/customize.less", "AssetFile": "_content/AntDesign/less/modal/style/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "88"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2kt2FBJOWG97IxEo14mSA2lrpkCu9knyEak5lY7tiM8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2kt2FBJOWG97IxEo14mSA2lrpkCu9knyEak5lY7tiM8="}]}, {"Route": "_content/AntDesign/less/modal/style/entry.less", "AssetFile": "_content/AntDesign/less/modal/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"N9AYbjX9eri3yubYXFBfCrhfai/pEgp7PlgBUx/fFqw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N9AYbjX9eri3yubYXFBfCrhfai/pEgp7PlgBUx/fFqw="}]}, {"Route": "_content/AntDesign/less/modal/style/index.less", "AssetFile": "_content/AntDesign/less/modal/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "194"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0V4BWwLdzMfk7BvCDrpXA9z4mtqvR0+UAXVCR8QVA7s=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0V4BWwLdzMfk7BvCDrpXA9z4mtqvR0+UAXVCR8QVA7s="}]}, {"Route": "_content/AntDesign/less/modal/style/modal.less", "AssetFile": "_content/AntDesign/less/modal/style/modal.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2921"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7BLXPEk1Pwx3nOcu8CWSEyzNaKTb/6IWhFkU0bmPFqM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7BLXPEk1Pwx3nOcu8CWSEyzNaKTb/6IWhFkU0bmPFqM="}]}, {"Route": "_content/AntDesign/less/modal/style/patch.less", "AssetFile": "_content/AntDesign/less/modal/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1045"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BljNVGst7vPFnLzNJsV3fPgg1hF1aGCprWyhAGDEvXM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BljNVGst7vPFnLzNJsV3fPgg1hF1aGCprWyhAGDEvXM="}]}, {"Route": "_content/AntDesign/less/modal/style/rtl.less", "AssetFile": "_content/AntDesign/less/modal/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1464"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JpKBJjH1YLW7sTlF4JykEF5+HJAvHgEaA1aXckOCuks=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JpKBJjH1YLW7sTlF4JykEF5+HJAvHgEaA1aXckOCuks="}]}, {"Route": "_content/AntDesign/less/notification/style/customize.less", "AssetFile": "_content/AntDesign/less/notification/style/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "95"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/glYxSAqPeL6v6L5I+x8da4ydPRRxlo8tuarmSrjM+I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/glYxSAqPeL6v6L5I+x8da4ydPRRxlo8tuarmSrjM+I="}]}, {"Route": "_content/AntDesign/less/notification/style/entry.less", "AssetFile": "_content/AntDesign/less/notification/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0="}]}, {"Route": "_content/AntDesign/less/notification/style/index.less", "AssetFile": "_content/AntDesign/less/notification/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4326"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+JhkxbodKBYSipEA9rKEN+0c4IWL5kmg1QXW80sA4aU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+JhkxbodKBYSipEA9rKEN+0c4IWL5kmg1QXW80sA4aU="}]}, {"Route": "_content/AntDesign/less/notification/style/patch.less", "AssetFile": "_content/AntDesign/less/notification/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "172"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"AfZOtOz6UdzaZD7++hgfJi06J2vA1A43wgx3ZdF9zA4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AfZOtOz6UdzaZD7++hgfJi06J2vA1A43wgx3ZdF9zA4="}]}, {"Route": "_content/AntDesign/less/notification/style/placement.less", "AssetFile": "_content/AntDesign/less/notification/style/placement.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1358"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"lJxkNJinsYXWEqU3nR6vk1sCcDFAyFbfviO6fL8CYOg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lJxkNJinsYXWEqU3nR6vk1sCcDFAyFbfviO6fL8CYOg="}]}, {"Route": "_content/AntDesign/less/notification/style/rtl.less", "AssetFile": "_content/AntDesign/less/notification/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"mVMqugKwNvxBVYNVOnFbItVXRpxvtAboGIztobhn9LY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mVMqugKwNvxBVYNVOnFbItVXRpxvtAboGIztobhn9LY="}]}, {"Route": "_content/AntDesign/less/page-header/style/entry.less", "AssetFile": "_content/AntDesign/less/page-header/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "152"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"kn2ZwM7YNZnQL83L6s1lqa1eeZEiCmonWPLNewoYq+M=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kn2ZwM7YNZnQL83L6s1lqa1eeZEiCmonWPLNewoYq+M="}]}, {"Route": "_content/AntDesign/less/page-header/style/index.less", "AssetFile": "_content/AntDesign/less/page-header/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2315"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"L1sBHmdWK1FFWBHGJlERj6+YGJKDgqOI5Q8wpRbB4YM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L1sBHmdWK1FFWBHGJlERj6+YGJKDgqOI5Q8wpRbB4YM="}]}, {"Route": "_content/AntDesign/less/page-header/style/patch.less", "AssetFile": "_content/AntDesign/less/page-header/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"y/ae7QZY323rTHO+EFOSeF5cSnHpZhYdgFE0rSetc5k=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y/ae7QZY323rTHO+EFOSeF5cSnHpZhYdgFE0rSetc5k="}]}, {"Route": "_content/AntDesign/less/page-header/style/rtl.less", "AssetFile": "_content/AntDesign/less/page-header/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4viVnfOiEM5m2JGxHQfS5jGIqkn6+uTaKjP8Xba6zu0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4viVnfOiEM5m2JGxHQfS5jGIqkn6+uTaKjP8Xba6zu0="}]}, {"Route": "_content/AntDesign/less/pagination/style/entry.less", "AssetFile": "_content/AntDesign/less/pagination/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tWiClD/cAXUFVukQilJzsjdBmFQLT9PPJNwbGWYgd7o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tWiClD/cAXUFVukQilJzsjdBmFQLT9PPJNwbGWYgd7o="}]}, {"Route": "_content/AntDesign/less/pagination/style/index.less", "AssetFile": "_content/AntDesign/less/pagination/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9835"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8eOvg1cIQlDZ7zYCm2GAmcVlhuCvsXPD6FJCKviIG9E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8eOvg1cIQlDZ7zYCm2GAmcVlhuCvsXPD6FJCKviIG9E="}]}, {"Route": "_content/AntDesign/less/pagination/style/patch.less", "AssetFile": "_content/AntDesign/less/pagination/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/pagination/style/rtl.less", "AssetFile": "_content/AntDesign/less/pagination/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1176"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iRXoHtYffWmEnWGrdrilG94V8meS4OCYSjzunZkqYGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iRXoHtYffWmEnWGrdrilG94V8meS4OCYSjzunZkqYGs="}]}, {"Route": "_content/AntDesign/less/popconfirm/style/entry.less", "AssetFile": "_content/AntDesign/less/popconfirm/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "160"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6VHP7OIzu1XmZwgP7+3C4IP6dXaeZDgNUzVrhsQNcIY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6VHP7OIzu1XmZwgP7+3C4IP6dXaeZDgNUzVrhsQNcIY="}]}, {"Route": "_content/AntDesign/less/popconfirm/style/index.less", "AssetFile": "_content/AntDesign/less/popconfirm/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hXUooFYMgu305X5HWX2Q4LZHNupCXXVZelMkCmZdWgM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXUooFYMgu305X5HWX2Q4LZHNupCXXVZelMkCmZdWgM="}]}, {"Route": "_content/AntDesign/less/popconfirm/style/patch.less", "AssetFile": "_content/AntDesign/less/popconfirm/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ElkjVaPS5Q6957HCwHXVs9YFAvg999VSqKDy/pLTerE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ElkjVaPS5Q6957HCwHXVs9YFAvg999VSqKDy/pLTerE="}]}, {"Route": "_content/AntDesign/less/popover/style/customize.less", "AssetFile": "_content/AntDesign/less/popover/style/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "90"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CaJn9PIyTgOsCGa6a0eEs5BLx/dT6S5pKeUPnyw9DdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CaJn9PIyTgOsCGa6a0eEs5BLx/dT6S5pKeUPnyw9DdQ="}]}, {"Route": "_content/AntDesign/less/popover/style/entry.less", "AssetFile": "_content/AntDesign/less/popover/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"J7NZLahYLDuG33fbyq8IrtRV6wNEV4Pp+IOItMheFCE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J7NZLahYLDuG33fbyq8IrtRV6wNEV4Pp+IOItMheFCE="}]}, {"Route": "_content/AntDesign/less/popover/style/index.less", "AssetFile": "_content/AntDesign/less/popover/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5629"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"clIcrpbwNBt9smqzP+wYKwJwOD0j/KswbpN834FP/wU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-clIcrpbwNBt9smqzP+wYKwJwOD0j/KswbpN834FP/wU="}]}, {"Route": "_content/AntDesign/less/popover/style/patch.less", "AssetFile": "_content/AntDesign/less/popover/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ElkjVaPS5Q6957HCwHXVs9YFAvg999VSqKDy/pLTerE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ElkjVaPS5Q6957HCwHXVs9YFAvg999VSqKDy/pLTerE="}]}, {"Route": "_content/AntDesign/less/popover/style/rtl.less", "AssetFile": "_content/AntDesign/less/popover/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "640"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ho1iKxVwYEEEpX9GBePQjQo/qlS5gGCC9eKyfeeuNAQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ho1iKxVwYEEEpX9GBePQjQo/qlS5gGCC9eKyfeeuNAQ="}]}, {"Route": "_content/AntDesign/less/progress/style/entry.less", "AssetFile": "_content/AntDesign/less/progress/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0="}]}, {"Route": "_content/AntDesign/less/progress/style/index.less", "AssetFile": "_content/AntDesign/less/progress/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4131"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pJTiH/TsufIC+2HZSxR6Xn8BmrjS2SQ9TVKKgfrx0eg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pJTiH/TsufIC+2HZSxR6Xn8BmrjS2SQ9TVKKgfrx0eg="}]}, {"Route": "_content/AntDesign/less/progress/style/patch.less", "AssetFile": "_content/AntDesign/less/progress/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "399"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QNJNW2+/k2kjAizfQhAsdpN9sX/Bh8Y3qXXolNBkats=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QNJNW2+/k2kjAizfQhAsdpN9sX/Bh8Y3qXXolNBkats="}]}, {"Route": "_content/AntDesign/less/progress/style/rtl.less", "AssetFile": "_content/AntDesign/less/progress/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"n1uZPwHtTasL2zb3vprXLNwkNhsLytVMQp3o1k3N8ac=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n1uZPwHtTasL2zb3vprXLNwkNhsLytVMQp3o1k3N8ac="}]}, {"Route": "_content/AntDesign/less/radio/style/entry.less", "AssetFile": "_content/AntDesign/less/radio/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/radio/style/index.less", "AssetFile": "_content/AntDesign/less/radio/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7835"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ra2Pas4YlbeFQIC283hGS0BAGz4ELAu534NJEBzqujc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ra2Pas4YlbeFQIC283hGS0BAGz4ELAu534NJEBzqujc="}]}, {"Route": "_content/AntDesign/less/radio/style/patch.less", "AssetFile": "_content/AntDesign/less/radio/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "57"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"L7jVhT2AyycS33llsjHK2dscIrJW+5SKj1Q9rjQefDo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L7jVhT2AyycS33llsjHK2dscIrJW+5SKj1Q9rjQefDo="}]}, {"Route": "_content/AntDesign/less/radio/style/rtl.less", "AssetFile": "_content/AntDesign/less/radio/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1561"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qMG8SuCywS+ltJFoEMhVH6Y5t2fByS6SKAns8fQAX0I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qMG8SuCywS+ltJFoEMhVH6Y5t2fByS6SKAns8fQAX0I="}]}, {"Route": "_content/AntDesign/less/rate/style/entry.less", "AssetFile": "_content/AntDesign/less/rate/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "88"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Yk70Ir/wiDLJc5uPUNkor5q6wwR455t8nfgpnuIDo6g=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yk70Ir/wiDLJc5uPUNkor5q6wwR455t8nfgpnuIDo6g="}]}, {"Route": "_content/AntDesign/less/rate/style/index.less", "AssetFile": "_content/AntDesign/less/rate/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1483"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CmvQ4Q+0QTZRgLQ+Mq33n+9IyARIfgvdjUEz+iMIK/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CmvQ4Q+0QTZRgLQ+Mq33n+9IyARIfgvdjUEz+iMIK/w="}]}, {"Route": "_content/AntDesign/less/rate/style/rtl.less", "AssetFile": "_content/AntDesign/less/rate/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "299"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PpSyPVOaqJbFMz6Vw9RhimGVxPO1B8c7IFB3v1Y0eFA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PpSyPVOaqJbFMz6Vw9RhimGVxPO1B8c7IFB3v1Y0eFA="}]}, {"Route": "_content/AntDesign/less/result/style/entry.less", "AssetFile": "_content/AntDesign/less/result/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3Hp/9bNRfKFhdSjUL/0ZZGRTK8kMtN5yfT70BuIW/ic=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3Hp/9bNRfKFhdSjUL/0ZZGRTK8kMtN5yfT70BuIW/ic="}]}, {"Route": "_content/AntDesign/less/result/style/index.less", "AssetFile": "_content/AntDesign/less/result/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1310"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UQUVC3tWSh1uDpanmdo79rM/mc+MU2EkvvtH1Pu5vmM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UQUVC3tWSh1uDpanmdo79rM/mc+MU2EkvvtH1Pu5vmM="}]}, {"Route": "_content/AntDesign/less/result/style/patch.less", "AssetFile": "_content/AntDesign/less/result/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/result/style/rtl.less", "AssetFile": "_content/AntDesign/less/result/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "411"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3NWG96HElVMfNk/JBs3uqaYpw93OVxBfxZ3XiT8gMAs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3NWG96HElVMfNk/JBs3uqaYpw93OVxBfxZ3XiT8gMAs="}]}, {"Route": "_content/AntDesign/less/segmented/style/entry.less", "AssetFile": "_content/AntDesign/less/segmented/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/segmented/style/index.less", "AssetFile": "_content/AntDesign/less/segmented/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2587"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"C94DG/uRXYqD3Mlpf/bw5dWNYKQFPc6quYd9LaIDcag=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C94DG/uRXYqD3Mlpf/bw5dWNYKQFPc6quYd9LaIDcag="}]}, {"Route": "_content/AntDesign/less/segmented/style/mixins.less", "AssetFile": "_content/AntDesign/less/segmented/style/mixins.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "498"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2tiw4kFAIi8ac4jVkFQYW2fnqffpdFj6k1//HtlsKXI=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2tiw4kFAIi8ac4jVkFQYW2fnqffpdFj6k1//HtlsKXI="}]}, {"Route": "_content/AntDesign/less/segmented/style/rtl.less", "AssetFile": "_content/AntDesign/less/segmented/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "260"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BJpdpvB+aQuPe0ITqglEol5CBqEG0sLAizXX9Tr7XVc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJpdpvB+aQuPe0ITqglEol5CBqEG0sLAizXX9Tr7XVc="}]}, {"Route": "_content/AntDesign/less/select/style/entry.less", "AssetFile": "_content/AntDesign/less/select/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "110"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"q9hdK89KpaedyL2ySK1pjGs+fGKuyJ0MB4cURxZKUDw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q9hdK89KpaedyL2ySK1pjGs+fGKuyJ0MB4cURxZKUDw="}]}, {"Route": "_content/AntDesign/less/select/style/index.less", "AssetFile": "_content/AntDesign/less/select/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7979"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PYm1qx7CNga1F5YRFjqwWe0M7rW21/Sk+xG+9/nmx1o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PYm1qx7CNga1F5YRFjqwWe0M7rW21/Sk+xG+9/nmx1o="}]}, {"Route": "_content/AntDesign/less/select/style/multiple.less", "AssetFile": "_content/AntDesign/less/select/style/multiple.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6662"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hnQU+n7lU9zjhwX7BROm1w0bU2aQUbasscyE8XKZCME=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hnQU+n7lU9zjhwX7BROm1w0bU2aQUbasscyE8XKZCME="}]}, {"Route": "_content/AntDesign/less/select/style/patch.less", "AssetFile": "_content/AntDesign/less/select/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "_content/AntDesign/less/select/style/rtl.less", "AssetFile": "_content/AntDesign/less/select/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"SVX8SREul+HL8XvZOp5EUCJHbjQfMassQIvN1Ov4glk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SVX8SREul+HL8XvZOp5EUCJHbjQfMassQIvN1Ov4glk="}]}, {"Route": "_content/AntDesign/less/select/style/single.less", "AssetFile": "_content/AntDesign/less/select/style/single.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4839"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Hq1ksXZWHveP2fu+vGZhOzbIqOCe6l98ndFWyohw5xU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hq1ksXZWHveP2fu+vGZhOzbIqOCe6l98ndFWyohw5xU="}]}, {"Route": "_content/AntDesign/less/select/style/status.less", "AssetFile": "_content/AntDesign/less/select/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1372"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"HAA7+A9UM2xw3cKRUo9EAZUJzwL1ebs2ypL57VmHeog=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HAA7+A9UM2xw3cKRUo9EAZUJzwL1ebs2ypL57VmHeog="}]}, {"Route": "_content/AntDesign/less/skeleton/style/entry.less", "AssetFile": "_content/AntDesign/less/skeleton/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/skeleton/style/index.less", "AssetFile": "_content/AntDesign/less/skeleton/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6023"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wETKNCoZ1boHD/wt6xiOZseCaQPyW1KzzfgP8Vd6TXs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wETKNCoZ1boHD/wt6xiOZseCaQPyW1KzzfgP8Vd6TXs="}]}, {"Route": "_content/AntDesign/less/skeleton/style/rtl.less", "AssetFile": "_content/AntDesign/less/skeleton/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1107"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Rtubj9+s+hP3PB1d+f5sUiBDZUroly8ONKGTBEFSk84=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rtubj9+s+hP3PB1d+f5sUiBDZUroly8ONKGTBEFSk84="}]}, {"Route": "_content/AntDesign/less/slider/style/entry.less", "AssetFile": "_content/AntDesign/less/slider/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "88"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Yk70Ir/wiDLJc5uPUNkor5q6wwR455t8nfgpnuIDo6g=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yk70Ir/wiDLJc5uPUNkor5q6wwR455t8nfgpnuIDo6g="}]}, {"Route": "_content/AntDesign/less/slider/style/index.less", "AssetFile": "_content/AntDesign/less/slider/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4487"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"d99P+n4eoRJrUhkFzZuObn1oahdxCRx0tySfwRuSn5w=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d99P+n4eoRJrUhkFzZuObn1oahdxCRx0tySfwRuSn5w="}]}, {"Route": "_content/AntDesign/less/slider/style/rtl.less", "AssetFile": "_content/AntDesign/less/slider/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "815"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"eKqOT3o+VjTMSEGjCDBl66nhWOCPWWf7R93gB2ce9c4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eKqOT3o+VjTMSEGjCDBl66nhWOCPWWf7R93gB2ce9c4="}]}, {"Route": "_content/AntDesign/less/space/style/compact.less", "AssetFile": "_content/AntDesign/less/space/style/compact.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "291"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M5MrHaJmepqJio5u94XHbO+SmGmwS5n/YsA9M83nZbE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M5MrHaJmepqJio5u94XHbO+SmGmwS5n/YsA9M83nZbE="}]}, {"Route": "_content/AntDesign/less/space/style/entry.less", "AssetFile": "_content/AntDesign/less/space/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y89I1SKMwXphLYQtAvExib1buHxRTcK80Dgkw1LY3d0="}]}, {"Route": "_content/AntDesign/less/space/style/index.less", "AssetFile": "_content/AntDesign/less/space/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "590"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"N+JxTEaCHhmAkGDaF6bVwYAWvIpCz6XH3uDW4HpNERM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N+JxTEaCHhmAkGDaF6bVwYAWvIpCz6XH3uDW4HpNERM="}]}, {"Route": "_content/AntDesign/less/space/style/patch.less", "AssetFile": "_content/AntDesign/less/space/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/space/style/rtl.less", "AssetFile": "_content/AntDesign/less/space/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"30b6EBaHJhSIrRPvW3bWKN3sp8ld5XgMb3NyeZvRr+0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-30b6EBaHJhSIrRPvW3bWKN3sp8ld5XgMb3NyeZvRr+0="}]}, {"Route": "_content/AntDesign/less/spin/style/entry.less", "AssetFile": "_content/AntDesign/less/spin/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/spin/style/index.less", "AssetFile": "_content/AntDesign/less/spin/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4189"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"P8vabzFFqIDDNqWSM/0APn34uuES1BNMDETNTqGPCF4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P8vabzFFqIDDNqWSM/0APn34uuES1BNMDETNTqGPCF4="}]}, {"Route": "_content/AntDesign/less/spin/style/patch.less", "AssetFile": "_content/AntDesign/less/spin/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/spin/style/rtl.less", "AssetFile": "_content/AntDesign/less/spin/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "279"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hmFliGcdy1CeoKflkzXpU1iBnztk47TTKeWXh7JDLrM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hmFliGcdy1CeoKflkzXpU1iBnztk47TTKeWXh7JDLrM="}]}, {"Route": "_content/AntDesign/less/statistic/style/entry.less", "AssetFile": "_content/AntDesign/less/statistic/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/statistic/style/index.less", "AssetFile": "_content/AntDesign/less/statistic/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "732"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qJO62akYzIM8kTAmSJQw4kCgHlVb4SevlIsogeC0tn4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qJO62akYzIM8kTAmSJQw4kCgHlVb4SevlIsogeC0tn4="}]}, {"Route": "_content/AntDesign/less/statistic/style/rtl.less", "AssetFile": "_content/AntDesign/less/statistic/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "321"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pqf0Jm7OPpHNbCes0P0wLz7yVbN9KkGDFI2MxsbHa+w=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pqf0Jm7OPpHNbCes0P0wLz7yVbN9KkGDFI2MxsbHa+w="}]}, {"Route": "_content/AntDesign/less/steps/style/compatibility.less", "AssetFile": "_content/AntDesign/less/steps/style/compatibility.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1140"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0cH2A67VGUhvBHLpcaqdFPEEplAuCnhqsQ8N77JwhNE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0cH2A67VGUhvBHLpcaqdFPEEplAuCnhqsQ8N77JwhNE="}]}, {"Route": "_content/AntDesign/less/steps/style/custom-icon.less", "AssetFile": "_content/AntDesign/less/steps/style/custom-icon.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0+OwviETHETbWUEoWyJhUS5vXLrIuJ0AeCv9w5G17S0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+OwviETHETbWUEoWyJhUS5vXLrIuJ0AeCv9w5G17S0="}]}, {"Route": "_content/AntDesign/less/steps/style/entry.less", "AssetFile": "_content/AntDesign/less/steps/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/steps/style/index.less", "AssetFile": "_content/AntDesign/less/steps/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5684"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fRn6dLdxwkN9yBL9scnpcAAS/MBPWz0EoR3FlyzGb64=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fRn6dLdxwkN9yBL9scnpcAAS/MBPWz0EoR3FlyzGb64="}]}, {"Route": "_content/AntDesign/less/steps/style/label-placement.less", "AssetFile": "_content/AntDesign/less/steps/style/label-placement.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "790"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Pm/R2eUeGTlesv6xrlTmT8mpOMZ+c/MRp6/cOTngleo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pm/R2eUeGTlesv6xrlTmT8mpOMZ+c/MRp6/cOTngleo="}]}, {"Route": "_content/AntDesign/less/steps/style/nav.less", "AssetFile": "_content/AntDesign/less/steps/style/nav.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2738"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"OPZXZLrOM/YotD+VW13QipduMiWdsgsDbW3KPkNQK+Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OPZXZLrOM/YotD+VW13QipduMiWdsgsDbW3KPkNQK+Q="}]}, {"Route": "_content/AntDesign/less/steps/style/progress-dot.less", "AssetFile": "_content/AntDesign/less/steps/style/progress-dot.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2568"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/9B0iBpJr46pRHuklIRv9fTAxmKEy+FHIARidad1wX8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/9B0iBpJr46pRHuklIRv9fTAxmKEy+FHIARidad1wX8="}]}, {"Route": "_content/AntDesign/less/steps/style/progress.less", "AssetFile": "_content/AntDesign/less/steps/style/progress.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "704"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"yC8fkNevgVAMr0e43Uw7jtAS5mKTVY03WdrKQS+A5/4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yC8fkNevgVAMr0e43Uw7jtAS5mKTVY03WdrKQS+A5/4="}]}, {"Route": "_content/AntDesign/less/steps/style/rtl.less", "AssetFile": "_content/AntDesign/less/steps/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5347"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"MJuIyiTBZVnArB20dZRgL7a40ktptsvvCILnj292URM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MJuIyiTBZVnArB20dZRgL7a40ktptsvvCILnj292URM="}]}, {"Route": "_content/AntDesign/less/steps/style/small.less", "AssetFile": "_content/AntDesign/less/steps/style/small.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1214"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vZtpAuF7EU3L7h/JtHTcbTk1B70iwdz34AMcX4d25JQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vZtpAuF7EU3L7h/JtHTcbTk1B70iwdz34AMcX4d25JQ="}]}, {"Route": "_content/AntDesign/less/steps/style/vertical.less", "AssetFile": "_content/AntDesign/less/steps/style/vertical.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1603"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Agi2GuUl9plKcMPBfQ6y9B7fqtcYhvG4XTlB7ZTVlJ0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Agi2GuUl9plKcMPBfQ6y9B7fqtcYhvG4XTlB7ZTVlJ0="}]}, {"Route": "_content/AntDesign/less/style/aliyun.less", "AssetFile": "_content/AntDesign/less/style/aliyun.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "84"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CQRyMojLUQeq4e6ywx+yJK1G+sx835E6LxP2Vr1m+V0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CQRyMojLUQeq4e6ywx+yJK1G+sx835E6LxP2Vr1m+V0="}]}, {"Route": "_content/AntDesign/less/style/color/bezierEasing.less", "AssetFile": "_content/AntDesign/less/style/color/bezierEasing.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3745"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"t8NcdehMO1E0d97P23RFTzx/ON10XifDE0y73o+b17I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t8NcdehMO1E0d97P23RFTzx/ON10XifDE0y73o+b17I="}]}, {"Route": "_content/AntDesign/less/style/color/colorPalette.less", "AssetFile": "_content/AntDesign/less/style/color/colorPalette.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2535"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ofjHzz/sw2VCITFO6iYqHN1Wq2dZUQ3qYZYXfK3aUHg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ofjHzz/sw2VCITFO6iYqHN1Wq2dZUQ3qYZYXfK3aUHg="}]}, {"Route": "_content/AntDesign/less/style/color/colors.less", "AssetFile": "_content/AntDesign/less/style/color/colors.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6946"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"i73Y79HcX7z22hud+bjpeFXTLcOcoKU3DvT5JpvgeRs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i73Y79HcX7z22hud+bjpeFXTLcOcoKU3DvT5JpvgeRs="}]}, {"Route": "_content/AntDesign/less/style/color/tinyColor.less", "AssetFile": "_content/AntDesign/less/style/color/tinyColor.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36699"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"H2RnSVSkpmleHIClOaNXQaMgfqOTqB/QpHAUzbdD2pc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H2RnSVSkpmleHIClOaNXQaMgfqOTqB/QpHAUzbdD2pc="}]}, {"Route": "_content/AntDesign/less/style/compact.less", "AssetFile": "_content/AntDesign/less/style/compact.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"U/2L2CmKQSgzGUbBDX7ioJlenOOucRk7J+O+2kEb8xs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U/2L2CmKQSgzGUbBDX7ioJlenOOucRk7J+O+2kEb8xs="}]}, {"Route": "_content/AntDesign/less/style/core/base.less", "AssetFile": "_content/AntDesign/less/style/core/base.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "236"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zk/UYUKENpCoDRsQHgGxq++ywhXKvp3gYbnRgJttQoQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zk/UYUKENpCoDRsQHgGxq++ywhXKvp3gYbnRgJttQoQ="}]}, {"Route": "_content/AntDesign/less/style/core/global.less", "AssetFile": "_content/AntDesign/less/style/core/global.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10828"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"V/J8ocu/PEcQq2Wk0XwjbeMEjfnTyy0KOYrOCmx1n7Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V/J8ocu/PEcQq2Wk0XwjbeMEjfnTyy0KOYrOCmx1n7Y="}]}, {"Route": "_content/AntDesign/less/style/core/iconfont.less", "AssetFile": "_content/AntDesign/less/style/core/iconfont.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "410"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"u2InCDwwgAcLnYA70XuTAM+L3Ht4YCF2saOX1YEYA6I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u2InCDwwgAcLnYA70XuTAM+L3Ht4YCF2saOX1YEYA6I="}]}, {"Route": "_content/AntDesign/less/style/core/index.less", "AssetFile": "_content/AntDesign/less/style/core/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "99"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"q1CsQFiztYQh40ZyoWIl1ACNI/DIdbFRyst4ahwjUgY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q1CsQFiztYQh40ZyoWIl1ACNI/DIdbFRyst4ahwjUgY="}]}, {"Route": "_content/AntDesign/less/style/core/motion.less", "AssetFile": "_content/AntDesign/less/style/core/motion.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "577"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pm/092bxFN6CmEdspG0rQa7xNgFmOUbXp4ctXTh9fsM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pm/092bxFN6CmEdspG0rQa7xNgFmOUbXp4ctXTh9fsM="}]}, {"Route": "_content/AntDesign/less/style/core/motion/fade.less", "AssetFile": "_content/AntDesign/less/style/core/motion/fade.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "478"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LsvmdRFKUwFqr3oA63SL08XN8Hyad6Kww12XmHoVMUM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LsvmdRFKUwFqr3oA63SL08XN8Hyad6Kww12XmHoVMUM="}]}, {"Route": "_content/AntDesign/less/style/core/motion/move.less", "AssetFile": "_content/AntDesign/less/style/core/motion/move.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ktEzUwjUW3fi+q4+3nIrt9voNbmfBG+4jijdysbC5l4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ktEzUwjUW3fi+q4+3nIrt9voNbmfBG+4jijdysbC5l4="}]}, {"Route": "_content/AntDesign/less/style/core/motion/other.less", "AssetFile": "_content/AntDesign/less/style/core/motion/other.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1166"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pvqvtegssSZalkyBMCV93PG/xZiir8DjnkzpmURFmR8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pvqvtegssSZalkyBMCV93PG/xZiir8DjnkzpmURFmR8="}]}, {"Route": "_content/AntDesign/less/style/core/motion/slide.less", "AssetFile": "_content/AntDesign/less/style/core/motion/slide.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2088"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZF0z7X+uHYkQ9pgvmdRZTXOI7ibOT/RpKQgcv8f/6VY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZF0z7X+uHYkQ9pgvmdRZTXOI7ibOT/RpKQgcv8f/6VY="}]}, {"Route": "_content/AntDesign/less/style/core/motion/swing.less", "AssetFile": "_content/AntDesign/less/style/core/motion/swing.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "625"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"W7QwCNdDqlCZZfEZePagtIftPXp8lB3/gGPK4AmHd6o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W7QwCNdDqlCZZfEZePagtIftPXp8lB3/gGPK4AmHd6o="}]}, {"Route": "_content/AntDesign/less/style/core/motion/zoom.less", "AssetFile": "_content/AntDesign/less/style/core/motion/zoom.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2812"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"1OcjtqYKMSH+yLqzyCdrQcWFg0wRzq3yGEULpEelpAs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1OcjtqYKMSH+yLqzyCdrQcWFg0wRzq3yGEULpEelpAs="}]}, {"Route": "_content/AntDesign/less/style/dark.less", "AssetFile": "_content/AntDesign/less/style/dark.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "82"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/uN3Ku8pU1yA9JL+9irNcwo5QQxJNOz8+/jLyAiVkK0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/uN3Ku8pU1yA9JL+9irNcwo5QQxJNOz8+/jLyAiVkK0="}]}, {"Route": "_content/AntDesign/less/style/default.less", "AssetFile": "_content/AntDesign/less/style/default.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "134"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Qsl/y0NnhuFVMA5YKhGrKzJ03S0twxYYwoofmq56Pdg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qsl/y0NnhuFVMA5YKhGrKzJ03S0twxYYwoofmq56Pdg="}]}, {"Route": "_content/AntDesign/less/style/entry.less", "AssetFile": "_content/AntDesign/less/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/style/index.less", "AssetFile": "_content/AntDesign/less/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "50"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+fhkRvxUnfIgBQWots8Wguf6GphQoEVuok1onwXNluc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+fhkRvxUnfIgBQWots8Wguf6GphQoEVuok1onwXNluc="}]}, {"Route": "_content/AntDesign/less/style/mixins/box.less", "AssetFile": "_content/AntDesign/less/style/mixins/box.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "99"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"gFv/cUbdkhpp+52XqNspalOd7OH2BBfxG/sHYIDtEWM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gFv/cUbdkhpp+52XqNspalOd7OH2BBfxG/sHYIDtEWM="}]}, {"Route": "_content/AntDesign/less/style/mixins/clearfix.less", "AssetFile": "_content/AntDesign/less/style/mixins/clearfix.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "337"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NJxGdawyRZYFDwMaBZ0iK9UsHPxgVcUZ10YnnvAdhmw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NJxGdawyRZYFDwMaBZ0iK9UsHPxgVcUZ10YnnvAdhmw="}]}, {"Route": "_content/AntDesign/less/style/mixins/compact-item-vertical.less", "AssetFile": "_content/AntDesign/less/style/mixins/compact-item-vertical.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "749"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2bhuChT2VxXIpa6+TKhf5wCUii/tzp8/3p/ro66PKFM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhuChT2VxXIpa6+TKhf5wCUii/tzp8/3p/ro66PKFM="}]}, {"Route": "_content/AntDesign/less/style/mixins/compact-item.less", "AssetFile": "_content/AntDesign/less/style/mixins/compact-item.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3241"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WHf+yoc42XVw77VDhDm27RZJxcK1wBrhMoCAEzqgN4c=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WHf+yoc42XVw77VDhDm27RZJxcK1wBrhMoCAEzqgN4c="}]}, {"Route": "_content/AntDesign/less/style/mixins/compatibility.less", "AssetFile": "_content/AntDesign/less/style/mixins/compatibility.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "281"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0jAJVy8doVJDfio/QeI6cmDclCZZLeeT5jkKKsvSxSY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0jAJVy8doVJDfio/QeI6cmDclCZZLeeT5jkKKsvSxSY="}]}, {"Route": "_content/AntDesign/less/style/mixins/customize.less", "AssetFile": "_content/AntDesign/less/style/mixins/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4883"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"C7176onCyaxW2TBJsD4v4qmguzr+BN4FL/QfjVs93Lo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C7176onCyaxW2TBJsD4v4qmguzr+BN4FL/QfjVs93Lo="}]}, {"Route": "_content/AntDesign/less/style/mixins/iconfont.less", "AssetFile": "_content/AntDesign/less/style/mixins/iconfont.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aByEdP5naL8H+kiA0NO7HPsij6NSoQQbeAiMXa+ULPM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aByEdP5naL8H+kiA0NO7HPsij6NSoQQbeAiMXa+ULPM="}]}, {"Route": "_content/AntDesign/less/style/mixins/index.less", "AssetFile": "_content/AntDesign/less/style/mixins/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "368"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"TMqhTSsqVkEodIyXv/ekZhGTSwuPLzS9GGN288MizKE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TMqhTSsqVkEodIyXv/ekZhGTSwuPLzS9GGN288MizKE="}]}, {"Route": "_content/AntDesign/less/style/mixins/modal-mask.less", "AssetFile": "_content/AntDesign/less/style/mixins/modal-mask.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "565"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Jnm2S9YJM01esI/9hyTGUchagyXTy6otrz6G59X1P38=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jnm2S9YJM01esI/9hyTGUchagyXTy6otrz6G59X1P38="}]}, {"Route": "_content/AntDesign/less/style/mixins/motion.less", "AssetFile": "_content/AntDesign/less/style/mixins/motion.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "883"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"uEMJxYrmQgl6PCYyRi+zkJwnY5o1n2QwVQfGBDLw7fc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uEMJxYrmQgl6PCYyRi+zkJwnY5o1n2QwVQfGBDLw7fc="}]}, {"Route": "_content/AntDesign/less/style/mixins/operation-unit.less", "AssetFile": "_content/AntDesign/less/style/mixins/operation-unit.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "221"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0CqJnil5Oolrc38hsQdKIm6/s7rcH7EyWc0Np02V+jM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0CqJnil5Oolrc38hsQdKIm6/s7rcH7EyWc0Np02V+jM="}]}, {"Route": "_content/AntDesign/less/style/mixins/reset.less", "AssetFile": "_content/AntDesign/less/style/mixins/reset.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "272"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0AKQRkXvaCUDdY4wW8QgbDLyKMVDFSTHuu7TxDlaqcs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0AKQRkXvaCUDdY4wW8QgbDLyKMVDFSTHuu7TxDlaqcs="}]}, {"Route": "_content/AntDesign/less/style/mixins/rounded-arrow.less", "AssetFile": "_content/AntDesign/less/style/mixins/rounded-arrow.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Gc/JEv6O9qtAHdHrM7+S3A2S+KLICyMVM3J6JzxDpqk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gc/JEv6O9qtAHdHrM7+S3A2S+KLICyMVM3J6JzxDpqk="}]}, {"Route": "_content/AntDesign/less/style/mixins/size.less", "AssetFile": "_content/AntDesign/less/style/mixins/size.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "127"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cxJzrUFG5cyNzQxCAFtyIB6bcgmpJgUEJiuWde+DOqM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxJzrUFG5cyNzQxCAFtyIB6bcgmpJgUEJiuWde+DOqM="}]}, {"Route": "_content/AntDesign/less/style/mixins/typography.less", "AssetFile": "_content/AntDesign/less/style/mixins/typography.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7s6km3ef/0fWV7QRGVAyHteo0QzY7ukL2VYzNa4pg/4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7s6km3ef/0fWV7QRGVAyHteo0QzY7ukL2VYzNa4pg/4="}]}, {"Route": "_content/AntDesign/less/style/patch.less", "AssetFile": "_content/AntDesign/less/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1442"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BeaGUkww+B/0cPeu9NclGzEY1YXar/DCVkCCGeVzg6U=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BeaGUkww+B/0cPeu9NclGzEY1YXar/DCVkCCGeVzg6U="}]}, {"Route": "_content/AntDesign/less/style/themes/aliyun.less", "AssetFile": "_content/AntDesign/less/style/themes/aliyun.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2137"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3kVlVmnIBEJLAGMk6JM/GtLa5bAGgdzPjx2aCH9YuAc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3kVlVmnIBEJLAGMk6JM/GtLa5bAGgdzPjx2aCH9YuAc="}]}, {"Route": "_content/AntDesign/less/style/themes/compact.less", "AssetFile": "_content/AntDesign/less/style/themes/compact.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6980"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"v1A0Ya2nHoM57DOhCCW0V42JXUW+SDzGO7muPxCXxQk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v1A0Ya2nHoM57DOhCCW0V42JXUW+SDzGO7muPxCXxQk="}]}, {"Route": "_content/AntDesign/less/style/themes/dark.less", "AssetFile": "_content/AntDesign/less/style/themes/dark.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18438"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vBgsHJwmVx0k62a+iXZIQagGB0dDal4mLv9RwVgcxlk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vBgsHJwmVx0k62a+iXZIQagGB0dDal4mLv9RwVgcxlk="}]}, {"Route": "_content/AntDesign/less/style/themes/default.less", "AssetFile": "_content/AntDesign/less/style/themes/default.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36437"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"53Vp/9Vs7ACABNKaZbTpOdg3qo6orr3hodnb41RKnQY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-53Vp/9Vs7ACABNKaZbTpOdg3qo6orr3hodnb41RKnQY="}]}, {"Route": "_content/AntDesign/less/style/themes/index.less", "AssetFile": "_content/AntDesign/less/style/themes/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "239"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"a5IKt95bBUmO49NYye5Ndd7mxrzA3ljUb67LR6mijkQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5IKt95bBUmO49NYye5Ndd7mxrzA3ljUb67LR6mijkQ="}]}, {"Route": "_content/AntDesign/less/style/themes/variable.less", "AssetFile": "_content/AntDesign/less/style/themes/variable.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39868"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CZZmUodxIj1ROr3uuC+h44YQaog5u54l0dM1I/5NSFo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZZmUodxIj1ROr3uuC+h44YQaog5u54l0dM1I/5NSFo="}]}, {"Route": "_content/AntDesign/less/style/v2-compatible-reset.less", "AssetFile": "_content/AntDesign/less/style/v2-compatible-reset.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "503"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"AEW4o7ug739+ayvdrmBCLGzWf+HFregdrXe8vjvANTw=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AEW4o7ug739+ayvdrmBCLGzWf+HFregdrXe8vjvANTw="}]}, {"Route": "_content/AntDesign/less/style/variable.less", "AssetFile": "_content/AntDesign/less/style/variable.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "87"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QiYaw01su43F3Q4PEAOFeIFKRrOdjX+3pkUgt+xge5c=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qi<PERSON>aw01su43F3Q4PEAOFeIFKRrOdjX+3pkUgt+xge5c="}]}, {"Route": "_content/AntDesign/less/switch/style/entry.less", "AssetFile": "_content/AntDesign/less/switch/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/switch/style/index.less", "AssetFile": "_content/AntDesign/less/switch/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3478"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0R4nkKk9j+ex4G5f+VvpAOs4zwG6Nuk7hECIy78E+fQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0R4nkKk9j+ex4G5f+VvpAOs4zwG6Nuk7hECIy78E+fQ="}]}, {"Route": "_content/AntDesign/less/switch/style/patch.less", "AssetFile": "_content/AntDesign/less/switch/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "_content/AntDesign/less/switch/style/rtl.less", "AssetFile": "_content/AntDesign/less/switch/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3vavtHLvQMnf5bb17618w32xmOIj6NY/opFTs9LH4s8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vavtHLvQMnf5bb17618w32xmOIj6NY/opFTs9LH4s8="}]}, {"Route": "_content/AntDesign/less/table/style/bordered.less", "AssetFile": "_content/AntDesign/less/table/style/bordered.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3851"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vj0bC5Cu9fqF7+dRXSmXzFOqBD9RN779OYdtPhjcCqM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vj0bC5Cu9fqF7+dRXSmXzFOqBD9RN779OYdtPhjcCqM="}]}, {"Route": "_content/AntDesign/less/table/style/entry.less", "AssetFile": "_content/AntDesign/less/table/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "344"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"dJ2KI+W0dPZxYMj7hbQSSnhLb7hcCybtNhwcMiqSQiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dJ2KI+W0dPZxYMj7hbQSSnhLb7hcCybtNhwcMiqSQiM="}]}, {"Route": "_content/AntDesign/less/table/style/index.less", "AssetFile": "_content/AntDesign/less/table/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17452"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"id1CaQda9AGVWp7Vb+qVn+QKSB4Hk9qN5D/3Ope5Src=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-id1CaQda9AGVWp7Vb+qVn+QKSB4Hk9qN5D/3Ope5Src="}]}, {"Route": "_content/AntDesign/less/table/style/patch.less", "AssetFile": "_content/AntDesign/less/table/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"B+vU9Yxm5Ea30i5XdqzFpUEILD8UIWNOZSNC2mSMGhU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B+vU9Yxm5Ea30i5XdqzFpUEILD8UIWNOZSNC2mSMGhU="}]}, {"Route": "_content/AntDesign/less/table/style/radius.less", "AssetFile": "_content/AntDesign/less/table/style/radius.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1137"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7yXEFigGll9/OxVWi0V9ZOVp/gGDtX7z0TljVxTmc8k=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7yXEFigGll9/OxVWi0V9ZOVp/gGDtX7z0TljVxTmc8k="}]}, {"Route": "_content/AntDesign/less/table/style/rtl.less", "AssetFile": "_content/AntDesign/less/table/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3924"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UxjAsftAGzFCQ5maxFExWOuCf1KzS85BrgX+ui8IPXo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UxjAsftAGzFCQ5maxFExWOuCf1KzS85BrgX+ui8IPXo="}]}, {"Route": "_content/AntDesign/less/table/style/size.less", "AssetFile": "_content/AntDesign/less/table/style/size.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1848"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"5/L199VyMG1BQwhhFbH/e9EnL5uC2aTF2Y8/NG4P480=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5/L199VyMG1BQwhhFbH/e9EnL5uC2aTF2Y8/NG4P480="}]}, {"Route": "_content/AntDesign/less/tabs/style/card-style.less", "AssetFile": "_content/AntDesign/less/tabs/style/card-style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4700"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"nIb3tDffvs3IvfND+p1BVWDX3RPQjuBEJkJ6fRB903E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nIb3tDffvs3IvfND+p1BVWDX3RPQjuBEJkJ6fRB903E="}]}, {"Route": "_content/AntDesign/less/tabs/style/card-style.rtl.less", "AssetFile": "_content/AntDesign/less/tabs/style/card-style.rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "372"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VRr6Df7dqA26kNkhpCJ8Dza6ugxIbJXMC5QGqYlx90o=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VRr6Df7dqA26kNkhpCJ8Dza6ugxIbJXMC5QGqYlx90o="}]}, {"Route": "_content/AntDesign/less/tabs/style/card.less", "AssetFile": "_content/AntDesign/less/tabs/style/card.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2471"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"AW6KaqTlYGlJ/fHZRsVKO0nk3X25pyxbmRneNquWREA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AW6KaqTlYGlJ/fHZRsVKO0nk3X25pyxbmRneNquWREA="}]}, {"Route": "_content/AntDesign/less/tabs/style/dropdown.less", "AssetFile": "_content/AntDesign/less/tabs/style/dropdown.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hFueGgxoX1jJehlqAPejaKGa1QGZ4GHDC5SE1y9egOM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hFueGgxoX1jJehlqAPejaKGa1QGZ4GHDC5SE1y9egOM="}]}, {"Route": "_content/AntDesign/less/tabs/style/entry.less", "AssetFile": "_content/AntDesign/less/tabs/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/tabs/style/index.less", "AssetFile": "_content/AntDesign/less/tabs/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4953"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"XZ890pzE8obsyV0Zn40Igr0nsAmX0yG2yeFpIW9CSl0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XZ890pzE8obsyV0Zn40Igr0nsAmX0yG2yeFpIW9CSl0="}]}, {"Route": "_content/AntDesign/less/tabs/style/patch.less", "AssetFile": "_content/AntDesign/less/tabs/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1822"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"K3AslAxJaYcdidYg6oHdwjCzJHcFV1Qe9rIntVfIXKs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K3AslAxJaYcdidYg6oHdwjCzJHcFV1Qe9rIntVfIXKs="}]}, {"Route": "_content/AntDesign/less/tabs/style/position.less", "AssetFile": "_content/AntDesign/less/tabs/style/position.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4356"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LQepFBtrpHcqT/0X8iBB531kNlzmO5a0uGwKhGWUDNk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LQepFBtrpHcqT/0X8iBB531kNlzmO5a0uGwKhGWUDNk="}]}, {"Route": "_content/AntDesign/less/tabs/style/rtl.less", "AssetFile": "_content/AntDesign/less/tabs/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1726"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"m2l2SwxXQw93G+Uju9Aabt33T0Pi8kwvmHelnVr9xnY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m2l2SwxXQw93G+Uju9Aabt33T0Pi8kwvmHelnVr9xnY="}]}, {"Route": "_content/AntDesign/less/tabs/style/size.less", "AssetFile": "_content/AntDesign/less/tabs/style/size.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "871"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+wf4mb6BmXSD+HtxVia+Bspv7nJmyk7Ha8//QhoOIys=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+wf4mb6BmXSD+HtxVia+Bspv7nJmyk7Ha8//QhoOIys="}]}, {"Route": "_content/AntDesign/less/tag/style/entry.less", "AssetFile": "_content/AntDesign/less/tag/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/tag/style/index.less", "AssetFile": "_content/AntDesign/less/tag/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2787"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2ubVY++m43WT6rDihZ599e3MnO9xQ65DEmbL2V1IYmk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2ubVY++m43WT6rDihZ599e3MnO9xQ65DEmbL2V1IYmk="}]}, {"Route": "_content/AntDesign/less/tag/style/patch.less", "AssetFile": "_content/AntDesign/less/tag/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "42"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cnyN7xUDcVgxAMk1EwgsW7T6WnQ9u4tpPuo6IN5veu0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cnyN7xUDcVgxAMk1EwgsW7T6WnQ9u4tpPuo6IN5veu0="}]}, {"Route": "_content/AntDesign/less/tag/style/rtl.less", "AssetFile": "_content/AntDesign/less/tag/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "504"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7wqSpFFod/EDsEkRh22HhfRfpiilQ4lgC2iixKSkqi0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:48:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7wqSpFFod/EDsEkRh22HhfRfpiilQ4lgC2iixKSkqi0="}]}, {"Route": "_content/AntDesign/less/time-picker/style/entry.less", "AssetFile": "_content/AntDesign/less/time-picker/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/time-picker/style/index.less", "AssetFile": "_content/AntDesign/less/time-picker/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "107"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EAxXPFaNg+4xgCO6F6V8POSHCN76WprR05C7nPkyyDA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EAxXPFaNg+4xgCO6F6V8POSHCN76WprR05C7nPkyyDA="}]}, {"Route": "_content/AntDesign/less/timeline/style/entry.less", "AssetFile": "_content/AntDesign/less/timeline/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVAs4UR39aDS+DkzKUq0FciwuwyQ/ALfs0lJIMpDP/Y="}]}, {"Route": "_content/AntDesign/less/timeline/style/index.less", "AssetFile": "_content/AntDesign/less/timeline/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3928"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ckJ5I9xGbsNiJ/IsEu/tIOyD9EaSiH2PwK5TUpFxklM=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ckJ5I9xGbsNiJ/IsEu/tIOyD9EaSiH2PwK5TUpFxklM="}]}, {"Route": "_content/AntDesign/less/timeline/style/rtl.less", "AssetFile": "_content/AntDesign/less/timeline/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2842"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vYrl0eQ0FSgCaa6iCAggh0QVCoAh6Ht32vTEnzI1UdQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vYrl0eQ0FSgCaa6iCAggh0QVCoAh6Ht32vTEnzI1UdQ="}]}, {"Route": "_content/AntDesign/less/tooltip/style/entry.less", "AssetFile": "_content/AntDesign/less/tooltip/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pGaOA8XwC0YJfQgBXN706FXi6Sl2c06F5SsI381wgcE="}]}, {"Route": "_content/AntDesign/less/tooltip/style/index.less", "AssetFile": "_content/AntDesign/less/tooltip/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5165"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"J5TQcSP3M9P0QJIJ9SAYen1co1wOZ2dtR0eMNN3mnPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J5TQcSP3M9P0QJIJ9SAYen1co1wOZ2dtR0eMNN3mnPY="}]}, {"Route": "_content/AntDesign/less/tooltip/style/patch.less", "AssetFile": "_content/AntDesign/less/tooltip/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7ytBi+1wUy6fwY45429yioNsbEagRAwpqc2XxbYLB5I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7ytBi+1wUy6fwY45429yioNsbEagRAwpqc2XxbYLB5I="}]}, {"Route": "_content/AntDesign/less/tooltip/style/rtl.less", "AssetFile": "_content/AntDesign/less/tooltip/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "242"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LAfm+5BKbizwRTAmwdzFNMTJVeq1qUggnQzvW+ZlER0=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LAfm+5BKbizwRTAmwdzFNMTJVeq1qUggnQzvW+ZlER0="}]}, {"Route": "_content/AntDesign/less/transfer/style/customize.less", "AssetFile": "_content/AntDesign/less/transfer/style/customize.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1237"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UTl9jLRnvtlsD0kcCC3XNOiqRutMGeosaQT5qTnP58E=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UTl9jLRnvtlsD0kcCC3XNOiqRutMGeosaQT5qTnP58E="}]}, {"Route": "_content/AntDesign/less/transfer/style/entry.less", "AssetFile": "_content/AntDesign/less/transfer/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "210"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4I6JOWCjkDYC0hfBsEQ92spyxGIE4Y7o8PmsYSSQDhc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4I6JOWCjkDYC0hfBsEQ92spyxGIE4Y7o8PmsYSSQDhc="}]}, {"Route": "_content/AntDesign/less/transfer/style/index.less", "AssetFile": "_content/AntDesign/less/transfer/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4819"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aBiH5WCC6gv/kH4jaWe0G6rJp1fBU+3dGdm/hkoMFls=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBiH5WCC6gv/kH4jaWe0G6rJp1fBU+3dGdm/hkoMFls="}]}, {"Route": "_content/AntDesign/less/transfer/style/rtl.less", "AssetFile": "_content/AntDesign/less/transfer/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1266"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VHMI7IASdO6MNqAaE23vN6elkNuCLVa86S6I6VYsVOs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHMI7IASdO6MNqAaE23vN6elkNuCLVa86S6I6VYsVOs="}]}, {"Route": "_content/AntDesign/less/transfer/style/status.less", "AssetFile": "_content/AntDesign/less/transfer/style/status.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "514"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"KEKkjJgNVgat9LP7cLnHbCaEFZ5WB2k5gazu8Tlb07I=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KEKkjJgNVgat9LP7cLnHbCaEFZ5WB2k5gazu8Tlb07I="}]}, {"Route": "_content/AntDesign/less/tree-select/style/entry.less", "AssetFile": "_content/AntDesign/less/tree-select/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "216"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CoUkI01n0TnAG9+eYI6ukTgR6xc7seu7LdMCBNBfHJE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CoUkI01n0TnAG9+eYI6ukTgR6xc7seu7LdMCBNBfHJE="}]}, {"Route": "_content/AntDesign/less/tree-select/style/index.less", "AssetFile": "_content/AntDesign/less/tree-select/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1343"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8lqlU+pZNlSul68zneI1aoJQbvrsruhOtag1XadcNYg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8lqlU+pZNlSul68zneI1aoJQbvrsruhOtag1XadcNYg="}]}, {"Route": "_content/AntDesign/less/tree-select/style/patch.less", "AssetFile": "_content/AntDesign/less/tree-select/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "254"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"dgVujY2jJPlE7uyWtQ1B062+FFhmW4Ok0SGmB7zteUo=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dgVujY2jJPlE7uyWtQ1B062+FFhmW4Ok0SGmB7zteUo="}]}, {"Route": "_content/AntDesign/less/tree/style/directory.less", "AssetFile": "_content/AntDesign/less/tree/style/directory.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1439"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2tQMCFQsB5e4RcUx/R3xtlmdXxXb/hqnPmNjJ47I7mU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2tQMCFQsB5e4RcUx/R3xtlmdXxXb/hqnPmNjJ47I7mU="}]}, {"Route": "_content/AntDesign/less/tree/style/entry.less", "AssetFile": "_content/AntDesign/less/tree/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2sisl3n2BiAZe+UcXzOf5e+c2nvtL/t2d5KnuNY+cU="}]}, {"Route": "_content/AntDesign/less/tree/style/index.less", "AssetFile": "_content/AntDesign/less/tree/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "395"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qQls7XLlFPwg9CVCS8YfQ6Csaftpl+Cmkfwthoz8ZlQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qQls7XLlFPwg9CVCS8YfQ6Csaftpl+Cmkfwthoz8ZlQ="}]}, {"Route": "_content/AntDesign/less/tree/style/mixin.less", "AssetFile": "_content/AntDesign/less/tree/style/mixin.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7663"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7NEDVTJ9Zb5JHwfeqLVmJPD4qqheUJLEt8dSDDUbXDA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7NEDVTJ9Zb5JHwfeqLVmJPD4qqheUJLEt8dSDDUbXDA="}]}, {"Route": "_content/AntDesign/less/tree/style/patch.less", "AssetFile": "_content/AntDesign/less/tree/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "359"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"MvxHLK+PM6/oaKUiExTjRygdxzhVDfXbi+fnWN564jQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MvxHLK+PM6/oaKUiExTjRygdxzhVDfXbi+fnWN564jQ="}]}, {"Route": "_content/AntDesign/less/tree/style/rtl.less", "AssetFile": "_content/AntDesign/less/tree/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1638"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"TkuV/4RUEmkvC9upFPPPPMYFv3Um4+8dpIatu/f1b74=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TkuV/4RUEmkvC9upFPPPPMYFv3Um4+8dpIatu/f1b74="}]}, {"Route": "_content/AntDesign/less/typography/style/entry.less", "AssetFile": "_content/AntDesign/less/typography/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "128"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"b8nKK2DPt7P+ukX5khkYVBJG3GD1mUZ0CYkQfaSyoMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b8nKK2DPt7P+ukX5khkYVBJG3GD1mUZ0CYkQfaSyoMg="}]}, {"Route": "_content/AntDesign/less/typography/style/index.less", "AssetFile": "_content/AntDesign/less/typography/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4961"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4wrprdXObg+WvHF0FjSiHGuSV2/l9I5b9Qxf8OqgaT8=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4wrprdXObg+WvHF0FjSiHGuSV2/l9I5b9Qxf8OqgaT8="}]}, {"Route": "_content/AntDesign/less/typography/style/rtl.less", "AssetFile": "_content/AntDesign/less/typography/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "852"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Oiv8wjJkNcKZRDsbmNFhPgJqa6pRI8BTBNMnKOAbEzE=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Oiv8wjJkNcKZRDsbmNFhPgJqa6pRI8BTBNMnKOAbEzE="}]}, {"Route": "_content/AntDesign/less/upload/style/entry.less", "AssetFile": "_content/AntDesign/less/upload/style/entry.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "155"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YaYol8U5h1tINA+QJUpVyYhjaV1Ta9EbbLBF7niY6Lc=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YaYol8U5h1tINA+QJUpVyYhjaV1Ta9EbbLBF7niY6Lc="}]}, {"Route": "_content/AntDesign/less/upload/style/index.less", "AssetFile": "_content/AntDesign/less/upload/style/index.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11621"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vS6yBa3esfjSYiUbWPUmK4Vrd7Q1UjkEFggzEahHGX4=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vS6yBa3esfjSYiUbWPUmK4Vrd7Q1UjkEFggzEahHGX4="}]}, {"Route": "_content/AntDesign/less/upload/style/patch.less", "AssetFile": "_content/AntDesign/less/upload/style/patch.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "92"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QIrb6h15qi1qSrYzIy0ihuAJy+dK9/m53+FYmzSBcMA=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QIrb6h15qi1qSrYzIy0ihuAJy+dK9/m53+FYmzSBcMA="}]}, {"Route": "_content/AntDesign/less/upload/style/rtl.less", "AssetFile": "_content/AntDesign/less/upload/style/rtl.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3227"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"QAheaAlWQQJ8Wz8yDmVX249pDBBpsHQQ28Y5Z0Z/Clg=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Sep 2023 16:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QAheaAlWQQJ8Wz8yDmVX249pDBBpsHQQ28Y5Z0Z/Clg="}]}, {"Route": "_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.bundle.scp.css", "AssetFile": "_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7233"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OUxXVXl0U91HFqmnqLch2q/0hgCL7fAagiIxkUMWmpY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:47:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OUxXVXl0U91HFqmnqLch2q/0hgCL7fAagiIxkUMWmpY="}]}, {"Route": "_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css", "AssetFile": "_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7233"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OUxXVXl0U91HFqmnqLch2q/0hgCL7fAagiIxkUMWmpY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:47:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "<PERSON>yi<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-OUxXVXl0U91HFqmnqLch2q/0hgCL7fAagiIxkUMWmpY="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/UFU.CoreFX.Shared.bundle.scp.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/css/app.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5574"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yFV87DQdh6znsWF519fmsM72ozuldbjtFSUJwjrexDw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yFV87DQdh6znsWF519fmsM72ozuldbjtFSUJwjrexDw="}]}, {"Route": "_content/UFU.CoreFX.Shared/css/app.ogew5pzpvg.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5574"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yFV87DQdh6znsWF519fmsM72ozuldbjtFSUJwjrexDw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ogew5pzpvg"}, {"Name": "integrity", "Value": "sha256-yFV87DQdh6znsWF519fmsM72ozuldbjtFSUJwjrexDw="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/css/app.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/css/base-list.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/base-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6677"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TzFJW3EFM42TjT4ufCnZ2+uSwSpcwB7qfkRxEDU3x/I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TzFJW3EFM42TjT4ufCnZ2+uSwSpcwB7qfkRxEDU3x/I="}]}, {"Route": "_content/UFU.CoreFX.Shared/css/base-list.h492093d3n.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/base-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6677"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TzFJW3EFM42TjT4ufCnZ2+uSwSpcwB7qfkRxEDU3x/I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h492093d3n"}, {"Name": "integrity", "Value": "sha256-TzFJW3EFM42TjT4ufCnZ2+uSwSpcwB7qfkRxEDU3x/I="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/css/base-list.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/css/base.a6epnovc5j.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/base.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "147854"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/iSS9fwlbh/XzJCmhBXXAbS0y40vC6FeOaDfxXK5DY0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Oct 2024 07:59:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a6epnovc5j"}, {"Name": "integrity", "Value": "sha256-/iSS9fwlbh/XzJCmhBXXAbS0y40vC6FeOaDfxXK5DY0="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/css/base.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/css/base.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/base.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "147854"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/iSS9fwlbh/XzJCmhBXXAbS0y40vC6FeOaDfxXK5DY0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Oct 2024 07:59:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/iSS9fwlbh/XzJCmhBXXAbS0y40vC6FeOaDfxXK5DY0="}]}, {"Route": "_content/UFU.CoreFX.Shared/css/common.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "306"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0/Qntj7gxvOATZ0awD4I6gUFlnhotHYSfP9WY3FFhlc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0/Qntj7gxvOATZ0awD4I6gUFlnhotHYSfP9WY3FFhlc="}]}, {"Route": "_content/UFU.CoreFX.Shared/css/common.lm8e0mto1e.css", "AssetFile": "_content/UFU.CoreFX.Shared/css/common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "306"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0/Qntj7gxvOATZ0awD4I6gUFlnhotHYSfP9WY3FFhlc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lm8e0mto1e"}, {"Name": "integrity", "Value": "sha256-0/Qntj7gxvOATZ0awD4I6gUFlnhotHYSfP9WY3FFhlc="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/css/common.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/demo.css", "AssetFile": "_content/UFU.CoreFX.Shared/font/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8429"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/demo.ze809w2t8w.css", "AssetFile": "_content/UFU.CoreFX.Shared/font/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8429"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze809w2t8w"}, {"Name": "integrity", "Value": "sha256-ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/demo.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/demo_index.html", "AssetFile": "_content/UFU.CoreFX.Shared/font/demo_index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "361541"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r3ttHAliCrWE3kSiML4FjLMp7nW6/zdfW+cmhEzPRmg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r3ttHAliCrWE3kSiML4FjLMp7nW6/zdfW+cmhEzPRmg="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/demo_index.x0nn8ia2u9.html", "AssetFile": "_content/UFU.CoreFX.Shared/font/demo_index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "361541"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r3ttHAliCrWE3kSiML4FjLMp7nW6/zdfW+cmhEzPRmg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0nn8ia2u9"}, {"Name": "integrity", "Value": "sha256-r3ttHAliCrWE3kSiML4FjLMp7nW6/zdfW+cmhEzPRmg="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/demo_index.html"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.3vfiobr2kw.css", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21733"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z3SQLQSPi006kNPRrWplOgJIJx6e7DzbdlRxIDCoEPk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3vfiobr2kw"}, {"Name": "integrity", "Value": "sha256-z3SQLQSPi006kNPRrWplOgJIJx6e7DzbdlRxIDCoEPk="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/iconfont.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.css", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21733"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z3SQLQSPi006kNPRrWplOgJIJx6e7DzbdlRxIDCoEPk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3SQLQSPi006kNPRrWplOgJIJx6e7DzbdlRxIDCoEPk="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.js", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "484270"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O9V7wXLbznvLv8Kc8u7Hl9dv5QLE79bRiqp4dvszJ4A=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O9V7wXLbznvLv8Kc8u7Hl9dv5QLE79bRiqp4dvszJ4A="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.json", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70121"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"7ASh9tIVxdijqVFzSTHQgOg/SIqxfVdsFBwMtpQ5L+A=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7ASh9tIVxdijqVFzSTHQgOg/SIqxfVdsFBwMtpQ5L+A="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.k8yj7801q0.json", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "70121"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"7ASh9tIVxdijqVFzSTHQgOg/SIqxfVdsFBwMtpQ5L+A=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8yj7801q0"}, {"Name": "integrity", "Value": "sha256-7ASh9tIVxdijqVFzSTHQgOg/SIqxfVdsFBwMtpQ5L+A="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/iconfont.json"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.l4mf1jwdqk.js", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "484270"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O9V7wXLbznvLv8Kc8u7Hl9dv5QLE79bRiqp4dvszJ4A=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l4mf1jwdqk"}, {"Name": "integrity", "Value": "sha256-O9V7wXLbznvLv8Kc8u7Hl9dv5QLE79bRiqp4dvszJ4A="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/iconfont.js"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.n88ynfox0w.woff2", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53508"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"yc6Kv6CZI02eUF2YW++2dWpDLg1F+i+MFr0BCn+Cu/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n88ynfox0w"}, {"Name": "integrity", "Value": "sha256-yc6Kv6CZI02eUF2YW++2dWpDLg1F+i+MFr0BCn+Cu/4="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/iconfont.woff2"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.ntv5lif2h1.woff", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "64720"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"paIuahFKsXHVyCHyYHtT47QSYSXQxhYcFAdGbr38XXY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ntv5lif2h1"}, {"Name": "integrity", "Value": "sha256-paIuahFKsXHVyCHyYHtT47QSYSXQxhYcFAdGbr38XXY="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/iconfont.woff"}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.ttf", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "116128"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"DzOK6K89PZ0GpcOxgMj5BCHOTwUT+z6KZGcJJlIziCg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DzOK6K89PZ0GpcOxgMj5BCHOTwUT+z6KZGcJJlIziCg="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.woff", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "64720"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"paIuahFKsXHVyCHyYHtT47QSYSXQxhYcFAdGbr38XXY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-paIuahFKsXHVyCHyYHtT47QSYSXQxhYcFAdGbr38XXY="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.woff2", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53508"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"yc6Kv6CZI02eUF2YW++2dWpDLg1F+i+MFr0BCn+Cu/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yc6Kv6CZI02eUF2YW++2dWpDLg1F+i+MFr0BCn+Cu/4="}]}, {"Route": "_content/UFU.CoreFX.Shared/font/iconfont.zrmz9f59na.ttf", "AssetFile": "_content/UFU.CoreFX.Shared/font/iconfont.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "116128"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"DzOK6K89PZ0GpcOxgMj5BCHOTwUT+z6KZGcJJlIziCg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zrmz9f59na"}, {"Name": "integrity", "Value": "sha256-DzOK6K89PZ0GpcOxgMj5BCHOTwUT+z6KZGcJJlIziCg="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/font/iconfont.ttf"}]}, {"Route": "_content/UFU.CoreFX.Shared/images/default.54hs0b48tu.png", "AssetFile": "_content/UFU.CoreFX.Shared/images/default.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3326"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"W25D2AeBoqW6dd4KDgFzR4mUMfyb14BlgTnnljY+xL8=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "54hs0b48tu"}, {"Name": "integrity", "Value": "sha256-W25D2AeBoqW6dd4KDgFzR4mUMfyb14BlgTnnljY+xL8="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/images/default.png"}]}, {"Route": "_content/UFU.CoreFX.Shared/images/default.png", "AssetFile": "_content/UFU.CoreFX.Shared/images/default.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3326"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"W25D2AeBoqW6dd4KDgFzR4mUMfyb14BlgTnnljY+xL8=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W25D2AeBoqW6dd4KDgFzR4mUMfyb14BlgTnnljY+xL8="}]}, {"Route": "_content/UFU.CoreFX.Shared/images/down.hs9fnbdhiu.svg", "AssetFile": "_content/UFU.CoreFX.Shared/images/down.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "870"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"r40S9gklFH70Kx2k+W//K7v8hqo8R28IQmwHRpIEwgQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hs9fnb<PERSON>u"}, {"Name": "integrity", "Value": "sha256-r40S9gklFH70Kx2k+W//K7v8hqo8R28IQmwHRpIEwgQ="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/images/down.svg"}]}, {"Route": "_content/UFU.CoreFX.Shared/images/down.svg", "AssetFile": "_content/UFU.CoreFX.Shared/images/down.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "870"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"r40S9gklFH70Kx2k+W//K7v8hqo8R28IQmwHRpIEwgQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r40S9gklFH70Kx2k+W//K7v8hqo8R28IQmwHRpIEwgQ="}]}, {"Route": "_content/UFU.CoreFX.Shared/images/sort.svg", "AssetFile": "_content/UFU.CoreFX.Shared/images/sort.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "959"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"YbF13w5AIhIsZK+RxpgLlSvaYpXCHOZvwtQUSIMNJD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YbF13w5AIhIsZK+RxpgLlSvaYpXCHOZvwtQUSIMNJD0="}]}, {"Route": "_content/UFU.CoreFX.Shared/images/sort.tdvqggcqbj.svg", "AssetFile": "_content/UFU.CoreFX.Shared/images/sort.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "959"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"YbF13w5AIhIsZK+RxpgLlSvaYpXCHOZvwtQUSIMNJD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdvqggcqbj"}, {"Name": "integrity", "Value": "sha256-YbF13w5AIhIsZK+RxpgLlSvaYpXCHOZvwtQUSIMNJD0="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/images/sort.svg"}]}, {"Route": "_content/UFU.CoreFX.Shared/images/up.favli1t8ef.svg", "AssetFile": "_content/UFU.CoreFX.Shared/images/up.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "858"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"Wah3+CWs282LHH/3o9JHyb8/QdQGEy2hopYqNa+hmMw=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "favli1t8ef"}, {"Name": "integrity", "Value": "sha256-Wah3+CWs282LHH/3o9JHyb8/QdQGEy2hopYqNa+hmMw="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/images/up.svg"}]}, {"Route": "_content/UFU.CoreFX.Shared/images/up.svg", "AssetFile": "_content/UFU.CoreFX.Shared/images/up.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "858"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"Wah3+CWs282LHH/3o9JHyb8/QdQGEy2hopYqNa+hmMw=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wah3+CWs282LHH/3o9JHyb8/QdQGEy2hopYqNa+hmMw="}]}, {"Route": "_content/UFU.CoreFX.Shared/index.c1zyc2x1op.html", "AssetFile": "_content/UFU.CoreFX.Shared/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "913"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4AldamWKOutrskjspSYHwTl266Jxd1+0/gROlszaLDE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c1zyc2x1op"}, {"Name": "integrity", "Value": "sha256-4AldamWKOutrskjspSYHwTl266Jxd1+0/gROlszaLDE="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/index.html"}]}, {"Route": "_content/UFU.CoreFX.Shared/index.html", "AssetFile": "_content/UFU.CoreFX.Shared/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "913"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4AldamWKOutrskjspSYHwTl266Jxd1+0/gROlszaLDE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4AldamWKOutrskjspSYHwTl266Jxd1+0/gROlszaLDE="}]}, {"Route": "_content/UFU.CoreFX.Shared/js/cookieStore.51drh6b8ti.js", "AssetFile": "_content/UFU.CoreFX.Shared/js/cookieStore.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R0xsqZJwIA7A8byarKzO7mAJmocmWwaxnnyeZWIVs90=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51drh6b8ti"}, {"Name": "integrity", "Value": "sha256-R0xsqZJwIA7A8byarKzO7mAJmocmWwaxnnyeZWIVs90="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/js/cookieStore.js"}]}, {"Route": "_content/UFU.CoreFX.Shared/js/cookieStore.js", "AssetFile": "_content/UFU.CoreFX.Shared/js/cookieStore.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R0xsqZJwIA7A8byarKzO7mAJmocmWwaxnnyeZWIVs90=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R0xsqZJwIA7A8byarKzO7mAJmocmWwaxnnyeZWIVs90="}]}, {"Route": "_content/UFU.CoreFX.Shared/js/createCanvas.js", "AssetFile": "_content/UFU.CoreFX.Shared/js/createCanvas.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3958"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yIXNqmQBBiNnVVeFavZo1Yj0WeNb5AIIlV/dWO+tARo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yIXNqmQBBiNnVVeFavZo1Yj0WeNb5AIIlV/dWO+tARo="}]}, {"Route": "_content/UFU.CoreFX.Shared/js/createCanvas.kt2yn3i7de.js", "AssetFile": "_content/UFU.CoreFX.Shared/js/createCanvas.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3958"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yIXNqmQBBiNnVVeFavZo1Yj0WeNb5AIIlV/dWO+tARo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kt2yn3i7de"}, {"Name": "integrity", "Value": "sha256-yIXNqmQBBiNnVVeFavZo1Yj0WeNb5AIIlV/dWO+tARo="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/js/createCanvas.js"}]}, {"Route": "_content/UFU.CoreFX.Shared/js/utils.c3ya2mr0el.js", "AssetFile": "_content/UFU.CoreFX.Shared/js/utils.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6CqSynp9wMDyuFBFbWimrRsZfiv26F9+8kWBch/Auwo=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c3ya2mr0el"}, {"Name": "integrity", "Value": "sha256-6CqSynp9wMDyuFBFbWimrRsZfiv26F9+8kWBch/Auwo="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/js/utils.js"}]}, {"Route": "_content/UFU.CoreFX.Shared/js/utils.js", "AssetFile": "_content/UFU.CoreFX.Shared/js/utils.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6CqSynp9wMDyuFBFbWimrRsZfiv26F9+8kWBch/Auwo=\""}, {"Name": "Last-Modified", "Value": "Mon, 02 Sep 2024 02:00:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6CqSynp9wMDyuFBFbWimrRsZfiv26F9+8kWBch/Auwo="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8429"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo.ze809w2t8w.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8429"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze809w2t8w"}, {"Name": "integrity", "Value": "sha256-ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo_index.html", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo_index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "200241"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r5eZoDWAwN02K8eOAffClYGr0JXWuOgv/ywI+JoDNKY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r5eZoDWAwN02K8eOAffClYGr0JXWuOgv/ywI+JoDNKY="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo_index.rliuuggbbg.html", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo_index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "200241"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r5eZoDWAwN02K8eOAffClYGr0JXWuOgv/ywI+JoDNKY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rliuuggbbg"}, {"Name": "integrity", "Value": "sha256-r5eZoDWAwN02K8eOAffClYGr0JXWuOgv/ywI+JoDNKY="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/demo_index.html"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.5zwgyea89k.ttf", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71800"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"D/vFOpq7llPXQROt4rUnA4BL7yf8vMIoC3QCF30DULs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5zwgyea89k"}, {"Name": "integrity", "Value": "sha256-D/vFOpq7llPXQROt4rUnA4BL7yf8vMIoC3QCF30DULs="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.ttf"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.c0si1gxvli.woff2", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37976"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"TA+ZVGs+CswVtdXIf8dEtkEaTMnxqCcD/d6sgjcl/GA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c0si1gxvli"}, {"Name": "integrity", "Value": "sha256-TA+ZVGs+CswVtdXIf8dEtkEaTMnxqCcD/d6sgjcl/GA="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff2"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z2jrz6i+E74HC3SkbA4epdZyUaVTh99SR/jch3crn8I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z2jrz6i+E74HC3SkbA4epdZyUaVTh99SR/jch3crn8I="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.eeo0vds9ht.woff", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44320"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"hk5QdjFkiDl760Ca0pcfgyaUfHgwtPMz7ynZpQCGJA8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eeo0vds9ht"}, {"Name": "integrity", "Value": "sha256-hk5QdjFkiDl760Ca0pcfgyaUfHgwtPMz7ynZpQCGJA8="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.js", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "474191"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Rww4TiDr7At+RDDY+sxTZESoNwBpxB6xeLvCS3Xbxbc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rww4TiDr7At+RDDY+sxTZESoNwBpxB6xeLvCS3Xbxbc="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.json", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "38103"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"aNZ5VHRcy4cg5wKilTEYAp/OS4RzW8wgUi0tgfT05ic=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aNZ5VHRcy4cg5wKilTEYAp/OS4RzW8wgUi0tgfT05ic="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.nwb8ew5at4.js", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "474191"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Rww4TiDr7At+RDDY+sxTZESoNwBpxB6xeLvCS3Xbxbc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nwb8ew5at4"}, {"Name": "integrity", "Value": "sha256-Rww4TiDr7At+RDDY+sxTZESoNwBpxB6xeLvCS3Xbxbc="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.js"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.oof07czp3s.json", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "38103"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"aNZ5VHRcy4cg5wKilTEYAp/OS4RzW8wgUi0tgfT05ic=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oof07czp3s"}, {"Name": "integrity", "Value": "sha256-aNZ5VHRcy4cg5wKilTEYAp/OS4RzW8wgUi0tgfT05ic="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.json"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.ttf", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71800"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"D/vFOpq7llPXQROt4rUnA4BL7yf8vMIoC3QCF30DULs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D/vFOpq7llPXQROt4rUnA4BL7yf8vMIoC3QCF30DULs="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44320"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"hk5QdjFkiDl760Ca0pcfgyaUfHgwtPMz7ynZpQCGJA8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hk5QdjFkiDl760Ca0pcfgyaUfHgwtPMz7ynZpQCGJA8="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff2", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37976"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"TA+ZVGs+CswVtdXIf8dEtkEaTMnxqCcD/d6sgjcl/GA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TA+ZVGs+CswVtdXIf8dEtkEaTMnxqCcD/d6sgjcl/GA="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.zh2nsokffv.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z2jrz6i+E74HC3SkbA4epdZyUaVTh99SR/jch3crn8I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh2nsokffv"}, {"Name": "integrity", "Value": "sha256-Z2jrz6i+E74HC3SkbA4epdZyUaVTh99SR/jch3crn8I="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/alifont/core/iconfont.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/demo.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8429"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/demo.ze809w2t8w.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8429"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze809w2t8w"}, {"Name": "integrity", "Value": "sha256-ZUyxaGH0fZvb77rSutssu76rLMuioYg+p5XZtTgR/Gk="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/demo.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/demo_index.html", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/demo_index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "116841"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5ax1HfwjgcuubKF+2QZpBNQD54k/VpjqG+HAX2g2PKE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5ax1HfwjgcuubKF+2QZpBNQD54k/VpjqG+HAX2g2PKE="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/demo_index.zynzbl9kb6.html", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/demo_index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "116841"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5ax1HfwjgcuubKF+2QZpBNQD54k/VpjqG+HAX2g2PKE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zynzbl9kb6"}, {"Name": "integrity", "Value": "sha256-5ax1HfwjgcuubKF+2QZpBNQD54k/VpjqG+HAX2g2PKE="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/demo_index.html"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.12dt1be4xp.svg", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "598466"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"p4ubkb5UXci07pwMTXrG9MUSerUJyvU1GIzi9oZVZYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "12dt1be4xp"}, {"Name": "integrity", "Value": "sha256-p4ubkb5UXci07pwMTXrG9MUSerUJyvU1GIzi9oZVZYs="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.svg"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.3t1al3jwu0.js", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "608126"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WYd63zl9RvHHrNrKql/aKZDuhQcXn+gXFgwMWuzpNh4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3t1al3jwu0"}, {"Name": "integrity", "Value": "sha256-WYd63zl9RvHHrNrKql/aKZDuhQcXn+gXFgwMWuzpNh4="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.js"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "66168"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5XCz0hURrebtKLnD1jjmgwHuAkZ+EqidEqerIjPrELM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5XCz0hURrebtKLnD1jjmgwHuAkZ+EqidEqerIjPrELM="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.eot", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78120"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"yzrWvx8f+pM6qMmdRELdsGrnjnuW71IAnVVCJdeU5xA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yzrWvx8f+pM6qMmdRELdsGrnjnuW71IAnVVCJdeU5xA="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.ht6ny5tvnn.woff2", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44168"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"u1fxnO0UlhnS/qzN8tQKgctnXm3YVcitE322pJlQ6dg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ht6ny5tvnn"}, {"Name": "integrity", "Value": "sha256-u1fxnO0UlhnS/qzN8tQKgctnXm3YVcitE322pJlQ6dg="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff2"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.js", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "608126"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WYd63zl9RvHHrNrKql/aKZDuhQcXn+gXFgwMWuzpNh4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WYd63zl9RvHHrNrKql/aKZDuhQcXn+gXFgwMWuzpNh4="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.json", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21851"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"L/7NfB6wG8WxtTAMghDInEB+LqDAbSNN6RGb92UAk4E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/7NfB6wG8WxtTAMghDInEB+LqDAbSNN6RGb92UAk4E="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.ju4lnatej0.eot", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "78120"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"yzrWvx8f+pM6qMmdRELdsGrnjnuW71IAnVVCJdeU5xA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ju4lnatej0"}, {"Name": "integrity", "Value": "sha256-yzrWvx8f+pM6qMmdRELdsGrnjnuW71IAnVVCJdeU5xA="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.eot"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.lvithvnkg2.json", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21851"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"L/7NfB6wG8WxtTAMghDInEB+LqDAbSNN6RGb92UAk4E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lvithvnkg2"}, {"Name": "integrity", "Value": "sha256-L/7NfB6wG8WxtTAMghDInEB+LqDAbSNN6RGb92UAk4E="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.json"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.rcd6yxga24.css", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "66168"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5XCz0hURrebtKLnD1jjmgwHuAkZ+EqidEqerIjPrELM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rcd6yxga24"}, {"Name": "integrity", "Value": "sha256-5XCz0hURrebtKLnD1jjmgwHuAkZ+EqidEqerIjPrELM="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.css"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.svg", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "598466"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"p4ubkb5UXci07pwMTXrG9MUSerUJyvU1GIzi9oZVZYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p4ubkb5UXci07pwMTXrG9MUSerUJyvU1GIzi9oZVZYs="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.ttf", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "77944"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"GX6D8BLVOptm3dMCeUyCje/y2raiPugLLB+Gjn/QwoU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GX6D8BLVOptm3dMCeUyCje/y2raiPugLLB+Gjn/QwoU="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.u5269712g4.woff", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51344"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cgVBXMb9QGJZtNjnZ2bEuYEWc6xkND1/+eRAWUeq69U=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u5269712g4"}, {"Name": "integrity", "Value": "sha256-cgVBXMb9QGJZtNjnZ2bEuYEWc6xkND1/+eRAWUeq69U="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51344"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cgVBXMb9QGJZtNjnZ2bEuYEWc6xkND1/+eRAWUeq69U=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cgVBXMb9QGJZtNjnZ2bEuYEWc6xkND1/+eRAWUeq69U="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff2", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44168"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"u1fxnO0UlhnS/qzN8tQKgctnXm3YVcitE322pJlQ6dg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u1fxnO0UlhnS/qzN8tQKgctnXm3YVcitE322pJlQ6dg="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.xma9cpt6p3.ttf", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "77944"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"GX6D8BLVOptm3dMCeUyCje/y2raiPugLLB+Gjn/QwoU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xma9cpt6p3"}, {"Name": "integrity", "Value": "sha256-GX6D8BLVOptm3dMCeUyCje/y2raiPugLLB+Gjn/QwoU="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/font/iconfont.ttf"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/images/1.9a8m6w3qyi.jpg", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/images/1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18748"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"jzbYKadIUh2gTzxUGp3AlsXA0kjmiYQGqMEo4/9eftY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9a8m6w3qyi"}, {"Name": "integrity", "Value": "sha256-jzbYKadIUh2gTzxUGp3AlsXA0kjmiYQGqMEo4/9eftY="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/images/1.jpg"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/images/1.jpg", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/images/1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18748"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"jzbYKadIUh2gTzxUGp3AlsXA0kjmiYQGqMEo4/9eftY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jzbYKadIUh2gTzxUGp3AlsXA0kjmiYQGqMEo4/9eftY="}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/images/2.erh481y0wu.jpg", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/images/2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7889"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"FgFtu6EI7WyiZJyTOC3+0KFEeraDOU5sKjJrVoKTthY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erh481y0wu"}, {"Name": "integrity", "Value": "sha256-FgFtu6EI7WyiZJyTOC3+0KFEeraDOU5sKjJrVoKTthY="}, {"Name": "label", "Value": "_content/UFU.CoreFX.Shared/lib/verify/images/2.jpg"}]}, {"Route": "_content/UFU.CoreFX.Shared/lib/verify/images/2.jpg", "AssetFile": "_content/UFU.CoreFX.Shared/lib/verify/images/2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7889"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"FgFtu6EI7WyiZJyTOC3+0KFEeraDOU5sKjJrVoKTthY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 03:12:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FgFtu6EI7WyiZJyTOC3+0KFEeraDOU5sKjJrVoKTthY="}]}, {"Route": "_content/UFU.IoT.Shared/UFU.IoT.Shared.bundle.scp.css", "AssetFile": "_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5693"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mwXoxlU7ji5HzFqxW59/3U6cYVcXw8zWLzzxAZ8J+Lg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:47:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mwXoxlU7ji5HzFqxW59/3U6cYVcXw8zWLzzxAZ8J+Lg="}]}, {"Route": "_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css", "AssetFile": "_content/UFU.IoT.Shared/UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5693"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mwXoxlU7ji5HzFqxW59/3U6cYVcXw8zWLzzxAZ8J+Lg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:47:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rsp46tpbl7"}, {"Name": "integrity", "Value": "sha256-mwXoxlU7ji5HzFqxW59/3U6cYVcXw8zWLzzxAZ8J+Lg="}, {"Name": "label", "Value": "_content/UFU.IoT.Shared/UFU.IoT.Shared.bundle.scp.css"}]}, {"Route": "airkiss.dkhcs8bo8w.html", "AssetFile": "airkiss.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1848"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7U8PKkWEtxh46JI8m92UvW9/sJUa8od7XPjz34F0sIo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dkhcs8bo8w"}, {"Name": "integrity", "Value": "sha256-7U8PKkWEtxh46JI8m92UvW9/sJUa8od7XPjz34F0sIo="}, {"Name": "label", "Value": "airkiss.html"}]}, {"Route": "airkiss.html", "AssetFile": "airkiss.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1848"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7U8PKkWEtxh46JI8m92UvW9/sJUa8od7XPjz34F0sIo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7U8PKkWEtxh46JI8m92UvW9/sJUa8od7XPjz34F0sIo="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7022"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KHxAMD9U6cNFShWxW2jMWJWeTsyK2Hqw2i4rZ7/Gqag=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KHxAMD9U6cNFShWxW2jMWJWeTsyK2Hqw2i4rZ7/Gqag="}]}, {"Route": "index.ktkzw6ryo0.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7022"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KHxAMD9U6cNFShWxW2jMWJWeTsyK2Hqw2i4rZ7/Gqag=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ktkzw6ryo0"}, {"Name": "integrity", "Value": "sha256-KHxAMD9U6cNFShWxW2jMWJWeTsyK2Hqw2i4rZ7/Gqag="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.min.html", "AssetFile": "index.min.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6064"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GddG0nOWlVlryveJcbKKl1bvUlqNuy5lq53Z2UFrobU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GddG0nOWlVlryveJcbKKl1bvUlqNuy5lq53Z2UFrobU="}]}, {"Route": "index.min.tfad1g4lu9.html", "AssetFile": "index.min.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6064"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GddG0nOWlVlryveJcbKKl1bvUlqNuy5lq53Z2UFrobU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfad1g4lu9"}, {"Name": "integrity", "Value": "sha256-GddG0nOWlVlryveJcbKKl1bvUlqNuy5lq53Z2UFrobU="}, {"Name": "label", "Value": "index.min.html"}]}, {"Route": "iotmsg.html", "AssetFile": "iotmsg.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1441"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VXXh0Rzha8VFqrdqewwC+gB+dwC/ZBg5z9BKFU0onkE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VXXh0Rzha8VFqrdqewwC+gB+dwC/ZBg5z9BKFU0onkE="}]}, {"Route": "iotmsg.teovowdu0s.html", "AssetFile": "iotmsg.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1441"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VXXh0Rzha8VFqrdqewwC+gB+dwC/ZBg5z9BKFU0onkE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "teovowdu0s"}, {"Name": "integrity", "Value": "sha256-VXXh0Rzha8VFqrdqewwC+gB+dwC/ZBg5z9BKFU0onkE="}, {"Name": "label", "Value": "iotmsg.html"}]}, {"Route": "netconfig301.006o1sg79w.html", "AssetFile": "netconfig301.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YIWSXEXzJo076tcPW3RBc1/O+I3nNtzxaeFJAOnGxvY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "006o1sg79w"}, {"Name": "integrity", "Value": "sha256-YIWSXEXzJo076tcPW3RBc1/O+I3nNtzxaeFJAOnGxvY="}, {"Name": "label", "Value": "netconfig301.html"}]}, {"Route": "netconfig301.html", "AssetFile": "netconfig301.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YIWSXEXzJo076tcPW3RBc1/O+I3nNtzxaeFJAOnGxvY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YIWSXEXzJo076tcPW3RBc1/O+I3nNtzxaeFJAOnGxvY="}]}, {"Route": "netconfigindex.ganed4i40t.html", "AssetFile": "netconfigindex.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11557"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"mLWRKobeKfXvUZws5QBUObnAPc/JwyRQj7DV94PGS1E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ganed4i40t"}, {"Name": "integrity", "Value": "sha256-mLWRKobeKfXvUZws5QBUObnAPc/JwyRQj7DV94PGS1E="}, {"Name": "label", "Value": "netconfigindex.html"}]}, {"Route": "netconfigindex.html", "AssetFile": "netconfigindex.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11557"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"mLWRKobeKfXvUZws5QBUObnAPc/JwyRQj7DV94PGS1E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mLWRKobeKfXvUZws5QBUObnAPc/JwyRQj7DV94PGS1E="}]}, {"Route": "websocket.3n88wbcuej.html", "AssetFile": "websocket.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v0kJ9rMP7HYrCXbblS1SQFeW9SZ1qeBOSY9Af6W5UVw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3n88wbcuej"}, {"Name": "integrity", "Value": "sha256-v0kJ9rMP7HYrCXbblS1SQFeW9SZ1qeBOSY9Af6W5UVw="}, {"Name": "label", "Value": "websocket.html"}]}, {"Route": "websocket.html", "AssetFile": "websocket.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v0kJ9rMP7HYrCXbblS1SQFeW9SZ1qeBOSY9Af6W5UVw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v0kJ9rMP7HYrCXbblS1SQFeW9SZ1qeBOSY9Af6W5UVw="}]}, {"Route": "wificonfig.giqexvpcp8.html", "AssetFile": "wificonfig.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SFVhxfZDjwjA1JSZpFTCf2GfJ5ycmcn0hx+zacrXFxY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "giqexvpcp8"}, {"Name": "integrity", "Value": "sha256-SFVhxfZDjwjA1JSZpFTCf2GfJ5ycmcn0hx+zacrXFxY="}, {"Name": "label", "Value": "wificonfig.html"}]}, {"Route": "wificonfig.html", "AssetFile": "wificonfig.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10251"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SFVhxfZDjwjA1JSZpFTCf2GfJ5ycmcn0hx+zacrXFxY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SFVhxfZDjwjA1JSZpFTCf2GfJ5ycmcn0hx+zacrXFxY="}]}, {"Route": "wificonfig.min.aown4g87o9.html", "AssetFile": "wificonfig.min.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5962"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"1vbVz2qgwqG/ZY7c0xnVkNgQvQvpywUCIcFMLsKrIQk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aown4g87o9"}, {"Name": "integrity", "Value": "sha256-1vbVz2qgwqG/ZY7c0xnVkNgQvQvpywUCIcFMLsKrIQk="}, {"Name": "label", "Value": "wificonfig.min.html"}]}, {"Route": "wificonfig.min.html", "AssetFile": "wificonfig.min.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5962"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"1vbVz2qgwqG/ZY7c0xnVkNgQvQvpywUCIcFMLsKrIQk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 02:49:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1vbVz2qgwqG/ZY7c0xnVkNgQvQvpywUCIcFMLsKrIQk="}]}]}