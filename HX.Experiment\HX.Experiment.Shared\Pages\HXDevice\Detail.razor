@page "/HXDevice/Detail"
@using HX.Experiment.Shared.Model
@using HX.Experiment.Shared.Services
@using UFU.IoT.Models
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
<style>
    .device-detail-page {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .device-status-online {
        background: linear-gradient(135deg, #4caf50 0%, #81c784 100%) !important;
    }

    .device-status-offline {
        background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%) !important;
    }

    .device-status-connected {
        background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%) !important;
    }

    .new-test-fab {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(1.05); }
    }
</style>

<div class="device-detail-page">
    <MContainer MaxWidth="md" Class="py-8">
        <!-- 页面标题 -->
        <MRow Class="mb-6">
            <MCol>
                <div class="d-flex align-center">
                    <MAvatar Size="48" Color="primary" Class="mr-4">
                        <MIcon Color="white" Size="24">mdi-devices</MIcon>
                    </MAvatar>
                    <div>
                        <div class="text-h4 font-weight-bold">设备管理中心</div>
                        <div class="text-subtitle-1 grey--text">实时监控设备状态和连接信息</div>
                    </div>
                </div>
            </MCol>
        </MRow>

        <!-- 错误状态 -->
        @if (!string.IsNullOrEmpty(ErrorMsg))
        {
            <MAlert Type="AlertTypes.Error" 
                  
                   ColoredBorder 
                   Elevation="2"
                   Class="mb-6">
                <MRow Align="AlignTypes.Center">
                    <MCol >
                        <MIcon Size="32">mdi-alert-circle-outline</MIcon>
                    </MCol>
                    <MCol>
                        <div class="text-h6 font-weight-bold">连接异常</div>
                        <div class="text-body-2">@ErrorMsg</div>
                    </MCol>
                </MRow>
            </MAlert>
        }
        else if (DeviceModel != null && DeviceModel.IsOnline)
        {
            <MCard Elevation="8" Class="mb-6">
                <MCardTitle Class="device-status-online white--text pa-6">
                    <MRow Align="AlignTypes.Center" Justify="JustifyTypes.SpaceBetween">
                        <MCol >
                            <div class="d-flex align-center">
                                <MIcon Size="32" Class="mr-3 pulse-animation">mdi-check-circle</MIcon>
                                <div>
                                    <div class="text-h5 font-weight-bold">设备在线</div>
                                    <div class="text-body-2 white--text text--lighten-1">设备运行正常，可以开始检测</div>
                                </div>
                            </div>
                        </MCol>
                        <MCol >
                            <MChip Color="success" Dark Large>
                                <MIcon Left>mdi-wifi</MIcon>
                                已连接
                            </MChip>
                        </MCol>
                    </MRow>
                </MCardTitle>

                <MCardText Class="pa-6">
                    <MRow>
                        <MCol Cols="12" Md="6">
                            <MList Dense>
                                <MListItem>
                                    <MListItemIcon>
                                        <MIcon Color="primary">mdi-identifier</MIcon>
                                    </MListItemIcon>
                                    <MListItemContent>
                                        <MListItemTitle>设备序列号</MListItemTitle>
                                        <MListItemSubtitle>
                                            <MChip Small Color="blue-grey lighten-4">@DeviceModel.DeviceSN</MChip>
                                        </MListItemSubtitle>
                                    </MListItemContent>
                                </MListItem>

                                <MListItem>
                                    <MListItemIcon>
                                        <MIcon Color="primary">mdi-desktop-classic</MIcon>
                                    </MListItemIcon>
                                    <MListItemContent>
                                        <MListItemTitle>绑定电脑</MListItemTitle>
                                        <MListItemSubtitle>
                                            <MChip Small Color="blue-grey lighten-4">@DeviceModel.Name</MChip>
                                        </MListItemSubtitle>
                                    </MListItemContent>
                                </MListItem>

                                @if (!string.IsNullOrEmpty(DeviceModel.ChipSN))
                                {
                                    <MListItem>
                                        <MListItemIcon>
                                            <MIcon Color="primary">mdi-chip</MIcon>
                                        </MListItemIcon>
                                        <MListItemContent>
                                            <MListItemTitle>芯片序列号</MListItemTitle>
                                            <MListItemSubtitle>
                                                <MChip Small Color="purple lighten-4">@DeviceModel.ChipSN</MChip>
                                            </MListItemSubtitle>
                                        </MListItemContent>
                                    </MListItem>
                                }
                            </MList>
                        </MCol>
                        <MCol Cols="12" Md="6" Class="d-flex align-center justify-center">
                            <div class="text-center">
                                <MIcon Size="80" Color="success" Class="mb-4 pulse-animation">mdi-heart-pulse</MIcon>
                                <div class="text-h6 font-weight-bold">设备就绪</div>
                                <div class="text-body-2 grey--text">可以开始心率检测</div>
                            </div>
                        </MCol>
                    </MRow>
                </MCardText>

                <MCardActions Class="pa-6 pt-0">
                    <MSpacer />
                    <MButton Color="primary" 
                            Large 
                            Elevation="4"
                            Class="new-test-fab"
                            OnClick="OpenNewTestDialog">
                        <MIcon Left>mdi-plus-circle</MIcon>
                        开始新检测
                    </MButton>
                </MCardActions>
            </MCard>
        }
        else if (BindInfo != null)
        {
            <MCard Elevation="8" Class="mb-6">
                <MCardTitle Class="device-status-offline white--text pa-6">
                    <MRow Align="AlignTypes.Center">
                        <MCol >
                            <MIcon Size="32" Class="mr-3">mdi-wifi-off</MIcon>
                        </MCol>
                        <MCol>
                            <div class="text-h5 font-weight-bold">设备离线</div>
                            <div class="text-body-2 white--text text--lighten-1">设备已绑定但当前处于离线状态</div>
                        </MCol>
                        <MCol >
                            <MChip Color="warning" Dark>
                                <MIcon Left>mdi-alert</MIcon>
                                离线
                            </MChip>
                        </MCol>
                    </MRow>
                </MCardTitle>

                <MCardText Class="pa-6">
                    <MList Dense>
                        <MListItem>
                            <MListItemIcon>
                                <MIcon Color="orange">mdi-identifier</MIcon>
                            </MListItemIcon>
                            <MListItemContent>
                                <MListItemTitle>设备ID</MListItemTitle>
                                <MListItemSubtitle>
                                    <MChip Small Color="orange lighten-4">@BindInfo.Data.DeviceId</MChip>
                                </MListItemSubtitle>
                            </MListItemContent>
                        </MListItem>

                        <MListItem>
                            <MListItemIcon>
                                <MIcon Color="orange">mdi-desktop-classic</MIcon>
                            </MListItemIcon>
                            <MListItemContent>
                                <MListItemTitle>电脑ID</MListItemTitle>
                                <MListItemSubtitle>
                                    <MChip Small Color="orange lighten-4">@BindInfo.Data.ComputerId</MChip>
                                </MListItemSubtitle>
                            </MListItemContent>
                        </MListItem>

                        @if (BindInfo.Data.DeviceModel != null)
                        {
                            <MListItem>
                                <MListItemIcon>
                                    <MIcon Color="orange">mdi-tag</MIcon>
                                </MListItemIcon>
                                <MListItemContent>
                                    <MListItemTitle>设备名称</MListItemTitle>
                                    <MListItemSubtitle>
                                        <MChip Small Color="orange lighten-4">@BindInfo.Data.DeviceModel.Name</MChip>
                                    </MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                        }
                    </MList>
                </MCardText>

                <MCardActions Class="pa-6 pt-0">
                    <MButton Color="orange" Outlined OnClick="RefreshData">
                        <MIcon Left>mdi-refresh</MIcon>
                        重新连接
                    </MButton>
                    <MSpacer />
                    <MButton Color="grey" Text>
                        <MIcon Left>mdi-help-circle</MIcon>
                        故障排除
                    </MButton>
                </MCardActions>
            </MCard>
        }
        else if (CurrentLinkedDevice != null)
        {
            <MCard Elevation="8" Class="mb-6">
                <MCardTitle Class="device-status-connected white--text pa-6">
                    <MRow Align="AlignTypes.Center">
                        <MCol >
                            <MIcon Size="32" Class="mr-3">mdi-usb</MIcon>
                        </MCol>
                        <MCol>
                            <div class="text-h5 font-weight-bold">设备已连接</div>
                            <div class="text-body-2 white--text text--lighten-1">检测到已插入的设备</div>
                        </MCol>
                        <MCol >
                            <MChip Color="info" Dark>
                                <MIcon Left>mdi-link</MIcon>
                                已连接
                            </MChip>
                        </MCol>
                    </MRow>
                </MCardTitle>

                <MCardText Class="pa-6">
                    <MList Dense>
                        <MListItem>
                            <MListItemIcon>
                                <MIcon Color="blue">mdi-network</MIcon>
                            </MListItemIcon>
                            <MListItemContent>
                                <MListItemTitle>设备MAC地址</MListItemTitle>
                                <MListItemSubtitle>
                                    <MChip Small Color="blue lighten-4">@CurrentLinkedDevice.ShowName</MChip>
                                </MListItemSubtitle>
                            </MListItemContent>
                        </MListItem>
                    </MList>
                </MCardText>

                <MCardActions Class="pa-6 pt-0">
                    <MButton Color="red" 
                            Outlined 
                            OnClick="HandleClearData">
                        <MIcon Left>mdi-delete-sweep</MIcon>
                        清空数据
                    </MButton>
                    <MSpacer />
                    <MButton Color="blue" OnClick="RefreshData">
                        <MIcon Left>mdi-sync</MIcon>
                        同步设备
                    </MButton>
                </MCardActions>
            </MCard>
        }
        else
        {
            <MCard Elevation="8" Class="text-center pa-8">
                <MCardText>
                    <MIcon Size="80" Color="grey lighten-1" Class="mb-4">mdi-devices-off</MIcon>
                    <div class="text-h5 font-weight-bold mb-2">未检测到设备</div>
                    <div class="text-body-1 grey--text mb-6">
                        请确保设备已正确连接并插入到电脑
                    </div>
                    
                    <MExpansionPanels>
                        <MExpansionPanel>
                            <MExpansionPanelHeader>
                                <MIcon Class="mr-3">mdi-help-circle</MIcon>
                                连接帮助
                            </MExpansionPanelHeader>
                            <MExpansionPanelContent>
                                <MList Dense>
                                    <MListItem>
                                        <MListItemIcon><MIcon Color="primary">mdi-numeric-1-circle</MIcon></MListItemIcon>
                                        <MListItemContent>
                                            <MListItemTitle>检查USB连接</MListItemTitle>
                                            <MListItemSubtitle>确保设备USB线连接牢固</MListItemSubtitle>
                                        </MListItemContent>
                                    </MListItem>
                                    <MListItem>
                                        <MListItemIcon><MIcon Color="primary">mdi-numeric-2-circle</MIcon></MListItemIcon>
                                        <MListItemContent>
                                            <MListItemTitle>检查设备电源</MListItemTitle>
                                            <MListItemSubtitle>确保设备已开机并正常工作</MListItemSubtitle>
                                        </MListItemContent>
                                    </MListItem>
                                    <MListItem>
                                        <MListItemIcon><MIcon Color="primary">mdi-numeric-3-circle</MIcon></MListItemIcon>
                                        <MListItemContent>
                                            <MListItemTitle>重新插拔设备</MListItemTitle>
                                            <MListItemSubtitle>尝试重新插拔USB连接</MListItemSubtitle>
                                        </MListItemContent>
                                    </MListItem>
                                </MList>
                            </MExpansionPanelContent>
                        </MExpansionPanel>
                    </MExpansionPanels>
                </MCardText>

                <MCardActions Class="justify-center">
                    <MButton Color="primary" 
                            Large 
                            OnClick="RefreshData">
                        <MIcon Left>mdi-refresh</MIcon>
                        刷新检测
                    </MButton>
                </MCardActions>
            </MCard>
        }

        <!-- 快速操作卡片 -->
        <MCard Elevation="4" Class="mt-6">
            <MCardTitle Class="pa-4">
                <MIcon Class="mr-2">mdi-lightning-bolt</MIcon>
                <span class="text-h6">快速操作</span>
            </MCardTitle>
            <MCardText Class="pa-4 pt-0">
                <MRow>
                    <MCol Cols="12" Sm="6" Md="3">
                        <MButton Block 
                                Color="primary" 
                                Outlined 
                                Class="mb-2"
                                OnClick="RefreshData">
                            <MIcon Left>mdi-refresh</MIcon>
                            刷新状态
                        </MButton>
                    </MCol>
                    <MCol Cols="12" Sm="6" Md="3">
                        <MButton Block
                                Color="info"
                                Outlined
                                Class="mb-2"
                                OnClick="ImportDataAsync"
                                Disabled="@_isImporting">
                            @if (_isImporting)
                            {
                                <MProgressCircular Indeterminate Size="16" Class="mr-2"></MProgressCircular>
                                <span>@_importProgress%</span>
                            }
                            else
                            {
                                <MIcon Left>mdi-cloud-upload</MIcon>
                                <span>数据导入</span>
                            }
                        </MButton>
                    </MCol>
                    <MCol Cols="12" Sm="6" Md="3">
                        <MButton Block
                                Color="success"
                                Outlined
                                Class="mb-2"
                                OnClick="OpenDataAnalysisDialog">
                            <MIcon Left>mdi-chart-line</MIcon>
                            数据分析
                        </MButton>
                    </MCol>
                    <MCol Cols="12" Sm="6" Md="3">
                        <MButton Block 
                                Color="warning" 
                                Outlined 
                                Class="mb-2">
                            <MIcon Left>mdi-help</MIcon>
                            使用帮助
                        </MButton>
                    </MCol>
                </MRow>
            </MCardText>
        </MCard>
    </MContainer>
</div>

<!-- 新建检测对话框 -->
<MDialog @bind-Value="isShowNewTest"
         MaxWidth="942"
         Persistent="false">
    <MCard>
        <MCardTitle Class="primary white--text pa-6">
            <div class="d-flex align-center">
                <MIcon Size="28" Class="mr-3">mdi-flask-outline</MIcon>
                <span class="text-h5 font-weight-bold">新建检测任务</span>
            </div>
        </MCardTitle>
        <MCardText Class="pa-0">
            <StudentTestDialog CloseDialog="async () => { isShowNewTest = false; await RefreshData(); }">
            </StudentTestDialog>
        </MCardText>
    </MCard>
</MDialog>

<!-- 数据分析对话框 -->
<MDialog @bind-Value="isShowDataAnalysis"
         MaxWidth="1200"
         Persistent="false">
    <MCard>
        <MCardTitle Class="success white--text pa-6">
            <div class="d-flex align-center">
                <MIcon Size="28" Class="mr-3">mdi-chart-line</MIcon>
                <span class="text-h5 font-weight-bold">数据分析</span>
            </div>
        </MCardTitle>
        <MCardText Class="pa-6">
            <DataAnalysisDialog
                DeviceModel="DeviceModel"
                CloseDialog="() => isShowDataAnalysis = false">
            </DataAnalysisDialog>
        </MCardText>
    </MCard>
</MDialog>

@code {
    // 现有的代码保持不变
}