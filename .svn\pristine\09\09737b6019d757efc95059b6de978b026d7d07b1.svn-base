﻿using AntDesign;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.CoreFX.Shared.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Shared.Pages.DeviceType.ConfigItemPage;

public partial class EditConfigItem : ComponentBase
{
    private DataItem _dataItem;
    [Parameter] public string key { get; set; }
    [Parameter]public string typeId { get; set; }

    private const string BaseUrl = "/api/v2.0/IoT/ConfigItems/";
    [Inject] private StateService _stateService{ get; set; }
    [Inject] private MessageService _messageService{ get; set; }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (!string.IsNullOrEmpty(key))
        {
            var res = await _stateService.GetAsJsonAsync<DataItem>(BaseUrl + "Get",
                new Dictionary<string, string> { { "key", key }, { "typeId", typeId } });
            if (res.Success)
            {
                InitDataItem(res.Data);
            }
            else
            {
                await _messageService.Error(res.Message);
            }
        }
        else
        {
            _dataItem = new DataItem();
        }
    }

    private void InitDataItem(DataItem dataItem)
    {
        _dataItem = dataItem;
    }

    public async Task HandleSubmitAsync()
    {
        var queryDic = new Dictionary<string, string> {  { "typeId", typeId } };
        if (!string.IsNullOrEmpty(key))
        {
            queryDic.Add("key", key);
        }
        var url = string.IsNullOrEmpty(key) ? BaseUrl + "Add": BaseUrl + "Edit";
        var res = await this._stateService.PostAsJsonAsync<DataItem>(url,  _dataItem,queryDic);
        if (res.Success)
        {
            InitDataItem(res.Data);
            await _messageService.Success("成功！");
            _stateService.NavigationManager.NavigateTo($"/IoT/DeviceType/ConfigItemList/{typeId}");
        }
        else
        {
            await _messageService.Error(res.Message);
        }
    }


}