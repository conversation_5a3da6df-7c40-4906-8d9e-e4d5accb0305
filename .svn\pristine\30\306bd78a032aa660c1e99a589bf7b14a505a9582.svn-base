﻿@page "/RsaRegister"
@using System.Text
@using System.Text.Json
@using HX.HRV.Shared.Models.HybridWebViewModel
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Utils
@inject IJSRuntime js
@attribute [Permission(AllowAnonymous = true)]
@layout EmptyLayout
<MContainer Fluid="true" Class="pa-4">
	<MRow Justify="JustifyTypes.Center" Style="justify-content: center;">
		<MCol Md="8" Lg="6" Xl="4">
			<MCard Elevation="2">
				<MCardTitle Class="primary ">
					<MIcon Left Color="white">mdi-key-variant</MIcon>
					产品未激活，请复制机器码，联系客服激活产品
				</MCardTitle>
				<MCardText Class="pt-4">
					<MTextarea Label="机器码"
							   @bind-Value="HostId"
							   Id="copyText"
							   AutoGrow
							   Filled
							   Rows="10"
							   RowHeight="15">
					</MTextarea>
					<MButton Type="button"
							 Color="Blue"
							 Large
							 Block
							 Class="mt-4"
							 @onclick="CopyKeyToClipboardAsync">
						@if (IsCopy)
						{
							<MIcon Left>mdi-success</MIcon>
						}
						else
						{
							<MIcon Left>mdi-content-copy-off</MIcon>
						}
						@((IsCopy ? "复制成功" : "复制机器码"))
					</MButton>
				</MCardText>
			</MCard>
			<MCard Elevation="2" Class="mt-4">
				<MCardText Class="pt-4">
					<MTextarea Label="激活码"
					@bind-Value="_contentKey"
					Filled
					Rows="10"
					RowHeight="15">
					</MTextarea>
					<MButton @onclick=" ActivateProductAsync"
					Color="blue"
					Large
					Block
					Class="mt-4">
						<MIcon Left>mdi-check-circle-outline</MIcon>
						激活产品
					</MButton>
				</MCardText>
			</MCard>
		</MCol>
	</MRow>
</MContainer>

@code {
	[Inject] private NavigationManager NavigationManager { get; set; }
	private IBrowserFile _selectFile;
	private HttpClient _httpClient;
	private string _contentKey;
	private bool algIsVerify;
	private bool thisIsVerify;
	public string HostId { get; set; }
	[Inject] private IConfiguration Configuration { get; set; }
	[Inject] private IPopupService PopupService { get; set; }
	private void HandleOnChange(IBrowserFile file)
	{
		_selectFile = file;
	}


	private void Validate()
	{
		if (algIsVerify && thisIsVerify && ClientVerify)
		{
			PopupService.HideProgressCircular();
			NavigationManager.NavigateTo("/client/login");
		}
	}

	private async Task ActivateProductAsync()
	{
		PopupService.ShowProgressCircular();
		HX.StandtardFearmwork.RsaRegister.WriteLicenseKey(_contentKey);
		thisIsVerify = HX.StandtardFearmwork.RsaRegister.VerifyAndParseLicenseKey(out var thisLicenseData);
		var algHost = Configuration.GetSection("AlgorithmProxy:Host").Value;
		var algPort = Configuration.GetSection("AlgorithmProxy:Port").Value;

		var httpContent = new StringContent(
			JsonSerializer.Serialize(_contentKey),
			Encoding.UTF8,       
			System.Net.Mime.MediaTypeNames.Application.Json);

		using var httpResponseMessage =
		await _httpClient.PostAsync($"http://{algHost}:{algPort}/api/ProductRegister/UploadVerifyContent", httpContent);
		algIsVerify = false;
		if (httpResponseMessage.IsSuccessStatusCode)
		{
			algIsVerify = true;
			var str = await httpResponseMessage.Content.ReadAsStringAsync();
			var algLicenseData = JsonTool.Deserialize<HX.StandtardFearmwork.Models.LicenseData>(str);
		}
		Validate();
		var msgJson = new
		{
			action = "Verify",
			content = _contentKey,
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeAsync<object>("SendMessageToClientByMsg", msgStr);
	}

	protected override void OnInitialized()
	{
		_httpClient = new HttpClient();
		HostId = HX.StandtardFearmwork.RsaRegister.GetDeviceId();
	}

	protected override async Task OnInitializedAsync()
	{
		var objectRef = DotNetObjectReference.Create(this);
		await js.InvokeVoidAsync("InitHybridWebView", objectRef, "HandleReciveFromClient");
		var msgJson = new
		{
			action = "getDeviceId",
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeAsync<object>("SendMessageToClientByMsg", msgStr);
		await base.OnInitializedAsync();
		var algHost = Configuration.GetSection("AlgorithmProxy:Host").Value;
		var algPort = Configuration.GetSection("AlgorithmProxy:Port").Value;
		var str = await _httpClient.GetStringAsync($"http://{algHost}:{algPort}/api/ProductRegister/GetDeviceId");
		HostId += "," + str;
	}

	private bool IsCopy { get; set; }
	/// <summary>
	///
	/// </summary>

	private async Task CopyKeyToClipboardAsync()
	{
		await js.InvokeVoidAsync("navigator.clipboard.writeText", HostId);
		IsCopy = true;
		await InvokeAsync(StateHasChanged);
	}

	[JSInvokable]
	public async Task HandleReciveFromClient(string json)
	{
		var data = JsonTool.Deserialize<RecivedDataFromClient>(json);
		switch (data.action)
		{
			case "getDeviceId":
				{
					var messageData = JsonTool.Deserialize<RecivedDataFromClient<GetDeviceIdData>>(json);
					if (!string.IsNullOrEmpty(messageData.data.deviceId))
					{
						HostId += "," + messageData.data.deviceId;
					}
				}
				break;
			case "Verify":
				{
					var messageData = JsonTool.Deserialize<RecivedDataFromClient<RecivedVerifyData>>(json);
					var licenseData = messageData.data.licenseData;
					if (messageData.data.isValid)
					{
						ClientVerify = true;
						await InvokeAsync(StateHasChanged);
					}
					Validate();
				}
				break;
		}
	}

	public bool ClientVerify { get; set; }

	public class RecivedVerifyData
	{
		public bool isValid { get; set; }
		public HX.StandtardFearmwork.Models.LicenseData licenseData { get; set; }
	}

	public class GetDeviceIdData
	{
		public string deviceId { get; set; }
		public bool isValid { get; set; }
	}
}