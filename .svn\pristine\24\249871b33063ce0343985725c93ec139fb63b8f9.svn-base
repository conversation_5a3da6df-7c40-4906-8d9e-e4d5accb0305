﻿using System.IO.Compression;
using HX.HRV.Shared.Models;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace HX.HRV.Web.Services;

/// <summary>
/// 
/// </summary>
public class FileExportService : BackgroundService
{
    private static List<DataModel<HxExportTaskModel>> _pendingTasks;

    /// <summary>
    /// 
    /// </summary>
    public FileExportService()
    {
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await Task.Delay(2000, stoppingToken);
        await using var db = new DataRepository(UserInfo.System);
        // 使用 dataRepository 执行你的任务
        // 从数据库中获取待处理的任务

        while (!stoppingToken.IsCancellationRequested) // 循环执行直到服务被停止
        {
            await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
            _pendingTasks = await GetPendingTasksAsync(db);
            try
            {
                if (_pendingTasks is not { Count: > 0 })
                {
                    continue;
                }

                for (int i = 0; i < _pendingTasks.Count; i++)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    // 执行任务（压缩文件等）
                    await ExportFilesAsync(_pendingTasks[i], db);
                    _pendingTasks.Remove(_pendingTasks[i]);
                }
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, "处理任务导出时发生错误");
            }
        }
    }

    private async Task<List<DataModel<HxExportTaskModel>>> GetPendingTasksAsync(DataRepository _dbContext)
    {
        var query = _dbContext.Query<HxExportTaskModel>()
            .Where(t => t.Data.ExportStatus == EnumExportTaskStatus.UnStart);
        return await query.ToListAsync();
    }

    public static async Task AddTask(string taskId)
    {
        await using var db = new DataRepository(UserInfo.System);
        var task = db.Query<HxExportTaskModel>()
            .FirstOrDefault(m => m.Id == taskId);
        if (task == null)
            return;
        _pendingTasks.Add(task);
    }



    public async Task ExportFilesAsync(DataModel<HxExportTaskModel> taskdata, DataRepository db)
    {
        // LogTool.Logger.Information("开始执行导出任务,任务ID：{taskId}", taskdata.Id);
        var task = taskdata.Data;
        if (task.ExportStatus != EnumExportTaskStatus.Exporting)
        {
            task.ExportStatus = EnumExportTaskStatus.Exporting;
        }

        db.Update(taskdata);
        await db.SaveChangesAsync();
        var ids = task.PatientRecordIds;
        var records = await db.Query<PatientRecordModel>().Where(r => ids.Contains(r.Id)).ToListAsync();
        var folders = records.Select(r => r.Data.GetRecordDirectoryPath()).Distinct().ToList();
        var outputDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "export");
        var outputZipPath = Path.Combine(outputDirectory, $"{task.Id}.zip");
        // 检查文件夹是否存在
        if (!Directory.Exists(outputDirectory))
        {
            Directory.CreateDirectory(outputDirectory);
        }

        // 检查目标 ZIP 文件是否已存在，如果存在则删除
        if (File.Exists(outputZipPath))
        {
            File.Delete(outputZipPath);
        }

        // 创建一个新的 ZIP 文件
        using var zipArchive = ZipFile.Open(outputZipPath, ZipArchiveMode.Create);
        for (var i = 0; i < folders.Count; i++)
        {
            var folderPath = folders[i];
            // 检查文件夹是否存在
            if (!Directory.Exists(folderPath))
            {
                Console.WriteLine($"文件夹不存在: {folderPath}");
            }

            if (Directory.Exists(folderPath))
            {
                // 获取文件夹的名称，用作 ZIP 中的根目录名称
                var folderName = Path.GetFileName(folderPath);
                // 将文件夹中的所有文件和子文件夹添加到 ZIP 文件
                AddFolderToZip(zipArchive, folderPath, folderName);
            }
            // 更新进度
            task.Progress = (i + 1) / folders.Count * 100;
            db.Update(taskdata);
            await db.SaveChangesAsync();
            await Task.Delay(500);
        }
        // 导出完成
        task.ExportStatus = EnumExportTaskStatus.Finished;
        taskdata.UpdateTime = DateTime.Now;
        taskdata.Data.FilePath = outputZipPath;
        db.Update(taskdata);
        await db.SaveChangesAsync();
    }

    // 将文件夹中的所有文件和子文件夹递归添加到 ZIP 文件
    private static void AddFolderToZip(ZipArchive zipArchive, string folderPath, string baseFolderName)
    {
        // 获取文件夹中的所有文件
        var files = Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories);
        foreach (var file in files)
        {
            // 计算文件在 ZIP 文件中的相对路径
            var relativePath = Path.Combine(baseFolderName, Path.GetRelativePath(folderPath, file));
            // 添加文件到 ZIP
            zipArchive.CreateEntryFromFile(file, relativePath);
        }
    }

    public override async Task StopAsync(CancellationToken stoppingToken)
    {
        LogTool.Logger.Information("导出任务服务正在停止");
        await base.StopAsync(stoppingToken);
    }
}