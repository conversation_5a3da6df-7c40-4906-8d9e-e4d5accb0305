using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.Shared.Models;
using HX.HRV.Web.Units;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Utils;
using UFU.IoT.Services;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Services;

/// <summary>
///
/// </summary>
public static class ByteAnalysisServices
{
    public static void AnalysisData()
    {
        BinaryIoTService.OnReceivedDeviceMsg += ReceiveDeviceMsgTask;
        BinaryIoTService.OnDeviceOnline += OnDeviceOnline;
        BinaryIoTService.OnDeviceOffline += OnDeviceOffline;
    }

    /// <summary>
    ///   前置处理PPG数据
    /// </summary>
    public static Func<
        Dictionary<string, List<uint>>,
        Dictionary<string, object>
    > OnBeforeSavePPGHandler;

    private static void ReceiveDeviceMsgTask(IConnect connect, string msg)
    {
        Task.Run(async () => { await ReceiveDeviceMsg(connect, msg); });
    }

    private static async Task ReceiveDeviceMsg(IConnect connect, string msg)
    {
        var jsonNode = JsonNode.Parse(msg);
        var deviceSn = jsonNode?["Device"]?["SN"]?.ToString();
        var type = jsonNode?["Data"]?["Type"]?.GetValue<int>();
        var cmd = jsonNode?["CMD"]?.GetValue<int>();
        var msgId = jsonNode?["MsgId"]?.GetValue<int>();
        var device = DeviceStatusDataService.GetDeviceStatusData(deviceSn);
        var collectModeStr = jsonNode?["Data"]?["CollectMode"];
        var toPageJson = new JsonObject();
        toPageJson.Add("SN", deviceSn);
        var timeSpan = jsonNode!["Data"]?["Time"]?.GetValue<long>();
        LogTool.Logger.Information(
            $@"-------------------------ReceiveDeviceMsgStart--------------------------
                        SN:{deviceSn} 
                        CMD:{cmd}  
                        MsgId:{jsonNode?["MsgId"]}
                        Type:{type?.ToString()}
                        CollectMode:{collectModeStr ?? "null"} 
                                    "
        );
        if (collectModeStr == null)
        {
            if (cmd == (int)BinaryCMD.Ack)
            {
                //开始检测
                if (msgId == 11111)
                {
                    device.IsStoping = false;
                    await using var db = new DataRepository();
                    var deviceRecord = await db.Query<PatientRecordModel>()
                        .OrderByDescending(m => m.AddTime)
                        .FirstOrDefaultAsync(m =>
                            m.Data.DeviceId == device.Device.Id
                            && m.Data.EnumPatientCheckStatus == EnumPatientCheckStatus.Readying
                        );
                    if (deviceRecord != null)
                    {
                        device.StartTime = timeSpan * 1000;
                        await DeviceStatusDataService.SetPatientRecordBegin(
                            deviceSn,
                            deviceRecord.Data
                        );
                        toPageJson.Add("Status", JsonValue.Create(device.DeviceStatus));
                        toPageJson.Add("DeviceRecord", JsonValue.Create(toPageJson));
                        toPageJson.Add("From_CollectMode", JsonValue.Create(1));
                        await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        return;
                    }
                }

                //结束检测
                if (msgId == 22222)
                {
                    var json = jsonNode.ToJsonString();
                    await StopCheck(device, timeSpan, toPageJson);
                }
            }

            return;
        }

        var isParseCollectMode = int.TryParse(collectModeStr?.ToString(), out var collectMode);
        if (
            isParseCollectMode
            && collectMode == 1
            && device.RecordModel is { EnumPatientCheckStatus: EnumPatientCheckStatus.Readying }
        )
        {
            LogTool.Logger.Information("未收到开始检测消息,但已经进入检测模式,开始检测");
            await DeviceStatusDataService.SetPatientRecordBegin(deviceSn, device?.RecordModel);
            return;
        }

        if (type >= 0 && device is { RecordModel: not null })
        {
            JsonNode dataJsonNode = null;
            try
            {
                switch (type)
                {
                    case (int)HXDataType.PPG:
                    {
                        dataJsonNode = jsonNode["Data"]["PPG_G"];
                        var ppg_g_data = JsonTool.Deserialize<List<uint>>(
                            jsonNode["Data"]["PPG_G"].ToJsonString()
                        );
                        var ppg_r_data = JsonTool.Deserialize<List<uint>>(
                            jsonNode["Data"]["PPG_R"].ToJsonString()
                        );
                        var ppg_i_data = JsonTool.Deserialize<List<uint>>(
                            jsonNode["Data"]["PPG_I"].ToJsonString()
                        );
                        try
                        {
                            var data = new Dictionary<string, List<uint>>()
                            {
                                { "PPG_G", ppg_g_data },
                                { "PPG_R", ppg_r_data },
                                { "PPG_I", ppg_i_data },
                            };
                            device.StartTime ??= timeSpan * 1000;
                            dataJsonNode = jsonNode["Data"]["PPG_G"];
                            if (device.StartTime != null)
                            {
                                try
                                {
                                    var ppaRate = jsonNode["Data"]["PPGRate"].GetValue<int>();
                                    _ = Task.Run(async () =>
                                    {
                                        await AlgWebSocketClient.SendMsgToAlg(
                                            device.RecordModel.Id,
                                            data,
                                            ppaRate,
                                            device.StartTime.Value,
                                            false
                                        );
                                    });
                                }
                                catch (Exception e)
                                {
                                    LogTool.Logger.Error(e, "发送消息到算法平台错误");
                                }
                            }

                            if (device.RecordModel != null)
                            {
                                DeviceStatusDataService.OnReceiveorgPPGData(device, ppg_g_data);
                                toPageJson.Add("ORGPPG", JsonValue.Create(ppg_g_data));
                                toPageJson.Add("From_PPG", JsonValue.Create(1));
                                _ = Task.Run(async () =>
                                {
                                    if (OnBeforeSavePPGHandler != null)
                                    {
                                        OnBeforeSavePPGHandler?.Invoke(data);
                                    }
                                    FileDataHelper.SaveDataToCsv(
                                        device.RecordModel.GetRecordDirectoryPath(),
                                        timeSpan.Value,
                                        HXDataType.PPG.ToString(),
                                        data,
                                        device.MarkedValue,
                                        afterMarked: () =>
                                        {
                                            if (!device.IsMarked) return;
                                            device.MarkedValue = string.Empty;
                                            device.IsMarked = false;
                                        }
                                    );
                                });
                                _ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                            }
                        }
                        catch (Exception e)
                        {
                            LogTool.Logger.Error(e, "保存PPG数据出现错误");
                        }

                        break;
                    }
                    case (int)HXDataType.SPO2:
                    {
                        var spo2Data = jsonNode["Data"]["SPO2"];
                        var bmpData = jsonNode["Data"]["Bmp"];
                        var batteryData = jsonNode["Data"]["Battery"];
                        dataJsonNode = jsonNode["Data"]["SPO2"];
                        var spo2 = JsonTool.Deserialize<uint>(spo2Data.ToJsonString());
                        DeviceStatusDataService.OnReceiveSPO2Data(spo2, deviceSn);

                        var bmp = JsonTool.Deserialize<uint>(bmpData.ToJsonString());
                        DeviceStatusDataService.OnReceiveBmpData(bmp, deviceSn);

                        var battery = JsonTool.Deserialize<uint>(batteryData.ToJsonString());
                        DeviceStatusDataService.OnReceiveBatteryData(battery, deviceSn);
                        if (device.RecordModel != null)
                        {
                            FileDataHelper.SaveDataToCsv(
                                device.RecordModel.GetRecordDirectoryPath(),
                                timeSpan.Value,
                                "SPO2",
                                new Dictionary<string, List<uint>>()
                                {
                                    {
                                        "SPO2",
                                        new List<uint> { spo2 }
                                    },
                                    {
                                        "bpm",
                                        new List<uint> { bmp }
                                    },
                                    {
                                        "Battery",
                                        new List<uint> { battery }
                                    }
                                }
                            );
                        }

                        toPageJson.Add("SPO2", spo2);
                        toPageJson.Add("Bmp", bmp);
                        toPageJson.Add("Battery", battery);
                        toPageJson.Add("From_Battery", JsonValue.Create(1));
                        _ = HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        break;
                    }
                    case (int)HXDataType.IMU:
                    {
                        dataJsonNode = jsonNode["Data"]["IMU"]["GYRO-X"];
                        var data = JsonTool.Deserialize<List<short>>(dataJsonNode.ToJsonString());
                        DeviceStatusDataService.OnReceiveIMuData(data, deviceSn);
                        if (device.RecordModel != null)
                        {
                            FileDataHelper.SaveDataToCsv(
                                device.RecordModel.GetRecordDirectoryPath(),
                                timeSpan.Value,
                                "IMU",
                                new Dictionary<string, List<short>>() { { "IMU", data } }
                            );
                        }

                        toPageJson.Add("IMU", JsonValue.Create(data));
                        toPageJson.Add("From_IMU", JsonValue.Create(1));
                        await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        break;
                    }
                    case (int)HXDataType.SKINTEMP:
                    {
                        dataJsonNode = jsonNode["Data"]["SKINTEMP"];
                        var data = JsonTool.Deserialize<List<float>>(dataJsonNode.ToJsonString());
                        var gsrData = data.Select(m => 1 / m);
                        DeviceStatusDataService.OnReceiveSKINTEMPData(data, deviceSn);
                        toPageJson.Add("SKINTEMP", JsonValue.Create(gsrData));
                        toPageJson.Add("From_SKINTEMP", JsonValue.Create(1));
                        await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        break;
                    }
                    case (int)HXDataType.EDA:
                    {
                        dataJsonNode = jsonNode["Data"]["EDA"];
                        var data = JsonTool.Deserialize<List<float>>(dataJsonNode.ToJsonString());
                        var viewData = data.Select(m => m > 0 ? 1f / m : 0.0f).ToList();
                        DeviceStatusDataService.OnReceiveEDAData(viewData, deviceSn);
                        toPageJson.Add("EDA", JsonValue.Create(viewData));
                        toPageJson.Add("From_EDA", JsonValue.Create(1));
                        if (device.RecordModel != null)
                        {
                            FileDataHelper.SaveDataToCsv(
                                device.RecordModel.GetRecordDirectoryPath(),
                                timeSpan.Value,
                                "EDA",
                                new Dictionary<string, List<float>>() { { "EDA", data } }
                            );
                        }

                        try
                        {
                            _ = SendGsrMsgToAlg(
                                device.RecordModel.Id,
                                data.ToArray(),
                                timeSpan ?? DateTime.Now.ToUnixMs(),
                                device.StartTime ?? device.RecordModel.CollectStartTime.ToUnixMs()
                            );
                        }
                        catch (Exception e)
                        {
                            LogTool.Logger.Error(e, "SendGsrMsgToAlg");
                        }

                        await HXAdminWebSocket.SendMsgToAdmin(toPageJson.ToJsonString());
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                LogTool.Logger.Error(
                    e,
                    $"---------------------数据解析错误--------------------------:{dataJsonNode.ToJsonString()}"
                );
            }
        }

        LogTool.Logger.Information(
            $@"-------------------------ReceiveDeviceMsgEnd--------------------------"
        );
    }

    private static async Task StopCheck(
        DeviceStatusData device,
        long? timeSpan,
        JsonObject toPageJson
    )
    {
        var deviceData = DeviceStatusDataService.GetDeviceStatusData(device.Device.DeviceSN);
        if (deviceData.RecordModel is { EnumPatientCheckStatus: EnumPatientCheckStatus.Checked })
        {
            //检测结束给算法网关发送消息
            try
            {
                if (device.StartTime.HasValue)
                {
                    if (!device.IsSendEnd)
                    {
                        //await SendEndMsgToAlg(deviceData.RecordModel.Id, device.StartTime.Value, timeSpan ?? 0,true);
                        device.IsSendEnd = true;
                    }

                    await DeviceStatusDataService.SetPatientRecordEnd(device);
                }
            }
            catch (Exception e)
            {
                LogTool.Logger.Information($"发送数据到算法网关失败：" + e);
            }
        }
    }

    /// <summary>
    /// 构建存储到文件的类型
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    private static byte[] BuildStorageData(JsonNode jsonNode, long timeTicks, uint freq = 20)
    {
        //头（ 固定的两个字节 ） + 时间+数据类型+长度+频率+数据+尾
        var head = new byte[2] { 0x55, 0x80 };
        var time = BitConverter.GetBytes(timeTicks);
        var type = BitConverter.GetBytes((int)HXDataType.PPG);
        //将Data转为Json 然后转为字节数组
        var json = jsonNode.ToJsonString();
        var dataBytes = BitConverter.GetBytes(json.Length);
        var frequency = BitConverter.GetBytes(freq);
        var bytes = Encoding.UTF8.GetBytes(json);
        var tail = new byte[2] { 0x55, 0x81 };
        var all = new byte[
            head.Length
            + time.Length
            + type.Length
            + dataBytes.Length
            + frequency.Length
            + bytes.Length
            + tail.Length
        ];
        Buffer.BlockCopy(head, 0, all, 0, head.Length);
        Buffer.BlockCopy(time, 0, all, head.Length, time.Length);
        Buffer.BlockCopy(type, 0, all, head.Length + time.Length, type.Length);
        Buffer.BlockCopy(
            frequency,
            0,
            all,
            head.Length + time.Length + type.Length + dataBytes.Length,
            frequency.Length
        );
        Buffer.BlockCopy(
            dataBytes,
            0,
            all,
            head.Length + time.Length + type.Length,
            dataBytes.Length
        );
        Buffer.BlockCopy(
            bytes,
            0,
            all,
            head.Length + time.Length + type.Length + dataBytes.Length + frequency.Length,
            bytes.Length
        );
        Buffer.BlockCopy(
            tail,
            0,
            all,
            head.Length
            + time.Length
            + type.Length
            + dataBytes.Length
            + frequency.Length
            + bytes.Length,
            tail.Length
        );
        return all;
    }

    /// <summary>
    ///  存储文件
    ///  bin/year/month/day/key
    /// </summary>
    private static void SaveData(string path, string dataType, byte[] data)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }

        var filePath = Path.Combine(path, $"{dataType}.bin");
        File.AppendAllText(filePath, data.ToHex());
    }

    private static void OnDeviceOnline(ConnectDevice device)
    {
        var deviceData = DeviceStatusDataService.GetDeviceStatusData(device.Device.DeviceSN);
        if (deviceData is { DeviceStatus: EnumDeviceStatus.离线 })
        {
            Task.Run(async () =>
            {
                DeviceStatusDataService.SetDeviceOnline(device.DeviceId);
                await HXAdminWebSocket.SendMsgToAdmin(
                    JsonTool.SerializeIgnoreNull(
                        new
                        {
                            SN = device.Device.DeviceSN,
                            Status = deviceData.DeviceStatus,
                            From_Online = 1
                        }
                    )
                );
            });
        }
    }

    private static void OnDeviceOffline(ConnectDevice device)
    {
        LogTool.Logger.Information($"OnDeviceOffline:{device?.Device.DeviceSN}");
        var deviceData = DeviceStatusDataService.GetDeviceStatusData(device.Device.DeviceSN);
        if (deviceData != null && deviceData.DeviceStatus != EnumDeviceStatus.检测中)
        {
            DeviceStatusDataService.SetDeviceOffline(device.DeviceId);
            _ = HXAdminWebSocket.SendMsgToAdmin(
                JsonTool.SerializeIgnoreNull(
                    new
                    {
                        SN = device.Device.DeviceSN,
                        Status = deviceData.DeviceStatus,
                        From_Offline = 1
                    }
                )
            );
        }
    }

    /// <summary>
    /// 发送数据到算法
    /// </summary>
    /// <param name="device"></param>
    private static async Task SendMsgToAlg(
        string recordId,
        uint[] ppg_g_data,
        uint[] ppg_r_data,
        uint[] ppg_i_data,
        int ppgRate,
        long StartTime
    )
    {
        byte[] ppg_g_Bytes = new byte[ppg_g_data.Count() * sizeof(uint)];
        Buffer.BlockCopy(ppg_g_data.ToArray(), 0, ppg_g_Bytes, 0, ppg_g_Bytes.Length);

        byte[] ppg_r_Bytes = new byte[ppg_r_data.Count() * sizeof(uint)];
        Buffer.BlockCopy(ppg_r_data.ToArray(), 0, ppg_r_Bytes, 0, ppg_r_Bytes.Length);

        byte[] ppg_i_Bytes = new byte[ppg_i_data.Count() * sizeof(uint)];
        Buffer.BlockCopy(ppg_i_Bytes.ToArray(), 0, ppg_i_Bytes, 0, ppg_i_Bytes.Length);
        var jsonData = new
        {
            MessageType = 3,
            ClientId = recordId,
            Algorithm = "PPGCalculator",
            IsEnd = false,
            PPGRate = ppgRate,
            StartTime,
            Data = new
            {
                PPG_G_Length = ppg_g_Bytes.Length,
                PPG_R_Length = ppg_r_Bytes.Length,
                PPG_I_Length = ppg_i_Bytes.Length
            }
        };
        var bytes = new byte[ppg_g_Bytes.Length + ppg_r_Bytes.Length + ppg_i_Bytes.Length];
        Buffer.BlockCopy(ppg_g_Bytes, 0, bytes, 0, ppg_g_Bytes.Length);
        Buffer.BlockCopy(ppg_r_Bytes, 0, bytes, ppg_g_Bytes.Length, ppg_r_Bytes.Length);
        Buffer.BlockCopy(
            ppg_i_Bytes,
            0,
            bytes,
            ppg_g_Bytes.Length + ppg_r_Bytes.Length,
            ppg_i_Bytes.Length
        );
        var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
        await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, bytes, recordId);
    }

    private static async Task SendGsrMsgToAlg(
        string recordId,
        float[] gsr_g_data,
        long timeSpan,
        long StartTime
    )
    {
        byte[] databytes = new byte[gsr_g_data.Count() * sizeof(float)];
        Buffer.BlockCopy(gsr_g_data.ToArray(), 0, databytes, 0, databytes.Length);
        var jsonData = new
        {
            MessageType = 3,
            ClientId = recordId,
            Algorithm = "GSRSeparated",
            IsEnd = false,
            StartTime,
            Time = timeSpan * 1000,
            Data = new { gsr_Length = databytes.Length, }
        };
        var jsonBytes = Encoding.UTF8.GetBytes(JsonTool.Serialize(jsonData));
        LogTool.Logger.Information("SendGsrMsgToAlg:" + JsonTool.Serialize(new { gsr_g_data }));
        await AlgWebSocketClient.SendToAlgAsyncAndRegister(jsonBytes, databytes, recordId);
    }

   
}