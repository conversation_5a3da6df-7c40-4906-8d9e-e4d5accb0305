@page "/"
@using System.ComponentModel.DataAnnotations
@using Majorsoft.Blazor.Extensions.BrowserStorage
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Pages
@using UFU.CoreFX.Shared.Services
@layout EmptyLayout

<MApp>
    <MMain>
        <MContainer Fluid Class="login-container">
            <!-- 背景装饰 -->
            <div class="background-decoration"></div>
            
            <div class="login-wrapper">
                <MCard Width="450" Class="login-card" Elevation="24">
                    <!-- 顶部装饰条 -->
                    <div class="card-header-decoration"></div>

                    <MCardText Class="pa-8">
                        <!-- Logo 区域 -->
                        <div class="text-center mb-8">
                            <div class="logo-container mb-4">
                                <MAvatar Size="80" Color="primary" Class="elevation-8">
                                    <MIcon Size="40" Color="white">mdi-school</MIcon>
                                </MAvatar>
                            </div>
                            <MLabel Variant="h4" Class="font-weight-bold primary--text mb-2">
                                教学产品系统
                            </MLabel>
                            <MLabel
                                Variant="subtitle1" Class="grey--text text--darken-1">
                                欢迎回来，请登录您的账户
                            </MLabel>
                        </div>

                        <!-- 登录表单 -->
                        <MForm @ref="loginForm" Model="loginInfo" OnValidSubmit="UserLogin">
                            <DataAnnotationsValidator />
                            
                            <MTextField @bind-Value="loginInfo.Name"
                                       Label="学号"
                                       Outlined
                                       Dense
                                       PrependInnerIcon="mdi-account"
                                       Class="login-input mb-4"
                                       BackgroundColor="grey lighten-5"
                                       />

                            <MTextField @bind-Value="loginInfo.Password"
                                       Label="密码"
                                       Type="@(showPassword ? "text" : "password")"
                                       Outlined
                                       Dense
                                       PrependInnerIcon="mdi-lock"
                                       AppendIcon="@(showPassword ? "mdi-eye" : "mdi-eye-off")"
                                       OnAppendClick="() => showPassword = !showPassword"
                                       Class="login-input mb-4"
                                       BackgroundColor="grey lighten-5"
                                   
                                       />

                            <!-- 记住我和忘记密码 -->
                            <div class="d-flex justify-space-between align-center mb-6">
                                <MCheckbox @bind-Value="rememberMe" 
                                          Label="记住我" 
                                          Dense 
                                          Color="primary" />
                                <MButton Text Color="primary" Small OnClick="ShowForgotPassword">
                                    忘记密码？
                                </MButton>
                            </div>

                            <!-- 登录按钮 -->
                            <MButton Type="submit"
                                    Class="login-btn"
                                    Height="48"
                                    Block
                                    Color="primary"
                                    Large
                                    Loading="isLoading"
                                    Disabled="isLoading">
                                <MIcon Left>mdi-login</MIcon>
                                立即登录
                            </MButton>
                        </MForm>

                        <!-- 分割线 -->
                        <MDivider Class="my-6">
                            <span class="px-4 grey--text">或</span>
                        </MDivider>

                        
                    </MCardText>
                </MCard>
            </div>

            <!-- 底部信息 -->
            <MFooter Absolute Color="transparent" Class="text-center">
                <div class="white--text">
                    <MLabel Variant="body2" Class="mb-1">
                        © 2024 汇心软件 - 专业的教育技术解决方案
                    </MLabel>
                    <MLabel Variant="caption" Class="opacity-75">
                        版本 v2.0.1 | 技术支持
                    </MLabel>
                </div>
            </MFooter>
        </MContainer>
    </MMain>
</MApp>

<style>
    .login-container {
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .background-decoration {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23ffffff" opacity="0.1"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    .login-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        position: relative;
        z-index: 1;
    }

    .login-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 12px !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .login-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 24px 48px rgba(0, 0, 0, 0.15) !important;
    }

    .card-header-decoration {
        height: 6px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px 12px 0 0;
    }

    .logo-container {
        animation: pulse 2s infinite;
    }

    .login-input {
        transition: all 0.3s ease;
    }

    .login-input:hover {
        transform: translateY(-2px);
    }

    .login-btn {
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%) !important;
        transition: all 0.3s ease;
        text-transform: none !important;
        font-weight: 600;
        border-radius: 8px !important;
    }

    .login-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    }

    @@keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @@keyframes float {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .opacity-75 {
        opacity: 0.75;
    }
</style>

@code {
    private MForm? loginForm;
    private bool showPassword = false;
    private bool rememberMe = false;
    private bool isLoading = false;

    public class LoginInfo
    {
        [Required(ErrorMessage = "请输入学号")]
        public string Name { get; set; } = "";
        
        [Required(ErrorMessage = "请输入密码")]
        public string Password { get; set; } = "";
    }

    LoginInfo loginInfo = new()
    {
        Name = "UFU",
        Password = "ufuadminp@ssw0rd"
    };

    [Inject] private IPopupService PopupService { get; set; } = default!;
    [Inject] private StateService state { get; set; } = default!;
    [Inject] private NavigationManager navigationManager { get; set; } = default!;

    private async Task UserLogin()
    {
        if (loginForm?.Validate() != true) return;

        isLoading = true;
        
        try
        {
            var result = await state.PostAsJsonAsync<LoginTokenModel>("/api/v2/Core/Auth/Login", loginInfo);
            
            if (result.Success)
            {
                if (rememberMe)
                {
                    var cookie = new Cookie
                    {
                        Name = "token",
                        Value = result.Data.Token,
                        Expires = DateTime.Now.AddDays(7).ToUnixMs(),
                        SameSite = "lax"
                    };
                    await state.Cookies.SetAsync(cookie);
                }
                
                state.Token = result.Data.Token;
                
                await PopupService.EnqueueSnackbarAsync("登录成功！", AlertTypes.Success);
                navigationManager.NavigateTo("/experiment");
            }
            else
            {
                await PopupService.EnqueueSnackbarAsync(result.Message ?? "登录失败", AlertTypes.Error);
            }
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync("网络错误，请稍后重试", AlertTypes.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task ShowForgotPassword()
    {
        await PopupService.EnqueueSnackbarAsync("忘记密码功能即将上线", AlertTypes.Info);
    }

    private async Task ShowComingSoon(string feature)
    {
        await PopupService.EnqueueSnackbarAsync($"{feature}功能即将上线", AlertTypes.Info);
    }
}