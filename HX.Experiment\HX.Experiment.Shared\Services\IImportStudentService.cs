﻿using HX.Experiment.Shared.Model;
using UFU.CoreFX.Models;

namespace HX.Experiment.Shared.Services;

public interface IImportStudentService
{
    /// <summary>
    /// 从Excel文件导入学生信息
    /// </summary>
    /// <param name="fileStream">Excel文件流</param>
    /// <param name="fileName">文件名</param>
    /// <param name="classId">班级ID</param>
    /// <returns>导入结果</returns>
    Task<ImportResult> ImportStudentsFromExcelAsync(Stream fileStream, string fileName, string classId);

    /// <summary>
    /// 验证Excel文件格式
    /// </summary>
    /// <param name="fileStream">Excel文件流</param>
    /// <param name="fileName">文件名</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateExcelFormatAsync(Stream fileStream, string fileName);

    /// <summary>
    /// 获取Excel模板文件
    /// </summary>
    /// <returns>模板文件流</returns>
    Task<Stream> GetExcelTemplateAsync();
}

/// <summary>
/// 导入结果
/// </summary>
public class ImportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 成功导入的学生数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败的学生数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> ErrorMessages { get; set; } = new();

    /// <summary>
    /// 成功导入的学生列表
    /// </summary>
    public List<DataModel<StudentInfo>> ImportedStudents { get; set; } = new();

    /// <summary>
    /// 失败的学生信息
    /// </summary>
    public List<FailedStudentInfo> FailedStudents { get; set; } = new();
}

/// <summary>
/// 失败的学生信息
/// </summary>
public class FailedStudentInfo
{
    /// <summary>
    /// 行号
    /// </summary>
    public int RowNumber { get; set; }

    /// <summary>
    /// 学生姓名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 学号/身份证号
    /// </summary>
    public string CardId { get; set; } = string.Empty;

    /// <summary>
    /// 错误原因
    /// </summary>
    public string ErrorReason { get; set; } = string.Empty;
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> ErrorMessages { get; set; } = new();

    /// <summary>
    /// 预览的学生数据
    /// </summary>
    public List<StudentPreviewInfo> PreviewStudents { get; set; } = new();
}

/// <summary>
/// 学生预览信息
/// </summary>
public class StudentPreviewInfo
{
    /// <summary>
    /// 行号
    /// </summary>
    public int RowNumber { get; set; }

    /// <summary>
    /// 学生姓名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 学号/身份证号
    /// </summary>
    public string CardId { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID（可选）
    /// </summary>
    public string UserId { get; set; } = string.Empty;
}