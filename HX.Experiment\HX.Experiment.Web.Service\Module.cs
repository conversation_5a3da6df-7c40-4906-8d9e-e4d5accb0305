﻿using HX.Experiment.Web.Service.Services;
using HX.Experiment.Shared.Services;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace HX.Experiment.Web.Service
{
    /// <summary>
    /// 
    /// </summary>
    public class Module : BaseModule
    {
        public override string Name { get; set; } = "教学产品服务";
        public override string Icon { get; set; } = "iconfont icon-admin";
        public override string Description { get; set; } = "";
        public override async void Configure(IServiceCollection services)
        {
			ConfigureItems.Add(new ConfigureItem
            {
                Name = "教学产品服务",
                Order = 920,
                Services = async (serviceCollection) =>
                {
                    LogTool.Logger.Information("注册教学产品服务");
                    var hxDeviceTypeId = "2407160100000001";
                    serviceCollection.AddKeyedScoped<ISocketHandleService,HRVSocketHandleService>(hxDeviceTypeId);

                    // 注册Excel导入服务
                    // serviceCollection.AddScoped<IImportStudentService, ImportStudentService>();
                }
            });
        }
    }
}