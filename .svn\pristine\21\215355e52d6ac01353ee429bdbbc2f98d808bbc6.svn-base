﻿
@font-face {
    font-style: normal;
    font-family: 'Harmony_Regular';
    src: url('/_content/HX.HRV.Web//font/HarmonyOS_Sans_SC_Regular.ttf') format('truetype');
}

:root {
    --hx-hrv-blue: #9ac5fa;
    --body-font:'Harmony_Regular', sans-serif;
}


/*当页面宽度大于1000px且小于1200px的时候执行，1000-1200*/
@media screen and (min-width:1000px) and (max-width: 1200px){
    body{
        font-size:12px
    }
}
/*当页面宽度大于1280px且小于1366px的时候执行,1280-1366*/
@media screen and (min-width:1280px) and (max-width: 1366px){
    body{
        font-size:16px
    }
}
/*当页面宽度大于1440px且小于1600px的时候执行,1440-1600*/
@media screen and (min-width:1440px) and (max-width:1600px){
    body{
        font-size:20px
    }
}
/*当页面宽度大于1680px且小于1920px的时候执行,1680-1920*/
@media screen and (min-width:1680px) and (max-width:1920px){
    body{
        font-size:24px
    }
}
/*当页面宽度大于1680px且小于1920px的时候执行,1680-1920*/
@media screen and (min-width:1920px) and (max-width:2560px){
    body{
        font-size:28px
    }
}

.hx-flex-align {
    align-items: center;
    display: flex;
}

.hx-p-initial{
    padding: initial;
}

.hx-card-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

#bottom_version {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    height: 1rem;
}


.hx-h-100 {
    height: 100%;
}

.hx-w-100 {
    width: 100%;
}

.customer-button {
    background: linear-gradient(0deg, #008ef7, #1e9fff);
    border-radius: 28px;
    color: white;
    margin-left: 4px;
    font-size: 1.5rem;
}
.customer-button .m-btn__content{
    color: white;
}






