﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.IoT.Models;

namespace UFU.IoT.Services
{
    /// <summary>
    /// 虚拟设备
    /// </summary>
    public class VirtualDeviceService
    {
        /// <summary>
        /// 虚拟设备列表（根据id查询）
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="isVirtual"></param>
        /// <returns></returns>
        public async static Task<List<DataModel<DeviceModel>>> List(string ids, bool isVirtual = true)
        {

            await using var _context = new DataRepository(UserInfo.System);

            var result = new List<DataModel<DeviceModel>>();
            var query = _context.Query<DeviceModel>();

            if (isVirtual)
            {
                query = query.Where(m => m.Data.IsVirtual == true);
            }
            else
            {
                query = query.Where(m => m.Data.IsVirtual != true);
            }            
            if (!string.IsNullOrWhiteSpace(ids))
            {
                var idlist = ids.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Id));
            }
            
            var list = await query.ToListAsync();
            //设备类型
            var typeIds = list.Select(m => m.Data.TypeId).Distinct().ToList();
            var deviceTypes = await _context.Query<DeviceTypeModel>().Where(m => typeIds.Contains(m.Id)).Select(m => m.Data).ToListAsync();
            foreach (var item in list)
            {
                item.Data.DeviceType = deviceTypes.FirstOrDefault(m => m.Id == item.Data.TypeId);
                item.Data.IsOnline = IoTService.Devices.Values.Any(m => m.DeviceId == item.Id);
            }

            //最新数据
            var dataids = list.Select(m => m.Id);
            var latestData = await _context.Query<LatestDataModel>().Where(m => dataids.Contains(m.Data.DeviceId)).ToListAsync();
            foreach (var item in latestData)
            {
                var device = list.FirstOrDefault(m => m.Id == item.Data.DeviceId);
                if (device != null)
                {
                    device.Data.LatestData = item.Data;
                }
            }
            result = list;
            return result;

        }
    }
}
