﻿using UFU.CoreFX.Utils;
using System.Collections.Concurrent;
using System.Timers;
using Timer = System.Timers.Timer;
namespace HX.HRV.Web.Services;
/// <summary>
/// 
/// </summary>
public static class FileBufferManager
{
    private static readonly ConcurrentDictionary<string, ConcurrentQueue<List<string>>> BufferDict = new();
    private const int BatchSize = 10000;
    private const double FlushInterval = 2000; 

    //Elapsed执行次数
    private static readonly Dictionary<string, int> WaitFlushDictionary = new();

    //Elapsed超过次数未写入数据 删除BufferDict中的key
    private const int WaitFlushCount = 10;
    private static readonly Timer FlushTimer = new(FlushInterval);
    static FileBufferManager()
    {
        FlushTimer.Elapsed += (sender, e) => FlushAllBuffers();
        FlushTimer.Start();
    }

    /// <summary>
    /// 异步向缓冲区添加数据
    /// </summary>
    public static async Task AddToBuffer<T>(string filePath, Dictionary<string, List<T>> data, long timeSpan,
        string markValue = "") where T : struct
    {
        LogTool.Logger.Information("添加数据到缓冲区:");
        var str = string.Join(",", data.Select(m => "【" + m.Key + "】" + ":" + m.Value.Count));
        LogTool.Logger.Information(str);
        var bufferKey = filePath;
        var dataKeys = data.Keys.ToList();
            var newQueue = new ConcurrentQueue<List<string>>();
        var queue = BufferDict.GetOrAdd(bufferKey, _ =>
        {
            if (!File.Exists(filePath))
            {
                if (!Directory.Exists(Path.GetDirectoryName(filePath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(filePath));
                }
                var csvHeader = new List<string> { "Time", }; // 补充Time列（根据FileDataHelper逻辑）
                csvHeader.AddRange(dataKeys);
                csvHeader.Add("Event");
                newQueue.Enqueue(new List<string>(){string.Join(",",csvHeader)});
                
                File.WriteAllText(filePath, string.Empty);
            }
            WaitFlushDictionary.TryAdd(filePath, 0);
            return newQueue;
        });

        // 构造数据行（同步操作）
        var dataFirst = data.FirstOrDefault();
        var count = dataFirst.Value.Count;
        DateTime dateTime = DateTimeOffset.FromUnixTimeSeconds(timeSpan).LocalDateTime;
        var dataList = new List<string>();
        for (int i = 0; i < count; i++)
        {
            var date = dateTime.AddMilliseconds((1000.0 / count) * i);
            var strParts = new List<string> { date.ToString("yyyy-MM-dd HH:mm:ss.fff") }; // 时间列
            foreach (var key in dataKeys)
            {
                strParts.Add(data[key]?[i].ToString());
            }

            if (!string.IsNullOrEmpty(markValue) && i == 0)
            {
                strParts.Add(markValue);
            }

            dataList.Add(string.Join(",", strParts));
        }
        queue.Enqueue(dataList);
        if (queue.Count >= BatchSize)
        {
            await FlushBuffer(filePath); 
        }
    }

    /// <summary>
    /// 异步刷新指定文件的缓冲区
    /// </summary>
    private static async Task FlushBuffer(string filePath)
    {
        if (!BufferDict.TryGetValue(filePath, out var queue)) return;
        var batchData = new List<string>();
        var batchCount = queue.Count;
        while (batchCount-- > 0 && queue.TryDequeue(out var item))
        {
            batchData.AddRange(item);
        }
        if (batchData.Any())
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                await File.AppendAllLinesAsync(filePath, batchData);
            }
            catch (Exception e)
            {
                LogTool.Logger.Error("异步写入文件失败：" + e.StackTrace);
            }
        }
        else
        {
            if (WaitFlushDictionary.TryGetValue(filePath, out var count))
            {
                if (count >= WaitFlushCount)
                {
                    BufferDict.TryRemove(filePath, out _);
                    WaitFlushDictionary.Remove(filePath);
                }
                else
                {
                    WaitFlushDictionary[filePath] = count + 1;
                }
            }
        }
    }

    /// <summary>
    /// 异步刷新所有缓冲区
    /// </summary>
    private static void FlushAllBuffers()
    {
        var keys = BufferDict.Keys.ToList();
        Parallel.ForEach(keys, async key =>
        {
            try
            {
                await FlushBuffer(key);
            }
            catch (Exception e)
            {
                LogTool.Logger.Error("全局刷新异常：" + e);
            }
        });
    }
}