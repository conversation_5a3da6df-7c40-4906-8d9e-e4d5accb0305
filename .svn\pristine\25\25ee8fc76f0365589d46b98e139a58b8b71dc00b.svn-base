﻿using System.Management;
using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Nodes;
using HX.HRV.MAUI.Services;
using Makaretu.Dns;
using Microsoft.Extensions.Configuration;
using Microsoft.UI.Xaml.Controls;
using Microsoft.Web.WebView2.Core;

namespace HX.HRV.MAUI;

public partial class MainPage : ContentPage
{
    private readonly UDiskService _udiskService;
    private readonly SerialPortService _comPortService;
    private static string Host;

    public MainPage(IConfiguration config, SerialPortService _serialPortService, UDiskService uDiskService)
    {
        var ipAddresses = MulticastService.GetIPAddresses();
        foreach (var ipAddress in ipAddresses)
        {
            //假如是IPv4地址
            if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
            {
                Host = ipAddress + ":5219";
                break;
            }
        }

        Host ??= File.ReadAllText("host.txt").Trim() is { Length: > 0 } host ? host : "127.0.0.1:5001";
        InitializeComponent();
        _udiskService = uDiskService;
        _comPortService = _serialPortService;

        _udiskService.StartMonitoring();
        _comPortService.StartMonitoring();
        hybridWebView.SetInvokeJavaScriptTarget(this);
        hybridWebView.Loaded += (s, e) =>
        {
            ((HybridWebView)s).EvaluateJavaScriptAsync($"window.location = 'http://{Host}'");
        };
    }

//    protected override void OnHandlerChanged()
//    {
//        base.OnHandlerChanged();
//        //设置 windows 下自动全屏显示 https://www.saoniuhuo.com/question/detail-2593977.html
//#if WINDOWS
//        var window = App.Current.Windows.FirstOrDefault().Handler.PlatformView as Microsoft.UI.Xaml.Window;
//        IntPtr windowHandle = WinRT.Interop.WindowNative.GetWindowHandle(window);
//        Microsoft.UI.WindowId windowId = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(windowHandle);
//        Microsoft.UI.Windowing.AppWindow appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(windowId);
//        //全屏显示，无最大化、最小化、关闭栏。无窗体外壳。
//        appWindow.SetPresenter(Microsoft.UI.Windowing.AppWindowPresenterKind.FullScreen);
//#endif
//    }
    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        // 获取底层的 WebView2 对象

        if (hiddenWebView.Handler.PlatformView is WebView2 webView2
            && webView2.CoreWebView2 != null
            && webView2.CoreWebView2.CookieManager != null)
        {
            // 清除所有 cookie
            webView2.CoreWebView2.CookieManager.DeleteAllCookies();
        }

        if (hybridWebView.Handler.PlatformView is WebView2 webView)
        {
            webView.CoreWebView2.CookieManager.DeleteAllCookies();
        }
    }

    private void hybridWebView_RawMessageReceived(object sender, HybridWebViewRawMessageReceivedEventArgs e)
    {
        var jsonNode = JsonNode.Parse(e.Message);
        var action = jsonNode["action"].GetValue<string>();
        var deviceSN = string.Empty;
        switch (action)
        {
            case "ExportToClient":
                Task.Run(async () => await UploadFileAsync(e.Message));
                break;
            case "getDeviceInfo":
                hybridWebView.SendRawMessage(JsonSerializer.Serialize(new
                {
                    action = jsonNode["action"],
                    data = _udiskService.DiskList
                }));
                break;
            case "printPdf":
                // 获取底层的 WebView2 对象
                if (hiddenWebView.Handler.PlatformView is WebView2 webView2)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        try
                        {
                            CoreWebView2PrintSettings printSettings = null;
                            printSettings = webView2.CoreWebView2.Environment.CreatePrintSettings();
                            printSettings.MarginBottom = 0;
                            printSettings.MarginLeft = 0;
                            printSettings.MarginRight = 0;
                            printSettings.MarginTop = 0.1;
                            var id = jsonNode["printId"].GetValue<string>();
                            webView2.CoreWebView2.Navigate($"http://{Host}/client/report/report-detail/empty/{id}");
                            await Task.Delay(5000);
                            await webView2.CoreWebView2.PrintAsync(printSettings);
                        }
                        catch (Exception)
                        {
                            await File.AppendAllTextAsync($"error_{DateTime.Now:yyyy-M-d}.txt", e.Message);
                        }
                    });
                }

                break;
            case "toSerialPort":
                deviceSN = jsonNode["Device"]["SN"].GetValue<string>();
                var data = jsonNode.ToJsonString();
                _comPortService.SendMessageToComPort(data, deviceSN);
                break;
            case "openSerialPort":
                deviceSN = jsonNode["Device"]["SN"].GetValue<string>();
                _comPortService.OpenSerialPort(deviceSN);
                break;
            case "closeSerialPort":
                deviceSN = jsonNode["Device"]["SN"].GetValue<string>();
                _comPortService.CloseSerialPort(deviceSN);
                break;
        }
    }


    /// <summary>
    /// 复制文件夹及文件
    /// </summary>
    /// <param name="sourceFolder">原文件路径</param>
    /// <param name="destFolder">目标文件路径</param>
    /// <returns></returns>
    public bool CopyFolder(string sourceFolder, string destFolder)
    {
        try
        {
            //如果目标路径不存在,则创建目标路径
            if (!Directory.Exists(destFolder))
            {
                Directory.CreateDirectory(destFolder);
            }

            int index = 0;
            //得到原文件根目录下的所有文件
            string[] files = Directory.GetFiles(sourceFolder);
            foreach (string file in files)
            {
                index++;
                string name = Path.GetFileName(file);
                string dest = Path.Combine(destFolder, name);
                File.Copy(file, dest); //复制文件
            }

            //得到原文件根目录下的所有文件夹
            string[] folders = Directory.GetDirectories(sourceFolder);

            foreach (string folder in folders)
            {
                index++;
                string name = Path.GetFileName(folder);
                string dest = Path.Combine(destFolder, name);
                CopyFolder(folder, dest); //构建目标路径,递归复制文件
            }

            return true;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    private HttpClient _httpClient;

    public async Task UploadFileAsync(string json)
    {
        var jsonNode = JsonNode.Parse(json);
        var deviceSn = jsonNode["deviceSn"].GetValue<string>();
        var diskInfo =
            _udiskService.DiskList.FirstOrDefault(m =>
                m.ShowName.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
        if (diskInfo == null)
        {
            return;
        }

        diskInfo.IsParsing = true;
        var targretPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "data", diskInfo.ShowName,
            Guid.NewGuid().ToString("N"));
        //如果目标路径不存在,则创建目标路径
        if (!Directory.Exists(targretPath))
        {
            Directory.CreateDirectory(targretPath);
        }

        int copyIndex = 0;
        //得到原文件根目录下的所有文件
        string[] copyfiles = Directory.GetFiles(Path.Combine(diskInfo.LogicalName, "data"));
        foreach (string file in copyfiles)
        {
            copyIndex++;
            string name = Path.GetFileName(file);
            string dest = Path.Combine(targretPath, name);
            await Task.Delay(10);
            File.Copy(file, dest); //复制文件
            diskInfo.Progress = copyIndex * 50 / copyfiles.Length;
        }

        diskInfo.IsParsing = false;

        _ = Task.Run(() => DeleteFiles(diskInfo.LogicalName));
        var files = Directory.GetFiles(targretPath, "*.bin");
        _httpClient = new HttpClient();
        var recordId = jsonNode["recordId"].GetValue<string>();
        var index = 0;
        var filesCount = files.Length;
        var guid = Guid.NewGuid();
        var url = $"http://{Host}/api/v2.0/HRV_HX/PatientRecord/UploadFile";


        var sortedFiles = files.OrderBy(file =>
        {
            // 提取时间戳
            string fileName = Path.GetFileNameWithoutExtension(file);
            string[] parts = fileName.Split('-');
            // 提取时间戳 最后一个
            var timestampString = parts.ElementAt(parts.Length - 1);
            return long.Parse(timestampString);
            //return long.MaxValue; // 如果无法解析时间戳，放到最后
        }).ToArray();
        foreach (var fileName in sortedFiles)
        {
            var formData = new MultipartFormDataContent();
            index++;
            // 创建 MultipartFormDataContent 来处理文件上传
            // 读取文件内容
            var fileContent = new StreamContent(new FileStream(Path.Combine(fileName), FileMode.Open, FileAccess.Read));
            fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            // 将文件添加到表单
            formData.Add(fileContent, "file", fileName);
            var name = Path.GetFileName(fileName);
            // 其他字段
            formData.Add(new StringContent(name), "name");
            formData.Add(new StringContent(guid.ToString("N")), "guid");
            formData.Add(new StringContent(recordId), "recordId");
            formData.Add(new StringContent((index == filesCount).ToString()), "isEnd");
            var progress = (index * 50 / filesCount) + 50;
            formData.Add(new StringContent(progress.ToString()), "progress");
            formData.Add(new StringContent(diskInfo.ShowName), "deviceSn");
            try
            {
                // 发送POST请求
                var response = await _httpClient.PostAsync(url, formData);
                var responseContent = await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"上传过程中发生错误: {ex.Message}");
            }
        }
    }

    private void DeleteFiles(string logicalName)
    {
        var files = Directory.GetFiles(Path.Combine(logicalName, $"data"), "*.bin");
        var index = 0;
        foreach (var file in files)
        {
            index++;
            File.Delete(file);
        }
    }
}