@inherits LayoutComponentBase
@implements IDisposable
@using System.Net.WebSockets
@using System.Text
@using System.Text.Json
@using System.Text.Json.Nodes
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using UFU.CoreFX.Shared.Models
@using HX.HRV.Shared.Pages.Client.Components
@using HX.HRV.Shared.Services
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Utils
@using UFU.IoT.Models
@using Menu = UFU.CoreFX.Models.Menu
@inject IJSRuntime js
<MApp Style="background-color:#f3f9ff">
	<MSystemBar App Height="@("6rem")"
	Style="background: url(/images/layout_header.png) , linear-gradient(to bottom right, #2979d4, #0e69d1); ">
		<HRVHeader Menus="Menus" />
	</MSystemBar>
	@if (SubMenus != null && IsShowLeftNavgation)
	{
		<MNavigationDrawer App>
			<MList Routable Nav>
				@foreach (var item in SubMenus)
				{
					<MListItem Href="@item.Url" ActiveClass="primary--text">
						<MListItemIcon>
							<MIcon Style="line-height: 2.5rem;font-size: 1.25rem;height: 2.5rem">@item.Icon</MIcon>
						</MListItemIcon>
						<MListItemContent>
							<MListItemTitle Style="line-height: 2.5rem;font-size: 1.25rem;height: 2.5rem">@item.Name</MListItemTitle>
						</MListItemContent>
					</MListItem>
				}
			</MList>
		</MNavigationDrawer>
	}
	<MMain Style="padding-top:6rem ">
		<div style="height: calc(100vh - 7rem);">
			<ErrorBoundary>
				<ChildContent>
					<CascadingValue Value="ClientWebSocket" Name="ClientWebSocket">
						<CascadingValue Value="DeviceDataList" Name="DeviceDataList">
							<CascadingValue Value="DeviceList" Name="DeviceList">
								<CascadingValue Value="PopupService" Name="PopupService">
									<CascadingValue Value="SystemConfig" Name="SystemConfig">
										<CascadingValue Value="ALGDataSubjectService" Name="ALGDataSubjectService">
											@Body
										</CascadingValue>
									</CascadingValue>
								</CascadingValue>
							</CascadingValue>
						</CascadingValue>
					</CascadingValue>
				</ChildContent>
				<ErrorContent Context="exception">
					<UFU.CoreFX.Shared.Pages.Core.ErrorPage._500 ReturnUrl="/client/device-status"
					Exception="exception" />
				</ErrorContent>
			</ErrorBoundary>
		</div>
		<div id="bottom_version">
			<span style="opacity: 0.5" Cols="12">
				© @DateTime.Now.Year 汇心健康 @Configuration.GetSection("Version").Value
			</span>
		</div>
	</MMain>
</MApp>
@code {
	public void Dispose()
	{
		cancellationToken.Cancel();
	}

	/// <summary>
	/// 路由数据
	/// </summary>
	[CascadingParameter]
	public AuthRouteData AuthRouteData { get; set; }

	private ALGDataSubjectService ALGDataSubjectService { get; set; }
	[Inject] StateService _stateService { get; set; }
	private List<Menu> Menus { get; set; } = new();
	private List<Menu> SubMenus { get; set; } = new();
	private bool IsShowLeftNavgation { get; set; } = false;
	[Inject] IConfiguration Configuration { get; set; }
	private List<DeviceStatusViewModel> DeviceDataList { get; set; } = new();
	private List<DeviceModel> DeviceList { get; set; } = new();
	[Inject] private IPopupService PopupService { get; set; }
	[Inject] NavigationManager Navigation { get; set; }

	protected override void OnParametersSet()
	{
		if (AuthRouteData?.CurrentModule != null)
		{
			var module = AuthRouteData?.CurrentModule;
			Menus = module?.Menus ?? new List<Menu>();
			SetIsSelectedMenu();
			IsShowLeftNavgation = Menus.Any(m => m.Active && m.Children.Any());
			SubMenus = module?.Menus?.Where(m => m.Children?.Count > 0)
				?.SelectMany(m => m.Children)
				.ToList();
		}

		base.OnParametersSet();
	}

	private void SetIsSelectedMenu()
	{
		var currentUri = _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
		var path = currentUri.AbsolutePath.ToLower();
		foreach (var item in Menus)
		{
			item.Active = item.Url.Equals(path)
								|| (item.Children?.Any(m => m.Url.Equals(path)) ?? false);
		}
	}

	public ClientWebSocket ClientWebSocket { get; set; }


	private CancellationTokenSource cancellationToken = new CancellationTokenSource();
	protected override async Task OnInitializedAsync()
	{
		await InitHxSystemConfig();
		await InitDevicesAsync();
		await InitPatientRecordModelListAsync();
		await InitWebsocket();
		var objectRef = DotNetObjectReference.Create(this);
		await js.InvokeVoidAsync("InitHybridWebView", objectRef, "HandleReciveFromClient");
		_ = Task.Run(async () =>
			{
				while (true)
				{
					if (cancellationToken.IsCancellationRequested)
					{
						break;
					}
					await Task.Delay(5000);
					try
					{
						await js.InvokeAsync<object>("SendMessageToClient");

					}
					catch (Exception e)
					{
						Console.WriteLine("SendMessageToClient error" + e.Message);
					}
				}
			});
		await base.OnInitializedAsync();
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);
	}

	private const string DeviceTypeId = "2407160100000001";

	private async Task InitDevicesAsync()
	{
		PopupService.ShowProgressCircular();
		var queryParam = new Dictionary<string, string>
		{
			{ "typeId", DeviceTypeId },
			{ "page", "0 " },
			{ "pageSize", "100" },
		};
		var deviceListUrl = "/api/v2.0/IoT/Devices/List";
		var result = await _stateService
			.GetAsJsonAsync<List<DataModel<DeviceModel>>>(deviceListUrl, queryParam);
		DeviceList = result?.Data
			//?.Take(16)
			?.Select(m => m.Data)?.OrderBy(m => m.Name)
			.ToList();

		if (SystemConfig.LayoutConfig is { DisplayNumber: > 0 })
		{
			DeviceDataList = DeviceList?.Take(SystemConfig.LayoutConfig.DisplayNumber)?.Select(m =>
			{
				var batteryStr = m?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");
				double battery = 0;
				if (batteryStr?.Value?.Value is JsonValue jsonValue)
				{
					battery = jsonValue.GetValue<double>();
				}
				else if (batteryStr?.Value?.Value is double doubleValue)
				{
					battery = doubleValue;
				}

				return new DeviceStatusViewModel()
					{
						Device = m,
						DeviceSN = m.DeviceSN,
						Battery = battery,
						DeviceStatus = m.IsOnline ? EnumDeviceStatus.空闲 : EnumDeviceStatus.离线,
					};
			}).ToList();
		}
		else
		{
			DeviceDataList = DeviceList?.Select(m =>
			{
				var batteryStr = m?.LatestData?.GroupData?["Data"]?.FirstOrDefault(x => x.Key == "Battery");
				double battery = 0;
				if (batteryStr?.Value?.Value is JsonValue jsonValue)
				{
					battery = jsonValue.GetValue<double>();
				}
				else if (batteryStr?.Value?.Value is double doubleValue)
				{
					battery = doubleValue;
				}
				return new DeviceStatusViewModel()
					{
						Device = m,
						DeviceSN = m.DeviceSN,
						Battery = battery,
						DeviceStatus = m.IsOnline ? EnumDeviceStatus.空闲 : EnumDeviceStatus.离线,
					};
			}).ToList();
		}

		PopupService.HideProgressCircular();
	}

	private async Task InitPatientRecordModelListAsync()
	{
		var deviceIds = DeviceDataList.Select(m => m.Device.Id).ToArray();
		var res = await _stateService
			.GetAsJsonAsync<List<PatientRecordModel>>(
				"/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelList", new Dictionary<string, string>()
					{
					{ "deviceIds", string.Join(",", deviceIds) }
					});
		var recordModels = res?.Data?.ToList();
		if (recordModels != null)
		{
			foreach (var record in recordModels)
			{
				var deviceData = DeviceDataList.FirstOrDefault(m => m.Device.Id == record.DeviceId);
				deviceData.RecordModel = record;
			}
		}
	}

	/// <summary>
	/// 初始化系统配置
	/// </summary>
	private async Task InitHxSystemConfig()
	{
		var systemConfig = await _stateService
			.GetAsJsonAsync<DataModel<HxSystemConfigModel>>("/api/v2.0/HRV_HX/HxSystemConfig/GetSystemConfigModelDetail");
		this.SystemConfig = systemConfig?.Data?.Data ?? new HxSystemConfigModel
			{
				IsAutoPrint = false,
				CheckTimeList = new(),
				PatientSource = new()
			};
		SystemConfig.SystemRateConfig ??= DefalultData.SystemRateConfig;
	}

	private HxSystemConfigModel SystemConfig { get; set; }

	private async Task InitWebsocket()
	{
		var currentUri = _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
		var webSocketUrl = $"ws://{currentUri.Authority}/iot/hx_hrv/v1?token={_stateService.Token}&clientSource=1";
		// await ALGDataSubjectService.Init(webSocketUrl);
		// ClientWebSocket = ALGDataSubjectService.ClientWebSocket;
		// ALGDataSubjectService.OnMessageReceived +=  (jsonNode) =>Task.Run(async () =>
		// {
		//     await OnReceiveMessageAsync(jsonNode);
		// });
		//

		ClientWebSocket = new ClientWebSocket();
		var serverUri = new Uri(webSocketUrl);
		await ClientWebSocket.ConnectAsync(serverUri, cancellationToken.Token);
		ALGDataSubjectService = new ALGDataSubjectService(ClientWebSocket);
		_ = Task.Run(async () =>
		{
			try
			{

				while (true)
				{
					if (cancellationToken.IsCancellationRequested)
					{
						break;
					}
					var buffer = new byte[1024 * 1024];
					var result = await ClientWebSocket.ReceiveAsync(buffer, cancellationToken.Token);
					if (result.Count > 0)
					{
						var msg = Encoding.UTF8.GetString(buffer, 0, result.Count);
						var jsonNode = JsonNode.Parse(msg);
						await OnReceiveMessageAsync(jsonNode);
					}
				}
			}
			catch (Exception e)
			{
				Console.WriteLine(" \r\nDefaultLayout,OnReceiveMessageAsync error：\r\n" + e.Message);
				//LogTool.Logger.Error(e, "DefaultLayout,OnReceiveMessageAsync");
				// 强制刷新当前页面
				Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
			}
		});
	}

	private async Task OnReceiveMessageAsync(JsonNode jsonNode)
	{
		try
		{
			//  var buffer = new byte[1024 * 10 * 4];
			//var result = await ClientWebSocket.ReceiveAsync(buffer, CancellationToken.None);
			//   var msg = Encoding.UTF8.GetString(buffer, 0, result.Count);
			// 解析 JSON 字符串为对象列表
			//  var jsonNode = JsonNode.Parse(msg);
			var deviceSn = jsonNode?["SN"]?.ToString();
			ALGDataSubjectService?.OnMessageReceived?.Invoke(jsonNode);
			if (!string.IsNullOrEmpty(deviceSn))
			{
				var deviceViewModel = DeviceDataList.FirstOrDefault(m => m.DeviceSN.Equals(deviceSn, StringComparison.CurrentCultureIgnoreCase));
				if (deviceViewModel != null)
				{
					if (!string.IsNullOrEmpty(deviceSn))
					{
						var status = jsonNode["Status"]?.GetValue<int>();
						if (status != null && Enum.IsDefined(typeof(EnumDeviceStatus), status))
						{
							if ((EnumDeviceStatus)status == EnumDeviceStatus.检测中&& deviceViewModel.DeviceStatus != EnumDeviceStatus.检测中)
							{
								deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
								deviceViewModel.IsBegining = false;
								deviceViewModel.IsPrint = false;
								
							}
							if ((EnumDeviceStatus)status == EnumDeviceStatus.空闲 && deviceViewModel.DeviceStatus == EnumDeviceStatus.检测中)
							{
								deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
							}
							else
							{
								deviceViewModel.DeviceStatus = (EnumDeviceStatus)status;
							}
							PopupService.HideProgressCircular();
						}

						//电量
						var battery = jsonNode["Battery"]?.GetValue<double>();
						if (battery != null)
						{
							deviceViewModel.Battery = (double)battery;
						}

						//SPO2
						var SPO2 = jsonNode["SPO2"]?.GetValue<uint>();
						if (SPO2 != null)
						{
							deviceViewModel.OnSPO2Data?.Invoke(SPO2 ?? 0);
							deviceViewModel.SPO2Data.Add((uint)SPO2);
						}

						//Bmp
						var Bmp = jsonNode["Bmp"]?.GetValue<uint>();
						if (Bmp != null)
						{
							deviceViewModel.OnReciveBMPDatas?.Invoke(Bmp ?? 0);
							deviceViewModel.BMPDatas.Add((uint)Bmp);
						}

						//PPG
						if (jsonNode["PPG"] is JsonArray PPG)
						{
							var ppgData = PPG.GetValues<int>()?.ToList();
							deviceViewModel.OnRecivePPGData?.Invoke(ppgData);
							deviceViewModel.PPGData.AddRange(ppgData);
						}

						if (jsonNode["ORGPPG"] is JsonArray ORGPPG)
						{
							var ppgData = ORGPPG.GetValues<uint>()?.ToList();
							deviceViewModel.OnOrgPPGData?.Invoke(ppgData);
							deviceViewModel.OrgPPGData.AddRange(ppgData);
						}

						//PPG
						if (jsonNode["IMU"] is JsonArray IMU)
						{
							var imuData = IMU.GetValues<short>();
							deviceViewModel.ImuDatas.AddRange(imuData);
						}
						//PPG
						if (jsonNode["SKINTEMP"] is JsonArray SKINTEMP)
						{
							var imuData = SKINTEMP.GetValues<float>();
							deviceViewModel.SKINTEMPs.AddRange(imuData);
						}
						//PPG
						if (jsonNode["SCL"] is JsonArray SCL)
						{
							var SCLData = SCL.GetValues<float>()?.ToList();
							deviceViewModel.SCLDatas.AddRange(SCLData);
							deviceViewModel.OnReciveSCLDatasData?.Invoke(SCLData);
						}
						//PPG
						if (jsonNode["SCR"] is JsonArray SCR)
						{
							var SCRData = SCR.GetValues<float>()?.ToList();
							deviceViewModel.SCRDatas.AddRange(SCRData);
							deviceViewModel.OnReciveSCRDatasData?.Invoke(SCRData);
						}
						if (jsonNode["EDA"] is JsonArray EDA)
						{
							var EDAData = EDA.GetValues<float>()?.ToList();
							deviceViewModel.OnReciveEDAsData?.Invoke(EDAData);
							deviceViewModel.EDAs.AddRange(EDAData);
						}
						try
						{
							//deviceRecord
							var deviceRecord = jsonNode["DeviceRecord"]?.GetValue<PatientRecordModel>();
							if (deviceRecord != null)
							{
								deviceViewModel.RecordModel = deviceRecord;
							}
						}
						catch (Exception e)
						{
							Console.WriteLine(e);
						}
						//deviceRecord
						var progress = jsonNode["Progress"]?.GetValue<double>();
						if (progress != null)
						{
							deviceViewModel.Progress = (double)progress;
						}

						if (jsonNode.AsObject().ContainsKey("CollectTime"))
						{
							var collectTime = jsonNode["CollectTime"]?.ToString();
							if (collectTime != null && TimeSpan.TryParse(collectTime, out var timeSpan))
							{
								deviceViewModel.CollectTime = timeSpan;
							}
						}
						var IsChecked = jsonNode["IsChecked"]?.GetValue<bool>();
						if (IsChecked ?? false)
						{
							deviceViewModel.ClearData();
						}
						await InvokeAsync(StateHasChanged);
					}
				}
			}
		}
		catch (Exception e)
		{
			//LogTool.Logger.Error(e, "");
		}
	}
	private class RecivedDataFromClient
	{
		public string action { get; set; }
	}
	private class RecivedDataFromClient<T> : RecivedDataFromClient
	{
		public T data { get; set; }

	}



	[JSInvokable]
	public async Task HandleReciveFromClient(string json)
	{

		var jsonNode = JsonNode.Parse(json);
		var data = JsonTool.Deserialize<RecivedDataFromClient>(json);

		switch (data.action)
		{
			case "getDeviceInfo":
				var jsonData = JsonTool.Deserialize<RecivedDataFromClient<List<DiskInfo>>>(json);
				var disks = jsonData.data;
				if (disks == null || disks.Count == 0)
				{
					return;
				}
				var isChange = false;

				foreach (var deviceStatusViewModel in DeviceDataList)
				{
					if (disks.Any(m => m.ShowName.Equals(deviceStatusViewModel.Device.DeviceSN, StringComparison.InvariantCultureIgnoreCase)))
					{
						deviceStatusViewModel.IsUsbLinked = true;
					}
					else
					{
						deviceStatusViewModel.IsUsbLinked = false;
					}
				}
				var notExistDevices = disks?.Where(m => !DeviceDataList.Any(x => x.Device.DeviceSN.Equals(m.ShowName, StringComparison.InvariantCultureIgnoreCase)))?.ToList();

				if (notExistDevices?.Any() == true)
				{
					foreach (var diskInfo in notExistDevices)
					{

						DeviceModel newDevice = new DeviceModel()
							{
								DeviceSN = diskInfo.ShowName,
								ChipSN = diskInfo.ShowName,
								TypeId = DeviceTypeId,
							};
						var result = await _stateService.PostAsJsonAsync<DataModel<DeviceModel>>("api/v2/IoT/Devices/Add", newDevice);
						var device = result?.Data?.Data;
						if (device != null)
						{
							var deviceViewModel = new  DeviceStatusViewModel()
								{
									Device = result?.Data?.Data,
									DeviceSN = result?.Data?.Data?.DeviceSN,
									Battery = 0,
									DeviceStatus = EnumDeviceStatus.离线,
								};
							DeviceDataList.Add(deviceViewModel);
						}

					}
				}

				await InvokeAsync(StateHasChanged);

				break;

		}
		await InvokeAsync(StateHasChanged);
	}

	public class DiskInfo
	{
		public string LogicalName { get; set; }
		public string Caption { get; set; }
		public string ShowName { get; set; }
	}

}