{"format": 1, "restore": {"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj": {}}, "projects": {"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj", "projectName": "UFU.IoT.Shared", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AntDesign": {"target": "Package", "version": "[0.15.5, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.10, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj": {"version": "3.0.0", "restore": {"projectUniqueName": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj", "projectName": "UFU.IoT", "projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\UFU.IoT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT\\obj\\publish\\linux-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj": {"projectPath": "D:\\Project\\HuiXin\\HX.HRV\\UFU.IoT.HRV\\UFU.IoT.Shared\\UFU.IoT.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.8, )", "autoReferenced": true}, "NPOI": {"target": "Package", "version": "[2.6.0, )"}, "RulesEngine": {"target": "Package", "version": "[5.0.3, )"}, "UFU.CoreFX": {"target": "Package", "version": "[5.0.3, )"}, "UFU.IoT.UI": {"target": "Package", "version": "[2.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj": {"version": "5.0.6", "restore": {"projectUniqueName": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj", "projectName": "UFU.CoreFX.Shared", "projectPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\UFU.CoreFX.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\NetCore\\UFU.CoreFX.Blazor\\UFU.CoreFX.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AntDesign": {"target": "Package", "version": "[0.15.5, )"}, "Majorsoft.Blazor.Extensions.BrowserStorage": {"target": "Package", "version": "[1.5.0, )"}, "Microsoft.AspNetCore.Components.Authorization": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[6.0.10, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.4, )"}, "blazor-dragdrop": {"target": "Package", "version": "[2.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}}