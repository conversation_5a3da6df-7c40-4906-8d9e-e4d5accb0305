{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["D26splGMektx1xgD9zd6qhY39XqYFW6BcG87JXiqa/I=", "dmbdmuwTFBZo134a2yE88Eg/Fn7HLuVPu5e6gAZ6IgY=", "eLS9zdPR97LLy6ULAyAcsJzg05DbtKNGqOio8PzYKbE=", "PoECyUNlx1x+IC6psCNWuzHbwDGp0hWbDch5bI5gW8M=", "nCjZV2ahuFBBq599dE8EukLFaAJjLugX/JPSVBvGzkk=", "2LEY7WO2VQk7zPvdgwn6Uqd/OousIKBSZrNCX766RzE=", "6R/AGCVIsClxF+enMNX7GpMrcAePfibLPMNszwC82N8=", "z1FmTEOJLlKoNkkKxiopO8OWSzdg//i/mUjrB71pcQ8=", "K12FLrPJfwnNkR4QDNuYMh2eqFDQao89pOBlvqBVFBA=", "z3Ip4wxJGBDjv6Y+pKoY87x2dYo4+rmFnIDTviE1pdY=", "1NJT3C4gEEkAEwoV2CVuBy0+xGoh/ViG9qw3u3rG4Sw=", "Wt8a0D2RKDgPh55acjYP3XwpFGCLfElYhc6pQfliY6g=", "4+hcsFi61Iu4pJ9NK2HcD1u/5wXpCDP/6a9lqgsrhRA=", "9hqxf+oPz6uQDtR2xKvSO/eLcpJziCzHYHhbhP2Y6do=", "kPUlcXftlw7GryoDqaHHxJkDwj++tEKy2Fo+XSTMaRw=", "Y6gNMRK1HiOCqjC4QF/OUDzH6ThTjb3wuDWT/kIZnQk=", "QtCeJcCCJIoCViuFjywFRswK2LtCudafviEeaYK7LjY=", "ToXmBqtuZDQgFZ54zeGYWANlFqz68ys/ttWPkbB2Rks=", "DPQM5dFWnYfrtGXyHo5fphiMXRippUauZLwTfACgvYU=", "R1t2EXaJk6E1TXHtw2m9AFjaDD7eesFMByPqej5wGNc=", "+JLu7xlWMIlyVaj8SNCRzoJ+Hlcn40q2QK6hZc585I4=", "8xiwILHEftTRRmaj0GkesOxSIxXFhAtQAfUJMYcVpR0=", "avi2zmilMogQ8/r7vKyv24hNg3mKPHn/fWG9TUf/dwA=", "L6SFwRNWvoEDQYge67awk9b1xGUQasCCYAncMrAIDxg=", "x1t29QWVZXKvyccZI+T1x1vUMd0ayihIyIl6my6MBbo=", "aOm1oOlKl6+f+rlgPzM8zWT+0gthvvBmJ/p9TFEuXmc=", "tqBhIwf6aXYgbYyBfcOL/GKonYiiyI6boKxpe6cvc6I=", "dfZXyONW54+cf3lW+XJc8BUOke1IpHMmdQHlLSMrLKM=", "aBj/lHq7fqL0udwIz8mJblFLn/V1Re0PJH+zDmyjAQk=", "cWXlc4qM2Bd1xAexPkjtL6uCwGOOE4t0hu9ZkZD8pjg=", "vZ1vA0eh9Jwts/3j0J5Xqc1U7PQHO9VuFO4jrMFx4Dw=", "CAU2sHoXa7WSMRm+D/Yj48dhf+nGe+bbC+ROpI82Yl8=", "/wlvvFHPsbDI1tjUQBQ2JeTULoaGOvohkj7L6pULWKI=", "8EeRtovLIA5zEXsJXPguNdJFAvcLx4Dkn0Yf7GRNurE=", "FXcbfmOnW9MoBGM5Dr5Zimr1sG+JDUDPwYtAbkkp3u8=", "Ovq0hXoeJZCQCIQOwij+qrlWKgRutHJxDoYM64oGLGA=", "AKLzhbgvKhVHBdvfFF0V38KAM9vkTkVE0BCUgmEqDMM=", "ZZYdl6keZHG8hdU1xczJfzB44WNWgikJDo3gUqt7qK4=", "lxVhD0wYWkfMI5RZcxPEQNUiHVw2fmUg06yJFbffbd0=", "+b9ONmvz7MUFHlho3zN5lPowqCQLPdoXRYcyglE3MtE=", "jYinE/TK5u64njKvdtKqhq5Efv+99HLcIqobOulifg0=", "LxtpT6wnxsURDN8NyTRFHtad5oZL3kHdV4Nzv8gLGrQ=", "oloSVuMpF6MxaffepturH0n0AHW/rnsAcFpoXclbjaA=", "UWt1kS5GVybSR5ll86E/aHYhZJndKepBBTv1x3FsNJI=", "Rvd82iw16xsGLDDP2kvmi97tVi6MndG8CtuaBZ7ypVo=", "JOE6B9yV+dE0W9sHRlOdix7X9RN6/DHHiFnLm5a7yXM=", "3sizww066fnSRuGvGzfWGL5fJStHmuy8St5NFMS8ZUE=", "oz3HA0ID1c5NFt+I5urV0lU0TOhk3gIsDw8ECFQVFd0=", "kJpvfiWPQEjD3MKsvi/rqTHDyjyg1kpdxWyCQfvt83w=", "r6xVazMWfVI4zTsS62R9YD8MS1zy9Y+f6HSR01DKw9A=", "AblG1HLIXLKCt5Xmkqv2JdFgYAp2tIZ8kxkdsQ8VlwI=", "lMLZ9MIwCeofuKqSPSlr3/ByM87XDp3KiG08ahVC+Wk=", "pHzXaHYID7kyNvSwq6wWmN8veUAFbjze1CoNEyUcOvQ=", "BYQTbfUUZQlmXwlRMugVW6lidv9vtiHwfIvFsbJ/tvo=", "2C1/fY5OWFVzhJObnQWSSXTYfx+McjoFw7dCYuJ4YZQ=", "AXXvwJPqAw2OtOIZQZm1SHMPdNLUu1WbBWSIcYvF+zs=", "PDR1FJ0wuUgSyoPxucM3BZQHD85F/H1nBpD+BPkGsPQ=", "2xdGiY66tEhDCXcQAlv61EfNzqmy8uDdTFvhzD1LLbg=", "kOruBv348ke14/4/GpgMKKMXRAOgOlw7mFttnst7hqM=", "Vm98htKPEW+hRB62ScMxO/bpIFGIpXY97RMl1MNqggg=", "xrkucdw0MCEtxVqDp1jIySsIJ/6KGrgNJ6oMgJdSuXk=", "JU+dUAUwfKjRGBvumkskFQTBrtrtYlAsDGq6KOTM/ss=", "RG7FPqGjkStPzilJoUrWllnpeAZC3A5ZS58xW9zxmdE=", "XCY+fT7Z0t5Z5rLOOhcqg8pUODszTJia4A5JSXNefUI=", "cptp0f9pGflClqAo3Wvb2xc47BercMRDrmVknFih+oc=", "JcaPdv8Kk7kMT8g3jd/uEhQQquNKUQebZmz2crNhJaI=", "FK+y0dyWXTFrNByb49LB4l0Jz2jLDnh4RLP9wg70GG0=", "KtmztVQrhvoAHpfOz4uQoAJwf7GE043gBk/7lhNaJJg=", "nLZmSOtbJqyvmf01as6m0BEDe5Csm6VuAcJn+7scpLY=", "BNgWjig0pi1o8Z/x7kSGs/LXeWWgZYAnAEYAT4wBQ1w=", "cI11AtwGDkgcXmwK6nJULaDc1mAOiHXAgNyufDQWoSE=", "Efk5SG2w99MUu7v4hnRpTeLqdW5HFMmmr5LxB5WseXw=", "GGjRDAWoZdiYzp/7ztCx8kUzAB3fJbFUWjmWQkRFIW8=", "ZU1lPtzZJVjsA9weECOsfiWjuFheLX/3ok6INBq5sls=", "e0ktV/UMz97M6tix9hErMhDJr8PMhF4tehyrqA1YKT4=", "BlcVIwhbPb08hz7qN0tFA0Lt+np3Oq7n0jyeGOSwQdk=", "k7fDoKMJGdQgy35fY3cSep2yyknIxw83bTX9Y5DZSCs=", "h9v4+YsA/eHsvKVBNO3MOx7ZblsYJn2gyBGDIvB5QMs=", "Qfk4sPDjrAlBfM4q6omOSZhwVKGYhJo4TiHHra0r/Q0=", "DJb3lzXXbTHDzJ45KvghCst4LIaqbe/cqiZVlBNdwd8=", "xYi8/7R/ypuSg72J9Q2pJLfJMVZz5x/2X5fZvbXlGz8=", "j32UrbzU0PvSryLH/+FAk3kTEFyi2UovFGHjKYGYkQs=", "paN4Sx3OtBrJddY5tYgZ6/7IIjMDBjCx342Y/SNaipk=", "bMmr0iuWez6twmRH/64K6xBEqAZUSQlrBinWMBKRrYE=", "lAHBcmkdIWYqDGbBUn2p2s1ukU3HVuPxgSa1vjpD9Mc=", "KhYHCPsjQvCDPACOyLm2A4q7QEKTDU1MaxgFUz8K7R8=", "5AdBASXyn7e0kbBazeQkaB0VCsJyjv2VoDodjzy7Bf4=", "2q50CprO3pb8PoBQtXkvrd3OsCrFyhSv3aeBlmKHtfk=", "obvrpY/R9uXw1Y9O8VgWp+nbgyBcEjtSApqN/J1Odtw=", "tlHdXQkPThrieoCIzeJiYTR6NSmIzMmqOrdoI3VpAQU=", "sw21wQ0MOlHG9n7yy6MMW9eDqsD6sfxY6dihagxegaI=", "ksnhCBIN/eoBbnJdNmRfsWg4ngyB9Us6NRPdnAM7NxI=", "/LC13XpUm88JCaUnt6UTulBpeJVewMkg2hJIgNiaJRM=", "uH6ExqXapX2Chq3ICoD1FuFyeRqB6TbEUxGWM7nnufo=", "xjifMJhAsAnrACjYvKlx8O7vTfxA5SarXJKeQe8jTyI=", "+QXHIro4OU9lmd2WKeSARKRGw4Q52sfvrmUB2Yzd6hg=", "CxT6mul7xD5wX5bocGehrlB0A6xdUGsVtyu5IKtsK8I=", "TT5uArVp2cV4j4LFFk/OaciCyRSRXnQQinpmy7w1Vr4=", "AuidevUrxwKWxzxCkXQzN9hc/7dnPvtSD6RIscPVLIo=", "qMhTl3U+ZBiyH96yrm12ToLb9c0xwpcIZSm+d5NKZrU=", "4JEcLSpcNFdZks8GfynlYahb+8mCagA/ynLG9F1WnbQ=", "aBUaTnbGBSo0wrG3dD12q7OxwZe16BbZncZ05Kboa00=", "6HHnpGXWohcWRtUld0fGp/EOV1UThiJLyP2LqPCiUs0=", "tj70ezrA1aSR4K8M2V/hWCm7J/A+sPk+dPP6EoNmUbQ=", "k0jgKCXQWARlJAvC90fm39nZuRU7GsVBRrPFNN3Jrfo=", "CrT8yxpShAj1gIyQit9TFNcQHIEEkpbzWMK1vwajjBk=", "l/VAAqRPrbS46H3h6fSGCwnVDwWDN12AgB5ozns+Qkw=", "juw9ygl9/WTGvLPuNfwgN0H1m7BOrH2nAX1H41vyx8A=", "bS0VKP20ahLj6ng2pRU0QJ0za2kGh6+7fm+1WNDLuo4=", "ozpJxSGubRnYkUaPwyHT4GXNJvoKJE+go6Ji/EaUr/o=", "jqRD7PSi9xiQI9DPJD0GCh2/TVn73UPj3EcYR9rmWBA=", "Ln5tMPq55ugntTZ9t2qa1H8LlQWJhtF//JhaCpDkl0Y=", "x7RXQ/FiWYzusjPcqKOVLxWIkjdY3WZ/oIsJIvD/XwU=", "Ktj2fN07uyTz+2AlQ+n9TPwSRHFHXPjUZoENLC2GeUA=", "hENexVMo6qiIm27l4YgtAW1DVQVLomxxFZzo2V/4eXg=", "RLpYqbKH96BmDg9ZWIaLnA0w2WlYUPb1nz6I38khxAE=", "l6z5XL1Ilrga2NTJ5P7OIAvi/5Cka+egvWxNQvZHrV0=", "GKNBbpwODHMRQce+sYEoE2GHpjkpBAgbLqIJFPW9MKI=", "Q0lu51er8/YXre9C3xRsP0TYh5gEldiH2PxTdG6ANYc=", "/cHMkDnvwAKMneId8krLw/DwIsk6sq2X7sTMUU1vSQc=", "fXiI7gF9GwfyoUe7oraGYinqX84/3CBMrg++g5xXaoo=", "XFy52wW+bDZkVMgf3NRgbV8aUsZjij3QYT//DdMx4+g=", "x0Kvbk1i2uLkN6WcfN9rK9LhgYY+ySnPLGqaYJhCQ/E=", "OLlliWBtnH9kniYiuoK7RDrPUO3QUjhinJNP+gjZMNs=", "HBaWJE7uLzE8wZ5eUSaDl6HA9p3ueO26+wIoMMCtXgc=", "TXvJrZiyk7OXnzRUICtOqrARmFJmegrlc+JbVA6+fmM=", "cfv7sy+EKBcDjOo8QYxgvTRTyUPAfwPHo2ckcyv/Qao=", "jRI/SJURei0n3RUKibu8/ZiLv4sDgGeWJi7afzqQAcA=", "Q/aisksgApSxnmzbGRBlEfepyKIuHv8PzhlT+CvMEUc=", "2H/DWqnrykVVc0Gi2qs903QgtDavn392IvKoO5ZcpEM=", "WXvN2nl1GIDwZQP9EROdVlTAmeRjUlTFOTKKMZgz8WQ=", "ykmFQ7LjRa9qMYQsHJ/P0c+XXeaIIjFUVivRfR597xc=", "rM3g0pz2ViH8D2SrrFrvX5yikq/ZTVTEJXUbYjU4QSA=", "LLX69S4sUX8jMMBkwDauQ97ffYyXDMEwYKeqHcGmthU=", "DoGp1ZSR6ckaJ3zYS7/4tg2rkWXxWo51WBBllM10KyA=", "MRBxEqL1JT9ZBwtGiPExX8j0GokiZPQm65O+Ujz2NKc=", "atDNrmXUB3THbO3Pq/RO0Wes6MKUsMfrUPBmD8dGWfo=", "IkiA+JamaQIbRo2bRmXbgTrmJSEaGM55isTD5jEJdS8=", "9PfgLIam4jzlSb8fgVaIltXXaDz0qWe7Vkbtf5jnIMg=", "sLoNm4NZkkQGlpeSP6PbuoOt0hy8VFV9clRt3xCZ6kE=", "uj4SC7t+PhYF8CqyAUp6xoB6kxwh/DQCT9u/imEbB8E=", "G25bRAAaWiQnrKRVcT+PDpWdd+fM8lHDqzBHsvHfmwA=", "sRkYDIOEBEz3xc0okgUn2fs2a+olriJSgw9/VOkAXZw=", "xSH08CmD2/P3ONH1T9FdmLcLal8/h+afPk9A1mzv82E=", "bd2X0SYitBToMzFRM0Evxgfc/YccPgKJAGLf3GqIpWY=", "zGmGgI+3mR/8HWroZ7KbAef1nqnUEj5NSmJX2AGR9kc=", "nEke+M+RYU/LIo9ZLc/ugundwAwfyQjNEeo0EQdP4y4=", "/Ftl2l54DyrgvLAw98RTGmVM1g8smeKCsd2n/UZYx6g=", "rSFC8asmlcibn6otY6Q63vMzlim6ysLi6J+i0n530Bk=", "dzOd3ULXHQ45HN/AhTsIE/eWvXFujTuD4EFFeHXJ31Q=", "h4o1xmcON8wnIsSn04pcTPugKQl7i1DQPok9mwlBXZ4=", "wtIynD4q0+y3fy+sbyVXhv4CKwuwhds7fzQnHk/rryI=", "pP/f1jm9VD2tnJpvis/PGTcsokzOOmm7SLzy8Cc69zU=", "DQnZGGBggVNqDu5ivRdJtpx3iYRcrgO8qebMZKTqw3A=", "Q0pehFX6hHj4Fz3nP1anqNsasofSSt+Ifqv/bTws1ms=", "Fk5hWX+Uo7AgR8MRmZHcd4mr4WkYLxxahoc9mV78vWo=", "JIcnFknccGubL8/5lYSibwKa+ALJLhw+F0NT9gLHtig=", "GVC2lO4uselUM5rQwjOoB/e5WlJQBrnkkGe0StRoodo=", "4sKkdJA26glQ2fwl5lxfSD0W323WeUDtYGPRF/yfKos=", "Gg8JUKc54xNvhLtKLwOCNgdSgGpNic6ze59siTyHnXQ=", "uzyCfxHgp1eOmauttvDbKMM5arfU7PqJk/fQrxTNmIc=", "4Y77Gb8EDvOiOmFWeM1lmxur0wF9OBo40MX+y8pYpEI=", "gwTvTnthSUZzbmeYoECv8qxSBiZw97LtAuld+Q047eo=", "LiFmDAsNlqgRudP0hPs/ZQQ99Yb12cdc094/nGkFqAw=", "ntOypTTX0OcnkIHnB+HQvBuSuVO2EuR7kwnWld8ofpg=", "dK1wp5br9qO4ZWlx+wcp5eH/qVaIsEagGc1nNtGMwMg=", "XxVtXyJG4pffgyXMFu3RWBQnNyd9AozPyuNx4m9p3q0=", "Tf9TaFbBcLmm/Vc13QGpmsXxAKzNM8UJGk8tpb5nFes=", "QTysWN+1uuEdVAWP7BpuHylnIrT3qkK5Jslyz3rhW90=", "1g+3nPXkic12wtuURpFJhxU3szj9U5QEhwMkahNbYhc=", "yNfB7BoWLprD5u7xdr80qgYLX1UeR59gfiivbJzH8Pw=", "wczViKt+bL/dnsTyEhsJrJ1EmenB3qLTXU4ZzuVxmcs=", "3b3ztYoNX8P2dQXfzHPEfgH7VMICSuMVeLVFNjAeyXw=", "5MCd+ajktXZIy0r4PfhU/pnCqljHMCyNj+mIsNyuACg=", "NsOd1VUfRNeo9f144UlCTL778T71f1InfmLQqFBfZ9k=", "BIBLeJ8I07qNxv/V59EB4KhNqyMCXii6VDHd+ZodUNs=", "vJ/xcYKa0Jdqx3PcdJ0wtYoc5XwgrEpHF5tLFfSeUXU=", "IZKdZnEy9i0uhyQ0ph658TD/dMtkmpyjKGirfRIHdMw=", "pj2Yb+hSTQQVpxHTLA4zYL2yVIACNN7DUY6MjinuRpc=", "uI8pzBe/DPveJK/4QVuIevmJwCRoPytVwf5izFPc0uM=", "dk3PxAXTXg2P7bQ3Aw/OUdrblB6JOmTrlkkBOXV/D+8=", "3ydPoTn+ilxRlebjGO4eUd2a3O32nGH23AvZwxlqGQQ=", "nEWAhvJU14eXxwYvIXgA0mYmnz6FloaWBlnE/nwJF70=", "7aTia658eMCwd/6IgauGPsZOoD32Td0OoVIEnzuXAC8=", "sZyK83P+6GnQlJfNOGplz7CNHQyX2vBPv+Di9LNqhsw=", "QKmCvo7R8oYR6Io30AOfDWX5hXeZzi8/w0WXCj50OBo=", "4J+ANGS6K6P1tR69740vvElqevFfX13RYX3e3yHl5KE=", "9i7rNICM7yUUF2Md0QaaPPnkgByTKUfo/PTiV09fBwI=", "8cGkALopsDX4zLEiEV+yFAuFse1BFdE5pMrkX4aN+Ds=", "xaxO8qooaTuFF/o378g4oEMpNO5w+MkTqoRCp+4bnGQ=", "83PtB07a6xu4kJJf648DCjAL1J86atPsPsYIj7uA/IE=", "AFcNwfKWcyxXfVw/wynctb78m9CMAdlIzsuPanmaFUw=", "EKFA4BxYvXzcZfAU9zVKGhHHDF6LBQF09hxRs1UoLHo=", "eJvxvaaoEP1AnYdkFz9aM0OCKPhuV3c08Na0/mC5kz0=", "6yGYYo4eK7U5IDhthM4doq2waZbf/vjD8tqbh0+io1A=", "TOoEid8iXe92+RUvzbRpqLduh2lcSY4JpvG4q+l6jaQ=", "RGhv8pkPPoa8fODtrT299WC7uPBIsNZ5XApvfZA7WB8=", "Ie2ZQHC11Gop59EGQg997m6GXHg0wng8f11l2zUo+FY=", "DyCbZBKOAMRcEfeEsFqB+YTW0rBW5brtTGSDnu0rM4E=", "lbBVdFw/n5Inw2El3u0khOjrulbJtkBPvSUeqzoO8Og=", "lvS0cpP+bACZoNp9Mu4W0Wty7LLmz/53J4wmIaRtZHo=", "TazjVRimNZxPOgdlHwgBYIwq7ivpI3OwpI/BS2awVII=", "ehE0ClfetC7pBUV5SVwlrJpVun6WTIsHTx/dWYNOJU8=", "7YqgbyZ95Em5k/1Xly0pyAq525IR6r2ppC+GhIHVmQ4=", "64LMuw0jDNUvYODzS0knRso0NfCb5RI5yqv5PNMb9tE=", "HJ7v0mgxLF0PbR5vAUFf10pxYEMBzgokwMsdrHDkN3g=", "Mkz6D/tPplSgncEMwteRviIy1TxGlmoqDKI4SXxufZU=", "boT9GdmnbHQ2zhImvfB9IN0uhM0ugOsOpe9GnijAB7E=", "z7WXVbOcCKxQY+S3sLblIRhPrZCLt495TzSwWvERJEg=", "+HBlkpyr6RnoQLPYOD+PBXCNA3fwKKNdgEfYNwEB9jM=", "K35t9e5gdogcsM2hAYRXzdnrQ9Nb6GiaHdNt0yvxN24=", "29BWqpQfwlR3kEmlQIqQUj0A8i7tzq0GiSilfGrlj7M=", "A+LJ4NW/AZNojzw7fYW6OURP0ZzkKhzuXsBjLQQ0XP0=", "pVSo/a0cass77f8RCItqf138BdMjAhgvaNsW/DbQu84=", "12fOkjsGA932qQt8A2OgKlfbqjinfDKlEIqjSNPM9f4=", "eUkuv75HdSi+j4DrxQMx7lCdqPazbKBhdhbf4wmr3w0=", "kP0vA21ZivI/Lg+pz8lGPUUjcTPWVbAN5ElNAwXoIpA=", "1W9gMqt1OPA8hs1INTZDhBYzJRJVCPtgAzL9epeWd2s=", "xEH6EE9kv4rnc4zGQhLl2h+zv65k2icQXf+JalGT25A=", "cyenuwZUkdlkvnIt4OVspnVixd8K5WbnOeJyrJt6jCw=", "xDxupKrq49CjPKznAvCL6wpAFfGRUfQ9EeUDVfGK1AA=", "sEqGbTTcKPPK5E20GIcuOqT7uKzEESt3cLybv0fzKqQ=", "hUZHetAg5QvGsA8gDixn3IBtDlY5J3JiCoKiHeC5Y70=", "jMr3umOlAeS+V74suegXE4pigxT/hFBv5jZHThCEFBQ=", "9Mzb28k8FeseXvpHjREoHAYTuaNjAsdIpc92zyqZJOk=", "rIvLfDPMsVHAtk3VqMt4jmPgwBQDozbC68dJpWcDyDU=", "puor7XUJJk4krrtMylGENFlA8g64uVjQ7hWalC7VYTU=", "gLZ/KU8CxHAJc8TDuc20ZMt2UgCB8Phi4kYtoMHtE9I=", "SFxWmha0WmR+dgfYcRwj4HoUSitC27M89ilSc57cPmQ=", "QFCGdoUR3MS95/gn27mQROAcl/BHTmejgJDTIQq+ooA=", "nQscb9ImYE522jkHHrA8yVhcQYSQyvWl6CpCU9Je/sc=", "e735zhpIWz1xcVWVNTG7nTEQWuejp0DzETaveBaL5pQ=", "7VDfHXwzGX/Oqc0WO5dh2W78KeZ1jdhxj0vi99unFSQ=", "QYrr0nxIUqsWBo17dR58PhSqdTwo5MxYpYdRHaArzK4=", "/rEmqBCSCttqVe5GyzE6MklZFQxULypSge4XxrVreDM=", "xnsiCOCbiLELT3uk96w2EKPfsblKJpAqmuKk8paguZs=", "1YfqMHKEGqW31Fa0CJHy855YL+5nKavs0IgopX7KtIw=", "O3bCqfIX4QPYJ/voZoeVCczkeg6o9Jwd8HubBgcTmN0=", "+8kJn36CtQ3LKqEfvVcswUW06vgyPWlcx5XZE/yooT8=", "/xG1gLe2GH+ifXpsvgfrHLrlOplPIGeFAnj1PmJ0VHw=", "2092h5QmtrGP0aDAsBjn4E7gX3DvzPO3KLVrdZYbVuA=", "LJW/M6alnbmUWna2r91/tlgJUjF5k3GPj4Lqty2s24E=", "Li6NAz/mp3dQtRTTg7o9ex8wGbINWDlkJTMkYoj7oQc=", "tLBpJ8OKc/or9zdjaxpKaGAcmKlZfdt+Y7CaRwBg4MU=", "xxyWUL5XNLYhQHsLySPNIA0xmakUc0KvZ8plSThdfU8=", "OOzH2/13RmMsrazsIIYqv8WOJ+YUg8eWsuEhUZWc6w8=", "br4Q+tLgRvVSgvKKmxk17UFanhKbd5e8hqmzdt96HCc=", "UR2QksNnMvzJT6uunxNPCkay6w5gRIcpag9cT5teFjo=", "ZgsJXw/W8t8oB5z70LTqxsri8ue+oZugPiGjTsr3WFU=", "Y694UY72cY1WhCB+R+L6H/3DhYpbaPIgGWLRhuQGSoc=", "hjct6392eBf6bVVOIHfcoYPZQ4gDf/uyhtN6sfqNoPg=", "9KuOwLjtsX1GmqQKcJrXf662xJwYffCMF9AwPWBSSd0=", "DaLro/Un7mnHUuiXu1BAlEAQ/tRnR2pZjk8hdT6KmC0=", "LZUVYO4w+QxjyeRfTK5T9rqxFvMFfo3OXhyuXglCKzM=", "3rXG5qiWlIIhW4FpLY6Cmoq6EN4UalFvV6olyxDB9oU=", "IEbzn3LLRpJvdpbgqnoEMVbTByAH2cLQOEJT/qcGLnc=", "FiKl4HKPs74eKy4TNpShAq2Ko13alaohGuzEhEaFdwE=", "PBoHWRkMmgh8iXDoWElsx6llr8wcZN0VHuUbRXJsS1s=", "J6fasojcef/0XClm2afTg4+AU9+hZGrAAyoyTkPog9s=", "3E2mzQ79ge0wnFXHOwYtYwyYpHSNn2cbzRRXle+ERVQ=", "omiOrqGz74t9u1JF8uydjMTLWKKpeGsGtHcFExOEI/w=", "fKFzKah6nX5/0FNJcOrnSsRVCdWtpPvsE7MuJ9dwTws=", "ThTTHbTD/GQn4JoGOU8eBIjiagWyxHfITDtFaMuuQNE=", "ob65Yw1UZGCswUi+WZN0m2QLCRZsAUCpyYP5v2428oQ=", "Erafzb+mFirfVNfiRP9w2MxEGaz/9eJ/jj/xyfBg8g8=", "rquNMzE2Zc1gR+bkTDfuqwWs16PneEXNUkklnC5+ZLA=", "0bj8jcTWtkP799CT5BBNzmZp50phxoeLz+rGEUX6Y3Y=", "+ERBE30zIlwNm/2zBVfDMfP5kXlmxn6j21xzt6+eU7M="], "CachedAssets": {"ThTTHbTD/GQn4JoGOU8eBIjiagWyxHfITDtFaMuuQNE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\kbwx5fy7uh-e2palb9wxg.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/HX.Experiment.Shared#[.{fingerprint=e2palb9wxg}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "te6osc4f7q", "Integrity": "i32wuNM7FSjQBhbVDOHmYKzGHLiRqXkm1gmkQA2+FSE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Shared.pdb", "FileLength": 109101, "LastWriteTime": "2025-08-05T10:55:08.5600768+00:00"}, "nCjZV2ahuFBBq599dE8EukLFaAJjLugX/JPSVBvGzkk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5ozbnmqx4c-less8k5dp3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e402i885od", "Integrity": "4mBwQ/MqaMqYqTYb2HFPmMMltxsnU8sHyzi7sq+P5n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.css", "FileLength": 75127, "LastWriteTime": "2025-07-28T05:52:10.5305456+00:00"}, "eLS9zdPR97LLy6ULAyAcsJzg05DbtKNGqOio8PzYKbE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1zsqth3rcz-x67401ppqt.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.compact.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0c5yxzlw3", "Integrity": "8T0v0lIgEXCx0/8hv5A4IOc3DJG1hrSfxrIYfDFbKPM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.css", "FileLength": 75109, "LastWriteTime": "2025-07-28T05:52:10.5024585+00:00"}, "PoECyUNlx1x+IC6psCNWuzHbwDGp0hWbDch5bI5gW8M=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vyboxlqsxg-82f7tjxk3h.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.compact.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8w5f9zha1b", "Integrity": "aI8eX8SgkFjsvq09UBplk+Oz2beVaNyFxzQY9IjAOwM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.compact.min.css", "FileLength": 71608, "LastWriteTime": "2025-07-28T05:52:10.5129663+00:00"}, "puor7XUJJk4krrtMylGENFlA8g64uVjQ7hWalC7VYTU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\urrdhtks56-ilymmvzuiv.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=ilymmvzuiv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rm1i1nlqhg", "Integrity": "PfqECs/Ij0Koj/rjC2c29DDRUOpzt4LPgUVjdxg7upU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2318, "LastWriteTime": "2025-07-28T05:52:10.4715997+00:00"}, "2LEY7WO2VQk7zPvdgwn6Uqd/OousIKBSZrNCX766RzE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\sqjvzvz5ns-0i9c2pqcbz.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f73vv6kcvr", "Integrity": "+7hT3MIQEl4CF/AwyoUxvp1BGa56glRh+VZ3Obj9XIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.css", "FileLength": 76595, "LastWriteTime": "2025-07-28T05:52:10.5495195+00:00"}, "D26splGMektx1xgD9zd6qhY39XqYFW6BcG87JXiqa/I=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6q525j3tlr-less8k5dp3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.aliyun.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e402i885od", "Integrity": "4mBwQ/MqaMqYqTYb2HFPmMMltxsnU8sHyzi7sq+P5n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.css", "FileLength": 75127, "LastWriteTime": "2025-07-28T05:52:10.4615395+00:00"}, "gLZ/KU8CxHAJc8TDuc20ZMt2UgCB8Phi4kYtoMHtE9I=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xa4f2oojwq-xvyp5vwm4h.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=xvyp5vwm4h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bk1ipnuw02", "Integrity": "ldLZAYEcIhH5tbGTwMvpmsmR71amZw+Mw27LKhZDymc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2235, "LastWriteTime": "2025-07-28T05:52:10.4743583+00:00"}, "K12FLrPJfwnNkR4QDNuYMh2eqFDQao89pOBlvqBVFBA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\qwz8zeekkt-koqihchfaq.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.variable.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "atlazl7hc1", "Integrity": "7rPj7qxQWVl+Rd3Xro7bn4U/VJdDRoxTT9ouP0L0gIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.css", "FileLength": 75707, "LastWriteTime": "2025-07-28T05:52:10.5908296+00:00"}, "z3Ip4wxJGBDjv6Y+pKoY87x2dYo4+rmFnIDTviE1pdY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\s2vzjyofgz-pvc8ota2an.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.variable.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9sxdqkycsq", "Integrity": "t8l3Yu1GR6nXUHCp8c8aX4BA70UOGbOvC0rHtpEznc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.variable.min.css", "FileLength": 72059, "LastWriteTime": "2025-07-28T05:52:10.6025565+00:00"}, "1NJT3C4gEEkAEwoV2CVuBy0+xGoh/ViG9qw3u3rG4Sw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1xpyc92csh-2jzru2q90w.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "js/ant-design-blazor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktyn9s7183", "Integrity": "+KaSx0H99B28TLdnTl+uqeU2PhlSUf0jb37ZnvY5SLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js", "FileLength": 20880, "LastWriteTime": "2025-07-28T05:52:10.6061162+00:00"}, "Wt8a0D2RKDgPh55acjYP3XwpFGCLfElYhc6pQfliY6g=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\9prptnwetl-islnrq0c31.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "js/ant-design-blazor.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4scx56kee", "Integrity": "opGGUImPQDDEVCzW3cI4J2Z5t3xVcLTlicJ8Hos8mcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\js\\ant-design-blazor.js.map", "FileLength": 69608, "LastWriteTime": "2025-07-28T05:52:10.6187742+00:00"}, "6R/AGCVIsClxF+enMNX7GpMrcAePfibLPMNszwC82N8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\zb5s6wfjbh-l551dbsw1z.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.dark.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ha881xsv8", "Integrity": "9VwzILe8TJfU+97Qu7p2EgzMtbaAMV3/IRjPcV4VC9I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.dark.min.css", "FileLength": 73010, "LastWriteTime": "2025-07-28T05:52:10.5642046+00:00"}, "omiOrqGz74t9u1JF8uydjMTLWKKpeGsGtHcFExOEI/w=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\dl5csvumjz-kzhe208m4u.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/UFU.CoreFX.Shared#[.{fingerprint=kzhe208m4u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.CoreFX.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyoxl62fov", "Integrity": "pulpNrtUTo4Q/CUPD7ZXEA7+XLv7UpxclNnGwzWpRMU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.CoreFX.Shared.wasm", "FileLength": 397733, "LastWriteTime": "2025-07-29T06:48:39.1164299+00:00"}, "fKFzKah6nX5/0FNJcOrnSsRVCdWtpPvsE7MuJ9dwTws=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\eerma7wfbg-taff1t37nw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/UFU.IoT.Shared#[.{fingerprint=taff1t37nw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.IoT.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "crrdv3fgp5", "Integrity": "yCi3gz4XJltm664D5gbturS+3QwKkkrZcvBaReEv7kI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.IoT.Shared.wasm", "FileLength": 127030, "LastWriteTime": "2025-07-29T06:48:39.1008977+00:00"}, "+ERBE30zIlwNm/2zBVfDMfP5kXlmxn6j21xzt6+eU7M=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\9sphss2tm2-0t0cbz3r51.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yl0hg1myek", "Integrity": "VrEe//LLHIYTPzlAf351Rcc3snRHy+R3BA4UluHfXLE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 13575, "LastWriteTime": "2025-08-05T10:55:08.5580677+00:00"}, "0bj8jcTWtkP799CT5BBNzmZp50phxoeLz+rGEUX6Y3Y=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ygwna291tn-c00z3j5cty.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/HX.Experiment.Web.Client#[.{fingerprint=c00z3j5cty}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Web.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8q8oicuv0d", "Integrity": "MOQfm9cQup5sEmgwp/5horXTG69BeYvkFcUocTBZsKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Web.Client.pdb", "FileLength": 8994, "LastWriteTime": "2025-08-05T10:55:08.5560682+00:00"}, "Erafzb+mFirfVNfiRP9w2MxEGaz/9eJ/jj/xyfBg8g8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\pa59i2ib0c-uvfygq5m20.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/UFU.IoT.Shared#[.{fingerprint=uvfygq5m20}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.IoT.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c3toapiyk2", "Integrity": "SJcLTQWrQkJQJpaqF9xUr9+Wg1icOMvBl7TVUcyCKkI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.IoT.Shared.pdb", "FileLength": 130537, "LastWriteTime": "2025-08-04T03:08:27.2054219+00:00"}, "3E2mzQ79ge0wnFXHOwYtYwyYpHSNn2cbzRRXle+ERVQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\06rq299ulu-rcvsh7enrr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/HX.Experiment.Shared#[.{fingerprint=rcvsh7enrr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jykgicr84z", "Integrity": "5X9+f/4nSaiQ6xGAMxkdbrDSL/K0kKbMmNHF/yREKQY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Shared.wasm", "FileLength": 85876, "LastWriteTime": "2025-08-05T10:55:08.5676002+00:00"}, "BYQTbfUUZQlmXwlRMugVW6lidv9vtiHwfIvFsbJ/tvo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\d52ys1dcik-g11xymr7w5.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Masa.Blazor#[.{fingerprint=g11xymr7w5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Masa.Blazor.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5wtjb913n9", "Integrity": "62IjyUCGpgjVbW/hf2TeEav9W4174CEgBrFrm58+FQ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Masa.Blazor.wasm", "FileLength": 841401, "LastWriteTime": "2025-07-29T02:57:01.9597707+00:00"}, "+b9ONmvz7MUFHlho3zN5lPowqCQLPdoXRYcyglE3MtE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\rxxrt4efv6-xex8mncx4b.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "Presets/EllipsisText/EllipsisText.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\Presets\\EllipsisText\\EllipsisText.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ofb6iup3u", "Integrity": "GJIE/xQaAzNbFvACgcIsBQs3DZPxkvLd+SCy+60JmRY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\Presets\\EllipsisText\\EllipsisText.razor.js", "FileLength": 190, "LastWriteTime": "2025-07-29T02:57:01.9002712+00:00"}, "lxVhD0wYWkfMI5RZcxPEQNUiHVw2fmUg06yJFbffbd0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\m9sr6dry09-2mtw4tr229.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/vditor/vditor-helper.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\vditor\\vditor-helper.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ihllssoz23", "Integrity": "BixBjuiIDOFrDWD9cMHo+OyCR77F6XwaktUaA9R7tuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\vditor\\vditor-helper.js", "FileLength": 837, "LastWriteTime": "2025-07-29T02:57:01.9043179+00:00"}, "ZZYdl6keZHG8hdU1xczJfzB44WNWgikJDo3gUqt7qK4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\hphvn5lkuo-6jrkf04sja.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/sortable.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\sortable.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21fi0zof95", "Integrity": "lsjdaHdoy2wXKwZywBEvbCNV2jAMeCc4Gd4BFUNl5kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\sortable.js", "FileLength": 13242, "LastWriteTime": "2025-07-29T02:57:01.9037957+00:00"}, "AKLzhbgvKhVHBdvfFF0V38KAM9vkTkVE0BCUgmEqDMM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ypalxvgq99-466423quza.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/quill/quill-helper.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\quill\\quill-helper.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18e5k8krz6", "Integrity": "QYvCrHWtZqwlrg6/1JWmgbz+qrzRLbANYYQJ3PoLY/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\quill\\quill-helper.js", "FileLength": 2174, "LastWriteTime": "2025-07-29T02:57:01.9017783+00:00"}, "Ovq0hXoeJZCQCIQOwij+qrlWKgRutHJxDoYM64oGLGA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\srs9i3hsfa-u9u24q7mqc.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/monaco-editor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\monaco-editor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aqws97y52x", "Integrity": "TuaMLOFeIg7OnsDghoQbW4mtDa+ZrLRjLHEx9hJEjB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\monaco-editor.js", "FileLength": 438, "LastWriteTime": "2025-07-29T02:57:01.9002712+00:00"}, "FXcbfmOnW9MoBGM5Dr5Zimr1sG+JDUDPwYtAbkkp3u8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\s9f7hgutfm-uyjj58hnqg.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/echarts.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\echarts.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sdopeze86h", "Integrity": "sGHMDlIXyoQBhy0JcYKHFuw7Wj9MmDLhYSDPaJUvHgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\echarts.js", "FileLength": 827, "LastWriteTime": "2025-07-29T02:57:01.9073369+00:00"}, "8EeRtovLIA5zEXsJXPguNdJFAvcLx4Dkn0Yf7GRNurE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\55df3tpy1w-ta6451bhu3.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/drawflow-proxy.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\drawflow-proxy.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p7kmo9aldf", "Integrity": "KzS+rG+8viCxGJl1U9dOY80WsWLSjQuPx247iY/FRuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\drawflow-proxy.js", "FileLength": 1048, "LastWriteTime": "2025-07-29T02:57:01.9058312+00:00"}, "/wlvvFHPsbDI1tjUQBQ2JeTULoaGOvohkj7L6pULWKI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\96ra3xu8vv-6tqei36j6p.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/wrappers/baidumap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\baidumap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jmf94bueuh", "Integrity": "W6f/CmZBY2pm+h5GoswtGxAe/T8ygy1TaHfeyMgg24k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\wrappers\\baidumap.js", "FileLength": 1357, "LastWriteTime": "2025-07-29T02:57:01.9058312+00:00"}, "CAU2sHoXa7WSMRm+D/Yj48dhf+nGe+bbC+ROpI82Yl8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\jbtdqpyjb0-pv848xjna4.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/resize/index-07a0c3f6.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\resize\\index-07a0c3f6.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7lc4eh8i27", "Integrity": "RTVqu6Q8Vgz6jhbB9hu5o6Wwu+eQALaUI4Fl6aPp8M8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\resize\\index-07a0c3f6.js", "FileLength": 624, "LastWriteTime": "2025-07-29T02:57:01.9043179+00:00"}, "vZ1vA0eh9Jwts/3j0J5Xqc1U7PQHO9VuFO4jrMFx4Dw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xp8j0y02uu-4x1zod59k2.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/outside-click/index-47d0ce8d.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\outside-click\\index-47d0ce8d.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c1tnpibpl5", "Integrity": "QNxLPp7CNZTCx0DoCX3VCX5rccA+u3HzbfxQe4G0BPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\outside-click\\index-47d0ce8d.js", "FileLength": 475, "LastWriteTime": "2025-07-29T02:57:01.9043179+00:00"}, "cWXlc4qM2Bd1xAexPkjtL6uCwGOOE4t0hu9ZkZD8pjg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\j1jr3esqjc-hrfj977lv2.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/intersect/index-f360c115.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\intersect\\index-f360c115.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l2iwg5zs3k", "Integrity": "g2io+OCVNKnoF+UC/I4LnzbX+TXgUgvJWDgzQvYEnOI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\intersect\\index-f360c115.js", "FileLength": 827, "LastWriteTime": "2025-07-29T02:57:01.9037957+00:00"}, "aBj/lHq7fqL0udwIz8mJblFLn/V1Re0PJH+zDmyjAQk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\hve6ogtdoi-cy2ms3wvwg.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/mixins/activatable/index-82cb7376.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\activatable\\index-82cb7376.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1d38q58yrm", "Integrity": "BVElU6bNQ1FfMurErQu0RXaJFeHLR+BU3n6q2suMmWY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\mixins\\activatable\\index-82cb7376.js", "FileLength": 1101, "LastWriteTime": "2025-07-29T02:57:01.9027874+00:00"}, "dfZXyONW54+cf3lW+XJc8BUOke1IpHMmdQHlLSMrLKM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\s682nmenk5-hoaxe64hrl.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/masa-blazor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\masa-blazor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "itbl5thmx1", "Integrity": "FniS0zr1ZXxTrRM5B15uM6mU+vvyjB3t3U2Ye575v2c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\masa-blazor.js", "FileLength": 11317, "LastWriteTime": "2025-07-29T02:57:01.9027874+00:00"}, "tqBhIwf6aXYgbYyBfcOL/GKonYiiyI6boKxpe6cvc6I=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\zp1f6whp0t-oye2tnu4h3.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/manifest.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zpqw22tmn9", "Integrity": "RynJC66GUY1sFeSmRabTbqbwSQinbs4wvE+12AAY3yI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\manifest.json", "FileLength": 302, "LastWriteTime": "2025-07-29T02:57:01.9002712+00:00"}, "aOm1oOlKl6+f+rlgPzM8zWT+0gthvvBmJ/p9TFEuXmc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3v9sscxtw3-apt2gw0td9.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/window/touch-f9d2ba92.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\window\\touch-f9d2ba92.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ztpzsu37d5", "Integrity": "hzHozXX5lqcn8+/kxmXGcsv573gIzG9qRO5h7P8/Osw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\window\\touch-f9d2ba92.js", "FileLength": 774, "LastWriteTime": "2025-07-29T02:57:01.9095787+00:00"}, "x1t29QWVZXKvyccZI+T1x1vUMd0ayihIyIl6my6MBbo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3pkgqoc23x-4e7su8ls4e.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/transition/index-339f8848.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\transition\\index-339f8848.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7o653ec6hk", "Integrity": "B8ygYx6K0OI8ENfmjUUmAWVaaYsq/HaDZVnO9SkxYJ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\transition\\index-339f8848.js", "FileLength": 532, "LastWriteTime": "2025-07-29T02:57:01.9089905+00:00"}, "L6SFwRNWvoEDQYge67awk9b1xGUQasCCYAncMrAIDxg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\j9ep1wbucs-b97pdmzs4h.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/scroll-to-target/index-1c14c8ac.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\scroll-to-target\\index-1c14c8ac.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "22pu2u29k0", "Integrity": "pksFbg5pnPKYtEtHAR7sKVmAl9yZCgcIeyCpEnXYz6E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\scroll-to-target\\index-1c14c8ac.js", "FileLength": 646, "LastWriteTime": "2025-07-29T02:57:01.9083435+00:00"}, "avi2zmilMogQ8/r7vKyv24hNg3mKPHn/fWG9TUf/dwA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\jsex7tijp6-suojt78vcy.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/page-stack/touch-ecbec91c.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\touch-ecbec91c.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0jd8shmh6l", "Integrity": "hOlv4nP6p7uhJ1nO5nHNDxtbtN6xzmwlw43E6C9pYTE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\touch-ecbec91c.js", "FileLength": 1234, "LastWriteTime": "2025-07-29T02:57:01.9073369+00:00"}, "8xiwILHEftTRRmaj0GkesOxSIxXFhAtQAfUJMYcVpR0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vl9avoc1ur-ftbbm70hxr.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/page-stack/index-3d21987e.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\index-3d21987e.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "38ys6psiok", "Integrity": "vVmJeJfWFBeTrTdl9wccNvYG4x5uJo/6BcDsdjk6+cw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\page-stack\\index-3d21987e.js", "FileLength": 862, "LastWriteTime": "2025-07-29T02:57:01.9058312+00:00"}, "+JLu7xlWMIlyVaj8SNCRzoJ+Hlcn40q2QK6hZc585I4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ypc3d9zz7v-ex5yhgml6j.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/overlay/scroll-strategy-dc362cab.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\overlay\\scroll-strategy-dc362cab.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ecqoecv18n", "Integrity": "Xknd9Rh0fyIZqQfymUoc1WBw7RMRAmJww2uYnsu155g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\overlay\\scroll-strategy-dc362cab.js", "FileLength": 904, "LastWriteTime": "2025-07-29T02:57:01.9058312+00:00"}, "LxtpT6wnxsURDN8NyTRFHtad5oZL3kHdV4Nzv8gLGrQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\lrif7gnk2k-elnr3bjcxu.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "service-worker.published#[.{fingerprint=elnr3bjcxu}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\service-worker.published.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v0362qbhxt", "Integrity": "MdGGOcVLqfb92AaqmuE/4yx3SucSBkH35lN25lNAhWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\service-worker.published.js", "FileLength": 928, "LastWriteTime": "2025-07-28T05:52:10.4405337+00:00"}, "jYinE/TK5u64njKvdtKqhq5Efv+99HLcIqobOulifg0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\sygxa5xdk5-p5osetst1n.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "service-worker#[.{fingerprint=p5osetst1n}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\service-worker.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eweymjul8h", "Integrity": "7qHBRQkWSJFi7FQGtHM0PmDUfQl+DUeEBI1T0TnRLNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\service-worker.js", "FileLength": 212, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "oloSVuMpF6MxaffepturH0n0AHW/rnsAcFpoXclbjaA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\56j4f81bx3-tswichycbp.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint=tswichycbp}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\appsettings.Development.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18s7itl1b8", "Integrity": "X/+i+NVLERSsvyIbCPwVbpCnFJxSjGtBN0hXRQ/QJ3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\appsettings.Development.json", "FileLength": 110, "LastWriteTime": "2025-07-28T05:52:10.443633+00:00"}, "UWt1kS5GVybSR5ll86E/aHYhZJndKepBBTv1x3FsNJI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\003nt3dehh-tswichycbp.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint=tswichycbp}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\appsettings.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18s7itl1b8", "Integrity": "X/+i+NVLERSsvyIbCPwVbpCnFJxSjGtBN0hXRQ/QJ3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\wwwroot\\appsettings.json", "FileLength": 110, "LastWriteTime": "2025-07-28T05:52:10.4537794+00:00"}, "JOE6B9yV+dE0W9sHRlOdix7X9RN6/DHHiFnLm5a7yXM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\p7gsl2id9l-5vcnc68aiq.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "HX.Experiment.Web.Client#[.{fingerprint=5vcnc68aiq}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\HX.Experiment.Web.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nm2i8wt94p", "Integrity": "z3ULiFzgTSAkKdaHvgr5QpKDf7b+OWFNOtELx3shmJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\HX.Experiment.Web.Client.styles.css", "FileLength": 141, "LastWriteTime": "2025-07-28T05:52:10.4610316+00:00"}, "Rvd82iw16xsGLDDP2kvmi97tVi6MndG8CtuaBZ7ypVo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\b8ta3yvrax-md9yvkcqlf.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tu2qkcmwci", "Integrity": "C8sFFlTouzLtqtRyeQRNMFLPF4W+BCiy3mRTsrDEK1U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18006, "LastWriteTime": "2025-07-28T05:52:10.4585172+00:00"}, "3sizww066fnSRuGvGzfWGL5fJStHmuy8St5NFMS8ZUE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\jcj5tc7i1y-nodm4c89wl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/AntDesign#[.{fingerprint=nodm4c89wl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\AntDesign.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3ny7mto4eq", "Integrity": "H5X2ysswBiwfXS8c6unw6NdTVlj+TVNcu8vipNBlYvw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\AntDesign.wasm", "FileLength": 879878, "LastWriteTime": "2025-07-28T05:52:10.6458412+00:00"}, "oz3HA0ID1c5NFt+I5urV0lU0TOhk3gIsDw8ECFQVFd0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\kj0b86795t-9ci0d0am18.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/BemIt#[.{fingerprint=9ci0d0am18}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\BemIt.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8zexqrm13j", "Integrity": "FBBqhukld/8MKvNH+S9s1di8rsL/2fWxjc+KHImTid8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\BemIt.wasm", "FileLength": 5404, "LastWriteTime": "2025-07-28T05:52:10.6468457+00:00"}, "kJpvfiWPQEjD3MKsvi/rqTHDyjyg1kpdxWyCQfvt83w=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\rc7nuvcbsh-moygjlvik6.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Plk.Blazor.DragDrop#[.{fingerprint=moygjlvik6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Plk.Blazor.DragDrop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5lpq7chnnn", "Integrity": "9xRvo057Cyewto2+ykrs9Zu3qfMVfKeUEOdco33F8c8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Plk.Blazor.DragDrop.wasm", "FileLength": 7477, "LastWriteTime": "2025-07-28T05:52:10.6493588+00:00"}, "r6xVazMWfVI4zTsS62R9YD8MS1zy9Y+f6HSR01DKw9A=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\znfnxju3as-f4ud32ue6m.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/DeepCloner.Core#[.{fingerprint=f4ud32ue6m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\DeepCloner.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2vb7y7auvk", "Integrity": "zjU4HoOPdvJ2dXTFiKOdgTezkT1XH11IEW1+TqdFcY0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\DeepCloner.Core.wasm", "FileLength": 13296, "LastWriteTime": "2025-07-28T05:52:10.6508675+00:00"}, "AblG1HLIXLKCt5Xmkqv2JdFgYAp2tIZ8kxkdsQ8VlwI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\2wq2itmaeq-psv1xceu9w.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/FluentValidation#[.{fingerprint=psv1xceu9w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\FluentValidation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "akwraz8tee", "Integrity": "mlOEZYVSwpI72PAGdsFAMMXQCdFcyBm/RRP8/19jj2s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\FluentValidation.wasm", "FileLength": 141927, "LastWriteTime": "2025-07-28T05:52:10.664487+00:00"}, "lMLZ9MIwCeofuKqSPSlr3/ByM87XDp3KiG08ahVC+Wk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\23xaq6a7zq-41c0wmz8h2.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/FluentValidation.DependencyInjectionExtensions#[.{fingerprint=41c0wmz8h2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\FluentValidation.DependencyInjectionExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vfcxm0rlcn", "Integrity": "O4MVAu3ZTE8r1qATCmH3xDHLX2R+yZKarhFFWumwuW0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\FluentValidation.DependencyInjectionExtensions.wasm", "FileLength": 11614, "LastWriteTime": "2025-07-28T05:52:10.6654894+00:00"}, "pHzXaHYID7kyNvSwq6wWmN8veUAFbjze1CoNEyUcOvQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\annjkdt9fh-z90721mvh7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Majorsoft.Blazor.Extensions.BrowserStorage#[.{fingerprint=z90721mvh7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Majorsoft.Blazor.Extensions.BrowserStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i2yt2o3v1f", "Integrity": "uuPL60TDUMh/Skh2oj9njN64g2yu+E7zrT+hejGecA0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Majorsoft.Blazor.Extensions.BrowserStorage.wasm", "FileLength": 10007, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "R1t2EXaJk6E1TXHtw2m9AFjaDD7eesFMByPqej5wGNc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\y33gtmb7m2-3eyeiubupr.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/navigation-drawer/touch-47e3a6be.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\navigation-drawer\\touch-47e3a6be.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9gviplhe8e", "Integrity": "R9Na9KC/tCWYrMKaCqE+HEvynXKRpSJKiLwCrZ/HrBY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\navigation-drawer\\touch-47e3a6be.js", "FileLength": 1316, "LastWriteTime": "2025-07-29T02:57:01.9043179+00:00"}, "2C1/fY5OWFVzhJObnQWSSXTYfx+McjoFw7dCYuJ4YZQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\u6dxf1u7u2-5pnzt8jjrf.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Masa.Blazor.MobileComponents#[.{fingerprint=5pnzt8jjrf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Masa.Blazor.MobileComponents.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m2pb1vey0g", "Integrity": "rjDTMttDMdw9yNkUMTc6LgBrxi1pvNoLH+fDUEfqxAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Masa.Blazor.MobileComponents.wasm", "FileLength": 73596, "LastWriteTime": "2025-07-28T05:52:10.6051127+00:00"}, "AXXvwJPqAw2OtOIZQZm1SHMPdNLUu1WbBWSIcYvF+zs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\m3ryl804ui-bvu82j4ad3.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=bvu82j4ad3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rn7ywsb9ec", "Integrity": "uaQ1LS1jmFmZzh352GTAoJXC27d1XRYZDoQEPFlsiaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17687, "LastWriteTime": "2025-07-28T05:52:10.4738536+00:00"}, "PDR1FJ0wuUgSyoPxucM3BZQHD85F/H1nBpD+BPkGsPQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\28rlbrqs6d-ptfrz3fits.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=ptfrz3fits}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4u1wmazeum", "Integrity": "TL/RI2mglttuA23zZN+3As8ND500F7dvLyGQn7Xe1lw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 132526, "LastWriteTime": "2025-07-28T05:52:10.5109626+00:00"}, "2xdGiY66tEhDCXcQAlv61EfNzqmy8uDdTFvhzD1LLbg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\0h7nb59pc6-a48sropenz.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Authorization#[.{fingerprint=a48sropenz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jynreno9u", "Integrity": "cI7U8oO0eETR510gNucvrhWGSLevLriiRtYwoxEWrLU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "FileLength": 9871, "LastWriteTime": "2025-07-28T05:52:10.5129663+00:00"}, "kOruBv348ke14/4/GpgMKKMXRAOgOlw7mFttnst7hqM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\s0k4oigw3c-elz03823ys.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.DataAnnotations.Validation#[.{fingerprint=elz03823ys}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.DataAnnotations.Validation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1a5h3e6jn", "Integrity": "GL9e/fF8iV8r28OtBWR+6njw7Ipj2WYZXL5DMPgV5Pc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.DataAnnotations.Validation.wasm", "FileLength": 4839, "LastWriteTime": "2025-07-28T05:52:10.5139666+00:00"}, "Vm98htKPEW+hRB62ScMxO/bpIFGIpXY97RMl1MNqggg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\q0tytkajje-73oi73dvgk.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=73oi73dvgk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d75ticaql6", "Integrity": "meE31nuqz9R2oYPZOEtKSHF3XFPlsTCkYGsBqmm7dFo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16299, "LastWriteTime": "2025-07-28T05:52:10.5164744+00:00"}, "xrkucdw0MCEtxVqDp1jIySsIJ/6KGrgNJ6oMgJdSuXk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\e50cqxxrcs-pm8mpy5cip.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=pm8mpy5cip}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21fcg1h6jt", "Integrity": "Fhsan/uHdrVJPrYtZJ7AdY+/Olc7tnGsiQjT8SU3N6E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 70894, "LastWriteTime": "2025-07-28T05:52:10.5275287+00:00"}, "JU+dUAUwfKjRGBvumkskFQTBrtrtYlAsDGq6KOTM/ss=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1q978tfs9n-4ni28tl690.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=4ni28tl690}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gz8oxf86xt", "Integrity": "hF+uo59V6fSnHMubh9MjQsPYQ5d42TeoCuAa1Cvsghw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 65866, "LastWriteTime": "2025-07-28T05:52:10.5392475+00:00"}, "RG7FPqGjkStPzilJoUrWllnpeAZC3A5ZS58xW9zxmdE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\z0ikoxdne6-eyher82q7e.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=eyher82q7e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jj5cwd5cf1", "Integrity": "8ZT///Rb87GeFxRs8ownOt5YfUiDt14xFuC+fwwi6eU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2408, "LastWriteTime": "2025-07-28T05:52:10.5407902+00:00"}, "XCY+fT7Z0t5Z5rLOOhcqg8pUODszTJia4A5JSXNefUI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\f8xgnn6gd1-itm12vk377.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=itm12vk377}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47xmptjdfp", "Integrity": "kA6glfdKoe419kfVHMWH24oM0VrZlbfJ+1poRVIX8qM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15536, "LastWriteTime": "2025-07-28T05:52:10.5433077+00:00"}, "cptp0f9pGflClqAo3Wvb2xc47BercMRDrmVknFih+oc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ebgit10kwd-8ewlps0g9m.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=8ewlps0g9m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dow44okadv", "Integrity": "bSJ+SFJ/bScm11NbnU36NJhWLFBQNJOuoTFJ6qLV9LU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8297, "LastWriteTime": "2025-07-28T05:52:10.5454543+00:00"}, "JcaPdv8Kk7kMT8g3jd/uEhQQquNKUQebZmz2crNhJaI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\q9ra9h4rk6-yr6bnfroy5.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=yr6bnfroy5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "96l2cly7lv", "Integrity": "T0gWzb1BTmawe7uWGA2eEKqwVDRkNOmQW/QDVgSF6Ik=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14557, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "FK+y0dyWXTFrNByb49LB4l0Jz2jLDnh4RLP9wg70GG0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\i72gxu5yyx-9nblf8ao5a.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=9nblf8ao5a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lthadgh0qu", "Integrity": "Rwof7iXFRmjFK5/p4UUtzpCxTDfU6kS1xuzo110/y/g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8235, "LastWriteTime": "2025-07-28T05:52:10.4515677+00:00"}, "KtmztVQrhvoAHpfOz4uQoAJwf7GE043gBk/7lhNaJJg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\0yhg9y3fch-mjuqqf9ko8.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=mjuqqf9ko8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "epyw0a8qfl", "Integrity": "st+/dlynGsd6TqU5XffhNlEj367ZDOW6rYKKdFKLkoE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8049, "LastWriteTime": "2025-07-28T05:52:10.4548403+00:00"}, "nLZmSOtbJqyvmf01as6m0BEDe5Csm6VuAcJn+7scpLY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ifqwap0foe-v66dtpac4v.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=v66dtpac4v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dkwoqybme8", "Integrity": "I/OMmyZYJZeCzJGlYBCulvwLuLnpQzjuMyQeOFAYZUI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35425, "LastWriteTime": "2025-07-28T05:52:10.4630663+00:00"}, "BNgWjig0pi1o8Z/x7kSGs/LXeWWgZYAnAEYAT4wBQ1w=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ucn1io3fpy-apuz8nsfml.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=apuz8nsfml}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pcx65pklbi", "Integrity": "WZf6gUcVZKdt3yMmpALH0MfBLIPdkDDh8GWnoPhvOTc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 21605, "LastWriteTime": "2025-07-28T05:52:10.4681864+00:00"}, "cI11AtwGDkgcXmwK6nJULaDc1mAOiHXAgNyufDQWoSE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\olc1matjxp-8bt7as0i9i.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=8bt7as0i9i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "73xrqg8xww", "Integrity": "QzqHZH6Nf89y1+mYIsDTZOB1Ms5ojwvpdSu5uRcuaJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5629, "LastWriteTime": "2025-07-28T05:52:10.4699666+00:00"}, "Efk5SG2w99MUu7v4hnRpTeLqdW5HFMmmr5LxB5WseXw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\tyrq2id8hj-etpym877t9.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=etpym877t9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18z4cwkpl6", "Integrity": "URfa6SrsHHcjvo8zY+p+EtGEceHmoueCcxEZhkCbi+w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16908, "LastWriteTime": "2025-07-28T05:52:10.4738536+00:00"}, "GGjRDAWoZdiYzp/7ztCx8kUzAB3fJbFUWjmWQkRFIW8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\spenc976dh-g2w0sei4ut.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=g2w0sei4ut}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kzu125hawo", "Integrity": "IFm4pNkaJspeO25SUceROy0KwlN7li1kKyl3EyOMP1s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16427, "LastWriteTime": "2025-07-28T05:52:10.4784997+00:00"}, "ZU1lPtzZJVjsA9weECOsfiWjuFheLX/3ok6INBq5sls=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5jp04j5a6u-ul0xzjnwdm.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=ul0xzjnwdm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "94izmhvvny", "Integrity": "ebCNdlTO4dYFeZ4XmQTtTTH8IBgEwAGn1UY9W69jRXQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18938, "LastWriteTime": "2025-07-28T05:52:10.4825246+00:00"}, "e0ktV/UMz97M6tix9hErMhDJr8PMhF4tehyrqA1YKT4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6sf9qgjeeu-nwxyu3e2hm.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=nwxyu3e2hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zd0w7usuiu", "Integrity": "CVdLNG4s96Guh/RQeSdL5V//VcDth8QDYPT1iXj3AJU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 24537, "LastWriteTime": "2025-07-28T05:52:10.4875339+00:00"}, "BlcVIwhbPb08hz7qN0tFA0Lt+np3Oq7n0jyeGOSwQdk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\l0yxa4bi6g-l36scmr1xu.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=l36scmr1xu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdndjvgz3w", "Integrity": "l0axP0Jtcq9s7NNOa7gZKZP0HuTGb3hAtiGMeJGlSPY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23736, "LastWriteTime": "2025-07-28T05:52:10.4912786+00:00"}, "k7fDoKMJGdQgy35fY3cSep2yyknIxw83bTX9Y5DZSCs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\12mw3q3xim-358c2dzezi.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=358c2dzezi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwvm9cincv", "Integrity": "9Xua7toR182nfeb/sTiYATwjO677ueIPZJuTadK38J4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15236, "LastWriteTime": "2025-07-28T05:52:10.4942871+00:00"}, "h9v4+YsA/eHsvKVBNO3MOx7ZblsYJn2gyBGDIvB5QMs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\qcr54qxf7q-nanjlpvyw1.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=nanjlpvyw1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qcj5gomn8", "Integrity": "LyxS4shThCVKPTN9Y4FuUKYqLO+W1PQDpSB9TVPMIE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 23587, "LastWriteTime": "2025-07-28T05:52:10.4969631+00:00"}, "Qfk4sPDjrAlBfM4q6omOSZhwVKGYhJo4TiHHra0r/Q0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\jl3u2p8b0p-zjb9sj3c45.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=zjb9sj3c45}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ds2mktd27m", "Integrity": "Y9Bre0ky2HedHqRMHPJExm9A8EQmrYJNb17LzlZXMIg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5729, "LastWriteTime": "2025-07-28T05:52:10.4340272+00:00"}, "DJb3lzXXbTHDzJ45KvghCst4LIaqbe/cqiZVlBNdwd8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7slosrludk-z1vz4pqseg.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/OneOf#[.{fingerprint=z1vz4pqseg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\OneOf.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g90qbpue37", "Integrity": "LC0ftiQdqEe3niWBYVKjvc0zCkpmp0XI+jwZ5BkZU0g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\OneOf.wasm", "FileLength": 30738, "LastWriteTime": "2025-07-28T05:52:10.4537794+00:00"}, "xYi8/7R/ypuSg72J9Q2pJLfJMVZz5x/2X5fZvbXlGz8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3lstgfz3t0-3yeepjwilb.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Util.Reflection#[.{fingerprint=3yeepjwilb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Util.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fgvnlmre0r", "Integrity": "/3ufT5dS/v9/NAcwVkwPZPDnjxtl9z1O7gHS/nbiXiQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Util.Reflection.wasm", "FileLength": 25306, "LastWriteTime": "2025-07-28T05:52:10.4600258+00:00"}, "j32UrbzU0PvSryLH/+FAk3kTEFyi2UovFGHjKYGYkQs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\qzvl43wc8j-wwkdnpv5zz.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=wwkdnpv5zz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kk2hjdclcw", "Integrity": "vJFmmGCeTvnEJ3uVyNZuzlcurBREYPOEajs6RW6Jz4I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 130654, "LastWriteTime": "2025-07-28T05:52:10.4932876+00:00"}, "paN4Sx3OtBrJddY5tYgZ6/7IIjMDBjCx342Y/SNaipk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\h88h11jt4w-xnsuvdrxcm.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=xnsuvdrxcm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ez9ond45pp", "Integrity": "ipNZAOQJx6Y9pBf2wlHhLZRtx4ucVFzqLSXaWRToUS8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 167042, "LastWriteTime": "2025-07-28T05:52:10.5195056+00:00"}, "bMmr0iuWez6twmRH/64K6xBEqAZUSQlrBinWMBKRrYE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\wngl5erv1h-9dosfnk555.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=9dosfnk555}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3alsej3xuy", "Integrity": "u8Tu+aCYhuYjkGxDYZnlEF7/MHV+iZLl5s7qVMuR/0s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2851, "LastWriteTime": "2025-07-28T05:52:10.5195056+00:00"}, "lAHBcmkdIWYqDGbBUn2p2s1ukU3HVuPxgSa1vjpD9Mc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\oqeho6gywz-o0bea8efhe.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=o0bea8efhe}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u520rx5t2o", "Integrity": "8rndPcbtlhZbnEHlNi4seb9wGcO44IW3L/Ut/5q1e/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2196, "LastWriteTime": "2025-07-28T05:52:10.5210105+00:00"}, "KhYHCPsjQvCDPACOyLm2A4q7QEKTDU1MaxgFUz8K7R8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6qbaprkznp-10xfh4xvhd.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=10xfh4xvhd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3bunjk11bq", "Integrity": "0fOiXMVRxhOlclYXbVLhNjG66RBL86XZ7+cS8YbM3tY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9085, "LastWriteTime": "2025-07-28T05:52:10.4805189+00:00"}, "5AdBASXyn7e0kbBazeQkaB0VCsJyjv2VoDodjzy7Bf4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ye9j53tdka-juaach1ctn.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=juaach1ctn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "icjzy0m5xc", "Integrity": "sk5UQjQBopwYM1fO+s1Yy270OtWTqErC3qa+Ip0g1w4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2096, "LastWriteTime": "2025-07-28T05:52:10.481524+00:00"}, "2q50CprO3pb8PoBQtXkvrd3OsCrFyhSv3aeBlmKHtfk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\zf83e4yy6n-tevaucknsr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=tevaucknsr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9vl4pgj3lo", "Integrity": "tTCMnYFlz0YIHq+eUDkUvRvWGLotoIsEWJHfzqTVi2E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2104, "LastWriteTime": "2025-07-28T05:52:10.4825246+00:00"}, "obvrpY/R9uXw1Y9O8VgWp+nbgyBcEjtSApqN/J1Odtw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ye81q17dj4-db6mieq8cw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=db6mieq8cw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rnq9of5shx", "Integrity": "N/Nslx9VHWh2qJnyBnAdrz8Mnnso4jkL9HB9/alqZc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 33900, "LastWriteTime": "2025-07-28T05:52:10.4895473+00:00"}, "tlHdXQkPThrieoCIzeJiYTR6NSmIzMmqOrdoI3VpAQU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6tfm6vrsyo-r0oz85e9cl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=r0oz85e9cl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "omqyrupwkp", "Integrity": "UH50cWQ8IJNaTKteuMlqM6lYpBne3bhmVeEvPBczVMQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 99008, "LastWriteTime": "2025-07-28T05:52:10.5090057+00:00"}, "sw21wQ0MOlHG9n7yy6MMW9eDqsD6sfxY6dihagxegaI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\golwaaz93v-tfini80nq3.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=tfini80nq3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8uboklxglm", "Integrity": "EJZsRp2srNvPODFqRm9PhDbLrycP4eTpSjVXRQyghXk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14676, "LastWriteTime": "2025-07-28T05:52:10.5119664+00:00"}, "ksnhCBIN/eoBbnJdNmRfsWg4ngyB9Us6NRPdnAM7NxI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\owbjrwwqok-ez39nsje6t.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=ez39nsje6t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zm6t7vopsp", "Integrity": "28nvBeYF/Ck0VInyPbNMoyOczLIbEABLtD1z1l2oa7I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16278, "LastWriteTime": "2025-07-28T05:52:10.4361332+00:00"}, "/LC13XpUm88JCaUnt6UTulBpeJVewMkg2hJIgNiaJRM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1zw6cuy93t-r26g9n8is8.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=r26g9n8is8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cnpaifpg4j", "Integrity": "WB7xrmCY5adoWHEFHu+0lmAHS6HrBPuF6O062ZiyKvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 48818, "LastWriteTime": "2025-07-28T05:52:10.4537794+00:00"}, "uH6ExqXapX2Chq3ICoD1FuFyeRqB6TbEUxGWM7nnufo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\h1779pf41e-pdn5bck3j7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=pdn5bck3j7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gs48zqsb4y", "Integrity": "QPX5IG/zd6gM1vmrZMec9/VM8/oAFnAqVgKu0Y0Ol4E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35438, "LastWriteTime": "2025-07-28T05:52:10.4615395+00:00"}, "xjifMJhAsAnrACjYvKlx8O7vTfxA5SarXJKeQe8jTyI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\4u2ev5tk7x-y7prqysv5u.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=y7prqysv5u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jqmdtmevwg", "Integrity": "pVbYjtwOprTsRng4rhDKMh3M5E+hl+HiKc155oyG4cM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2569, "LastWriteTime": "2025-07-28T05:52:10.4645781+00:00"}, "+QXHIro4OU9lmd2WKeSARKRGw4Q52sfvrmUB2Yzd6hg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\h5c5owzpmk-t9a9wlka3k.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=t9a9wlka3k}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f5yuj5te95", "Integrity": "nYDEtBSyHIWLg1BtNVcM2u29lDuSygWLJs3JrHzg+kg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6756, "LastWriteTime": "2025-07-28T05:52:10.4686909+00:00"}, "CxT6mul7xD5wX5bocGehrlB0A6xdUGsVtyu5IKtsK8I=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3e235ii6kj-f2y7zqa15g.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=f2y7zqa15g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uklj3hqgio", "Integrity": "/lqNP1onbMcYMn1VUTqy1xf04RdDUSUODwM1+0ruz00=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13342, "LastWriteTime": "2025-07-28T05:52:10.4728476+00:00"}, "TT5uArVp2cV4j4LFFk/OaciCyRSRXnQQinpmy7w1Vr4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3az95z54hw-s6d7pax0a7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=s6d7pax0a7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zh5b91imxr", "Integrity": "aSLhhqcnyJHSzEZBJ2XvrBEfT4WbeXMDbL7kpHeGALg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 122337, "LastWriteTime": "2025-07-28T05:52:10.4959589+00:00"}, "AuidevUrxwKWxzxCkXQzN9hc/7dnPvtSD6RIscPVLIo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\shyb8o7gxo-u0b472gpzn.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=u0b472gpzn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "igvymehwsr", "Integrity": "Pk4e2wgloNGe3tvigM7lRl3+WAhSEKqUXRuARoqtD9Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2546, "LastWriteTime": "2025-07-28T05:52:10.4969631+00:00"}, "qMhTl3U+ZBiyH96yrm12ToLb9c0xwpcIZSm+d5NKZrU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\dobvfppk1g-kluew6mdyf.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=kluew6mdyf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1igaddhcex", "Integrity": "CQMsAzZwaR8PObgDUKCWo79oaw9k0ChwIq1UQWv5Rfo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3099, "LastWriteTime": "2025-07-28T05:52:10.4984663+00:00"}, "4JEcLSpcNFdZks8GfynlYahb+8mCagA/ynLG9F1WnbQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\qgf3xyg5y6-vkyu6p469j.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=vkyu6p469j}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4a8emlbgu5", "Integrity": "lOSGV3LXOY3lFu7dszXBgJCZjOf3wbN71BYYCrshSh4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19347, "LastWriteTime": "2025-07-28T05:52:10.5004553+00:00"}, "aBUaTnbGBSo0wrG3dD12q7OxwZe16BbZncZ05Kboa00=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\on5hzexerz-49bxp5tzzm.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=49bxp5tzzm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3fysvmzv8u", "Integrity": "PyQylI6xFRpm3Du+MMFf9fnTqw64rtglUB6Nde0PZ4I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4544, "LastWriteTime": "2025-07-28T05:52:10.5024585+00:00"}, "6HHnpGXWohcWRtUld0fGp/EOV1UThiJLyP2LqPCiUs0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\4y4vi4n7g4-q3ol77wfoq.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=q3ol77wfoq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ixrg8knao", "Integrity": "oO0dXnvfR8K5Xf1M+P+uHaOdhOqLHQ6McV4/QSnJI34=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 374780, "LastWriteTime": "2025-07-28T05:52:10.5697589+00:00"}, "tj70ezrA1aSR4K8M2V/hWCm7J/A+sPk+dPP6EoNmUbQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\fgoqekynez-zbh2metxpn.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=zbh2metxpn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2u5bv6ucqa", "Integrity": "SimjYps1GO0JaMBI5C5kzEnyHHvH7H+0SWrWSwWPF9A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2060, "LastWriteTime": "2025-07-28T05:52:10.5702633+00:00"}, "k0jgKCXQWARlJAvC90fm39nZuRU7GsVBRrPFNN3Jrfo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\x40ds0psr1-tpf8dxdpz9.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=tpf8dxdpz9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ftfopub171", "Integrity": "E0cwaSbQ+1AXLFxoGh/nYQM1fKIZPlwsZ8bJMVhEYG8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4989, "LastWriteTime": "2025-07-28T05:52:10.4350327+00:00"}, "CrT8yxpShAj1gIyQit9TFNcQHIEEkpbzWMK1vwajjBk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\gnf4fveybq-xe0jinlila.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=xe0jinlila}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b2gizalqke", "Integrity": "LaOsXE+FJEubcmuVrthb5q1OPEUijBtYqcyxbDYK7Gs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2374, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "l/VAAqRPrbS46H3h6fSGCwnVDwWDN12AgB5ozns+Qkw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\cq35vobftz-b54ei8zud7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=b54ei8zud7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yvpg97wcqr", "Integrity": "DgxAjk8VQXhUAuf83x/cgkyrOwaCgQ/upAk6rPVsOGk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2261, "LastWriteTime": "2025-07-28T05:52:10.4419783+00:00"}, "juw9ygl9/WTGvLPuNfwgN0H1m7BOrH2nAX1H41vyx8A=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\lgtnyzv8ug-wk1j35mh70.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=wk1j35mh70}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7pi1yk7nek", "Integrity": "63bM1KC6yvwTy3BGDJsW9NkwKERgY03PvyL4+FvEqJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 73050, "LastWriteTime": "2025-07-28T05:52:10.4640732+00:00"}, "bS0VKP20ahLj6ng2pRU0QJ0za2kGh6+7fm+1WNDLuo4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\963d134j7x-c8h918kj1s.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=c8h918kj1s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72triqy0hw", "Integrity": "3+UGNCZEFsNfDS0KZ/LPZuSsIL/SLdv8Z0Pyeb7Fo2Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5099, "LastWriteTime": "2025-07-28T05:52:10.4681864+00:00"}, "ozpJxSGubRnYkUaPwyHT4GXNJvoKJE+go6Ji/EaUr/o=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\thud4kn6vn-vv59ito6pv.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=vv59ito6pv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aqtkjx7pi6", "Integrity": "fmVza/n/wgyVvZg6Y80GBALS5XrZsVHB31QQgps/6mo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16135, "LastWriteTime": "2025-07-28T05:52:10.4715997+00:00"}, "jqRD7PSi9xiQI9DPJD0GCh2/TVn73UPj3EcYR9rmWBA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ozmu3kfboc-tn3gqtft0g.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=tn3gqtft0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aob53gmef6", "Integrity": "pjKLt93ZNKC0jt9qEE6ZbAye2uKHr7h3RRyIji40oCM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7342, "LastWriteTime": "2025-07-28T05:52:10.4758684+00:00"}, "Ln5tMPq55ugntTZ9t2qa1H8LlQWJhtF//JhaCpDkl0Y=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\8faebq7s5v-wm0g8gvffy.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=wm0g8gvffy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "odx5i58kd7", "Integrity": "xMH63QtHI6fe0KoyfpDoGblkfRwXbgk6R6Gz2HLHHeo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9346, "LastWriteTime": "2025-07-28T05:52:10.4784997+00:00"}, "x7RXQ/FiWYzusjPcqKOVLxWIkjdY3WZ/oIsJIvD/XwU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\y78h3wa3or-h8fqcjxsh7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=h8fqcjxsh7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vp8z18b6f6", "Integrity": "HfgFZrQN32Hhi2SVIVt509m6jrbKfAa6i8NMOxOT0YQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2168, "LastWriteTime": "2025-07-28T05:52:10.4790032+00:00"}, "Ktj2fN07uyTz+2AlQ+n9TPwSRHFHXPjUZoENLC2GeUA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\otl11qpr26-cz2trkz68x.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=cz2trkz68x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yux5x0b7mc", "Integrity": "qmuRsEOPawVoA1E31Kay7ukDEkJbluC8tIMihtZgw5M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20045, "LastWriteTime": "2025-07-28T05:52:10.4845285+00:00"}, "hENexVMo6qiIm27l4YgtAW1DVQVLomxxFZzo2V/4eXg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\orzl7fmvpe-d4yu0wu953.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=d4yu0wu953}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9vihxqw4a3", "Integrity": "xeLfkPtwvxkjIfWjg7QkuJ1c/TToBmQ5KTYubrG5dcc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2483, "LastWriteTime": "2025-07-28T05:52:10.4855335+00:00"}, "RLpYqbKH96BmDg9ZWIaLnA0w2WlYUPb1nz6I38khxAE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\2rf7713l75-k8bxwla3m3.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=k8bxwla3m3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "weq1bf1m3c", "Integrity": "CK1pV1PdGMPbm34iPxWTNHWrgiMDaBWM7VGpQvmt5Cc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24016, "LastWriteTime": "2025-07-28T05:52:10.4932876+00:00"}, "l6z5XL1Ilrga2NTJ5P7OIAvi/5Cka+egvWxNQvZHrV0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\z83yfbqcdt-ym2mp801y1.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=ym2mp801y1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "05lj9dj18l", "Integrity": "xF4nHMlL22EpuB6T15DQ5cFGvRos8X98iDP8u3lpynI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3831, "LastWriteTime": "2025-07-28T05:52:10.4954556+00:00"}, "GKNBbpwODHMRQce+sYEoE2GHpjkpBAgbLqIJFPW9MKI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\mt9th0xa5q-fvt6iv7fue.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=fvt6iv7fue}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "66oj4o7kcb", "Integrity": "4qpdqjDpVCzc+ffKA4vKlzuKn/Lu/o1BJCE8HewbUOE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2431, "LastWriteTime": "2025-07-28T05:52:10.4340272+00:00"}, "Q0lu51er8/YXre9C3xRsP0TYh5gEldiH2PxTdG6ANYc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\bfhjfokcc6-zf5zxewa61.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=zf5zxewa61}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "njl157jv71", "Integrity": "U7S+NvsCILJJnNTO9+iD+X7+K9mugvuvN2/8rxaoUKU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35118, "LastWriteTime": "2025-07-28T05:52:10.4526446+00:00"}, "/cHMkDnvwAKMneId8krLw/DwIsk6sq2X7sTMUU1vSQc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\soq8e4cr6v-vm9yj3trgw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=vm9yj3trgw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcro48kkmp", "Integrity": "dIRRDEbiippXk3i+246UWvz7QUSITV8ydLENQN2fg8k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10390, "LastWriteTime": "2025-07-28T05:52:10.4574889+00:00"}, "fXiI7gF9GwfyoUe7oraGYinqX84/3CBMrg++g5xXaoo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\krulnxm506-0fwt3e8qiw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=0fwt3e8qiw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wd8ec95hq0", "Integrity": "9AH1DF91ajtfKNH2khMG4hUYYr1c1Z9nLDjW4W5rk5Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2288, "LastWriteTime": "2025-07-28T05:52:10.4585172+00:00"}, "XFy52wW+bDZkVMgf3NRgbV8aUsZjij3QYT//DdMx4+g=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\zz7mzgeaky-qixeimdcsl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=qixeimdcsl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukobvb19rj", "Integrity": "LiwuYf2QohtXowZCZn+N/WriPTWC2k9aEpQd24fNb6U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2167, "LastWriteTime": "2025-07-28T05:52:10.4615395+00:00"}, "x0Kvbk1i2uLkN6WcfN9rK9LhgYY+ySnPLGqaYJhCQ/E=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\szxumuzjyo-icrjyztu0b.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=icrjyztu0b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ysma0jvq4", "Integrity": "DHZCUTzH5fM0E5kc+EXptxPmDEL//dgaa10zRe0DL70=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2252, "LastWriteTime": "2025-07-28T05:52:10.4640732+00:00"}, "OLlliWBtnH9kniYiuoK7RDrPUO3QUjhinJNP+gjZMNs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\p9ja6o8kj6-wm1biee1tr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=wm1biee1tr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xm4vfaoa5t", "Integrity": "x/8Ni5Xe+Pji9QPrmI7YSNAMyBfUpteGhcc+9hoxxnE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6957, "LastWriteTime": "2025-07-28T05:52:10.4686909+00:00"}, "HBaWJE7uLzE8wZ5eUSaDl6HA9p3ueO26+wIoMMCtXgc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\t7udtq4wpq-ej4yzjlwjv.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=ej4yzjlwjv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mdm05n74pl", "Integrity": "tj3rRTMMkN7F3uTLy5s2E4Eu/xrLQTHJNSgrBu/ExTA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1983, "LastWriteTime": "2025-07-28T05:52:10.4715997+00:00"}, "TXvJrZiyk7OXnzRUICtOqrARmFJmegrlc+JbVA6+fmM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\tpnej1seux-0gu3rcpl5a.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=0gu3rcpl5a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3gue5xe6a4", "Integrity": "XcXT+wDpfb/UpcBeuyKQVhEhXnWa1BuYAcWUTpAEMoU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12407, "LastWriteTime": "2025-07-28T05:52:10.4753636+00:00"}, "cfv7sy+EKBcDjOo8QYxgvTRTyUPAfwPHo2ckcyv/Qao=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\d4eevtejl6-dcw20thh5h.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=dcw20thh5h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1ca9mjx1l", "Integrity": "enOeZ5sunHmtwqzpJxHfM7nbr059IR4ajlb+Rl3G+hc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42939, "LastWriteTime": "2025-07-28T05:52:10.4845285+00:00"}, "jRI/SJURei0n3RUKibu8/ZiLv4sDgGeWJi7afzqQAcA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xmzk8bzv4t-mr9kqgonfr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=mr9kqgonfr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k5a6xzafuq", "Integrity": "6AWZgU0Xbrs9xG4YKQsCSwMMKH8+6I8T3VR7lUx40WY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8456, "LastWriteTime": "2025-07-28T05:52:10.4865342+00:00"}, "Q/aisksgApSxnmzbGRBlEfepyKIuHv8PzhlT+CvMEUc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\kga36fzpr9-tmng2ufvun.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=tmng2ufvun}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b71tmnxbj4", "Integrity": "Vw3OaM/aR/B6p5DEqP7SgjUMTYBSFHQMzsPLecSoLqQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6008, "LastWriteTime": "2025-07-28T05:52:10.4880386+00:00"}, "2H/DWqnrykVVc0Gi2qs903QgtDavn392IvKoO5ZcpEM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\km0ij7tbut-xipxlxuqia.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=xipxlxuqia}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ffegbrncb", "Integrity": "ldFPgF3S1nF9kXNPoHsyO5ZZ9y1GI7Bj0+z3e0faSWs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2168, "LastWriteTime": "2025-07-28T05:52:10.4885427+00:00"}, "WXvN2nl1GIDwZQP9EROdVlTAmeRjUlTFOTKKMZgz8WQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7x3p1x56oa-2pw1y8vrcc.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=2pw1y8vrcc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w6fnrmmo07", "Integrity": "WOBDGAcp3Lzo0VfOgfsFWRR2mZfaUWo+EMiAr34DlPM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8743, "LastWriteTime": "2025-07-28T05:52:10.4375008+00:00"}, "ykmFQ7LjRa9qMYQsHJ/P0c+XXeaIIjFUVivRfR597xc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3h5gv1vq1z-j6u7vtcm66.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=j6u7vtcm66}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mh6igijswn", "Integrity": "pkZgoYstikGb3k5m3LFMm5PQqaM7xqTx4bxh8d6Q3fU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2285, "LastWriteTime": "2025-07-28T05:52:10.4405337+00:00"}, "rM3g0pz2ViH8D2SrrFrvX5yikq/ZTVTEJXUbYjU4QSA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\lzvtr9vizr-k2zd4vdn8q.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=k2zd4vdn8q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jrpg8i7h43", "Integrity": "byZE/NF5OH5b11y3lDKaBLTjrinr49RZ59UjY2SEnOM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9333, "LastWriteTime": "2025-07-28T05:52:10.4526446+00:00"}, "LLX69S4sUX8jMMBkwDauQ97ffYyXDMEwYKeqHcGmthU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\s4uzqopw8v-mun4y0k3g1.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=mun4y0k3g1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tn66er2zqo", "Integrity": "x54a8+rpv4TFvQlDuB2jUChtDK1g8kdxaBfnsFi89IE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16569, "LastWriteTime": "2025-07-28T05:52:10.4585172+00:00"}, "DoGp1ZSR6ckaJ3zYS7/4tg2rkWXxWo51WBBllM10KyA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ks5472hs06-udwbdnds43.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=udwbdnds43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oeqje5a2uq", "Integrity": "cC6jseDq5VMQly2NErK1IRXxJdfGp8NWjBf0jn79yXg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 30353, "LastWriteTime": "2025-07-28T05:52:10.4655837+00:00"}, "MRBxEqL1JT9ZBwtGiPExX8j0GokiZPQm65O+Ujz2NKc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\uik32v6nor-umfwhvvguz.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=umfwhvvguz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "omrmdb0pz1", "Integrity": "oFGCvDBaY6KBATkh+H9Wqz/C7bavl3y77EYAyUMZyGk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5551, "LastWriteTime": "2025-07-28T05:52:10.4686909+00:00"}, "atDNrmXUB3THbO3Pq/RO0Wes6MKUsMfrUPBmD8dGWfo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\wkwuhby3e5-t0nelwxf25.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=t0nelwxf25}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ptbfnsytef", "Integrity": "Y08mPq+5tqzWYOF8cmWupGk/+YbdR+M9FfI5jJ8f7ck=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11363, "LastWriteTime": "2025-07-28T05:52:10.4715997+00:00"}, "IkiA+JamaQIbRo2bRmXbgTrmJSEaGM55isTD5jEJdS8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5hy18k3bb1-4x3nc2vatj.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=4x3nc2vatj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l024k7n7tl", "Integrity": "y9xSIp1pJr/2JhasieY++h6/9zm1MROwRrWbJIRTg/Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2196, "LastWriteTime": "2025-07-28T05:52:10.4753636+00:00"}, "9PfgLIam4jzlSb8fgVaIltXXaDz0qWe7Vkbtf5jnIMg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\a77uxxfqef-ve06ce93x9.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=ve06ce93x9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nxz0caoyge", "Integrity": "66n0oxwX5+UYWHMEMYY4+K7kn3p6kOuyqG4A6HZFyjE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2245, "LastWriteTime": "2025-07-28T05:52:10.4774939+00:00"}, "sLoNm4NZkkQGlpeSP6PbuoOt0hy8VFV9clRt3xCZ6kE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\cx88rmugz9-iy9qiif4uw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=iy9qiif4uw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bps48x2tkg", "Integrity": "T/YepxT/d301LOMqQqiQwBcd0mEBIylaH/pDS65HQsA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213268, "LastWriteTime": "2025-07-28T05:52:10.5184998+00:00"}, "uj4SC7t+PhYF8CqyAUp6xoB6kxwh/DQCT9u/imEbB8E=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\bvgj74ix1b-w5wg4705i0.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=w5wg4705i0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6psqfjlvk9", "Integrity": "NkGkMUtMuQyPUD6rIzdSuWHElT7ajBY7mHYEjRiNG2w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86360, "LastWriteTime": "2025-07-28T05:52:10.5337138+00:00"}, "G25bRAAaWiQnrKRVcT+PDpWdd+fM8lHDqzBHsvHfmwA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\bo1q1fee9j-xu7xjw8zxu.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=xu7xjw8zxu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mi0frtlpi1", "Integrity": "ojBGpTajNnHRPeAGuoL2XneEMCR+kdA5oc5vXqIHJcQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 20738, "LastWriteTime": "2025-07-28T05:52:10.5377204+00:00"}, "sRkYDIOEBEz3xc0okgUn2fs2a+olriJSgw9/VOkAXZw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\biscpxc6ie-0mgcdzew60.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=0mgcdzew60}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "686qwjd0nk", "Integrity": "Br67eEblNrdZavzB4ZM7eWdcT0r5Bs7aGHpXGRzBNaE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 55281, "LastWriteTime": "2025-07-28T05:52:10.5469629+00:00"}, "xSH08CmD2/P3ONH1T9FdmLcLal8/h+afPk9A1mzv82E=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\0hl39ch037-jhmbff0p68.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=jhmbff0p68}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7khnfmhb7h", "Integrity": "pw9zmAVgg+RzS8k7HsytEDTvBUDaHAJq2y6UHQnqlnc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20561, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "bd2X0SYitBToMzFRM0Evxgfc/YccPgKJAGLf3GqIpWY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\nvzfc4f997-3qsuuerl8f.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=3qsuuerl8f}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lit9qqoxp2", "Integrity": "rb8GryyKTswyIMbvLawstB5ZOWrMXS0jp/wsazBY+fk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19507, "LastWriteTime": "2025-07-28T05:52:10.4526446+00:00"}, "zGmGgI+3mR/8HWroZ7KbAef1nqnUEj5NSmJX2AGR9kc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\spxmoh7nl0-ul8zn5e1cx.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=ul8zn5e1cx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "czd5c071dg", "Integrity": "+O1jFD26A7C3A4rNdI6HKsYOoCkmq5KhCqvz4fbt5ZI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 113558, "LastWriteTime": "2025-07-28T05:52:10.4738536+00:00"}, "nEke+M+RYU/LIo9ZLc/ugundwAwfyQjNEeo0EQdP4y4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\gn288xw5lw-291ybasbd5.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=291ybasbd5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tw8ifcbac7", "Integrity": "Y7O2WGP7ydLBo2p8i301K9M9w9f9DjWWBQ8hgnFGj/U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16015, "LastWriteTime": "2025-07-28T05:52:10.4774939+00:00"}, "/Ftl2l54DyrgvLAw98RTGmVM1g8smeKCsd2n/UZYx6g=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\p7hccigexc-f7c6cnpbzl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=f7c6cnpbzl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rzz8tvdz9", "Integrity": "wWvegN81t/pxcbuqLn0npbQBpLvgzpRI46QwERQthAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41489, "LastWriteTime": "2025-07-28T05:52:10.4855335+00:00"}, "rSFC8asmlcibn6otY6Q63vMzlim6ysLi6J+i0n530Bk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5na1utnla9-pgdlqf6hf0.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=pgdlqf6hf0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mz2fcy4hju", "Integrity": "4pBx3TLdGMEVVqQ0N1QOoOpOfyK2zG/Z1fhnl8ADPpI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5908, "LastWriteTime": "2025-07-28T05:52:10.4865342+00:00"}, "dzOd3ULXHQ45HN/AhTsIE/eWvXFujTuD4EFFeHXJ31Q=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ia7msd7qsc-v9f1nrwq71.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=v9f1nrwq71}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "orn5cnruic", "Integrity": "lH+wBS54vLxeJayAhoBjtWCodoBvocqaQyALfbssSXs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12697, "LastWriteTime": "2025-07-28T05:52:10.4885427+00:00"}, "h4o1xmcON8wnIsSn04pcTPugKQl7i1DQPok9mwlBXZ4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\wss7hty5uj-vco9yp72z9.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=vco9yp72z9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1q19p0bd3w", "Integrity": "B+ZWKGh/YDXN+6vM2VJ4YjLtuHHVHJYX/FHmNZPDbos=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7509, "LastWriteTime": "2025-07-28T05:52:10.4895473+00:00"}, "wtIynD4q0+y3fy+sbyVXhv4CKwuwhds7fzQnHk/rryI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\pg79nholpt-3qke94s48t.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=3qke94s48t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uhu71w5e2", "Integrity": "OP+sO9o5sMvg81Ywcj3eeeLysyFP/n+egzOOQK//v7o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 45407, "LastWriteTime": "2025-07-28T05:52:10.4959589+00:00"}, "pP/f1jm9VD2tnJpvis/PGTcsokzOOmm7SLzy8Cc69zU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ovjemgs9t4-j27d86c1ax.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=j27d86c1ax}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mm1z8qlb1", "Integrity": "QdRW6+STGnvw9UjrGJUtdTEYx03W1OvirGNRQIkIHbU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10839, "LastWriteTime": "2025-07-28T05:52:10.4969631+00:00"}, "DQnZGGBggVNqDu5ivRdJtpx3iYRcrgO8qebMZKTqw3A=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\pjdaukm8jf-3trem8k1q3.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=3trem8k1q3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "voqrukb83n", "Integrity": "IeKfiGmW/X2sVFJ96YKQc/xaKEv1l2LnuEUWMyrLyww=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20261, "LastWriteTime": "2025-07-28T05:52:10.5004553+00:00"}, "Q0pehFX6hHj4Fz3nP1anqNsasofSSt+Ifqv/bTws1ms=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\hrtjhcovkb-92ksbzd5fa.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=92ksbzd5fa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6zxu2jmmhp", "Integrity": "glVNtkYszaMlBJEq4IQhsc9Mud3WpzzWymt2ZGrhSoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 32529, "LastWriteTime": "2025-07-28T05:52:10.5049619+00:00"}, "Fk5hWX+Uo7AgR8MRmZHcd4mr4WkYLxxahoc9mV78vWo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\8l3ny8a3w6-uobo3ckmyg.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=uobo3ckmyg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4e9au3b6xm", "Integrity": "DEj84qeb+mUCZ1vPH9w27EgLHOf52Rw4pjeReY+DYJk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2166, "LastWriteTime": "2025-07-28T05:52:10.5059661+00:00"}, "JIcnFknccGubL8/5lYSibwKa+ALJLhw+F0NT9gLHtig=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7jldv3v1i9-96hzvc4k8s.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=96hzvc4k8s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q2s1dd309y", "Integrity": "YvXmwTYE5GFH/o5xf58G9hVaisxpK+1FO/9bYD66nkM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22952, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "GVC2lO4uselUM5rQwjOoB/e5WlJQBrnkkGe0StRoodo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\f7f9pjg13l-2im9s78dal.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=2im9s78dal}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cu1equt5qn", "Integrity": "1KLFahOlNQZGiNXsXHNOBO70FSKcdhYtxmYxYOOrkqA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14329, "LastWriteTime": "2025-07-28T05:52:10.4526446+00:00"}, "4sKkdJA26glQ2fwl5lxfSD0W323WeUDtYGPRF/yfKos=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\n11jyptobi-wgh9g8utaw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=wgh9g8utaw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ykuq59km3d", "Integrity": "rjlPy15eXi+FTTqD87eoXEzwusXiY7bsD7T1nVcIdRI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10175, "LastWriteTime": "2025-07-28T05:52:10.4574889+00:00"}, "Gg8JUKc54xNvhLtKLwOCNgdSgGpNic6ze59siTyHnXQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\97pfyb7xsk-gd7qi6iakg.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=gd7qi6iakg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9abdz57xia", "Integrity": "n55mciVM4x/Vc1DdWNBpk1hBOdgCWU0Fe8ai+29xo/A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5581, "LastWriteTime": "2025-07-28T05:52:10.4585172+00:00"}, "uzyCfxHgp1eOmauttvDbKMM5arfU7PqJk/fQrxTNmIc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7w0qrks0l1-vjtlqd9u82.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=vjtlqd9u82}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ltzzzlp0si", "Integrity": "51iJviKiXQSvV9a/RH+JuZ2dDNZSc/oY9Wv5U5oFYi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16998, "LastWriteTime": "2025-07-28T05:52:10.4630663+00:00"}, "4Y77Gb8EDvOiOmFWeM1lmxur0wF9OBo40MX+y8pYpEI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\mm9yh4vxdi-64mxs31z7u.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=64mxs31z7u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i22lkqld7e", "Integrity": "usnIkcPUNXwBof/W1xFqcy8zMzXtAMHqERXHP8GmTg4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 38031, "LastWriteTime": "2025-07-28T05:52:10.4699666+00:00"}, "gwTvTnthSUZzbmeYoECv8qxSBiZw97LtAuld+Q047eo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\qi5qo6kn85-x0ioum7hid.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=x0ioum7hid}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m4ug5iktd0", "Integrity": "hiljb/VAc1hPUEKnpLl7RkrkUPe0vpMZXP3x17mhoao=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2737, "LastWriteTime": "2025-07-28T05:52:10.4715997+00:00"}, "LiFmDAsNlqgRudP0hPs/ZQQ99Yb12cdc094/nGkFqAw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\siztupr9pf-6p39t98e2q.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=6p39t98e2q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bmmwiawlem", "Integrity": "gzRyjVFNFLkpHbaaygaOXzHRWdAYLNZ6DPxk8kiXTZA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2249, "LastWriteTime": "2025-07-28T05:52:10.4743583+00:00"}, "ntOypTTX0OcnkIHnB+HQvBuSuVO2EuR7kwnWld8ofpg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\dh9qefyxww-mpxwzvv9gh.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=mpxwzvv9gh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g0xqzwazi4", "Integrity": "oAxk+Ygp4MEIOukhNqOW0rjDSRjUCP6urOp3U/nQ3/0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2018, "LastWriteTime": "2025-07-28T05:52:10.4758684+00:00"}, "dK1wp5br9qO4ZWlx+wcp5eH/qVaIsEagGc1nNtGMwMg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3pb6le1vpe-0h2ofj4d0g.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=0h2ofj4d0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86hsygqu7y", "Integrity": "8AdooM2vkkE6bRj9ESzIGPARHhn8V1Fqpmkd9E1iRaY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13331, "LastWriteTime": "2025-07-28T05:52:10.4790032+00:00"}, "XxVtXyJG4pffgyXMFu3RWBQnNyd9AozPyuNx4m9p3q0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\bn7xe8bxor-s8o48w3zhs.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=s8o48w3zhs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qvefftvbmh", "Integrity": "6lP2oR3WlQGsniF6qOaEoonLPsKTk8xPK0krMjPWs3o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 299910, "LastWriteTime": "2025-07-28T05:52:10.5305456+00:00"}, "Tf9TaFbBcLmm/Vc13QGpmsXxAKzNM8UJGk8tpb5nFes=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\26l015ie1k-u8z9sw3duu.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=u8z9sw3duu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8b1jxpudst", "Integrity": "ZHIiCDwFZNtSg3FUSuiLx9sUHdL8QHEo6DYdZ4ZMvQA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41388, "LastWriteTime": "2025-07-28T05:52:10.5367201+00:00"}, "QTysWN+1uuEdVAWP7BpuHylnIrT3qkK5Jslyz3rhW90=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\c8onfpk8ky-nw2es9etws.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=nw2es9etws}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yzv5p1zovg", "Integrity": "Pr6zAhygkjxHY9gCcFet3rPgwtwI3Q0SLjvOYPCPxaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58368, "LastWriteTime": "2025-07-28T05:52:10.546458+00:00"}, "1g+3nPXkic12wtuURpFJhxU3szj9U5QEhwMkahNbYhc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\fl4tva9fd5-ceekp79nva.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=ceekp79nva}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6nwtyqjrbp", "Integrity": "gkT/BnW30RFWPuk0LD32ZCSJ2tAtaXFkelIAhAsX3Pk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1051055, "LastWriteTime": "2025-07-28T05:52:10.6293074+00:00"}, "yNfB7BoWLprD5u7xdr80qgYLX1UeR59gfiivbJzH8Pw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\28d3res6gc-yuilq41vqn.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=yuilq41vqn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qqehum01nu", "Integrity": "HmVbZblkriSxUs8quh0bLvJ8H7mjDCRPrGlcbTe9MjI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12772, "LastWriteTime": "2025-07-28T05:52:10.4912786+00:00"}, "wczViKt+bL/dnsTyEhsJrJ1EmenB3qLTXU4ZzuVxmcs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\gvcbyrq4bt-60tb0ho2sl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=60tb0ho2sl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lhzb2i9xo", "Integrity": "xNaIKxjdgek6SnWk2yzWer6IQygYjxkzJzQLeGrnG4o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2256, "LastWriteTime": "2025-07-28T05:52:10.4922826+00:00"}, "3b3ztYoNX8P2dQXfzHPEfgH7VMICSuMVeLVFNjAeyXw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\0fqjuufo4b-lkjxsh5fdq.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=lkjxsh5fdq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x352bfl1z1", "Integrity": "o2KjbdFM3aH9WwC+QUaiqLoWylTx+6Nf++7K/mOwk+A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2218, "LastWriteTime": "2025-07-28T05:52:10.4932876+00:00"}, "5MCd+ajktXZIy0r4PfhU/pnCqljHMCyNj+mIsNyuACg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\jzofhankt9-mntt6z4my7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=mntt6z4my7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5r6tp3k5ps", "Integrity": "cewCpC31ILJnaRz4oUcA1+ZcZ3KGu25h0504RMSb7HQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 51912, "LastWriteTime": "2025-07-28T05:52:10.5014586+00:00"}, "NsOd1VUfRNeo9f144UlCTL778T71f1InfmLQqFBfZ9k=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\um4axcetpp-qcz580dp2c.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=qcz580dp2c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jyd7pqy9je", "Integrity": "awNGzrpGxZxZys/30rtK5K35njnhwB8sF1stYHcTNBo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2134, "LastWriteTime": "2025-07-28T05:52:10.5024585+00:00"}, "BIBLeJ8I07qNxv/V59EB4KhNqyMCXii6VDHd+ZodUNs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\p94sbz801v-qlfabz3vrd.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=qlfabz3vrd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jkhkd8o1ub", "Integrity": "DV1NMYL/eWDnIbES3tvQcxMnTK+Oaq2DHfYDwRU3JYU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 191583, "LastWriteTime": "2025-07-28T05:52:10.5382269+00:00"}, "vJ/xcYKa0Jdqx3PcdJ0wtYoc5XwgrEpHF5tLFfSeUXU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\jdsj2j9ibk-4ioali3dtl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=4ioali3dtl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lqypcc5k0q", "Integrity": "4/f5mGX6SVjD2RUOuwTmqSxFfc17pImI9jfZfYvrZuc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2346, "LastWriteTime": "2025-07-28T05:52:10.5392475+00:00"}, "IZKdZnEy9i0uhyQ0ph658TD/dMtkmpyjKGirfRIHdMw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\a68kl3aedj-94422c2jz8.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=94422c2jz8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0sio8dezbg", "Integrity": "EHEZ7Og5gTkIr/KBakpP9IDWTVY9RwaJY/56e+t+IZY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5646, "LastWriteTime": "2025-07-28T05:52:10.5402585+00:00"}, "pj2Yb+hSTQQVpxHTLA4zYL2yVIACNN7DUY6MjinuRpc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6e3047j23b-az1ws9l8sb.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=az1ws9l8sb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jz5qy5xiq1", "Integrity": "MTlxAz2oSFTCUlcmzVl//81KN7UHdDWj3+61ucZJrIU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2444, "LastWriteTime": "2025-07-28T05:52:10.5407902+00:00"}, "uI8pzBe/DPveJK/4QVuIevmJwCRoPytVwf5izFPc0uM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ztkh9lrb7l-7crqeatgdf.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=7crqeatgdf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v4yxrxdy4f", "Integrity": "D5oTBMQHPsx+c2wpnJ1tcWgETRJ5sapJIrdjQZQjoIM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2106, "LastWriteTime": "2025-07-28T05:52:10.5423027+00:00"}, "dk3PxAXTXg2P7bQ3Aw/OUdrblB6JOmTrlkkBOXV/D+8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\hec1msuhcl-vhjl4emd1k.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=vhjl4emd1k}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sl7d71lla5", "Integrity": "QP0yS6PFIlLSfBIFtKoKJkT6Cy86di5dk5scTGuTUWs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2222, "LastWriteTime": "2025-07-28T05:52:10.5433077+00:00"}, "3ydPoTn+ilxRlebjGO4eUd2a3O32nGH23AvZwxlqGQQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xx6nv0owly-g2ueqliklk.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=g2ueqliklk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "87bdko3c43", "Integrity": "9J3hUSTQAjAh451sHw+aYYedQOxIxi4O/JpmsFFx7S4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7582, "LastWriteTime": "2025-07-28T05:52:10.5449328+00:00"}, "nEWAhvJU14eXxwYvIXgA0mYmnz6FloaWBlnE/nwJF70=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\n8w6by31ze-tikfkmjbfm.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=tikfkmjbfm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kwqa1fybk8", "Integrity": "TDyOF1HWd/35U5bnXfWRnz8eEFy7d1eW8b7a168rdRg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2115, "LastWriteTime": "2025-07-28T05:52:10.4375008+00:00"}, "7aTia658eMCwd/6IgauGPsZOoD32Td0OoVIEnzuXAC8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\oe7niu30i7-ikre56ww8x.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=ikre56ww8x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ix01svj6k6", "Integrity": "9uKXRrblnII74J/6B074ZxI/LqbpUTyWGp3ju4r3I8w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3027, "LastWriteTime": "2025-07-28T05:52:10.4395277+00:00"}, "sZyK83P+6GnQlJfNOGplz7CNHQyX2vBPv+Di9LNqhsw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\j4y9b7j321-d774vcz111.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=d774vcz111}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vajoyk1qii", "Integrity": "tznZ3f8GwWMJknkMQLIgQ05wN37ICusjKW8G47GTXPk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2975, "LastWriteTime": "2025-07-28T05:52:10.4419783+00:00"}, "QKmCvo7R8oYR6Io30AOfDWX5hXeZzi8/w0WXCj50OBo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vpo213vgqe-udw3yppzvg.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=udw3yppzvg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwqrpk53p6", "Integrity": "8lkQf8dw92hJLXROcHUctDHvVbhoYZ0BRQlk5ZS6mP0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2185, "LastWriteTime": "2025-07-28T05:52:10.4526446+00:00"}, "4J+ANGS6K6P1tR69740vvElqevFfX13RYX3e3yHl5KE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ta25cdr7ip-ccb9zx17su.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=ccb9zx17su}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpfqs9tdml", "Integrity": "A0K45q1ghes4+TAtD1byJuhK5kYIflAuClZw6XpbS0E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 30936, "LastWriteTime": "2025-07-28T05:52:10.4600258+00:00"}, "9i7rNICM7yUUF2Md0QaaPPnkgByTKUfo/PTiV09fBwI=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7abl7p56hq-n0wkls2ql3.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=n0wkls2ql3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hl55js4n2n", "Integrity": "a3E5HvPp2hqXo+NqbonmYsFF1iHTlxY9wm3+rsJ2oP0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2142, "LastWriteTime": "2025-07-28T05:52:10.4625464+00:00"}, "8cGkALopsDX4zLEiEV+yFAuFse1BFdE5pMrkX4aN+Ds=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xrlqs9pmhd-kg92xkhxu6.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=kg92xkhxu6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nma0974vlt", "Integrity": "MaUveyDH6yqsde4kpRr7HA5JIh9zQQzw3qNEtzZZEiw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23222, "LastWriteTime": "2025-07-28T05:52:10.4681864+00:00"}, "xaxO8qooaTuFF/o378g4oEMpNO5w+MkTqoRCp+4bnGQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ar33ofqh9p-ep140l5t7u.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=ep140l5t7u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxxwn61p1s", "Integrity": "zxKZWCXrNktgF49uOgUTkwpXIMa9NW5hI6YecfQNwi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2722, "LastWriteTime": "2025-07-28T05:52:10.4699666+00:00"}, "83PtB07a6xu4kJJf648DCjAL1J86atPsPsYIj7uA/IE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ugfyog12m3-0ytrfmq3yo.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=0ytrfmq3yo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "itiy4dgj46", "Integrity": "OtRKnuPPjo7+M4dPYF8lVdjB0pOJydrxOSyKl2eWo3M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2303, "LastWriteTime": "2025-07-28T05:52:10.4728476+00:00"}, "AFcNwfKWcyxXfVw/wynctb78m9CMAdlIzsuPanmaFUw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\11urtleb0a-4jdlfxag51.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=4jdlfxag51}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dh2s7k0s0j", "Integrity": "kTKXx7YxYflMh7r37bj+3hSdNgIdLtvIEG3rok2VnQs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 52005, "LastWriteTime": "2025-07-28T05:52:10.481524+00:00"}, "EKFA4BxYvXzcZfAU9zVKGhHHDF6LBQF09hxRs1UoLHo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\x178vlgtks-8hvyc6n71g.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=8hvyc6n71g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fujotwml7o", "Integrity": "d9eqgXsA9UY573eFTLlwVZvTEBefNyX8mWbgW1ILOVM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24092, "LastWriteTime": "2025-07-28T05:52:10.4845285+00:00"}, "eJvxvaaoEP1AnYdkFz9aM0OCKPhuV3c08Na0/mC5kz0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\isi2bdaajs-3zz321dhx5.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=3zz321dhx5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bct3zkg19r", "Integrity": "tTUDrrmsOg+pKXbDuBMJ5cz0BNHN/nhobQQOT9cVibo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2241, "LastWriteTime": "2025-07-28T05:52:10.4855335+00:00"}, "6yGYYo4eK7U5IDhthM4doq2waZbf/vjD8tqbh0+io1A=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5gc7rw3w26-b17xem4kd9.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=b17xem4kd9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hewp1djuz0", "Integrity": "SRUjBdSHkYcDA+bViMCBG+wyGzKizH7iP8r2vxw3v/Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5398, "LastWriteTime": "2025-07-28T05:52:10.4865342+00:00"}, "TOoEid8iXe92+RUvzbRpqLduh2lcSY4JpvG4q+l6jaQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\dljnh50jkm-7c8iwi484h.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=7c8iwi484h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "11mypu1pla", "Integrity": "+xqnggIaemTBJDmri18WsJyizCNTbEqkI1irXDth8UE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2548, "LastWriteTime": "2025-07-28T05:52:10.4375008+00:00"}, "RGhv8pkPPoa8fODtrT299WC7uPBIsNZ5XApvfZA7WB8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1t3y5syvkg-joa9uzwb91.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=joa9uzwb91}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7disea2k9k", "Integrity": "BV42g1wXD570n04cx55UHMPr4Cq//UxxullFRFGT9+o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2493, "LastWriteTime": "2025-07-28T05:52:10.4405337+00:00"}, "Ie2ZQHC11Gop59EGQg997m6GXHg0wng8f11l2zUo+FY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vykcrby2r2-ds6ouyv99t.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=ds6ouyv99t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxcylnksvt", "Integrity": "AVlpaBhLAkX2W4mid1agyf4d4SGEyB1Q8VQZYi8k6OU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10655, "LastWriteTime": "2025-07-28T05:52:10.4526446+00:00"}, "DyCbZBKOAMRcEfeEsFqB+YTW0rBW5brtTGSDnu0rM4E=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6wb75hnxpa-7hjfpq508c.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=7hjfpq508c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bdo4lwepd8", "Integrity": "odYpCM4QJP/ifftWuKG5FYkMUWN43Sn+a6RNBlvmJ4U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16806, "LastWriteTime": "2025-07-28T05:52:10.4580012+00:00"}, "lbBVdFw/n5Inw2El3u0khOjrulbJtkBPvSUeqzoO8Og=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\8f5f3mhg33-nli2l5xz80.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=nli2l5xz80}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kh5c39bjds", "Integrity": "yL1mUVNaclNQVOKm8yZJzKdFHCU5dn+suyxYbMLR9xw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16086, "LastWriteTime": "2025-07-28T05:52:10.4615395+00:00"}, "lvS0cpP+bACZoNp9Mu4W0Wty7LLmz/53J4wmIaRtZHo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ka7c6e3xb7-mdf98ysb2r.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=mdf98ysb2r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xrxue90jeu", "Integrity": "fVjxGxvkN4kgu3Pd/cP0wGYyCuidKkTZ+buKY33MFtQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2686, "LastWriteTime": "2025-07-28T05:52:10.4630663+00:00"}, "TazjVRimNZxPOgdlHwgBYIwq7ivpI3OwpI/BS2awVII=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\8bjj9poy7h-6pipl4ncvr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=6pipl4ncvr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pkqs5uu3i1", "Integrity": "iXL4QpDp998Fdkca4pYJWkNgzidryfU0b0IhMytlG/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2459, "LastWriteTime": "2025-07-28T05:52:10.4655837+00:00"}, "ehE0ClfetC7pBUV5SVwlrJpVun6WTIsHTx/dWYNOJU8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\i7ru95pfde-3j5ancst43.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=3j5ancst43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vc5xbsjpx3", "Integrity": "oRvl9ll7PtbBPHncxHEj7vJ3NFWUN+XD01I19Id1eyM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2315, "LastWriteTime": "2025-07-28T05:52:10.4686909+00:00"}, "7YqgbyZ95Em5k/1Xly0pyAq525IR6r2ppC+GhIHVmQ4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\plqxifjhoy-3fqk27lesd.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=3fqk27lesd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqx9vkwwff", "Integrity": "DGL7OAwZB885m7kZZAnRUcgu+lgJN9WGXSiEhMnwPWg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2260, "LastWriteTime": "2025-07-28T05:52:10.4699666+00:00"}, "64LMuw0jDNUvYODzS0knRso0NfCb5RI5yqv5PNMb9tE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\sipfrxn3kw-dryrlehyhk.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=dryrlehyhk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i296dos9ld", "Integrity": "agBgmcHezxR0PjGW7xp+s+5i5R9Fk4iy/1NSksL92NA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2192, "LastWriteTime": "2025-07-28T05:52:10.4728476+00:00"}, "HJ7v0mgxLF0PbR5vAUFf10pxYEMBzgokwMsdrHDkN3g=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\fjkd5izzk8-q27tawox7d.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=q27tawox7d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a5jqyb85b1", "Integrity": "bhG+NZBtDqUSp4V94Rf+saZxKKPzSXFxS5uyiZb3Fdo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2318, "LastWriteTime": "2025-07-28T05:52:10.4758684+00:00"}, "Mkz6D/tPplSgncEMwteRviIy1TxGlmoqDKI4SXxufZU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1543n98eml-co86r5at5r.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=co86r5at5r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cfdo350dti", "Integrity": "+LDBaptz87RdeV7L/gtfvoDGtOkhggt+m1EIKguv/UA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2650, "LastWriteTime": "2025-07-28T05:52:10.4774939+00:00"}, "boT9GdmnbHQ2zhImvfB9IN0uhM0ugOsOpe9GnijAB7E=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\136zfhe4qq-m5z4m57awq.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=m5z4m57awq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v0mfl8ekf1", "Integrity": "P1KtsQjAS7wADJDMnaLqib66BGC6a30X2uFOwa90Ypk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 190181, "LastWriteTime": "2025-07-28T05:52:10.5119664+00:00"}, "z7WXVbOcCKxQY+S3sLblIRhPrZCLt495TzSwWvERJEg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\3yamx75yw4-gba50v5uxa.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=gba50v5uxa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s26w0cn0ky", "Integrity": "PPnCKcojExuvz1YuN1XV/hDgK3EwfzxWiRaKQO9lJ3E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11077, "LastWriteTime": "2025-07-28T05:52:10.438506+00:00"}, "+HBlkpyr6RnoQLPYOD+PBXCNA3fwKKNdgEfYNwEB9jM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xwllqd0tmt-3x4i57jlr7.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=3x4i57jlr7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "witv2fkhn2", "Integrity": "yPo3GfR8ZN69pwFM95gFPkp09di1Rhc59Ujzpjg7Ur4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2151, "LastWriteTime": "2025-07-28T05:52:10.4405337+00:00"}, "K35t9e5gdogcsM2hAYRXzdnrQ9Nb6GiaHdNt0yvxN24=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6wr20ljaps-22pvy7xdie.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=22pvy7xdie}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "657c384tlb", "Integrity": "1s3adeoGfwWQ7/bTJBy6hc4nFmXnWh97JIiHMmYZclw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2181, "LastWriteTime": "2025-07-28T05:52:10.443633+00:00"}, "29BWqpQfwlR3kEmlQIqQUj0A8i7tzq0GiSilfGrlj7M=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\oh4lhdgr82-0s30uo9nq8.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=0s30uo9nq8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79xchw8mhe", "Integrity": "pZuznfe9Q1K+Kdmqr7cSIy0piiKlNG8GKtv4VHRwHK0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2946, "LastWriteTime": "2025-07-28T05:52:10.4548403+00:00"}, "A+LJ4NW/AZNojzw7fYW6OURP0ZzkKhzuXsBjLQQ0XP0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\m6ow8vcj2s-p2hu7rgmvt.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=p2hu7rgmvt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rti05gc2yt", "Integrity": "TwSi2kpnAIcs1Pa4X9pRgwfoLTaDdDdzWoMnPyz+0Gc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2524, "LastWriteTime": "2025-07-28T05:52:10.4574889+00:00"}, "pVSo/a0cass77f8RCItqf138BdMjAhgvaNsW/DbQu84=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7s5syrf71b-9mp73ffmth.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=9mp73ffmth}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zf9vuwv906", "Integrity": "cTCpGdaMReSmHZAFqECE/F81OHru+fhEw/bL/eFWrCo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2289, "LastWriteTime": "2025-07-28T05:52:10.4585172+00:00"}, "12fOkjsGA932qQt8A2OgKlfbqjinfDKlEIqjSNPM9f4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\wc7229zpsr-7yh9p39jmr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=7yh9p39jmr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5glmz7x244", "Integrity": "69oCpJOfGLGOw6Sl+CReO5rMIQg8MIlaQf3M1gBwuwY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514867, "LastWriteTime": "2025-07-28T05:52:10.5059661+00:00"}, "eUkuv75HdSi+j4DrxQMx7lCdqPazbKBhdhbf4wmr3w0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\1rn26omey2-6rnks9ilk8.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=6rnks9ilk8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vsejamjc8u", "Integrity": "UZweatpnEVzWbPmF7zZy2mjF4uzT3UqWAkK5MOzKPpc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2244, "LastWriteTime": "2025-07-28T05:52:10.4880386+00:00"}, "kP0vA21ZivI/Lg+pz8lGPUUjcTPWVbAN5ElNAwXoIpA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\w5hridbqer-se1td1q3c0.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=se1td1q3c0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "elld8ows7j", "Integrity": "khZ6ADjk0YVnT1mrE4spVYZpw9TX66fa2YDQoe3yHaA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2221, "LastWriteTime": "2025-07-28T05:52:10.4885427+00:00"}, "1W9gMqt1OPA8hs1INTZDhBYzJRJVCPtgAzL9epeWd2s=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\35gd2x4tqu-ddpjqtd0cx.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=ddpjqtd0cx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "opbu3fnrfg", "Integrity": "NLoD/tUSHK3ZQaXHBYzJ2fCxpZYwTpcZkpDkXqbidGU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23418, "LastWriteTime": "2025-07-28T05:52:10.4922826+00:00"}, "xEH6EE9kv4rnc4zGQhLl2h+zv65k2icQXf+JalGT25A=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\w4nz29jn8v-5spx91fs77.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=5spx91fs77}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9071mmxr8", "Integrity": "8KlqU/g5nK2kw67vt1tXd4VwZKy6zNm5rXjsV+CqMFM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 216887, "LastWriteTime": "2025-07-28T05:52:10.5316899+00:00"}, "cyenuwZUkdlkvnIt4OVspnVixd8K5WbnOeJyrJt6jCw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\59ureflrmr-9ma84zffbe.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=9ma84zffbe}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "44p0gic6nq", "Integrity": "3Kg8+ORTn4Gg194236oUBYDh779PS3+KcpuQ01YWU9c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 153675, "LastWriteTime": "2025-07-28T05:52:10.5541751+00:00"}, "xDxupKrq49CjPKznAvCL6wpAFfGRUfQ9EeUDVfGK1AA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\09ygemzq5x-ncb64ukzfv.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=ncb64ukzfv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jeptljrir6", "Integrity": "JUksvYviVmLRRM1iDjEcdwATfGKwy0YtYkspLIYXhxQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 20581, "LastWriteTime": "2025-07-28T05:52:10.5588178+00:00"}, "sEqGbTTcKPPK5E20GIcuOqT7uKzEESt3cLybv0fzKqQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\4qzuip3g6n-bfemo0ymlw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=bfemo0ymlw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ari2wh3meg", "Integrity": "dn9vBevp5gp1p3yVJVBpBOvp/UgpIsZvDga4XOGaQ4U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2287, "LastWriteTime": "2025-07-28T05:52:10.4340272+00:00"}, "hUZHetAg5QvGsA8gDixn3IBtDlY5J3JiCoKiHeC5Y70=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ud7ttds9of-ldc2uat7ti.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=ldc2uat7ti}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z2rsdc89t", "Integrity": "Kwjxh8arIZmNOsNj+HOz1FSoWRxlfAWlUw1WOzvUnhA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73177, "LastWriteTime": "2025-07-28T05:52:10.4585172+00:00"}, "jMr3umOlAeS+V74suegXE4pigxT/hFBv5jZHThCEFBQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\tbcah8mbfy-cjzqcnpgaz.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=cjzqcnpgaz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l4797onw49", "Integrity": "VcPzydIwEc5k/3aRFtl+chjrM0nnxcWYaJP5CDAMHOQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2289, "LastWriteTime": "2025-07-28T05:52:10.4600258+00:00"}, "9Mzb28k8FeseXvpHjREoHAYTuaNjAsdIpc92zyqZJOk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\wdx0gu2kr4-adppmfa22c.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=adppmfa22c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v5zig0ect6", "Integrity": "fY0lqgT3S0mIeG8/ekhhclswSjUHeKuQBmlsDMyr+KU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21038, "LastWriteTime": "2025-07-28T05:52:10.4655837+00:00"}, "rIvLfDPMsVHAtk3VqMt4jmPgwBQDozbC68dJpWcDyDU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\f7e3a7i1nc-tqpw82krhi.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=tqpw82krhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3ct26io24c", "Integrity": "/bBqPgQxG0bc+x1p9wE1GH1l8RLyCAJiHVmi3MxD10U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2549, "LastWriteTime": "2025-07-28T05:52:10.4686909+00:00"}, "SFxWmha0WmR+dgfYcRwj4HoUSitC27M89ilSc57cPmQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\wojh9gry9m-rivg4u5uzk.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=rivg4u5uzk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agaexyytak", "Integrity": "lupG9D/UFYusX1SD7vjTu6T++56Vte5uOL1dtI99pw8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2115, "LastWriteTime": "2025-07-28T05:52:10.4758684+00:00"}, "QFCGdoUR3MS95/gn27mQROAcl/BHTmejgJDTIQq+ooA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7cndh3sbff-bsa3odzz7r.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=bsa3odzz7r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4fcik3z113", "Integrity": "oAC4+aPfcL3iq5ZlaGbLnODtPPpQaqf2YJI0Vks8+zI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14653, "LastWriteTime": "2025-07-28T05:52:10.4805189+00:00"}, "nQscb9ImYE522jkHHrA8yVhcQYSQyvWl6CpCU9Je/sc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\uw98eu3com-iyerwqdgfh.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=iyerwqdgfh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1m5i2oaeg", "Integrity": "GmfzaYJvGReD+YBMmt+JdVAJBUZ8l5xYHLY24nYXTnI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51428, "LastWriteTime": "2025-07-28T05:52:10.4895473+00:00"}, "e735zhpIWz1xcVWVNTG7nTEQWuejp0DzETaveBaL5pQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ysqcstphyr-naml9dcyig.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=naml9dcyig}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eq618et103", "Integrity": "VujBBM+TtY5/U5EKKvc0TypVGI/ymoLSLNthoMTMu+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2363, "LastWriteTime": "2025-07-28T05:52:10.4907441+00:00"}, "7VDfHXwzGX/Oqc0WO5dh2W78KeZ1jdhxj0vi99unFSQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vcbkqxq6ky-895t9zulx2.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=895t9zulx2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zpyjredy0c", "Integrity": "h3nPG//z7X8k7q1mqeo+brp0bMowwj/TV7kNOZuEYZg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2161, "LastWriteTime": "2025-07-28T05:52:10.4912786+00:00"}, "QYrr0nxIUqsWBo17dR58PhSqdTwo5MxYpYdRHaArzK4=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\czv8wz9o86-mgg9pig5ol.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=mgg9pig5ol}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dm8xc7jvnp", "Integrity": "5aH4u14xUZh432jvPwPQjkU9GZ0LZvOtrLl7lUBQIT4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 9837, "LastWriteTime": "2025-07-28T05:52:10.4932876+00:00"}, "/rEmqBCSCttqVe5GyzE6MklZFQxULypSge4XxrVreDM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\ri8ycr4g6w-kb7fjpryvy.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=kb7fjpryvy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s36e9uxsex", "Integrity": "aAfCAoV9BRM2x1ab9uu0qFNIXRakqz1FKNbJ4SB2sZg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2107, "LastWriteTime": "2025-07-28T05:52:10.4340272+00:00"}, "xnsiCOCbiLELT3uk96w2EKPfsblKJpAqmuKk8paguZs=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\9kgsqaeqrs-puqm3d1199.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=puqm3d1199}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qa8q3exkg3", "Integrity": "fmhRyl+0zHXVkVeRYW7q5nQfHMzHfAOhtQZzwDhYEE4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2269, "LastWriteTime": "2025-07-28T05:52:10.438506+00:00"}, "1YfqMHKEGqW31Fa0CJHy855YL+5nKavs0IgopX7KtIw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\19afb3ka31-tcm6y315ec.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=tcm6y315ec}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1c9sk2tgr", "Integrity": "yY4/mBILFxDN3ZF2dPWRkVPrpQCQ1SmiSNQIYHEAtOc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2193, "LastWriteTime": "2025-07-28T05:52:10.4405337+00:00"}, "O3bCqfIX4QPYJ/voZoeVCczkeg6o9Jwd8HubBgcTmN0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vqbat96kl5-nguzyrd9vx.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=nguzyrd9vx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3fft2mv6o", "Integrity": "0mA9lc9ogdvaR3A+EDC5+YcW5bzKNCm77mn6L7bTL+c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 3994, "LastWriteTime": "2025-07-28T05:52:10.4515677+00:00"}, "+8kJn36CtQ3LKqEfvVcswUW06vgyPWlcx5XZE/yooT8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5k9wjv3tvc-0ub3375c5y.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=0ub3375c5y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g8s5oz1rdb", "Integrity": "MHKoPre4LuQDP6ftuE+KsSXdSBBsXzl4eJiuQ4rCQpc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2226, "LastWriteTime": "2025-07-28T05:52:10.4564656+00:00"}, "/xG1gLe2GH+ifXpsvgfrHLrlOplPIGeFAnj1PmJ0VHw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\yv4yyic8v1-rjragxq3cy.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=rjragxq3cy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tjt2siqux9", "Integrity": "GUanEjF7UeFj0nb5JK/AWDKjixgb8PzQx0qEcvcMTWQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2380, "LastWriteTime": "2025-07-28T05:52:10.4574889+00:00"}, "2092h5QmtrGP0aDAsBjn4E7gX3DvzPO3KLVrdZYbVuA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\neb9x8l2vq-q0h1rtwxr6.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=q0h1rtwxr6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ceqchvknm", "Integrity": "FSuqAqBj5r8ECdi9HuzP3pBIEFOt3OW/+j6O5vW1sk4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2465, "LastWriteTime": "2025-07-28T05:52:10.4595221+00:00"}, "LJW/M6alnbmUWna2r91/tlgJUjF5k3GPj4Lqty2s24E=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\58effksgk5-ghy2aao3pw.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=ghy2aao3pw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ljv8g09pn9", "Integrity": "sTEnlesJFEclA+Y8DX/SQ+7eTpWHFh94pBM4fQmP/fo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2306, "LastWriteTime": "2025-07-28T05:52:10.4610316+00:00"}, "Li6NAz/mp3dQtRTTg7o9ex8wGbINWDlkJTMkYoj7oQc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\bh3p56r2xe-h1hxdwycbb.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=h1hxdwycbb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1yljbnv1ms", "Integrity": "JzOsZydvaf+LZYJEPu4DMyI3hflp5l7nnkqlwUL1fGY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2338, "LastWriteTime": "2025-07-28T05:52:10.4625464+00:00"}, "tLBpJ8OKc/or9zdjaxpKaGAcmKlZfdt+Y7CaRwBg4MU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\scva4k8nvk-1z2utc94ww.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=1z2utc94ww}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "54cactdg4i", "Integrity": "1UszNDgeLrUw7L8rPntt/dv7oNT/9+cPPhWNcY9CSrM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2839, "LastWriteTime": "2025-07-28T05:52:10.4645781+00:00"}, "xxyWUL5XNLYhQHsLySPNIA0xmakUc0KvZ8plSThdfU8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\i6limy63o0-8bxt02qawp.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=8bxt02qawp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gnnh8sldto", "Integrity": "BOZ57qiARCa7Qrsv0hMCYH4oih4roUbVcQuPfhvZR5M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4206, "LastWriteTime": "2025-07-28T05:52:10.4686909+00:00"}, "OOzH2/13RmMsrazsIIYqv8WOJ+YUg8eWsuEhUZWc6w8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\55tlikoe22-549ex8tazx.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=549ex8tazx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zs7rw1hjmg", "Integrity": "Q/S2/LOvew5fFeVg6UbfAV/CjwwLeGG1r0Z+CUyHJiY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11766, "LastWriteTime": "2025-07-28T05:52:10.4728476+00:00"}, "br4Q+tLgRvVSgvKKmxk17UFanhKbd5e8hqmzdt96HCc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\za1a1ykm3q-pg3o6xzigg.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=pg3o6xzigg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7vhhnnhbri", "Integrity": "TYih5AI/vWem3MrPnUjoEIVF28egk6cSXG2dN6YhGPk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2509, "LastWriteTime": "2025-07-28T05:52:10.4753636+00:00"}, "UR2QksNnMvzJT6uunxNPCkay6w5gRIcpag9cT5teFjo=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\qw5vt3ixx8-jl2pswegra.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=jl2pswegra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v84zwdga7d", "Integrity": "P/TR2XNCQHJw2cQOzwNBCMC57H85DJo7E8vlEVjyM3Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14818, "LastWriteTime": "2025-07-28T05:52:10.4350327+00:00"}, "ZgsJXw/W8t8oB5z70LTqxsri8ue+oZugPiGjTsr3WFU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\2nje7616dz-uriri6ambh.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=uriri6ambh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bd0tlemo81", "Integrity": "4ct+FRPTPTTd22HVfGAoeQPAoTGjYem4rHjB4WjLkRY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26070, "LastWriteTime": "2025-07-28T05:52:10.4419783+00:00"}, "Y694UY72cY1WhCB+R+L6H/3DhYpbaPIgGWLRhuQGSoc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\lrwpci953u-s7q7c40kwm.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=s7q7c40kwm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mb59q8mkl0", "Integrity": "Ml2zwsh7JNg+FeVyT0q+UJCAIrJ7egnmcdvW4IcroU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1496494, "LastWriteTime": "2025-07-28T05:52:10.6849342+00:00"}, "hjct6392eBf6bVVOIHfcoYPZQ4gDf/uyhtN6sfqNoPg=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\h7ey9od52v-cixy86cf2d.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=cixy86cf2d}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hyjd0le2je", "Integrity": "SXpDPTwpBF89Nn3o1yiKlSIpnWEyp5EkucjQ8/wDqXc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12663, "LastWriteTime": "2025-07-28T05:52:10.4790032+00:00"}, "9KuOwLjtsX1GmqQKcJrXf662xJwYffCMF9AwPWBSSd0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\5kn3qxhbm7-4bdu6o25zl.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=4bdu6o25zl}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wzmqgrdp86", "Integrity": "FJ1by575DlG+xalCX4zrng5d0NShqleAr5quqKlSNWY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21067, "LastWriteTime": "2025-07-28T05:52:10.4845285+00:00"}, "DaLro/Un7mnHUuiXu1BAlEAQ/tRnR2pZjk8hdT6KmC0=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\eee8q4pddu-st0ovrfdhi.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=st0ovrfdhi}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ejs237ref", "Integrity": "2FDUZ2VEm0LUAiMUcz0zeSmGgaKioZ5HWrhHLSY99kw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 34749, "LastWriteTime": "2025-07-28T05:52:10.4912786+00:00"}, "LZUVYO4w+QxjyeRfTK5T9rqxFvMFfo3OXhyuXglCKzM=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\a5dw04moak-67rumul467.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=67rumul467}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrlgp5q2sa", "Integrity": "Md857jCS8I8Op1zOrKKuZVf9ZMP8soUNUxT0bs7bElY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1179429, "LastWriteTime": "2025-07-28T05:52:10.6986868+00:00"}, "3rXG5qiWlIIhW4FpLY6Cmoq6EN4UalFvV6olyxDB9oU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\10esx203se-593bvuk5yc.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=593bvuk5yc}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ciahhbnu1j", "Integrity": "dPyXpMZnCysVYyWHWrGJfZinXGl8CRbxZ9t/+xkFWp0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 55610, "LastWriteTime": "2025-07-28T05:52:10.7044143+00:00"}, "IEbzn3LLRpJvdpbgqnoEMVbTByAH2cLQOEJT/qcGLnc=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\gccug8g0hu-egg93qn8th.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=egg93qn8th}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vcxjqdt9ip", "Integrity": "YRICSbljxM+8xTDlptufDVEIi0HKHCCgLIZYh95x4WE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 87811, "LastWriteTime": "2025-07-28T05:52:10.7196602+00:00"}, "FiKl4HKPs74eKy4TNpShAq2Ko13alaohGuzEhEaFdwE=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\6x4qax5pu1-tjcz0u77k5.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-07-28T05:52:10.7553674+00:00"}, "PBoHWRkMmgh8iXDoWElsx6llr8wcZN0VHuUbRXJsS1s=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xhc45yopsy-tptq2av103.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-07-28T05:52:10.7772252+00:00"}, "J6fasojcef/0XClm2afTg4+AU9+hZGrAAyoyTkPog9s=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\cp6uxzobrm-lfu7j35m59.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-07-28T05:52:10.8112064+00:00"}, "DPQM5dFWnYfrtGXyHo5fphiMXRippUauZLwTfACgvYU=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\xi6ba9wok3-y1br3qhvs0.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/components/input/index-b92d32d0.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\input\\index-b92d32d0.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1qc9e2xu4c", "Integrity": "V7FM7YgwFiC+yZ96/dx6UCNZWSE3BEASjdh481ah76U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\components\\input\\index-b92d32d0.js", "FileLength": 955, "LastWriteTime": "2025-07-29T02:57:01.9043179+00:00"}, "4+hcsFi61Iu4pJ9NK2HcD1u/5wXpCDP/6a9lqgsrhRA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7ftge31yl6-rnlyxgo5ri.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "css/masa-blazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\css\\masa-blazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v6cdduczlg", "Integrity": "CZxdu74zzD3t7cPD7Ut8kNi7UVmKaPcpe2p5YVhws24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\css\\masa-blazor.min.css", "FileLength": 68185, "LastWriteTime": "2025-07-29T02:57:01.9106467+00:00"}, "9hqxf+oPz6uQDtR2xKvSO/eLcpJziCzHYHhbhP2Y6do=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\tzep23ns6m-ggo88y3uma.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/EventType-63cda6c3.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\EventType-63cda6c3.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8fx6nw7pr", "Integrity": "Yg1BpTTytfJY/RwjO36ChfO17Yt9x9y0M3WLVCCyECU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\EventType-63cda6c3.js", "FileLength": 671, "LastWriteTime": "2025-07-29T02:57:01.9002712+00:00"}, "kPUlcXftlw7GryoDqaHHxJkDwj++tEKy2Fo+XSTMaRw=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\gntkt48ehx-hmh33oi7ee.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/helper-6d386307.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\helper-6d386307.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hw1dbttfn5", "Integrity": "H6biBGplz1Da+eWJLJG1lf7jDmekVGtUKes42Ia0CLk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\helper-6d386307.js", "FileLength": 891, "LastWriteTime": "2025-07-29T02:57:01.9002712+00:00"}, "ob65Yw1UZGCswUi+WZN0m2QLCRZsAUCpyYP5v2428oQ=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\4usggfrprb-5d793ioxap.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/UFU.CoreFX.Shared#[.{fingerprint=5d793ioxap}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.CoreFX.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5mb7lo", "Integrity": "pAhP2UIECeFRGegpG45HAVSBLx7fks0b7XN3Ea4X+rA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\UFU.CoreFX.Shared.pdb", "FileLength": 491930, "LastWriteTime": "2025-08-04T03:08:27.2019047+00:00"}, "QtCeJcCCJIoCViuFjywFRswK2LtCudafviEeaYK7LjY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\u85skt7njs-c85iknmnh6.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/touch-5a32c5ea.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\touch-5a32c5ea.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etst10tc3s", "Integrity": "IqiR/UEoeGkao3gDywrRR6GgiwlxCDe5pVosH5j12X0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\touch-5a32c5ea.js", "FileLength": 821, "LastWriteTime": "2025-07-29T02:57:01.9027874+00:00"}, "Y6gNMRK1HiOCqjC4QF/OUDzH6ThTjb3wuDWT/kIZnQk=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\b77ayb31m4-yvkv6cxmvi.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/index-cef005e4.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\index-cef005e4.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6eq30fxxws", "Integrity": "omvWztzznuDV27+OOzaeMqGe1lRx1/J7kSryeYKIrdc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\index-cef005e4.js", "FileLength": 262, "LastWriteTime": "2025-07-29T02:57:01.9017783+00:00"}, "rquNMzE2Zc1gR+bkTDfuqwWs16PneEXNUkklnC5+ZLA=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\7fbmh72xh6-qujlxnzngr.gz", "SourceId": "HX.Experiment.Web.Client", "SourceType": "Computed", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/HX.Experiment.Web.Client#[.{fingerprint=qujlxnzngr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Web.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qlpw9uspm", "Integrity": "wQFWzQAXCZySbmPe2fa2b53IWufA4h5n5XC3+7h5YSM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\HX.Experiment.Web.Client.wasm", "FileLength": 3524, "LastWriteTime": "2025-08-05T10:55:08.5550686+00:00"}, "ToXmBqtuZDQgFZ54zeGYWANlFqz68ys/ttWPkbB2Rks=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\98s27ux9m1-n1s3lxfdbi.gz", "SourceId": "Masa.<PERSON>", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Masa.Blazor", "RelativePath": "js/chunks/tslib.es6-68144fbe.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\tslib.es6-68144fbe.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l6rnf2o1cb", "Integrity": "o5/ZN0UDwh2fA07Fek7EWFzg3MgrPI30+I5+atZXnzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\masa.blazor\\1.10.3\\staticwebassets\\js\\chunks\\tslib.es6-68144fbe.js", "FileLength": 669, "LastWriteTime": "2025-07-29T02:57:01.9037957+00:00"}, "dmbdmuwTFBZo134a2yE88Eg/Fn7HLuVPu5e6gAZ6IgY=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\34blflduah-vhh2jufqs3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.aliyun.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfry0ibibp", "Integrity": "YXftugZCMhIfHCBMODujtXIpw3wgk4M+NelcU37rZYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.aliyun.min.css", "FileLength": 71500, "LastWriteTime": "2025-07-28T05:52:10.4790032+00:00"}, "z1FmTEOJLlKoNkkKxiopO8OWSzdg//i/mUjrB71pcQ8=": {"Identity": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\vivduzjdd9-vhh2jufqs3.gz", "SourceId": "AntDesign", "SourceType": "Package", "ContentRoot": "D:\\Project\\HuiXin\\HX.HRV\\HX.Experiment\\HX.Experiment.Web.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AntDesign", "RelativePath": "css/ant-design-blazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfry0ibibp", "Integrity": "YXftugZCMhIfHCBMODujtXIpw3wgk4M+NelcU37rZYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\antdesign\\0.15.5\\staticwebassets\\css\\ant-design-blazor.min.css", "FileLength": 71500, "LastWriteTime": "2025-07-28T05:52:10.575901+00:00"}}, "CachedCopyCandidates": {}}