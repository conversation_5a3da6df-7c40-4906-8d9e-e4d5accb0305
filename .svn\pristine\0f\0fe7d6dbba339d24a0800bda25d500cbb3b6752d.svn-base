@inherits LayoutComponentBase
@implements IDisposable
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using UFU.CoreFX.Shared.Models
@using HX.HRV.Shared.Pages.Client.Components
@using HX.HRV.Shared.Services
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Utils
@using UFU.IoT.Models
@using Menu = UFU.CoreFX.Models.Menu
@inject IJSRuntime js
@inject DeviceStateService DeviceDataService
<MApp Style="background-color:#f3f9ff">
	<MSystemBar App Height="@("6rem")"
	            Style="background: url(/images/layout_header.png) , linear-gradient(to bottom right, #2979d4, #0e69d1); ">
		<HRVHeader Menus="Menus"/>
	</MSystemBar>
	@if (SubMenus != null && IsShowLeftNavgation) {
		<MNavigationDrawer App>
			<MList Routable Nav>
				@foreach (var item in SubMenus) {
					<MListItem IsActive="item.Active"
					           Href="@item.Url"
					           ActiveClass="primary--text">
						<MListItemIcon>
							<MIcon Style="line-height: 2.5rem;font-size: 1.25rem;height: 2.5rem">@item.Icon</MIcon>
						</MListItemIcon>
						<MListItemContent>
							<MListItemTitle Style="line-height: 2.5rem;font-size: 1.25rem;height: 2.5rem">@item.Name</MListItemTitle>
						</MListItemContent>
					</MListItem>
				}
			</MList>
		</MNavigationDrawer>
	}
	<MMain Style="padding-top:6rem ">
		<div style="height: calc(100vh - 7rem);">
			<ErrorBoundary>
				<ChildContent>
					<CascadingValue Value="DeviceList" Name="DeviceList">
						<CascadingValue Value="PopupService" Name="PopupService">
							@Body
						</CascadingValue>
					</CascadingValue>
				</ChildContent>
				<ErrorContent Context="exception">
					<UFU.CoreFX.Shared.Pages.Core.ErrorPage._500 ReturnUrl="/client/device-status"
					                                             Exception="exception"/>
				</ErrorContent>
			</ErrorBoundary>
		</div>
		<div id="bottom_version">
			<span style="opacity: 0.5" Cols="12">
				© @DateTime.Now.Year 汇心健康 @Configuration.GetSection("Version").Value
			</span>
		</div>
	</MMain>
</MApp>

@code {

	public void Dispose() {
		DeviceDataService.OnDeviceNumberChange -= OnDeviceNumberChange;
		cancellationToken.Cancel();
	}

	/// <summary>
	/// 路由数据
	/// </summary>
	[CascadingParameter]
	public AuthRouteData AuthRouteData { get; set; }

	[Inject] StateService _stateService { get; set; }
	private List<Menu> Menus { get; set; } = new();
	private List<Menu> SubMenus { get; set; } = new();
	private bool IsShowLeftNavgation { get; set; } = false;
	[Inject] IConfiguration Configuration { get; set; }
	private List<DeviceModel> DeviceList { get; set; } = new();
	[Inject] private IPopupService PopupService { get; set; }

	protected override void OnParametersSet() {
		if (AuthRouteData?.CurrentModule != null) {
			var module = AuthRouteData?.CurrentModule;
			Menus = module?.Menus ?? new List<Menu>();
			SetIsSelectedMenu();
			SubMenus = module?.Menus?.Where(m => m.Children?.Count > 0)
				?.SelectMany(m => m.Children)
				.ToList();
		}

		base.OnParametersSet();
	}

	protected override void OnInitialized() {
		DeviceDataService.OnDeviceNumberChange += OnDeviceNumberChange;
	}

	private void OnDeviceNumberChange() {
		InvokeAsync(StateHasChanged);
	}

	private void SetIsSelectedMenu() {
		var currentUri = new Uri(_stateService.NavigationManager.Uri);
		var path = currentUri.AbsolutePath.ToLower();
		foreach (var item in Menus) {
			item.Active = item.Url.Equals(path, StringComparison.OrdinalIgnoreCase)
			              || (item.Children?.Any(m =>
				              m.Url.Equals(path, StringComparison.OrdinalIgnoreCase)
				              || m.Children?.Any(n => n.Url.Equals(path, StringComparison.OrdinalIgnoreCase)) == true
			              ) ?? false);
		}

		var currentMenu = Menus?.FirstOrDefault(m => m.Active);
		if (currentMenu != null) {
			if (currentMenu.Children?.Count > 0) {
				foreach (var currentMenuChild in currentMenu.Children) {
					currentMenuChild.Active = currentMenuChild.Url.Equals(path, StringComparison.OrdinalIgnoreCase)
					                          || (currentMenuChild.Children?.Any(m =>
						                          m.Url.Equals(path, StringComparison.OrdinalIgnoreCase)) == true);
				}
			}

			IsShowLeftNavgation = currentMenu.Children?.Count > 0;
		} else {
			IsShowLeftNavgation = false;
		}
	}

	private CancellationTokenSource cancellationToken = new CancellationTokenSource();

	protected override async Task OnInitializedAsync() {
		PopupService.ShowProgressCircular();
		var objectRef = DotNetObjectReference.Create(this);
		await js.InvokeVoidAsync("InitHybridWebView", objectRef, "HandleReciveFromClient");
		_ = Task.Run(async () => {
			while (true) {
				try {
					await Task.Delay(3000);
					await js.InvokeAsync<object>("SendMessageToClient");
				}
				catch (Exception e) {
					Console.WriteLine("【SendMessageToClient error】" + e.Message);
				}

				if (cancellationToken.IsCancellationRequested) {
					break;
				}
			}
		}, cancellationToken.Token);
		await base.OnInitializedAsync();
		PopupService.HideProgressCircular();
	}


	private const string DeviceTypeId = "2407160100000001";


	private class RecivedDataFromClient {
		public string action { get; set; }
	}

	private class RecivedDataFromClient<T> : RecivedDataFromClient {
		public T data { get; set; }
	}


	[JSInvokable]
	public async Task HandleReciveFromClient(string json) {
		var data = JsonTool.Deserialize<RecivedDataFromClient>(json);
		switch (data.action) {
			case "getDeviceInfo":
				Console.WriteLine("[getDeviceInfo]:" + json);
				var jsonData = JsonTool.Deserialize<RecivedDataFromClient<List<DiskInfo>>>(json);
				var disks = jsonData.data ?? new List<DiskInfo>();

				foreach (var deviceStatusViewModel in DeviceDataService.DeviceDataList) {
					var oldIsUsbLinked = deviceStatusViewModel.IsUsbLinked;
					var disk = disks?.FirstOrDefault(m => m.ShowName.Equals(deviceStatusViewModel.Device.DeviceSN, StringComparison.InvariantCultureIgnoreCase));
					if (disk != null) {
						deviceStatusViewModel.IsUsbLinked = true;
						if (disk.IsParsing) {
							deviceStatusViewModel.DeviceStatus = EnumDeviceStatus.USB传输中;
							deviceStatusViewModel.Progress = disk.Progress;
							deviceStatusViewModel.NotifyPropertyChanged();
							
							
							
						}
					} else {
						deviceStatusViewModel.IsUsbLinked = false;
					}

					if (oldIsUsbLinked != deviceStatusViewModel.IsUsbLinked) {
						deviceStatusViewModel.NotifyPropertyChanged();
					}
				}

				var notExistDevices = disks?.Where(m => !DeviceDataService.DeviceDataList.Any(x => x.Device.DeviceSN.Equals(m.ShowName, StringComparison.InvariantCultureIgnoreCase)))?.ToList();
				if (notExistDevices?.Any() == true) {
					var maxName = 1;
					if (DeviceDataService?.DeviceDataList?.Any() == true) {
						maxName = DeviceDataService.DeviceDataList.Max(m => {
							if (int.TryParse(m.Device?.Name, out var num)) {
								return num;
							}

							return 0;
						});
					}

					foreach (var diskInfo in notExistDevices) {
						maxName++;
						DeviceModel newDevice = new DeviceModel() {
							DeviceSN = diskInfo.ShowName.ToLower(),
							ChipSN = diskInfo.ShowName.ToLower(),
							Name = maxName.ToString(),
							TypeId = DeviceTypeId,
						};
						var result = await _stateService.PostAsJsonAsync<DataModel<DeviceModel>>("api/v2/IoT/Devices/Add", newDevice);
						var device = result?.Data?.Data;
						if (device != null && DeviceDataService.DeviceDataList.All(m => !m.Device.DeviceSN.Equals(diskInfo.ShowName, StringComparison.InvariantCultureIgnoreCase))) {
							var deviceViewModel = new DeviceStatusViewModel() {
								Device = result?.Data?.Data,
								DeviceSN = result?.Data?.Data?.DeviceSN,
								Battery = 0,
								DeviceStatus = EnumDeviceStatus.离线,
							};
							DeviceDataService.DeviceDataList.Add(deviceViewModel);
							InvokeAsync(StateHasChanged);
						}
					}
				}

				break;
		}
	}

	public class DiskInfo {
		public string LogicalName { get; set; }
		public string Caption { get; set; }
		public string ShowName { get; set; }

		/// <summary>
		/// 解析中
		/// </summary>
		public bool IsParsing { get; set; }

		/// <summary>
		/// 解析进度
		/// </summary>
		public int Progress { get; set; }
	}

}