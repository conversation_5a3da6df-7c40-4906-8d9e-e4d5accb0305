﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<!--<Nullable>enable</Nullable>-->
		<ImplicitUsings>enable</ImplicitUsings>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
		<DefaultAppHostRuntimeIdentifier>win-x64</DefaultAppHostRuntimeIdentifier>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
	  <PackageReference Include="DeviceId" Version="6.7.0" />
	  <PackageReference Include="Makaretu.Dns.Multicast" Version="0.27.0" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.35" />
	  <PackageReference Include="Microsoft.Fast.Components.FluentUI" Version="3.7.8" />
	  <PackageReference Include="MQTTnet" Version="4.3.3.952" />
	  <PackageReference Include="PDFsharp" Version="6.1.1" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="wwwroot\css\hx.hrv.css">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\HX.StandtardFearmwork\HX.StandtardFearmwork.csproj" />
	  <ProjectReference Include="..\HX.HRV.Shared\HX.HRV.Shared.csproj" />
	  <ProjectReference Include="..\HX.HRV.Web\HX.HRV.Web.csproj" />
	  <ProjectReference Include="..\HX.HRV.SCI.Shared\HX.HRV.SCI.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <_ContentIncludedByDefault Remove="Pages\Client\Account\List.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Components\HRVCheckTimeButton.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Components\HRVHeader.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Components\HRVSourceGroupButton.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatus.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusCard.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusCardContent.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusCardHead.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\DeviceStatus\DeviceStatusUserInfoCard.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\DeviceStatusDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\ExportRecordDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\ModifyPasswordDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\PatientDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Dialog\SelectedDeviceDialog.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\PatientRecord\List.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\PatientRecord\RecordList.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Patient\List.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoring.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringCard.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringCardHead.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringLargeLineChart.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\RealTimeMonitoring\RealTimeMonitoringLineChart.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Report\PreviewReportDetail.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Report\ReportDetail.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\Report\ShowReportDetail.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\SystemConfig\DeviceList.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\SystemConfig\HxSystemConfig.razor" />
	  <_ContentIncludedByDefault Remove="Pages\Client\TestAlgorithms.razor" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\echarts.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\echarts.js.map" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\echarts.min.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\echarts5.5.1.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\echartsInterop.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\exportpdf.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\html2pdf.bundle.min.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\scripts\realtime-echart.js" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\hx.hrv.web.css" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\materialdesign\v7.1.96\css\materialdesignicons.min.css" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.map" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.eot" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.ttf" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.woff" />
	  <_ContentIncludedByDefault Remove="wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.woff2" />
	</ItemGroup>
</Project>
