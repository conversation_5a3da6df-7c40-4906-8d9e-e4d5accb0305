﻿using AntDesign;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using UFU.CoreFX.Models;
using UFU.CoreFX.Models.ViewModels;
using UFU.CoreFX.Shared.Services;
using UFU.IoT.Models;


namespace UFU.IoT.Shared.Pages.DeviceType;

public partial class Edit : ComponentBase
{
    [Parameter] [FromRoute] public string Id { get; set; }
    /// <summary>
    /// 是否复制
    /// </summary>
    [Parameter]
    [SupplyParameterFromQuery]
    public bool isCopy { get; set; }
    [Inject] private StateService StateService { get; set; }
    [Inject] MessageService Message { get; set; }
    private DataModel<DeviceTypeModel> _deviceTypeModel;
    private List<OrganModel> _organList = new();
    private List<TableKeyValue> _paraList = new();
    private const string BaseUrl = "/api/v2.0/IoT/";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (!string.IsNullOrEmpty(Id))
        {
            var res = await StateService.GetAsJsonAsync<DataModel<DeviceTypeModel>>(BaseUrl +
                $"DeviceTypes/GetDeviceTypeModelDetail", new Dictionary<string, string>() { { "id", Id } });
            if (res.Success)
            {
                _deviceTypeModel = res.Data;
                _paraList = res.Data.Data.ParaList
                    ?.Select((m, index) => new TableKeyValue()
                    {
                        Index = index,
                        Key = m.Key,
                        Value = m.Value
                    })
                    ?.ToList();
                _deviceTypeModel.Data.ImageList = res.Data.Data.ImageList ?? new List<UploadFileViewModel>();
            }

            if (isCopy)
            {
                Id = string.Empty;
                _deviceTypeModel.Id = null;
                _deviceTypeModel.Data.Id = null;
                _deviceTypeModel.Data.Name += "副本";
            }
        }
        else
        {
            _deviceTypeModel = new DataModel<DeviceTypeModel>
            {
                Data = new DeviceTypeModel
                {
                    ParaList = new Dictionary<string, string>(),
                    ImageList = new List<UploadFileViewModel>()
                }
            };
            _paraList = new List<TableKeyValue>();
        }

        await InitOrgansAsync();
    }


    /// <summary>
    /// 初始化组织机构
    /// </summary>
    private async Task InitOrgansAsync()
    {
        var result = await StateService.GetAsJsonAsync<List<OrganModel>>("/api/v2/Core/Organs/List?pageSize=999");
        if (result.Success && result.Data != null)
        {
            _organList = result.Data.ToList();
        }
    }


    /// <summary>
    /// 提交保存
    /// </summary>
    /// <returns></returns>
    private async Task HandleSubmit()
    {
        if (_paraList is { Count: > 0 })
        {
            _deviceTypeModel.Data.ParaList = _paraList.ToDictionary(p => p.Key, p => p.Value);
        }

        if (string.IsNullOrEmpty(Id))
        {
            //新增   
            var res = await StateService.PostAsJsonAsync<DataModel<DeviceTypeModel>>(BaseUrl + "DeviceTypes/Add",
                _deviceTypeModel,
                new Dictionary<string, string>()
                {
                    { "id", _deviceTypeModel.Id }
                });
            if (res.Success)
            {
                await Message.Success("保存成功");
                HandleCancel();
            }
            else
            {
                await Message.Error(res.Message);
            }
        }
        else
        {
            //编辑
            var res = await StateService.PostAsJsonAsync<DataModel<DeviceModel>>(
                BaseUrl + "DeviceTypes/Edit?id=" + _deviceTypeModel.Id,
                _deviceTypeModel);
            if (res.Success)
            {
                _ = Message.Success("保存成功");
                HandleCancel();
            }
        }
    }

    private void HandleCancel()
    {
        StateService.NavigationManager.NavigateTo("/IoT/DeviceType/List");
    }

    private class TableKeyValue
    {
        public int Index { get; set; }
        public string Key { get; set; }
        public string Value { get; set; }
    }
}