<!DOCTYPE html><html><head><meta charset=utf-8 /><meta name=renderer content=webkit /><meta name=viewport content="width=device-width, initial-scale=1.0,user-scalable=no" /><title>设备配网</title><style>body{margin:0;padding:0}.container{margin:0 auto;padding:0;background:#fff;max-width:500px;padding-top:37px}.choiceBtn{color:#fff;display:block;border-radius:4px;padding:10px 0;text-align:center;margin:20px 15px;border:1px solid #13a8e2;background:#13a8e2;text-decoration:none}.netList{list-style:none;margin:0;padding:0}.netList>li{padding:12px 0;position:relative;overflow:hidden;border-bottom:.5px solid #dcdcdc}.netName{width:80%;float:left;text-indent:20px}.wifi{width:20%;float:left;text-align:center}.dialogMask{position:fixed;z-index:100;left:0;right:0;top:0;bottom:0;display:none;background-color:rgba(150,150,150,.7)}.dialog{width:260px;height:160px;border:1px solid #eee;border-radius:5px;padding:15px 20px 20px;background-color:#fff;position:absolute;left:50%;top:20%;transform:translateX(-50%)}.dialog_hd,.dialog_bd,.dialog_ft{padding:10px 0;position:relative}.dialog_bd,.dialog_ft{text-align:center}.dialog_hd{color:#333;text-align:center;font-size:16px}.dialog_bd>input{width:240px;outline:none;border:1px solid #eee;border-radius:4px;padding:10px}.dialog_ft>input{width:49%;float:left;height:44px;border:1px solid #eee;border-radius:5px;background-color:#efefef;font-size:16px;color:#333}.dialog_ft>input.submit{color:#fff;margin-right:1%;background:#13a8e2;border-color:#13a8e2}.dislog_ft>input.cancel{margin-left:1%}.loading{position:fixed;left:0;right:0;top:0;bottom:0;z-index:1000;display:none;background-color:rgba(255,255,255,.3)}.spinBox{padding:18px;position:absolute;left:50%;top:50%;border-radius:5px;background-color:rgba(0,0,0,.8);transform:translate(-50%,-50%)}.spins{text-align:center;display:inline-block}.spin{display:block;width:40px;height:40px;border-radius:50%;border:4px solid #dcdcdc;border-bottom-color:transparent;animation:loading 1s infinite linear}@keyframes loading{from{transform:rotate(0deg);-webkit-transform:rotate(0deg)}to{transform:rotate(1turn);-webkit-transform:rotate(1turn)}}.tip{z-index:99;font-size:13px;padding:10px 15px;position:fixed;top:0;left:0;right:0;background-color:#f1f8fb}.tip>.info{color:#666}.tip>.error{color:#f00}</style></head><body><div class=container><div class=tip><span class=info>选取网络...</span> <span class=error></span></div><div class=loading><div class=spinBox><div class=spins><em class=spin></em></div></div></div><ul class=netList></ul><li id=demo style="display: none"><div class=netName></div><div class=wifi><svg viewBox="0 0 200 200" width=20 height=20><path d=M103,175l30.9-41.4c-8.6-6.4-19.3-10.3-30.9-10.3s-22.4,3.9-30.9,10.3L103,175 /><path d=M103,71.7c-23.2,0-44.7,7.7-61.9,20.7l15.5,20.7c12.9-9.8,28.9-15.5,46.4-15.5s33.6,5.7,46.4,15.5l15.5-20.7C147.6,79.5,126.2,71.7,103,71.7z /><path d=M103,20.1c-34.8,0-67.1,11.6-93,30.9l15.5,20.7C47,55.6,73.9,45.8,103,45.8s56,9.6,77.5,25.9L196,51.1C169.9,31.7,137.8,20.1,103,20.1 /></svg></div></li><a class=choiceBtn href=# onclick=getWifi()>重新扫描</a><div class=dialogMask><div class=dialog><div id=error></div><div class=dialog_hd>请输入“<span class=wifiName></span>”的密码</div><div class=dialog_bd><input type=hidden id=na /><input type=password id=ps placeholder=密码 /></div><div class=dialog_ft><input class=submit type=button value=确定 onclick=connectWifi() /><input class=cancel type=button value=取消 onclick=cancel() /></div></div></div></div><script>function $(n){return document.querySelector(n)}function cancel(){$(".dialogMask").style.display="none"}function getWifi(){$(".loading").style.display="block";var n=new XMLHttpRequest;n.open("GET","/scan",!0);n.send(null);n.onreadystatechange=function(){var t,e,r,i,f,u;if(n.readyState==4&&n.status==200){for(t=JSON.parse(n.response),$(".netList").innerHTML="",sessionStorage.setItem("wifiId",t.id),t.wifi.sort(function(n,t){return t.level-n.level}),e=$("#demo").innerHTML,r=0;r<t.wifi.length;r++){for(i=document.createElement("li"),i.onclick=selectWifi,i.innerHTML=e,i.setAttribute("data-val",JSON.stringify(t.wifi[r])),i.querySelector(".netName").innerText=t.wifi[r].ssid,f=i.querySelectorAll("path"),u=0;u<f.length;u++)f[u].setAttribute("fill",t.wifi[r].level>=u+2?"#1E1E1E":"#BBBBBB");$(".netList").appendChild(i)}$(".info").innerText="选取网络...";$(".error").innerText="";setTimeout(function(){$(".loading").style.display="none"},300)}else $(".info").innerText="",$(".error").innerText="获取失败，请重新扫描",setTimeout(function(){$(".loading").style.display="none"},300)}}function connectWifi(){var t,i,r,n;count++;count>10&&!success?(setTimeout(function(){$(".loading").style.display="none"},500),$(".info").innerText="",$(".error").innerText="连接超时，请重试！"):(t=$("#ps").value,i=$("#na").value,$(".loading").style.display="block",$(".dialogMask").style.display="none",r="/conn?ssid="+i+"&password="+t,n=new XMLHttpRequest,n.open("GET",r,!0),n.send(),n.onreadystatechange=function(){if(n.readyState==4&&n.status==200){var t=JSON.parse(n.response);t.status==1?(isOK=!0,path=t.url,success=!0):alginConnect()}else alginConnect()})}function alginConnect(){!success&&count<=10&&setTimeout(connectWifi,1e3)}var isOK=!1,path=null,count,success,selectWifi;getWifi();count=0;success=!1;selectWifi=function(){count=0;$("#ps").value="";$(".error").innerText="";$(".info").innerText="选取网络...";var n=JSON.parse(this.getAttribute("data-val"));n.type=="None"?($("#na").value=n.ssid,querySelector(".loading").style.display="block"):($("#na").value=n.ssid,$(".wifiName").innerText=n.ssid,$(".dialogMask").style.display="block")},function(){setInterval(function(){if(isOK){var n=new XMLHttpRequest;n.open("GET","/",!0);n.send();n.onreadystatechange=function(){if(n.readyState==4&&n.status==200){var t=n.response.indexOf("WiFiConfig");(t>50||t==-1)&&(location.href=path)}}}},1e3)}()</script></body></html>