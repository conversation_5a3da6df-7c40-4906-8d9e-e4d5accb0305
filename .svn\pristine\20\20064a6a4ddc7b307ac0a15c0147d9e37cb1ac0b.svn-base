﻿@using HX.HRV.Shared.Pages.Client.RealTimeMonitoring
@using Masa.Blazor
@inherits HX.HRV.Shared.Pages.Client.RealTimeMonitoring.RealTimeMonitoringChartRow
<MRow Dense Justify="JustifyTypes.Center" Style="height: 100%;background-color: #c2d9f3;">
	<MCol Cols="3" Style="height: 50%;">
		@*  <RealTimeMonitoringLineChart DeviceStatusData="@DeviceStatusData" DataItemExpression="e=>e.BMPDatas" Color="#febaba" Name="HR" Unit="bpm" Take="60"  />*@
		<div style="height:100%; background-color:#febaba;border-radius:  10px 0 0 10px "
		     class="d-flex flex-column align-center justify-space-between ">
			<div class="text-center" style="font-size: 1rem;height: 100%;align-content: center;">HR</div>
			<div class="text-center"
			     style="font-size:0.875rem;width: 100%;align-content: center;text-align: center">@DeviceStatusData.BMPDatas?.LastOrDefault()</div>
			<div class="text-center" style="font-size: 0.650rem;height: 100%;align-content: center;">bpm</div>
		</div>
	</MCol>
	<MCol Cols="9" Style="height: 50%;">
		<RealTimeMonitoringLineChart TItem="uint" DeviceStatusData="@DeviceStatusData" IsHiddenValue="true"
		                             IsHiddenXAxis="true"
		                             DataItemExpression="e=>e.OrgPPGData" Take="2500" Color="#f9dba3" Name="PPG"/>
	</MCol>
	<MCol Cols="3" Style="height: 50%;">
		<div style="height:100%; background-color:#97d7b8;border-radius:  10px 0 0 10px "
		     class="d-flex flex-column align-center justify-space-between ">
			<div class="text-center" style="font-size: 1rem;height: 100%;align-content: center;">SpO2</div>
			<div class="text-center"
			     style="font-size:0.875rem;width: 100%;align-content: center;text-align: center">@DeviceStatusData.SPO2Data?.LastOrDefault()</div>
			<div class="text-center" style="font-size: 0.650rem;height: 100%;align-content: center;">%</div>
		</div>
		@*        <RealTimeMonitoringLineChart  DeviceStatusData="@DeviceStatusData" DataItemExpression="e=>e.SPO2Data" Color="#97d7b8" Name="SpO2" Unit="%" Take="60" /> *@
	</MCol>
	<MCol Cols="9" Style="height: 50%;">
		<RealTimeMonitoringLineChart IsHiddenValue="true" DeviceStatusData="@DeviceStatusData"
		                             DataItemExpression="e=>e.EDAs" Color="#9ac5fa" Name="GSR" Unit="μS" Take="60"/>
	</MCol>

</MRow>

@code {
}