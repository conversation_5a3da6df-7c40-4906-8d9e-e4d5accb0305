﻿@page "/IoT/command-config/List"

@using UFU.IoT.Models;
@using UFU.IoT.Shared.Models
@attribute [Permission("命令配置", IsMenu = true)]
<Form Model="@_queryModel"
      OnFinish="OnFinish"
      Layout="@FormLayout.Inline">
    <FormItem Label="命令名称">
        <Input @bind-Value="@context.Name"/>
    </FormItem>
    <FormItem>
        <Button Type="@ButtonType.Primary" HtmlType="submit">
            查询
        </Button>
        <Button Type="@ButtonType.Primary" HtmlType="button" OnClick="HandleAddClick">
            添加
        </Button>
    </FormItem>
</Form>
<Table TItem="CommandConfigModel"
       Total="_total"
       DataSource="@CommandConfigModels"
       Loading="_Loading"
       OnChange="HandleTableChange"
       @bind-SelectedRows="selectedRows"
       @bind-PageIndex="_pageIndex"
       @bind-PageSize="_pageSize">
    <Selection Key="@(context.Id)"/>
    <PropertyColumn Property="c => c.Id" Title="命令ID" Filterable="true"/>
    <PropertyColumn Property="c => c.Name" Title="命令名称"/>
    <PropertyColumn Property="c => c.Command" Title="命令内容"/>
    <ActionColumn Title="操作">
        <Space>
            <SpaceItem>
                <NavLink class="ant-btn ant-btn-primary"
                         target="_blank"
                         href="@($"/IoT/command-config/edit?Id={context.Id}")">
                    编辑
                </NavLink>
            </SpaceItem>
        </Space>
    </ActionColumn>
</Table>

@code {

    private void HandleAddClick()
    {
        StateService.NavigationManager.NavigateTo("/IoT/command-config/edit");
    }

}