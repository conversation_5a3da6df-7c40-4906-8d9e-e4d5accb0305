<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <meta name='renderer' content='webkit' />
    <meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>&#x8BBE;&#x5907;&#x914D;&#x7F51;</title>
    <style>
        body {
            padding: 0px;
            margin: 0px;
        }

        .section {
            max-width: 500px;
            margin: 0 auto;
            position: relative;
            padding: 0px;
        }

        .d {
            color: #666;
            font-size: 13px;
            padding: 10px 15px;
            background-color: #f1f8fb;
        }

        .tipInfo {
            padding: 20px 15px 0px;
        }

        #e {
            max-width: 500px;
            margin: 0 auto;
            padding: 0px 15px;
        }

        .j {
            display: block;
            border-radius: 4px;
            padding: 10px 0;
            text-align: center;
            margin: 20px 15px 0px;
            border: 1px solid #13a8e2;
            background-color: #13a8e2;
            color: #fff;
            text-decoration: none;
        }

        .f {
            padding: 10px 0;
            position: relative;
            overflow: hidden;
            border-bottom: .5px solid #dcdcdc;
        }

        .g {
            width: 80%;
            float: left;
        }

        .h {
            width: 20%;
            float: left;
            text-align: right;
        }

        #k {
            position: fixed;
            z-index: 100;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            display: none;
            background-color: rgba(150,150,150,0.7);
        }

        #k > div {
            width: 260px;
            height: 180px;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 30px 20px 20px;
            background-color: #fff;
            position: absolute;
            left: 50%;
            top: 20%;
            transform: translateX(-50%);
            -webkit-transform: translateX(-50%);
        }

        #error {
            position: absolute;
            right: 20px;
            left: 20px;
            top: 10px;
            color: #f00;
            font-size: 12px;
            text-align: center;
        }

        .l {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .i {
            text-align: center;
            padding: 10px 0;
        }

        .i input {
            width: 240px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
        }

        .o {
            text-align: center;
            padding: 10px 0;
        }

        .o input {
            width: 125px;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
            background-color: #efefef;
            font-size: 16px;
            color: #333;
        }

        .o input:first-child {
            border: 1px solid #13a8e2;
            background-color: #13a8e2;
            color: #fff;
        }

        .a {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: none;
            z-index: 101;
            background: rgba(0, 0, 0, .5);
        }

        .b {
            width: 80px;
            height: 40px;
            position: absolute;
            left: 50%;
            top: 50%;
            margin: -40px 0 0 -40px;
        }

        .b em {
            display: inline-block;
            width: 8px;
            height: 100%;
            border-radius: 4px;
            background: lightgreen;
            animation: lo 1s ease infinite;
        }

        .c {
            color: #fff;
            position: relative;
            bottom: -15px;
        }

        @keyframes lo {
            0%,100% {
                height: 40px;
                background: lightgreen;
            }

            50% {
                height: 70px;
                margin: -15px 0;
                background: lightblue;
            }
        }

        em.aa {
            animation-delay: 0.2s;
        }

        em.ab {
            animation-delay: 0.4s;
        }

        em.ac {
            animation-delay: 0.6s;
        }

        em.ad {
            animation-delay: 0.8s;
        }

        .st0 {
            fill: #1E1E1E;
        }

        .st1 {
            fill: #BBBBBB;
        }
    </style>
</head>
<body>
    <div class='section'>
        <div id='loading' class='a'>
            <div class='b'>
                <em></em>
                <em class='aa'></em>
                <em class='ab'></em>
                <em class='ac'></em>
                <em class='ad'></em>
                <p class='c'>连接中...</p>
            </div>
        </div>
        <div class='d'>选取网络...</div>
        <div id='e'></div>
        <a class='j' onclick='getData()'>重新扫描</a>
        <div id='k'>
            <div>
                <div id='error'></div>
                <div class='l'>
                    <label>WiFi名称：</label>
                    <label id='text'></label>
                </div>
                <div class='i'>
                    <input type='hidden' id='na' />
                    <input type='hidden' id='id'>
                    <input type='password' id='ps' placeholder='请输入wifi密码' />
                </div>
                <div class='o'>
                    <input type='button' value='确定' onclick='connect()' />
                    <input type='button' value='取消' onclick='cancel()' />
                </div>
            </div>
        </div>
    </div>
    <script>
        var backHref = '';
        var result = null;
        function $(id) {
            return document.getElementById(id);
        }
        function getData() {
            ajax({
                url: '/scan',
                success: function (res) {
                    window.localStorage.setItem('id', res.id);
                    if (res.wifi) {
                        result = res.wifi;
                        var htmls = '', level1, level2, level3;
                        res.wifi.sort(function (x, y) { return y.level - x.level }).forEach(function (item, idx) {
                            level1 = 'st1', level2 = 'st1', level3 = 'st1';
                            if (item.level == 2) {
                                level1 = 'st0';
                            } else if (item.level == 3) {
                                level1 = 'st0', level2 = 'st0';
                            } else if (item.level == 4) {
                                level1 = 'st0', level2 = 'st0', level3 = 'st0';
                            }
                            htmls += '<div class=\'f\' onclick=\'s(' + idx + ')\'><div class=\'g\'>' + item.ssid + '</div><div class=\'h\'>' +
                                '<svg version=\'1.1\' width=\'15\' height=\'15\' x=\'0px\' y=\'0px\' viewBox=\'83.6 148.1 8.9 8.9\' style=\'enable-background:new 83.6 148.1 8.9 8.9;\' xml:space=\'preserve\'>' +
                                '<path class=' + level3 + ' d=\'M88.1,150.4c1.4,0,2.8,0.6,3.8,1.5l0.7-0.8c-1.2-1.1-2.8-1.8-4.5-1.8c-1.7,0-3.3,0.7-4.5,1.8l0.7,0.8C85.3,151,86.6,150.4,88.1,150.4z\'/>' +
                                '<path class=' + level2 + ' d=\'M88,152.7c0.9,0,1.7,0.3,2.3,0.9l0.7-0.8c-0.8-0.7-1.9-1.2-3-1.2c-1.1,0-2.2,0.5-3,1.2l0.7,0.8C86.4,153.1,87.2,152.7,88,152.7z\'/>' +
                                '<path class=' + level1 + ' d=\'M88,153.9c-0.6,0-1.1,0.2-1.5,0.6l1.5,1.7l1.5-1.7C89.1,154.1,88.6,153.9,88,153.9z\'/>' +
                                '</svg></div></div>';

                        });
                        $('e').innerHTML = htmls;
                    } else {
                        $('e').innerHTML = '暂无可用网络';
                    }
                }
            })
        };
        getData();
        function s(idx) {
            if (result[idx].type == 'None') {
                var id = $('id').value;
                var name = $('na').value;
                $('loading').style.display = 'block';
                cb({
                    bssid: id,
                    ssid: name
                });
            } else {
                $('text').innerHTML = result[idx].ssid;
                $('na').value = result[idx].ssid;
                $('id').value = result[idx].bssid;
                $('k').style.display = 'block';
            }
        }
        function cancel() {
            $('k').style.display = 'none';
        }
        function connect() {
            var ps = $('ps').value;
            if (ps !== '') {
                var id = $('id').value;
                var name = $('na').value;
                $('loading').style.display = 'block';
                cb({
                    bssid: id,
                    ssid: name,
                    password: ps
                });
            }
        };
        function cb(data) {
            ajax({
                url: '/conn',
                data: data,
                success: function (res) {
                    if (res.error == 0) {
                        setTimeout(function () { location.href = res.url; }, 10000);  
                    } else {
                        $('error').innerText = res.msg;
                    }
                    $('loading').style.display = 'none';
                },
                error: function () {
                    cb(data);
                }
            })
        }
        function ajax(opt) {//封装原生ajax

            var xhr = new XMLHttpRequest();

            xhr.opt = opt;
            if (!opt.type) opt.type = 'GET';
            if (opt.type == 'GET') {
                if (opt.data) {
                    if (opt.url.indexOf('?') == -1) {
                        opt.url += '?';
                    }
                    opt.url += setParames(opt.data);
                }
                xhr.open('GET', opt.url, true);
                xhr.send();
            } else {
                xhr.open('POST', opt.url, true);
                if (opt.data) {
                    xhr.send(setParames(opt.data));
                } else {
                    xhr.send(null);
                }
            }

            xhr.onreadystatechange = function () {
                if (xhr.readyState == 4) {
                    if (xhr.status == 200) {
                        xhr.opt.success(JSON.parse(xhr.response));
                    } else {
                        if (opt.error) {
                            opt.error();
                        }
                    }
                }
            }
            if (opt.error) {
                xhr.onerror = opt.error;
            }
        }
        function setParames(data) {//给URL地址设置参数
            var arr = [];
            for (var item in data) {
                arr.push(item + '=' + data[item]);
            }
            return arr.join('&');
        }
    </script>
</body>
</html>