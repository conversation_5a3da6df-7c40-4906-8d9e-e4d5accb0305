﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace UFU.IoT.Models
{
    /// <summary>
    /// 设备登录Token
    /// </summary>
    [DataEntity("2307180100000003")]
    public class DeviceTokenModel
    {
        /// <summary>
        /// 授权编号
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 设备编号
        /// </summary>
        public string DeviceId { get; set; }
        /// <summary>
        /// 登录凭证
        /// </summary>
        public string Token { get; set; }
        /// <summary>
        /// 到期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }       
    }
}
