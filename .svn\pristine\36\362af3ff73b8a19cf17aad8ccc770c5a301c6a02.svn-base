﻿@page "/client/SCI/DataPlayback/{id}"
@attribute [Permission("数据回放", AllowAnonymous = true)]
@using System.Text
@using HX.Base.Shared.Components
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Models
@using UFU.IoT.Shared.Models
@if (!string.IsNullOrEmpty(FileContent))
{
    <DocumentDetail Height="40" FileContent="@FileContent"></DocumentDetail>
}
@code {
    [Parameter] public string id { get; set; }
    private PatientRecordModel patientRecordModel = new PatientRecordModel();
    private string FileContent { get; set; }
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var res = await InternalHttpClientService.GetPatientRecordModelDetailById(id);
        patientRecordModel = res.Data;
        var filePath =Path.Combine(patientRecordModel.GetRecordDirectoryPath(), $"{HXDataType.PPG}.csv");
        if (File.Exists(filePath))
        {
             FileContent =await File.ReadAllTextAsync(filePath, Encoding.UTF8);
        }
    }
}