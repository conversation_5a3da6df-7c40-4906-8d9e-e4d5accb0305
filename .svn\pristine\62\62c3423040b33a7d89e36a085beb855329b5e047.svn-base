using UFU.CoreFX;
using UFU.CoreFX.Shared;

// using UFU.IoT.Services;

BlazorApp.AppType = typeof(UFU.CoreFX.Shared.Pages.App);
BlazorApp.AppAssembly = typeof(HX.Experiment.Web.Service.Module).Assembly;
BlazorApp.DefaultLayout = typeof(UFU.CoreFX.Shared.Pages.DefaultLayout);
new HxApp().Start(args);

/// <summary>
/// 
/// </summary>
public class HxApp : CoreApp
{
    
    public static IServiceProvider Services{ get; set; }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="builder"></param>
    public override void Start(WebApplicationBuilder builder)
    {
        // var  type = typeof(UFU.IoT.IoTModule);
        this.ConfigureBuilder(builder);
        WebApplication app = builder.Build();
        app.UseBlazorFrameworkFiles();
        this.ConfigureApp(app);
        Services = app.Services;
        app.UseWebAssemblyDebugging();
        app.Run();
    }
}