﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<EnableMsixTooling>true</EnableMsixTooling>
		<TargetFrameworks>net9.0-windows10.0.26100.0;net9.0-windows10.0.22000.0;net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->
		<OutputType>WinExe</OutputType>
		<RootNamespace>HX.HRV.MAUI</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>HX.HRV.MAUI</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.hx.hrv.maui</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFrameworks)')) == 'windows'"></SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFrameworks)')) == 'windows'"></TargetPlatformMinVersion>
		<ApplicationIcon>Resources\AppIcon\logo.ico</ApplicationIcon>
	</PropertyGroup>
	<PropertyGroup>
		<SelfContained>true</SelfContained>
	</PropertyGroup>
	<PropertyGroup>
		<IncludeAllContentForSelfExtract>true</IncludeAllContentForSelfExtract>
		<NuGetAuditMode>Direct</NuGetAuditMode>
	</PropertyGroup>
	
	<ItemGroup>
	  <Compile Remove="Raw\**" />
	  <EmbeddedResource Remove="Raw\**" />
	  <MauiCss Remove="Raw\**" />
	  <MauiXaml Remove="Raw\**" />
	  <None Remove="Raw\**" />
	  <None Update="License.txt">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Makaretu.Dns.Multicast" Version="0.27.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.2" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
		<PackageReference Include="Microsoft.WindowsAppSDK" Version="1.7.250310001" />
		<PackageReference Include="System.IO.Ports" Version="9.0.3" />
		<PackageReference Include="System.Management" Version="9.0.2" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.250108002\buildTransitive\..\include\WindowsAppSDK-VersionInfo.cs" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.250108002\buildTransitive\..\include\DeploymentManagerAutoInitializer.cs" />
	</ItemGroup>

	<ItemGroup>
	  <EditorConfigFiles Include="appsettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EditorConfigFiles>
	</ItemGroup>

	<ItemGroup>
		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Resources\Styles\Colors.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Resources\Styles\Styles.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\HX.Encrypt\HX.Encrypt.csproj" />
	</ItemGroup>

</Project>
