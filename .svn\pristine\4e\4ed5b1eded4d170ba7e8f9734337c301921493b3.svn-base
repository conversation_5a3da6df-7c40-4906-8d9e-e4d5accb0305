using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UFU.CoreFX.Models;
using UFU.IoT.Models;

namespace HX.HRV.Shared.Models
{
    /// <summary>
    /// 检测记录
    /// </summary>
    [DataEntity("2408192400000006")]
    public class PatientRecordModel
    {
        /// <summary>
        /// 没有生成Emotion数据
        /// </summary>
        public bool? IsNotEmotion { get; set; }
        public bool IsNotBuildPdf { get; set; }
        public int PpgRate { get;set; }
        /// <summary>
        /// 检测记录ID
        /// </summary>
        public string Id { get; set; }
        
        public int RecordId { get; set; }
        /// <summary>
        /// 是否生成报告数据
        /// </summary>
        public BuildReportStatus BuildReportStatus { get; set; }
        public string BuildReportStatusName => "";
        /// <summary>
        /// 患者ID
        /// </summary>
        [Required]
        public string PatientId { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required]
        public string DeviceId { get; set; }
        /// <summary>
        /// 检测时长（分钟）
        /// </summary>
        public int CheckTime { get; set; }
        /// <summary>
        /// 检测日期
        /// </summary>
        public DateTime CollectStartTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime CollectEndTime { get; set; }
        /// <summary>
        /// 检测状态
        /// </summary>
        public EnumPatientCheckStatus EnumPatientCheckStatus { get; set; }
        /// <summary>
        ///  患者
        /// </summary>
        public PatientModel Patient { get; set; }
        /// <summary>
        /// 检测编号
        /// </summary>
        public string RecordCode { get; set; }
        /// <summary>
        /// 打印次数
        /// </summary>
        public int PrintCount { get; set; }
        public  string GetRecordDirectoryPath()
        {
            var binPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin");
            if (!Directory.Exists(binPath))
            {
                Directory.CreateDirectory(binPath);
            }
            var year = CollectStartTime.Year;
            var month = CollectStartTime.Month;
            var day = CollectStartTime.Day;
            var dir = Path.Combine(binPath, $"{year}", $"{month}-{day}",Id);
            return dir;
        }
        /// <summary>
        /// 统计数据
        /// </summary>
        public ReportDataStatistics Statistics { get; set; }
        public DateTime DeviceStartCheckTime { get; set; }

        public EmotionDataModel Emotion { get; set; }
        /// <summary>
        /// 文件上传时保证唯一
        /// </summary>
        public string UniqueId { get; set; }

		[NotMapped]
        public DeviceModel Device { get; set; }

    }       
    
    
    public class EmotionDataModel
    {
	    /// <summary>
	    /// 结果0代表健康，1代表抑郁
	    /// </summary>
	    public bool DepressPrediction { get; set; }
	    /// <summary>
	    /// 
	    /// </summary>
	    public double DepressConfidence { get; set; }
        /// <summary>
        /// 0/1/2代表无/轻度/中度，
        /// </summary>
        public int DepressLevelPrediction { get; set; }
        /// <summary>
        /// 置信概率0到100分，
        /// </summary>
        public double DepressLevelConfidence { get; set; }
  
        
        
        
        public int DepressScore {
	        get{
		        return DepressLevelPrediction switch
		        {
			        0 => (int)(DepressLevelConfidence / 100 * 30),
			        1 => (int)(DepressLevelConfidence / 100 * (100 - 30)) + 30,
			        2 => (int)(DepressLevelConfidence / 100 * (100 - 70)) + 70,
			        _ => 0
		        };
	        }
        }
        
        
        /// <summary>
        ///     情绪效价
        /// </summary>
        public double ValenceScore { get; set; }
        public int ValenceScoreType { get; set; }
        /// <summary>
        ///     情绪唤醒度
        /// </summary>
        public double ArousalScore { get; set; }
        public int ArousalScoreType { get; set; }
	}

	/// <summary>
	///  检测状态
	/// </summary>
	public enum  EnumPatientCheckStatus
    {
        /// <summary>
        /// 未知/初始
        /// </summary>
        UnKnown = 0,
        /// <summary>
        /// 已完成
        /// </summary>
        Checked = 1,
        /// <summary>
        ///  检查中
        /// </summary>
        Checking = 2,
        /// <summary>
        /// 准备检测
        /// </summary>
        Readying = 3,
        
        /// <summary>
        ///  已停止
        /// </summary>
        Stopped = 4
    }
    
    
    public enum BuildReportStatus
    {
        /// <summary>
        /// 未知
        /// </summary>
        [Display(Name = "待生成")]
        UnKnown = 0,
        /// <summary>
        /// 生成完成
        /// </summary>
        [Display(Name = "已生成")]
        Completed =1,
        /// <summary>
        /// 生成中
        /// </summary>
        [Display(Name = "生成中")]
        Building = 2,
        /// <summary>
        /// 未生成
        /// </summary>
        [Display(Name = "未生成")]
        Failed = 3,
        /// <summary>
        /// 超时
        /// </summary>
        [Display(Name = "超时")]
        Timeout = 4,
        /// <summary>
        /// 不具备生成条件
        /// </summary>
        [Display(Name = "数据无效")]
        NoCondition = 5,
		/// <summary>
		/// 不具备生成条件
		/// </summary>
		[Display(Name = "传输中")]
		IsLinked = 6,

	}
}