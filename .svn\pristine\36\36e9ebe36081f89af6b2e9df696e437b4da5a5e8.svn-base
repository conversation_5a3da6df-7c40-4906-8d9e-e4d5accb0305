﻿@using HX.HRV.Shared.Models.ViewModel
<MRow Dense Justify="JustifyTypes.Center" Style="height: 100%">
    <MCol Cols="12" Style="height: 50%;">
        <RealTimeMonitoringLineChart DeviceStatusData="@DeviceStatusData" DataItemExpression="e => e.BMPDatas" Color="#febaba" Name="HR" Unit="bpm" Take="60"/>
    </MCol>
    <MCol Cols="12" Style="height: 50%;">
        <RealTimeMonitoringLineChart DeviceStatusData="@DeviceStatusData" IsHiddenValue="true" IsHiddenXAxis="true" DataItemExpression="e => e.OrgPPGData" Take="2500" Color="#f9dba3" Name="PPG"/>
    </MCol>
</MRow>
@code {
    [Parameter] public DeviceStatusViewModel DeviceStatusData { get; set; }

}