﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Permission;
using UFU.IoT.Models;

namespace UFU.IoT.Areas.IoT.Controllers.V1
{
    /// <summary>
    /// 模板管理
    /// </summary>
    [Area("IoT")]
    [EnableCors("any")]
    public class TemplateController : Controller
    {
        private readonly DataRepository _context;

        /// <summary>
        /// 模板管理
        /// </summary>
        /// <param name="context"></param>
        public TemplateController(DataRepository context)
        {
            _context = context;
        }

        /// <summary>
        /// 模板管理接口/解压
        /// </summary>
        /// <param name="path">模板路径</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("模板管理接口/解压", IsAPI = true)]
        public IActionResult Unzip(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                ModelState.AddModelError("", "文件路径不能为空");
                return BadRequest(ModelState);
            }

            var file = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            if (!file.Exists)
            {
                ModelState.AddModelError("", "文件不存在");
                return BadRequest(ModelState);
            }
            if (".zip" != Path.GetExtension(file.Name)?.ToLower())
            {
                CoreFX.Utils.FileSystem.Delete(file.PhysicalPath);
                ModelState.AddModelError("", "只能为zip格式文件");
                return BadRequest(ModelState);
            }

            var destinationDirectory = Path.Combine(CoreFX.Utils.Env.HostEnvironment.WebRootPath, "temp", Guid.NewGuid().ToString("N"));
            try
            {
                CoreFX.Utils.Compression.UnZip(file.PhysicalPath, destinationDirectory);
                CoreFX.Utils.FileSystem.Delete(file.PhysicalPath);
            }
            catch
            {
                CoreFX.Utils.FileSystem.Delete(file.PhysicalPath);
                CoreFX.Utils.FileSystem.Delete(destinationDirectory);
                ModelState.AddModelError("", "文件解压失败");
                return BadRequest(ModelState);
            }


            DirectoryInfo directoryInfo = new DirectoryInfo(destinationDirectory);
            var tempFile = directoryInfo.GetFiles().Where(m => m.Name.ToLower() == "index.html").FirstOrDefault();
            if (tempFile == null)
            {
                foreach (var item in directoryInfo.GetDirectories())
                {
                    tempFile = item.GetFiles().Where(m => m.Name.ToLower() == "index.html").FirstOrDefault();
                    if (tempFile != null)
                    {
                        break;
                    }
                }
            }

            if (tempFile == null)
            {
                ModelState.AddModelError("", "未找到模板入口文件（index.html）");
                return BadRequest(ModelState);
            }

            var url = tempFile.FullName.TrimStart(CoreFX.Utils.Env.HostEnvironment.WebRootPath).Replace('\\', '/');
            return Ok(new { url });
        }

        /// <summary>
        /// 模板管理接口/更新设备模板
        /// </summary>
        /// <param name="id">设备类型编号</param>
        /// <param name="path">模板路径</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]/{id?}")]
        [Permission("模板管理接口/更新设备模板", IsAPI = true)]
        public async Task<IActionResult> UpdateDevice(string id, string path)
        {
            var file = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            if (!file.Exists)
            {
                ModelState.AddModelError("", "模板文件不存在");
                return BadRequest(ModelState);
            }

            var dir = Path.GetDirectoryName(file.PhysicalPath);
            if ("index.html" != file.Name)
            {
                CoreFX.Utils.FileSystem.Delete(dir);
                ModelState.AddModelError("", "模板文件只能为index.html");
                return BadRequest(ModelState);
            }

            var deviceType = await _context.Query<DeviceTypeModel>().FirstOrDefaultAsync(m => m.Id == id);
            if (deviceType == null)
            {
                ModelState.AddModelError("", "设备类型不存在！");
                return BadRequest(ModelState);
            }

            var dstDir = Path.Combine(CoreFX.Utils.Env.HostEnvironment.WebRootPath, "IoT", "Devices", deviceType.Id, DateTime.Now.ToString("yyyyMMddHHmmss"));
            CoreFX.Utils.FileSystem.Move(dir, dstDir);

            var url = Path.Combine(dstDir, "index.html").TrimStart(CoreFX.Utils.Env.HostEnvironment.WebRootPath).Replace('\\', '/');
            var old = deviceType.Data.Template;
            deviceType.Data.Template = url;
            deviceType.Data.UpdateTime = DateTime.Now;
            deviceType.Data.Version = Guid.NewGuid().ToString();
            deviceType.UpdateTime = deviceType.Data.UpdateTime;
            deviceType.Version = deviceType.Data.Version;
            _context.Update(deviceType);
            await _context.SaveChangesAsync();

            //暂不删除
            if (!string.IsNullOrWhiteSpace(old))
            {
                var oldFile = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            }

            return Ok(new { url });
        }

        /// <summary>
        /// 模板管理接口/更新场地模板
        /// </summary>
        /// <param name="id">场地编号</param>
        /// <param name="path">模板路径</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]/{id?}")]
        [Permission("模板管理接口/更新场地模板", IsAPI = true)]
        public async Task<IActionResult> UpdatePlace(string id, string path)
        {
            var file = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            if (!file.Exists)
            {
                ModelState.AddModelError("", "模板文件不存在");
                return BadRequest(ModelState);
            }

            var dir = Path.GetDirectoryName(file.PhysicalPath);
            if ("index.html" != file.Name)
            {
                CoreFX.Utils.FileSystem.Delete(dir);
                ModelState.AddModelError("", "模板文件只能为index.html");
                return BadRequest(ModelState);
            }

            var place = await _context.Query<PlaceModel>().FirstOrDefaultAsync(m => m.Id == id);
            if (place == null)
            {
                ModelState.AddModelError("", "场地不存在！");
                return BadRequest(ModelState);
            }

            var dstDir = Path.Combine(CoreFX.Utils.Env.HostEnvironment.WebRootPath, "IoT", "Places", place.Id, DateTime.Now.ToString("yyyyMMddHHmmss"));
            CoreFX.Utils.FileSystem.Move(dir, dstDir);

            var url = Path.Combine(dstDir, "index.html").TrimStart(CoreFX.Utils.Env.HostEnvironment.WebRootPath).Replace('\\', '/');
            var old = place.Data.Template;
            place.Data.Template = url;
            place.Data.UpdateTime = DateTime.Now;
            place.Data.Version = Guid.NewGuid().ToString();
            place.UpdateTime = place.Data.UpdateTime;
            place.Version = place.Data.Version;
            _context.Update(place);
            await _context.SaveChangesAsync();

            //暂不删除
            if (!string.IsNullOrWhiteSpace(old))
            {
                var oldFile = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            }

            return Ok(new { url });
        }

        /// <summary>
        /// 模板管理接口/更新设备默认模板
        /// </summary>
        /// <param name="path">模板路径</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("模板管理接口/更新设备默认模板", IsAPI = true)]
        public IActionResult UpdateDeviceDefault(string path)
        {
            var file = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            if (!file.Exists)
            {
                ModelState.AddModelError("", "模板文件不存在");
                return BadRequest(ModelState);
            }

            var dir = Path.GetDirectoryName(file.PhysicalPath);
            if ("index.html" != file.Name)
            {
                CoreFX.Utils.FileSystem.Delete(dir);
                ModelState.AddModelError("", "模板文件只能为index.html");
                return BadRequest(ModelState);
            }

            var dstDir = Path.Combine(CoreFX.Utils.Env.HostEnvironment.WebRootPath, "IoT", "Devices", "Default");
            CoreFX.Utils.FileSystem.Delete(dstDir);
            CoreFX.Utils.FileSystem.Move(dir, dstDir);
            var url = Path.Combine(dstDir, "index.html").TrimStart(CoreFX.Utils.Env.HostEnvironment.WebRootPath).Replace('\\', '/');
            return Ok(new { url });
        }

        /// <summary>
        /// 模板管理接口/更新场地默认模板
        /// </summary>
        /// <param name="path">模板路径</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("模板管理接口/更新场地默认模板", IsAPI = true)]
        public IActionResult UpdatePlaceDefault(string path)
        {
            var file = CoreFX.Utils.Env.HostEnvironment.WebRootFileProvider.GetFileInfo(path);
            if (!file.Exists)
            {
                ModelState.AddModelError("", "模板文件不存在");
                return BadRequest(ModelState);
            }

            var dir = Path.GetDirectoryName(file.PhysicalPath);
            if ("index.html" != file.Name)
            {
                CoreFX.Utils.FileSystem.Delete(dir);
                ModelState.AddModelError("", "模板文件只能为index.html");
                return BadRequest(ModelState);
            }

            var dstDir = Path.Combine(CoreFX.Utils.Env.HostEnvironment.WebRootPath, "IoT", "Places", "Default");
            CoreFX.Utils.FileSystem.Delete(dstDir);
            CoreFX.Utils.FileSystem.Move(dir, dstDir);

            var url = Path.Combine(dstDir, "index.html").TrimStart(CoreFX.Utils.Env.HostEnvironment.WebRootPath).Replace('\\', '/');
            return Ok(new { url });
        }

    }
}