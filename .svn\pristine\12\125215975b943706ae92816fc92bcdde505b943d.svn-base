﻿using System.Collections.Concurrent;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace UFU.IoT.Services
{
    /// <summary>
    /// 物联网设备WebSocket
    /// </summary>
    [AllowAnonymous]
    [Permission("物联网设备WebSocket", IsWebSocket = true, Url = "/iot/device/v2")]
    public class BinaryDeviceWebSocketConnect : IWebSocket, IConnect
    {
        public static ushort CalculateCRC16(byte[] data)
        {
            ushort crc = 0xFFFF;
            for (int i = 0; i < data.Length; i++)
            {
                crc = (ushort)(crc ^ (data[i]));
                for (int j = 0; j < 8; j++)
                {
                    crc = (crc & 1) != 0 ? (ushort)((crc >> 1) ^ 0xA001) : (ushort)(crc >> 1);
                }
            }

            return crc;
        }


       

        /// <summary>
        /// 连接方式
        /// </summary>
        public ConnectMode ConnectMode => ConnectMode.WebSocket;

        /// <summary>
        /// 收到文本消息
        /// </summary>
        /// <param name="data"></param>
        public override void OnReciveText(string data)
        {
            BinaryIoTService.ReceiveDeviceMsg(this, data);
        }

        private static ConcurrentDictionary<Guid, byte[]> TempBytes = new();

        private static Guid DefaultGuid = Guid.NewGuid();
        static string ParseDataToJson(byte[] datas)
        {
            var head = datas[0..2].ToHex();
            var size = datas[2..6].ToHex();
            var totalSize = BitConverter.ToUInt32(datas[2..6]);
            var zip = datas[6];
            var jsonSize = BitConverter.ToUInt16(datas[7..9]);
            var json = System.Text.Encoding.UTF8.GetString(datas, 9, jsonSize);
            var bin = datas[(9 + jsonSize)..^2].ToHex("");
            var binData = datas[(9 + jsonSize)..^2];
            var crc = BitConverter.ToUInt16(datas[^2..datas.Length]);
            var jsonObj = JsonNode.Parse(json);
            if (binData!=null&&binData.Length>0)
            {
	            ParseDataReceiveDeviceMsg(jsonObj,binData);
            }
            return jsonObj.ToJsonString();
        }
        private static void  ParseDataReceiveDeviceMsg( JsonNode jsonNode, byte[] bytes = null)
        {
	        var type = jsonNode?["Data"]?["Type"]?.GetValue<int>();
	        if (type>=0)
	        {
		        switch (type)
		        {
			        case (int)HXDataType.PPG:
			        {
				        var data = ParsePPGDataByDataType(bytes);
				        var rData = data["PPG-G"];
				        jsonNode["Data"]["PPG_G"] =  JsonValue.Create(rData);
				        jsonNode["Data"]["PPG_R"] =  JsonValue.Create(data["PPG-R"]);
				        jsonNode["Data"]["PPG_I"] =  JsonValue.Create(data["PPG-I"]);
				        break;
			        }
			        case (int)HXDataType.SPO2:
			        {
				        var data = ParseSPO2DataByDataType(bytes);
				        jsonNode["Data"]["Bmp"] =  JsonValue.Create(data.Item1);
				        jsonNode["Data"]["SPO2"] =  JsonValue.Create(data.Item2);
				        jsonNode["Data"]["Battery"] =  JsonValue.Create(data.Item3);
				        break;
			        }
			        case (int)HXDataType.IMU:
			        {
				        var data = ParseSensorData(bytes);
				        jsonNode["Data"]["IMU"] =  JsonValue.Create(data);
				        break;
			        }
			        case (int)HXDataType.EDA:
			        {
				        var data = ParseByData(bytes);
				        jsonNode["Data"]["EDA"] =  JsonValue.Create(data);
				        break;
			        }
			        case (int)HXDataType.SKINTEMP:
			        {
				        var data = ParseByData(bytes);
				        jsonNode["Data"]["SKINTEMP"] =  JsonValue.Create(data);
				        break;
			        }
		        }
	        }
        }
        /// <summary>
        /// 收到二进制数据
        /// </summary>
        /// <param name="data"></param>
        public override void OnReciveBinary(byte[] data)
        {
	        try
	        {
		        var json = ParseDataToJson(data);
		        BinaryIoTService.ReceiveDeviceMsg(this, json);
	        }
	        catch (Exception e)
	        {
		        LogTool.Logger.Error("OnReciveBinary",e);
	        }
        }
	private static Dictionary<string, List<short>> ParseSensorData(byte[] bytes)
	{
		// 初始化字典存储各传感器数据
		var data = new Dictionary<string, List<short>>()
		{
			["GYRO-X"] = new List<short>(),
			["GYRO-Y"] = new List<short>(),
			["GYRO-Z"] = new List<short>(),
			["ACC-X"] = new List<short>(),
			["ACC-Y"] = new List<short>(),
			["ACC-Z"] = new List<short>(),
			["GEO-X"] = new List<short>(),
			["GEO-Y"] = new List<short>(),
			["GEO-Z"] = new List<short>(),
		};

		// 每次采样的数据长度为 18 个字节（9 方向，每方向 2 字节）
		int frameSize = 18;

		// 检查输入字节数组的长度是否为 18 的倍数
		if (bytes.Length % frameSize != 0)
		{
			throw new ArgumentException("数据长度不符合传感器格式！");
		}
		// 遍历每一帧数据
		for (int i = 0; i < bytes.Length / frameSize; i++)
		{
			try
			{
				// 解析陀螺仪数据（小端模式）
				data["GYRO-X"].Add(BitConverter.ToInt16(bytes, i * frameSize + 0));   // C1C2 陀螺仪 X 轴
				data["GYRO-Y"].Add(BitConverter.ToInt16(bytes, i * frameSize + 2));   // B1B2 陀螺仪 Y 轴
				data["GYRO-Z"].Add(BitConverter.ToInt16(bytes, i * frameSize + 4));   // A1A2 陀螺仪 Z 轴

				// 解析加速度计数据（小端模式）
				data["ACC-X"].Add(BitConverter.ToInt16(bytes, i * frameSize + 6));    // F1F2 加速度计 X 轴
				data["ACC-Y"].Add(BitConverter.ToInt16(bytes, i * frameSize + 8));    // E1E2 加速度计 Y 轴
				data["ACC-Z"].Add(BitConverter.ToInt16(bytes, i * frameSize + 10));   // D1D2 加速度计 Z 轴

				// 解析地磁传感器数据（小端模式）
				data["GEO-X"].Add(BitConverter.ToInt16(bytes, i * frameSize + 12));   // 9192 地磁 X 轴
				data["GEO-Y"].Add(BitConverter.ToInt16(bytes, i * frameSize + 14));   // 8182 地磁 Y 轴
				data["GEO-Z"].Add(BitConverter.ToInt16(bytes, i * frameSize + 16));   // A1A2 地磁 Z 轴
			}
			catch (Exception ex)
			{
				Console.WriteLine("解析数据异常：" + ex.Message);
			}
		}
		return data;
	}
	/// <summary>
	/// 9个字节一组, 一组三个数据 ，每个数据三个字节
	/// //红光;//红外光//绿光
	/// 将数据添加到JsonNoe["data"]
	/// </summary>
	/// <param name="bytes"></param>
	/// <returns></returns>
	private static Dictionary<string, List<uint>> ParsePPGDataByDataType(byte[] bytes)
	{
		var data = new Dictionary<string, List<uint>>()
		{
		
			["PPG-R"] = new List<uint>(),
			["PPG-I"] = new List<uint>(),
			["PPG-G"] = new List<uint>(),
		};
		// 遍历每9个字节，分别解析PPG-R, PPG-I, 和 PPG-G数据
		for (int i = 0; i < bytes.Length / 9; i++)
		{
			try
			{
				var rbytes = bytes[(i * 9 + 0)..(i * 9 + 3)];
				Array.Reverse(rbytes);
				data["PPG-R"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));

				rbytes = bytes[(i * 9 + 3)..(i * 9 + 6)];
				Array.Reverse(rbytes);
				data["PPG-I"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));

				rbytes = bytes[(i * 9 + 6)..(i * 9 + 9)];
				Array.Reverse(rbytes);
				data["PPG-G"].Add((uint)((uint)rbytes[2] | ((uint)rbytes[1] << 8) | ((uint)rbytes[0] << 16)));
			}
			catch (System.Exception ex)
			{
				Console.WriteLine("解析数据异常：" + ex.Message);
			}
		}
		return data;
	}
	/// <summary>
	/// 解析数据类型中的心率、血氧和电量数据。
	/// </summary>
	/// <param name="bytes">要解析的字节数组。</param>
	/// <returns>包含心率、血氧和电量的元组。</returns>
	private static (uint, uint, uint) ParseSPO2DataByDataType(byte[] bytes)
	{
		Console.WriteLine($"---------------------心率：{(uint)bytes[0]},血氧：{(uint)bytes[1]},电量：{(uint)bytes[2]}------------------------");
		return (bytes[0], bytes[1], bytes[2]);
	}
	
	
	private static float[] ParseByData(byte[] bytes)
	{
	
	var data=	MemoryMarshal.Cast<byte, float>(bytes)
			.ToArray();
		return data;
	}
        
        /// <summary>
        /// 关闭连接
        /// 
        /// </summary>
        public override void Close()
        {
            //触发离线事件
            BinaryIoTService.DeviceConnectClosed(ConnectId);
            //关闭连接
            base.Close();
        }

        /// <summary>
        /// 发送文本消息
        /// </summary>
        /// <param name="data"></param>
        public bool SendText(string data)
        {
	        try
	        {
		        Task.Run(async () => await SendTextAsync(data));
		        return true;
	        }
	        catch (Exception ex)
	        {
		        LogTool.Logger.Error(ex, "WebSocket发送文本消息到设备异常");
		        Close();
		        return false;
	        }
        }

        /// <summary>
        /// 发送二进制消息
        /// </summary>
        /// <param name="data"></param>
        public bool SendBinary(byte[] data)
        {
            try
            {
                SendBinaryAsync(data).Wait();
                return true;
            }
            catch (Exception ex)
            {
                LogTool.Logger.Error(ex, $"WebSocket发送二进制消息到设备异常");
                Close();
                return false;
            }
        }
    }
    


   
}