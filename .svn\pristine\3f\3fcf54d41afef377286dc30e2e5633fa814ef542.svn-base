﻿@using System.Collections
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using Microsoft.JSInterop
@inherits LayoutComponentBase
@inject IJSRuntime Js
@typeparam TItem
<div style="height: 95%;width: 100%;background-color: #fff;border-radius: 10px" class="d-flex align-center justify-space-between">
    <div style="width: 20%;height:100%; background-color:@Color;border-radius:  10px 0 0 10px "
         class="d-flex flex-column align-center justify-space-between ">
        <div class="text-center"  style="font-size: 1rem;height: 100%;align-content: center;">@Name</div>
        @if (!IsHiddenValue)
        {
            <div  class="text-center" style="font-size:0.875rem;width: 100%;align-content: center;text-align: center">@GetShowValue()</div>
            <div class="text-center"  style="font-size: 0.650rem;height: 100%;align-content: center;">@Unit</div>
        }
    </div>
    <div style="width: 80%;height:95%;">
        <MECharts  Option="GenOption()" >
        </MECharts>
    </div>
</div>

@code {
    [Parameter] public int Take { get; set; } = 10;
    [Parameter] public bool IsHiddenValue { get; set; }
    [Parameter] public string Color { get; set; }
    [Parameter] public string Name { get; set; } = "";
    [Parameter] public string Unit { get; set; } = "";
    [Parameter] public bool IsHiddenXAxis { get; set; }
    [Parameter] public Func<DeviceStatusViewModel, string> ShowValueExpression { get; set; }
    [Parameter] public DeviceStatusViewModel DeviceStatusData { get; set; }
    [Parameter] public Func<DeviceStatusViewModel, List<TItem>> DataItemExpression { get; set; }

    private string GetShowValue()
    {
        if (ShowValueExpression != null)
        {
            return ShowValueExpression?.Invoke(DeviceStatusData) ?? default;
        }
        if (DataItemExpression == null)
        {
            return default;
        }
        return DataItemExpression.Invoke(DeviceStatusData)?.LastOrDefault()?.ToString() ?? string.Empty;
    }
    
    
    private  object GenOption()
    {
        var count = DataItemExpression.Invoke(DeviceStatusData).Count;
        var skip = Math.Max(0,count-Take);
        var now = DateTime.Now;
        var dataList = DataItemExpression?.Invoke(DeviceStatusData);
        var datas = dataList.Skip(skip)?.Take(Take)?.ToList();
        var obj = new
        {
            XAxis = new
            {
                Show = false,
                Type = "category",
                AxisLabel = new
                {
                    Show= false
                },
            },
            YAxis = new
            {
                Show = false,
                Type = "value",
                SplitLine = new
                {
                    Show = false
                },
                AxisLabel = new
                {
                    FontSize = 8,
                    Margin = 2
                },
                Max="dataMax",
                Min="dataMin",
            },
            Series = new[]
            {
                new
                {
                    Type = "line",
                    Data = datas,
                    ShowSymbol = false,
                    Smooth = false,
                },

            }
        };
        return obj;
    }

}