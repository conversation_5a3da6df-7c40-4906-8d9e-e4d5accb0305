using System.ComponentModel.DataAnnotations;
using UFU.CoreFX.Models;

namespace HX.HRV.Shared.Models
{
    /// <summary>
    /// 检测记录
    /// </summary>
    [DataEntity("2408192400000006")]
    public class PatientRecordModel
    {
        
        public int PpgRate { get;set; }

        /// <summary>
        /// 检测记录ID
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 是否生成报告数据
        /// </summary>
        public BuildReportStatus BuildReportStatus { get; set; }

        public string BuildReportStatusName => "";
        /// <summary>
        /// 患者ID
        /// </summary>
        [Required]
        public string PatientId { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required]
        public string DeviceId { get; set; }
        /// <summary>
        /// 检测时长（分钟）
        /// </summary>
        public int CheckTime { get; set; }
        /// <summary>
        /// 检测日期
        /// </summary>
        public DateTime CollectStartTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime CollectEndTime { get; set; }
        /// <summary>
        /// 检测状态
        /// </summary>
        public EnumPatientCheckStatus EnumPatientCheckStatus { get; set; }
        /// <summary>
        ///  患者
        /// </summary>
        public PatientModel Patient { get; set; }
        /// <summary>
        /// 检测编号
        /// </summary>
        public string RecordCode { get; set; }
        /// <summary>
        /// 打印次数
        /// </summary>
        public int PrintCount { get; set; }
        public  string GetRecordDirectoryPath()
        {
            var binPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin");
            if (!Directory.Exists(binPath))
            {
                Directory.CreateDirectory(binPath);
            }
            var year = CollectStartTime.Year;
            var month = CollectStartTime.Month;
            var day = CollectStartTime.Day;
            var dir = Path.Combine(binPath, $"{year}", $"{month}-{day}",Id);
            return dir;
        }
        /// <summary>
        /// 统计数据
        /// </summary>
        public ReportDataStatistics Statistics { get; set; }
        public DateTime DeviceStartCheckTime { get; set; }
    }       

    /// <summary>
    ///  检测状态
    /// </summary>
    public enum  EnumPatientCheckStatus
    {
        /// <summary>
        /// 未知/初始
        /// </summary>
        UnKnown = 0,
        /// <summary>
        /// 已完成
        /// </summary>
        Checked = 1,
        /// <summary>
        ///  检查中
        /// </summary>
        Checking = 2,
        /// <summary>
        /// 准备检测
        /// </summary>
        Readying = 3,
        
        /// <summary>
        ///  已停止
        /// </summary>
        Stopped = 4
    }
    
    
    public enum BuildReportStatus
    {
        /// <summary>
        /// 未知
        /// </summary>
        [Display(Name = "待生成")]
        UnKnown = 0,
        /// <summary>
        /// 生成完成
        /// </summary>
        [Display(Name = "已生成")]
        Completed =1,
        /// <summary>
        /// 生成中
        /// </summary>
        [Display(Name = "生成中")]
        Building = 2,
        /// <summary>
        /// 未生成
        /// </summary>
        [Display(Name = "未生成")]
        Failed = 3,
        /// <summary>
        /// 超时
        /// </summary>
        [Display(Name = "超时")]
        Timeout = 4,
        /// <summary>
        /// 不具备生成条件
        /// </summary>
        [Display(Name = "数据无效")]
        NoCondition = 5
    }
}