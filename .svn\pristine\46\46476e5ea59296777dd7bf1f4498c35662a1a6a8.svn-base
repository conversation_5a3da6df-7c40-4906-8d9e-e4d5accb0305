﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Services
{
    /// <summary>
    /// 物联网管理员WebSocket
    /// </summary>
    [Permission("物联网管理员WebSocket", IsWebSocket = true, AllowAllUser = true, Url = "/iot/admin/v1")]
    public class AdminWebSocket : IWebSocket
    {
        /// <summary>
        /// 设备编号
        /// </summary>
        public List<string> DeviceIds { get; set; } = new List<string>();

        /// <summary>
        /// 设备类型编号
        /// </summary>
        public List<string> DeviceTypeIds { get; set; } = new List<string>();

        /// <summary>
        /// 连接所有设备
        /// </summary>
        public bool ConnectAll { get; private set; } = false;

        public bool IsNew { get; private set; } = false;

        
        /// <summary>
        /// 0 默认 1客户端
        /// </summary>
        public int ClientSource { get; private set; } = 0;
        /// <summary>
        /// 关闭连接
        /// </summary>
        public override void Close()
        {
            IoTService.Admins.TryRemove(ConnectId, out AdminWebSocket uws);
            BinaryIoTService.Admins.TryRemove(ConnectId, out AdminWebSocket bws);
            base.Close();
        }

        /// <summary>
        /// 连接
        /// </summary>
        public override void OnConnect()
        {
            GetDevices();
            if (IsNew)
            {
                if (!BinaryIoTService.Admins.Any(m => m.Value.Socket == this.Socket))
                {
                    BinaryIoTService.Admins.AddOrUpdate(ConnectId, this, (key, value) => this);
                }
            }
            else
            {
                if (!IoTService.Admins.Any(m => m.Value.Socket == this.Socket))
                {
                    IoTService.Admins.AddOrUpdate(ConnectId, this, (key, value) => this);
                }
            }

         
        }

        /// <summary>
        /// 收到文本消息
        /// </summary>
        /// <param name="data"></param>
        public override void OnReciveText(string data)
        {
            if (IsNew)
            {
                var deviceId = this.Context.Request.Query["deviceId"].FirstOrDefault();
                BinaryIoTService.ReceiveUserMsg(this,deviceId,data);
            }
            else
            {
                if (data.Contains("Func"))
                {
                    var wsdata = JObject.Parse(data);
                    var method = wsdata["Func"]?.ToString();
                    if (method == "Msg")
                    {
                        var msg = wsdata["Data"].ToObject<Msg>();
                        IoTService.ReceiveUserMsg(this, msg);
                    }
                    else if (method == "Raw")
                    {
                        IoTService.ReceiveUserRawData(this, data);
                    }
                }  
            }
        }

        /// <summary>
        /// 获取要连接的设备
        /// </summary>
        private void GetDevices()
        {
            var deviceId = this.Context.Request.Query["deviceId"].FirstOrDefault();
            var deviceIds = this.Context.Request.Query["deviceIds"].FirstOrDefault();
            var userPlaceId = this.Context.Request.Query["userPlaceId"].FirstOrDefault();
            var placeId = this.Context.Request.Query["placeId"].FirstOrDefault();
            var deviceTypeId = this.Context.Request.Query["deviceTypeId"].FirstOrDefault();
            bool.TryParse(this.Context.Request.Query["isNew"].FirstOrDefault(), out var isNew);
            int.TryParse(this.Context.Request.Query["clientSource"].FirstOrDefault(), out var clientSource);
            IsNew = isNew;
            ClientSource=clientSource;
            using (DataRepository db = new DataRepository(UserInfo.System))
            {
                if (!string.IsNullOrWhiteSpace(deviceId))
                {
                    DeviceIds = db.Query<DeviceModel>().Where(m => m.Id == deviceId).Select(m => m.Id).ToList();
                }
                else if (!string.IsNullOrWhiteSpace(deviceIds))
                {
                    string[] ids = deviceIds.ToString().Split(',');
                    DeviceIds = db.Query<DeviceModel>().Where(m => ids.Contains(m.Id)).Select(m => m.Id).ToList();
                }
                else if (!string.IsNullOrWhiteSpace(userPlaceId))
                {
                    var userPlace = db.Query<PlaceUserModel>().Where(m => m.Id == userPlaceId).FirstOrDefault();
                    if (userPlace != null)
                    {
                        DeviceIds = db.Query<PlaceDeviceModel>().Where(m => m.Data.PlaceId == userPlace.Data.PlaceId)
                            .Select(m => m.Data.DeviceId).ToList();
                    }
                }
                else if (!string.IsNullOrWhiteSpace(placeId))
                {
                    DeviceIds = db.Query<PlaceDeviceModel>().Where(m => m.Data.PlaceId == placeId)
                        .Select(m => m.Data.DeviceId).ToList();
                }
                else if (!string.IsNullOrWhiteSpace(deviceTypeId))
                {
                    DeviceIds = db.Query<DeviceModel>()
                        .Where(m => m.Data.TypeId == deviceTypeId)
                        .Select(m => m.Id)
                        .ToList();
                }
                else
                {
                    ConnectAll = true;
                }
            }
        }
    }
}