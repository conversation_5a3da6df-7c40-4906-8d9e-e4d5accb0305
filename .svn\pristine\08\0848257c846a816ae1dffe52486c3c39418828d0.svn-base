﻿using System.Reflection.Metadata;
using AntDesign;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Mvc;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using UFU.CoreFX.Shared.Utils;
using UFU.IoT.Models;
using UFU.IoT.Shared.Utils;

namespace UFU.IoT.Shared.Pages.DeviceType.DataItemPage;

public partial class EditDataItem : ComponentBase
{
    [Parameter]     [SupplyParameterFromQuery(Name = "groupName")] public string GroupName { get; set; }
    public DataItem DataItem { get; private set; }
    [Parameter] public string key { get; set; }
    [Parameter]public string typeId { get; set; }
    
    
    public IEnumerable<string> GroupNames { get; set; } 
    /// <summary>
    /// 是否复制
    /// </summary>
    [Parameter]
    [SupplyParameterFromQuery]
    public bool isCopy { get; set; }
    private const string BaseUrl = "/api/v2.0/IoT/DataItems/";
    [Inject] private StateService _stateService{ get; set; }
    [Inject] private MessageService _messageService{ get; set; }
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (!string.IsNullOrEmpty(key))
        {
            var res = await _stateService.GetAsJsonAsync<DataItem>(BaseUrl + "Get",
                new Dictionary<string, string> { { "key", key }, { "typeId", typeId },{"groupName",GroupName} });
            if (res.Success)
            {
                InitDataItem(res.Data);
              
            }
            else  
            {
                await _messageService.Error(res.Message);
            } 
        }
        else
        {
            DataItem = new DataItem()
            {
                MappingItems = new List<MappingItem>(),
                Statistics = new List<Statistics>()
            };
        }
        await InitGroupNameItemAsync();
        IsShow = DataItem.IsShow ?? false;
        IsArray = DataItem.IsArray ?? false;
        SaveOutOfRangData = DataItem.SaveOutOfRangData ?? false;
        DataLength= DataItem.DataLength ?? 8;
        DataEnumStatistics= DataItem.Statistics.Select(x=>x.ToString()).ToArray();
        _mappingItems= DataItem.MappingItems ?? new  List<MappingItem>();
        StatisticsCycle= DataItem.StatisticsCycle?.Select(x=>x.ToString()).ToArray();
        DataItem.EnumValues ??= new List<EnumValue>();
    }

    private void InitDataItem(DataItem dataItem)
    {
        DataItem = dataItem;
        if (isCopy)
        {
            DataItem.Key += "副本";
            key = string.Empty;
        }
    }
    
    private async Task  InitGroupNameItemAsync()
    {
        var res = await _stateService.GetAsJsonAsync<DataModel<DeviceTypeModel>>(
            "/api/v2.0/IoT/DeviceTypes/GetDeviceTypeModelDetail",
            new Dictionary<string, string>() { { "id", typeId } });
        if (res.Success)
        {
            GroupNames = res.Data?.Data?.DataItemList?.GroupBy(o => string.IsNullOrEmpty(o.GroupName) ? string.Empty : o.GroupName)
                .Select(m => m.Key)
                .Where(o=>!string.IsNullOrEmpty(o));
        }
    }

    public async Task HandleSubmitAsync()
    {
        DataItem.IsShow = IsShow;
        DataItem.IsArray = IsArray;
        DataItem.SaveOutOfRangData = SaveOutOfRangData;
        DataItem.DataLength = DataLength;
        DataItem.MappingItems = _mappingItems;
        DataItem.StatisticsCycle =StatisticsCycle?.Select(int.Parse).ToList()??new List<int>();
        DataItem.Statistics= DataEnumStatistics.Select(x=>(Statistics)Enum.Parse(typeof(Statistics),x)).ToList();
        var queryDic = new Dictionary<string, string> {  { "typeId", typeId } };
        if (!string.IsNullOrEmpty(key))
        {
            queryDic.Add("key", key);
            queryDic.Add("groupName",GroupName);
        }
        var url = string.IsNullOrEmpty(key) ? BaseUrl + "Add": BaseUrl + "Edit";  
        var res = await this._stateService.PostAsJsonAsync<DataItem>(url,  DataItem,queryDic);
        if (res.Success)
        {
            InitDataItem(res.Data);
            await _messageService.Success("成功！");
            _stateService.NavigationManager.NavigateTo($"/IoT/DeviceType/DataItemList/{typeId}");
        }
        else
        {
            await _messageService.Error(res.Message);
        }
    }


    private bool _visible = false;

    private void ShowModal()
    {
        _visible = true;
    }

    private void HandleOk(MouseEventArgs e)
    {
        Console.WriteLine("e");
        _visible = false;
    }

    private void HandleCancel(MouseEventArgs e)
    {
        Console.WriteLine("e");
        _visible = false;
    }

    private bool IsShow { get; set; } //是否显示
    private bool IsArray { get; set; }
    private bool SaveOutOfRangData { get; set; }
    private int DataLength { get; set; }
    private readonly int[] _dataLengthList = { 8, 16, 32, 64 };

    private FormValidateErrorMessages validateMessages = new()
    {
        Required = "'{0}' 是必选字段",
    };

    private string[] StatisticsCycle { get; set; }

    private readonly CheckboxOption[] _options =
    {
        new() { Label = "1分钟", Value = "60" },
        new() { Label = "5分钟", Value = "300" },
        new() { Label = "10分钟", Value = "600" },
        new() { Label = "30分钟", Value = "1800" },
        new() { Label = "1小时", Value = "3600" },
        new() { Label = "1天", Value = "86400" },
        new() { Label = "1周", Value = "604800" },
        new() { Label = "1月", Value = "2592000" }, // 假设每月30天
    };

    private readonly CheckboxOption[] _statisticsEnumOptions = EnumConvert.ToCheckboxOption<Statistics>();
    private List<MappingItem> _mappingItems;
    private string[] DataEnumStatistics { get; set; }

    private void OnSelectedItemChangedHandler(string value)
    {
        DataItem.GroupName = value;
    }

    private void OnDataTypeChanged(DataItemType obj)
    {
        DataItem.DataType = obj;
        if (DataItem.DataType is not DataItemType.Bool) return;
        DataItem.EnumValues.Add(new EnumValue { ShowName = "关", Value = 0, RWType = RWType.ReadWrite });
        DataItem.EnumValues.Add(new EnumValue { ShowName = "开", Value = 1, RWType = RWType.ReadWrite });
    }
}