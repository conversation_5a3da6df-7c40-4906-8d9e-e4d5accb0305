﻿using System.Diagnostics;
using System.Drawing.Printing;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using HX.HRV.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace HX.HRV.Web.Areas.HRV_HX.Controllers;

/// <summary>
/// 设备管理
/// </summary>
[Area("HRV_HX")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class HxExportTaskController : Controller
{
    private readonly DataRepository _context;
    private readonly CoreDbContext _coreContext;
    private readonly JsonSerializerOptions OptionsIgnoreNull = new JsonSerializerOptions();

    /// <summary>
    /// 数据管理
    /// </summary>
    /// <param name="context"></param>
    /// <param name="coreContext"></param>
    public HxExportTaskController(DataRepository context, CoreDbContext coreContext)
    {
        _context = context;
        _coreContext = coreContext;

        //支持中文编码
        OptionsIgnoreNull.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        //使用PascalCase格式
        OptionsIgnoreNull.PropertyNamingPolicy = null;
        //忽略null值
        OptionsIgnoreNull.IgnoreNullValues = true;
    }

    /// <summary>
    /// 导出任务/添加
    /// </summary>
    /// <param name="hxExportTaskModel"></param>
    /// <returns></returns>
    [HttpPost]
    [Permission("心率变异性导出任务接口/添加", IsAPI = true)]
    public async Task<Result<DataModel<HxExportTaskModel>>> Add([FromBody] HxExportTaskModel hxExportTaskModel)
    {
        UserInfo userInfo = ViewBag.User;
        hxExportTaskModel.Id = UId.GetNewId();
        var result = new Result<DataModel<HxExportTaskModel>>();
        var model = new DataModel<HxExportTaskModel>
        {
            Data = hxExportTaskModel,
            Id = hxExportTaskModel.Id,
            OrganId = userInfo?.Organ?.GetTopOrganId(),
            UserId = userInfo?.Id,
            AddTime = DateTime.Now,
            UpdateTime = DateTime.Now
        };
        model.AddTime = model.AddTime;
        model.UpdateTime = model.UpdateTime;
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(model, result))
        {
            return result;
        }
        _context.Add(model);
        await _context.SaveChangesAsync();
        return result;
    }

    /// <summary>
    /// 导出任务/编辑
    /// </summary>
    /// <param name="id">编号</param>
    /// <param name="hxExportTaskModel">产品信息</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性导出任务接口/编辑", IsAPI = true)]
    public async Task<Result<HxExportTaskModel>> Edit(string id, [FromBody] HxExportTaskModel hxExportTaskModel)
    {
        var result = new Result<HxExportTaskModel>();
        if (!UFU.CoreFX.Shared.Utils.UValidator.Validate(hxExportTaskModel, result))
        {
            return result;
        }
        var model = await _context.Query<HxExportTaskModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (model == null)
        {
            result.AddError("设备类型不存在");
            return result;
        }
        _context.Update(model);
        await _context.SaveChangesAsync();
        result.Data = model.Data;
        return result;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="page"></param>
    /// <param name="pageSize"></param>
    /// <returns></returns>
    [HttpGet()]
    [Permission("心率变异性导出任务接口/列表", IsAPI = true, AllowAllUser = true)]
    public async Task<Result<PageList<DataModel<HxExportTaskModel>>>> GetHxExportTaskModelList(
        [FromQuery] int? page = 1,
        [FromQuery] int? pageSize = 100)
    {
        UserInfo userInfo = ViewBag.User;
        var query = _context.Query<HxExportTaskModel>();
        query = query.OrderByDescending(m => m.Id);
        var dataModels = await query.ToPageListAsync(pageIndex: page.Value, pageSize: pageSize.Value);
        return new Result<PageList<DataModel<HxExportTaskModel>>>()
            { Success = true, Data = dataModels, Page = dataModels.PageInfo };
    }

    /// <summary>
    /// 设备管理接口/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性导出任务接口/删除", IsAPI = true)]
    public async Task<Result<bool>> Delete(string id)
    {
        var result = new Result<bool>();
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<HxExportTaskModel>().FirstOrDefaultAsync(m => m.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }

        _context.Remove(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        return result;
    }

    /// <summary>
    /// 设备管理接口/删除
    /// </summary>
    /// <param name="id">设备编号</param>
    /// <returns></returns>
    [HttpPost()]
    [Permission("心率变异性导出任务接口/编辑", IsAPI = true)]
    public async Task<Result<bool>> Finished(string id)
    {
        var result = new Result<bool>()
        {
            Success = false
        };
        if (id == null)
        {
            return result;
        }

        var patient = await _context.Query<HxExportTaskModel>()
            .FirstOrDefaultAsync(m => m.Id == id);
        if (patient == null)
        {
            result.AddError("检测记录不存在");
            return result;
        }
        patient.UpdateTime = DateTime.Now;
        patient.Data.Progress = 100;
        _context.Update(patient);
        await _context.SaveChangesAsync();
        result.Data = true;
        result.Success = true;
        return result;
    }
    
    [HttpGet("{id}")]
    [Permission("心率变异性导出任务接口/下载", IsAPI = true,AllowAllUser = true)]
    public IActionResult DownloadFile([FromRoute] string id)
    {
        var hxExportTaskModel = _context.Query<HxExportTaskModel>().FirstOrDefault(m => m.Id == id);
        if ( hxExportTaskModel == null)
        {
            NotFound();
        }
        var filePath = Path.Combine(hxExportTaskModel.Data.FilePath); // 获取文件路径
        if (!System.IO.File.Exists(filePath))
        {
            return NotFound();
        }
        var fileName = System.IO.Path.GetFileName(filePath); // 获取文件名
        var fileBytes = System.IO.File.ReadAllBytes(filePath); // 读取文件内容
        return File(fileBytes, "application/octet-stream", fileName);
    }
   
}