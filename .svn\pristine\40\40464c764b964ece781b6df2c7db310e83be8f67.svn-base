﻿@page "/client/system/config"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Pages.Client.Components
@using Microsoft.Extensions.Configuration
@using UFU.CoreFX.Utils
@using UFU.IoT.Models
@using UFU.IoT.Shared.Models
@layout HRVDefaultLayout
@attribute [Permission("系统配置/系统项配置", MenuOrder = "6", Icon = "mdi-forum|xitong", IsMenu = true)]
@inject IJSRuntime js

<style>
	.customer-input .m-input__prepend-outer .m-label {
		width: 140px;
		font-size: 1.25rem;
	}
</style>

@if (SystemConfig != null)
{
	<MCard Class="pa-4">
		<MSwitch Label="是否自动打印报告" OnChange="EventCallback.Factory.Create<bool>(this, HandleIsAutoChange)"
		@bind-Value="SystemConfig.IsAutoPrint" />
		<MCol Cols="12">
			<!-- 患者来源 -->
			<div class="horizontal-stack">
				@* <MLabel>患者来源:</MLabel> *@
				<MLabel>被试来源:</MLabel>
				<HRVSourceGroupButton IsOnlyShow="true" ConfigModel="SystemConfig" SourceText="SourceText">
				</HRVSourceGroupButton>
			</div>
		</MCol>
		<MCol Cols="12">
			<div class="horizontal-stack">
				<MLabel>检测时长:</MLabel>
				<HRVCheckTimeButton IsOnlyShow="true" @bind-CheckTimeValue="CheckTimeValue" >
				</HRVCheckTimeButton>
			</div>
		</MCol>
		@* @if (isShowExport) *@
		@* { *@
		@* 	<MCol Cols="12"> *@
		@* 		<MCard Title="频率设置"> *@
		@* 			<MRow> *@
		@* 			$1$ 	<MCol Cols="12" Md="4"> #1# *@
		@* 			$1$ 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.HRRate" Dense #1# *@
		@* 			$1$ 		Solo #1# *@
		@* 			$1$ 		Filled> #1# *@
		@* 			$1$ 			<PrependContent> #1# *@
		@* 			$1$ 				<MLabel Class="mr-2">心率:</MLabel> #1# *@
		@* 			$1$ 			</PrependContent> #1# *@
		@* 			$1$ 		</MTextField> #1# *@
		@* 			$1$ 	</MCol> #1# *@
		@* 			$1$ 	<MCol Cols="12" Md="4"> #1# *@
		@* 			$1$ 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.EDARate" Dense #1# *@
		@* 			$1$ 		Solo #1# *@
		@* 			$1$ 		Label="皮肤电阻" Filled> #1# *@
		@* 			$1$ 			<PrependContent> #1# *@
		@* 			$1$ 				<MLabel Class="mr-2">皮肤电阻:</MLabel> #1# *@
		@* 			$1$ 			</PrependContent> #1# *@
		@* 			$1$ 		</MTextField> #1# *@
		@* 			$1$ 	</MCol> #1# *@
		@* 			$1$ 	<MCol Cols="12" Md="4"> #1# *@
		@* 			$1$ 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.ENVRate" Dense #1# *@
		@* 			$1$ 		Solo #1# *@
		@* 			$1$ 		Label="气压 环境温度 光照" Filled> #1# *@
		@* 			$1$ 			<PrependContent> #1# *@
		@* 			$1$ 				<MLabel Class="mr-2">气压 环境温度 光照:</MLabel> #1# *@
		@* 			$1$ 			</PrependContent> #1# *@
		@* 			$1$ 		</MTextField> #1# *@
		@* 			$1$ 	</MCol> #1# *@
		@* 			$1$ 	<MCol Cols="12" Md="4"> #1# *@
		@* 			$1$ #1# *@
		@* 			$1$ 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.IMURate" Dense #1# *@
		@* 			$1$ 		Solo #1# *@
		@* 			$1$ 		Label="九轴(IMU)" Filled> #1# *@
		@* 			$1$ 			<PrependContent> #1# *@
		@* 			$1$ 				<MLabel Class="mr-2">九轴(IMU):</MLabel> #1# *@
		@* 			$1$ 			</PrependContent> #1# *@
		@* 			$1$ 		</MTextField> #1# *@
		@* 			$1$ 	</MCol> #1# *@
		@* 			$1$ 	<MCol Cols="12" Md="4"> #1# *@
		@* 			$1$ 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.PPGRate" Dense #1# *@
		@* 			$1$ 		Solo #1# *@
		@* 			$1$ 		Label="PPGRate" Filled> #1# *@
		@* 			$1$ 			<PrependContent> #1# *@
		@* 			$1$ 				<MLabel Class="mr-2">PPG:</MLabel> #1# *@
		@* 			$1$ 			</PrependContent> #1# *@
		@* 			$1$ 		</MTextField> #1# *@
		@* 			$1$ 	</MCol> #1# *@
		@* 			$1$ 	<MCol Cols="12" Md="4"> #1# *@
		@* 			$1$ 		<MTextField Class="customer-input" @bind-Value="SystemConfig.SystemRateConfig.SPO2Rate" #1# *@
		@* 			$1$ 		Dense #1# *@
		@* 			$1$ 		Solo #1# *@
		@* 			$1$ 		Label="血氧" Filled> #1# *@
		@* 			$1$ 			<PrependContent> #1# *@
		@* 			$1$ 				<MLabel Class="mr-2">血氧:</MLabel> #1# *@
		@* 			$1$ 			</PrependContent> #1# *@
		@* 			$1$ 		</MTextField> #1# *@
		@* 			$1$ 	</MCol> #1# *@
		@* 		$1$ <MCol Cols="12" Md="3"> #1# *@
		@* 		$1$ 	<MSelect Items="SystemRateConfigStatic.PpgRates" #1# *@
		@* 		$1$ 	         @bind-Value="@SystemConfig.SystemRateConfig.HRRate" #1# *@
		@* 		$1$ 	         ItemText="u => u.ToString()" #1# *@
		@* 		$1$ 	         ItemValue="u => u" #1# *@
		@* 		$1$ 	         Label="PPG" #1# *@
		@* 		$1$ 	         Outlined #1# *@
		@* 		$1$ 	         Dense></MSelect> #1# *@
		@* 		$1$ #1# *@
		@* 		$1$ </MCol> #1# *@
		@* 		$1$ <MCol Cols="12" Md="3"> #1# *@
		@* 		$1$ #1# *@
		@* 		$1$ 	<MSelect Items="SystemRateConfigStatic.EdaRates" #1# *@
		@* 		$1$ 	         @bind-Value="@SystemConfig.SystemRateConfig.EDARate" #1# *@
		@* 		$1$ 	         ItemText="u => u.ToString()" #1# *@
		@* 		$1$ 	         ItemValue="u => u" #1# *@
		@* 		$1$ 	         Label="皮肤电阻" #1# *@
		@* 		$1$ 	         Outlined #1# *@
		@* 		$1$ 	         Dense></MSelect> #1# *@
		@* 		$1$ </MCol> #1# *@
		@* 		$1$ #1# *@
		@* 		$1$ <MCol Cols="12" Md="3"> #1# *@
		@* 		$1$ 	<MSelect Items="SystemRateConfigStatic.ImuRates" #1# *@
		@* 		$1$ 	         @bind-Value="@SystemConfig.SystemRateConfig.IMURate" #1# *@
		@* 		$1$ 	         ItemText="u => u.ToString()" #1# *@
		@* 		$1$ 	         ItemValue="u => u" #1# *@
		@* 		$1$ 	         Label="九轴(IMU)" #1# *@
		@* 		$1$ 	         Outlined #1# *@
		@* 		$1$ 	         Dense></MSelect> #1# *@
		@* 		$1$ </MCol> #1# *@
		@* 		$1$ #1# *@
		@* 		$1$ <MCol Cols="12" Md="3"> #1# *@
		@* 		$1$ 	<MSelect Items="SystemRateConfigStatic.SktRates" #1# *@
		@* 		$1$ 	         @bind-Value="@SystemConfig.SystemRateConfig.SKTRate" #1# *@
		@* 		$1$ 	         ItemText="u => u.ToString()" #1# *@
		@* 		$1$ 	         ItemValue="u => u" #1# *@
		@* 		$1$ 	         Label="皮肤温度" #1# *@
		@* 		$1$ 	         Outlined #1# *@
		@* 		$1$ 	         Dense></MSelect> #1# *@
		@* 		$1$ </MCol> #1# *@
		@* 			</MRow> *@
		@* 		</MCard> *@
		@* 	</MCol> *@
		@* } *@
		<MCol Cols="12">
			<MCard Title="设备布局设置" Class="pl-4">
				<MRow>
					<MCol Cols="3">
						<MTextField @bind-Value="SystemConfig.LayoutConfig.Row" Dense Solo
						Label="行"
						Filled>
							<PrependContent>
								<MLabel Class="mr-2">行:</MLabel>
							</PrependContent>
						</MTextField>
					</MCol>
					<MCol Cols="3">
						<MTextField @bind-Value="SystemConfig.LayoutConfig.Col" Dense Solo
						Label="列"
						Filled>
							<PrependContent>
								<MLabel Class="mr-2">列:</MLabel>
							</PrependContent>
						</MTextField>
					</MCol>
					<MCol Cols="3">
						<MTextField @bind-Value="SystemConfig.LayoutConfig.DisplayNumber" Dense
						Solo
						Label="显示数量" Filled>
							<PrependContent>
								<MLabel Class="mr-2">显示数量:</MLabel>
							</PrependContent>
						</MTextField>
					</MCol>
				</MRow>
				<MRow>
					<MCol Cols="3">
						<MSwitch @bind-Value="SystemConfig.LayoutConfig.IsShowHeader" Dense Solo
						Label="是否显示顶部操作" Filled>
						</MSwitch>
					</MCol>
					<MCol Cols="3">
						<MSwitch @bind-Value="SystemConfig.LayoutConfig.IsPage" Dense Solo
						Label="是否分页" Filled>
						</MSwitch>
					</MCol>
				</MRow>
			</MCard>
		</MCol>
		<MCol Cols="12" Md="4">
			<MButton OnClick="HandleIsAutoChange">保存</MButton>
		</MCol>
	</MCard>
}


@code {

	protected override async Task OnInitializedAsync()
	{
		var res = await stateService
			.GetAsJsonAsync<DataModel<HxSystemConfigModel>>(
				"/api/v2.0/HRV_HX/HxSystemConfig/GetSystemConfigModelDetail");
		SystemConfig= res.Data.Data;
		await base.OnInitializedAsync();
	}

	public string SourceText { get; set; }
	[Inject] IPopupService PopupService { get; set; }
	public int CheckTimeValue { get; set; }
	private HxSystemConfigModel SystemConfig{ get; set; }
	[Inject] IConfiguration Configuration { get; set; }
	private bool isShowExport => Configuration.GetValue<bool>("HrvVariable:IsShowExport");
	[Inject] public StateService stateService { get; set; }
	[Inject] public DeviceStateService	DeviceStateService { get; set; }




	private List<DeviceStatusViewModel> DeviceDataList=> DeviceStateService.DeviceDataList;

	private string DeviceSN { get; set; }="";

	private bool ServerLocation { get; set; }

	private bool SoftwareVersion { get; set; }

	private bool IsOnline { get; set; }
	

	[Inject] public InternalHttpClientService InternalHttpClientService { get; set; }

	private async Task HandleIsAutoChange()
	{
		var res = await InternalHttpClientService.EditSystemConfigAsync(SystemConfig);
		await PopupService.EnqueueSnackbarAsync("保存成功", AlertTypes.Success);
	}

	private const string DeviceTypeId = "2407160100000001";


	private async Task SendMsgToDeviceSerialPort() {
		var device = DeviceDataList.FirstOrDefault(m => m.Device?.DeviceSN ==DeviceSN);
		var msgJson = new {
			MsgId = 555555,
			action = "toSerialPort",
			//时间戳
			Time = DateTime.Now.ToUnixMs(),
			Device = new {
				SN = device?.Device.DeviceSN,
				Type = device?.Device.TypeId,
			},
			CMD = (int)BinaryCMD.Write,
			Data = new {
				ServerLocation = ServerLocation ? 1 : 0,
				SoftwareVersion = SoftwareVersion ? 1 : 0,
				IsOnline = IsOnline ? 1 : 0
			}
		};
		var msgStr = JsonTool.SerializeIgnoreNull(msgJson);
		await js.InvokeAsync<object>("SendMessageToClientByMsg", msgStr);
		
	}


}