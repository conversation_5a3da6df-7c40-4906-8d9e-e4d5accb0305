﻿namespace HX.HRV.Shared.Models;


/// <summary>
/// 设备的显示状态
/// </summary>
public enum EnumDeviceStatus
{
    /// <summary>
    /// 空闲
    /// </summary>
    空闲 = 1,
    /// <summary>
    /// 离线
    /// </summary>
    离线 = 2,
    /// <summary>
    /// 报警/异常
    /// </summary>
    报警 = 3,
    /// <summary>
    /// 完成
    /// </summary>
    完成 = 4,
    /// <summary>
    /// 运行/检测中
    /// </summary>
    检测中 = 5,
    /// <summary>
    /// USB传输中
    /// </summary>
    USB传输中 = 6,
    USB传输完成=7
    
}