﻿using System.Globalization;
using System.Text;
using HX.HRV.Shared.Models.CSVDataModel;
using UFU.CoreFX.Utils;
using UFU.IoT.Shared.Models;

namespace HX.HRV.Web.Units;

/// <summary>
/// 文件数据
/// </summary>
public static class CSVDataHelper
{
    /// <summary>
    ///  获取目标文件夹中的 EDA数据
    /// </summary>
    /// <param name="fileDir"></param>
    public static async Task<List<EDACSVDataModel>> GetDirectoryEDAData(string fileDir)
    {
        var scrData = new List<string>();
        var scrFilePath = Path.Combine(fileDir, $"SCL&SCR.csv");
        if (File.Exists(scrFilePath))
        {
            scrData = (await File.ReadAllLinesAsync(scrFilePath, Encoding.UTF8))?.Skip(1)?.ToList();
        }

        var edaData = new List<string>();

        var edaFilePath = Path.Combine(fileDir, $"EDA.csv");
        if (File.Exists(edaFilePath))
        {
            edaData = (await File.ReadAllLinesAsync(edaFilePath, Encoding.UTF8))?.Skip(1).ToList();
        }

        var edaDatas = new List<EDACSVDataModel>();
        for (int i = 0; i < edaData.Count; i++)
        {
            var scr = 0f;
            var scl = 0f;
            if (scrData != null && scrData.Count > i)
            {
                var scrItemData = scrData[i].Split(',');
                scl = float.Parse(scrItemData[1]);
                scr= float.Parse(scrItemData[2]);
            }
            var item = edaData[i].Split(',');
            var date = DateTime.TryParseExact(item[0], "yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture, DateTimeStyles.None, out var time) ? time : default;
            var eda = float.Parse(item[1]);
            edaDatas.Add(new EDACSVDataModel()
            {
                EDA = eda,
                Date = date,
                SCR =(float)( scr*10e8),
                SCL = scl,
            });
        }
        return edaDatas;
    }

    public static List<int> GetBmpData(string fileDir)
    {
        var bmpFilePath = Path.Combine(fileDir, $"{HXDataType.HRSPO2}.csv");
        if (File.Exists(bmpFilePath))
        {
            var bmpData = (File.ReadAllLines(bmpFilePath, Encoding.UTF8))?.Skip(1)?.ToList();
            var hrlist = new List<int>();
            for (int i = 0; i < bmpData.Count; i++)
            {
                var item = bmpData[i].Split(',');
                var hr = int.Parse(item[1]);
                hrlist.Add(hr);
            }
            return hrlist;
        }
        return new List<int>();
    }
}