﻿@page "/client/sci/patient/list"
@using System.Reactive
@using System.Reactive.Disposables
@using System.Reactive.Linq
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Pages.Client.PatientRecord
@using Masa.Blazor
@using ReactiveUI.Blazor
@using Masa.Blazor.Presets
@using UFU.CoreFX.Permission
@inherits ReactiveInjectableComponentBase<PatientListViewModel>
@attribute [Permission("被试管理", MenuName = "被试管理", Icon = "huanzhe", MenuOrder = "3", IsMenu = true)]


@if (ViewModel != null)
{
    <div  style="height: 100%">
        <MForm Class="ma-8">
            <MRow Class="search-row">
                <MCol Cols="3" Class="d-flex align-center justify-center">
                    <MTextField Clearable Class="customer-input" @bind-Value="ViewModel.QueryModel.CardId"
                                Solo
                                Dense>
                        <PrependContent>
                            <MLabel Class="mr-2">身份证号:</MLabel>
                        </PrependContent>
                    </MTextField>
                </MCol>
                <MCol Cols="3" Class="d-flex align-center justify-center">
                    <MTextField Clearable Solo
                                Dense Class="customer-input" @bind-Value="ViewModel.QueryModel.OutpatientNumberString">
                        <PrependContent>
                            <MLabel Class="mr-2">被试编号:</MLabel>
                        </PrependContent>
                    </MTextField>
                </MCol>
                <MCol Cols="3" Class="d-flex align-center justify-center">
                    <MTextField Clearable Solo
                                Dense Class="customer-input" @bind-Value="ViewModel.QueryModel.Name">
                        <PrependContent>
                            <MLabel Class="mr-2">姓名:</MLabel>
                        </PrependContent>
                    </MTextField>
                </MCol>
                <MCol Cols="1" Class="d-flex align-start justify-space-between">
                    <MButton Class="customer-button"
                             Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;"
                             OnClick="@(async () => await ViewModel.SearchCommand.Execute())">
                        搜索
                    </MButton>
                    <MButton Class="customer-button"
                             Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;"
                             OnClick="async () => await ViewModel.OpenAddDialogCommand.Execute()">新增被试
                    </MButton>
                </MCol>
            </MRow>
        </MForm>
        <MCard Class="ma-8">
            <MDataTable

                OnOptionsUpdate="async (op) => await ViewModel.SetOptionsCommand.Execute(op)"
                TItem="PatientModel"
                Stripe
                @key="ViewModel.Total"
                Style="font-size: 1.25rem;"
                Loading="ViewModel.Loading"
                Headers="_headers"
                Items="ViewModel.PatientList"
                ServerItemsLength="ViewModel.Total"
                FooterProps="@(props =>
                             {
                                 props.ShowFirstLastPage = true;
                                 props.ShowCurrentPage = true;
                                 props.PageText = @"共{2}条数据";
                                 props.DisableItemsPerPage = true;
                             })"
                ItemsPerPage="@ViewModel.Options.ItemsPerPage">
                <HeaderColContent Context="header">
                    <MLabel Style="font-weight: 400;font-size: 1.25rem;color: #28333E;">@header.Text</MLabel>
                </HeaderColContent>
                <ItemColContent Context="item">
                    @if (item.Header.Value == "actions")
                    {
                        <div class="d-flex align-center justify-center">
                                <MButton Style="font-size: 1.25rem;" Color="blue" Class="text-decoration-underline"
                                         OnClick="async () => await  ViewModel.OpenRecordListCommand.Execute(item.Item.Id)"
                                         Plain>
                                    检测记录
                                </MButton>
                            <MButton Style="font-size: 1.25rem;"
                                     OnClick="async () => await ViewModel.OpenEditDialogCommand.Execute(item.Item)"
                                     Color="blue"
                                     Class="text-decoration-underline"
                                     Plain>
                                编辑信息
                            </MButton>
                            <MButton Style="font-size: 1.25rem;"
                                     OnClick="async () => await  ViewModel.DeleteCommand.Execute(item.Item.Id) " Color="red"
                                     Class="text-decoration-underline" Plain>
                                删除
                            </MButton>
                        </div>
                    }
                    else
                    {
                        <MLabel Style="font-size: 1.25rem;color: #28333E;"> @item.Value</MLabel>
                    }
                </ItemColContent>
            </MDataTable>

        </MCard>
        <PModal
            Width="942"
            Persistent="false"
            @bind-Value="ViewModel.IsShowAddPatient">
            <TitleContent>
                <div style="flex-grow: 1;text-align: right;font-size: 1.5rem">新增被试</div>
            </TitleContent>
            <ChildContent>
                <HX.HRV.SCI.Shared.Pages.Client.Dialog.PatientDialog
                    DialogTitle="@ViewModel.PatientDialogTitle"
                    IsOnlyAdd="true"
                    CloseDialog="async () => await  ViewModel.CloseAddDialogCommand.Execute()"
                    PatientModel="@ViewModel.DialogData">
                </HX.HRV.SCI.Shared.Pages.Client.Dialog.PatientDialog>
            </ChildContent>
        </PModal>
        <PModal @bind-Value="ViewModel.IsShowRecordList">
            <TitleContent>
                <div style="flex-grow: 1;text-align: right;font-size: 1.5rem">检测记录</div>
            </TitleContent>
            <ChildContent>
                <RecordList PatientId="@ViewModel.ClickedId">
                </RecordList>
            </ChildContent>
        </PModal>
    </div>
}

@code {


    [Inject] IPopupService PopupService { get; set; }
    private IDisposable _interactionSubscriptions;

    protected override void OnInitialized()
    {
        _interactionSubscriptions = new CompositeDisposable(
            ViewModel.ConfirmDeleteInteraction.RegisterHandler(async context =>
            {
                var confirmed = await PopupService.ConfirmAsync("警告", context.Input, AlertTypes.Warning);
                context.SetOutput(confirmed);
            }),
            ViewModel.ShowSnackbarInteraction.RegisterHandler(async context =>
            {
                await PopupService.EnqueueSnackbarAsync(context.Input.Item1, context.Input.Item2 ? AlertTypes.Success : AlertTypes.Error);
                context.SetOutput(Unit.Default);
            })
        );
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    private List<DataTableHeader<PatientModel>> _headers => new()
    {
        new()
        {
            Text = "姓名",
            Align = DataTableHeaderAlign.Start,
            Sortable = false,
            Value = nameof(PatientModel.Name)
        },
        new() { Text = "被试编号", Value = nameof(PatientModel.OutpatientNumberString) },
        new() { Text = "身份证号", Value = nameof(PatientModel.CardId) },
        new() { Text = "年龄", Value = nameof(PatientModel.Age) },
        new() { Text = "性别", Value = nameof(PatientModel.Sex) },
        new() { Text = "身高", Value = nameof(PatientModel.Height) },
        new() { Text = "体重", Value = nameof(PatientModel.Weight) },
        new() { Text = "被试来源", Value = nameof(PatientModel.Source) },
        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Width = "100px",
            Align = DataTableHeaderAlign.Center,
        }
    };

    public new void Dispose()
    {
        _interactionSubscriptions?.Dispose();
        base.Dispose();
    }

}