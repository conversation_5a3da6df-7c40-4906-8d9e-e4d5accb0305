﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace UFU.IoT.Models
{
    /// <summary>
    /// 日志
    /// </summary>
    [Table("iot_log")]
    public class LogModel
    {
        /// <summary>
        /// 数据编号
        /// </summary>
        [Column("id"), Key, Display(Name = "数据编号")]
        public string Id { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        [Column("device_id"), Display(Name = "设备编号")]
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备类型编号
        /// </summary>
        [Column("type_id"), Display(Name = "设备类型")]
        public string TypeId { get; set; }

        /// <summary>
        /// 数据时间
        /// </summary>
        [Column("data_time"), Display(Name = "数据时间"), DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        [Column("msg_type"), Display(Name = "消息类型")]
        public MsgType MsgType { get; set; }

        /// <summary>
        /// 设备数据
        /// </summary>
        [Column("data", TypeName = "jsonb"), Display(Name = "设备数据")]
        public string Data { get; set; }
    }
}
