{"version": 3, "sources": ["node_modules/browser-pack/_prelude.js", "components/core/JsInterop/ObservableApi/mutationObserver.ts", "components/core/JsInterop/ObservableApi/observableApi.ts", "components/core/JsInterop/ObservableApi/resizeObserver.ts", "components/core/JsInterop/interop.ts", "components/core/JsInterop/modules/components/backtopHelper.ts", "components/core/JsInterop/modules/components/downloadHelper.ts", "components/core/JsInterop/modules/components/export.ts", "components/core/JsInterop/modules/components/iconHelper.ts", "components/core/JsInterop/modules/components/inputHelper.ts", "components/core/JsInterop/modules/components/mentionsHelper.ts", "components/core/JsInterop/modules/components/modalHelper.ts", "components/core/JsInterop/modules/components/overlay.ts", "components/core/JsInterop/modules/components/overlayHelper.ts", "components/core/JsInterop/modules/components/tableHelper.ts", "components/core/JsInterop/modules/components/uploadHelper.ts", "components/core/JsInterop/modules/dom/dragHelper.ts", "components/core/JsInterop/modules/dom/eventHelper.ts", "components/core/JsInterop/modules/dom/exports.ts", "components/core/JsInterop/modules/dom/infoHelper.ts", "components/core/JsInterop/modules/dom/manipulationHelper.ts", "components/core/JsInterop/modules/dom/types.ts", "components/core/JsInterop/modules/enums.ts", "components/core/JsInterop/modules/stateProvider.ts", "components/core/JsInterop/modules/styleHelper.ts", "components/main.ts", "node_modules/@ant-design/colors/dist/index.js", "node_modules/@ctrl/tinycolor/dist/conversion.js", "node_modules/@ctrl/tinycolor/dist/css-color-names.js", "node_modules/@ctrl/tinycolor/dist/format-input.js", "node_modules/@ctrl/tinycolor/dist/from-ratio.js", "node_modules/@ctrl/tinycolor/dist/index.js", "node_modules/@ctrl/tinycolor/dist/interfaces.js", "node_modules/@ctrl/tinycolor/dist/public_api.js", "node_modules/@ctrl/tinycolor/dist/random.js", "node_modules/@ctrl/tinycolor/dist/readability.js", "node_modules/@ctrl/tinycolor/dist/to-ms-filter.js", "node_modules/@ctrl/tinycolor/dist/util.js"], "names": ["r", "e", "n", "t", "o", "i", "f", "c", "require", "u", "a", "Error", "code", "p", "exports", "call", "length", "1", "module", "infoHelper_1", "mutationObserver", "create", "key", "invoker", "isDotNetInvoker", "observer", "MutationObserver", "mutations", "observerCallback", "mutationObservers", "set", "observe", "element", "options", "get", "dom<PERSON>lement", "infoHelper", "disconnect", "this", "dispose", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "invokeMethodAsync", "Map", "resizeObserver_1", "mutationObserver_1", "__createBinding", "ResizeObserverEntry", "resizeObserver", "isResizeObserverSupported", "window", "ResizeObserver", "entries", "observerCallBack", "resizeObservers", "unobserve", "mappedEntries_1", "Array", "for<PERSON>ach", "entry", "mEntry", "borderBoxSize", "blockSize", "inlineSize", "contentBoxSize", "contentRect", "x", "y", "width", "height", "top", "right", "bottom", "left", "push", "stateProvider_1", "exports_1", "observable", "styleHelper_1", "export_1", "dragHelper_1", "colors_1", "log", "text", "console", "backtopHel<PERSON>", "backTop", "target", "dom", "domInfoHelper", "domManipulationHelper", "slideTo", "scrollTop", "downloadHelper", "triggerFileDownload", "fileName", "url", "anchorElement", "document", "createElement", "href", "download", "click", "remove", "backtopHelper_1", "overlayHelper_1", "uploadHelper_1", "downloadHelper_1", "mentionsHelper_1", "modalHelper_1", "inputHelper_1", "tableHelper_1", "iconHelper_1", "iconHelper", "createIconFromfontCN", "scriptUrl", "script", "querySelector", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "observableApi_1", "inputHelper", "getTextAreaInfo", "result", "scrollHeight", "currentStyle", "parseFloat", "getComputedStyle", "defaultView", "getPropertyValue", "Object", "is", "NaN", "registerResizeTextArea", "minRows", "maxRows", "objReference", "state", "objReferenceDict", "id", "eventCallbackRegistry", "resizeTextArea", "addEventListener", "resize", "style", "disposeResizeTextArea", "removeEventListener", "rowHeight", "oldHeight", "rows", "newHeight", "dims", "offsetHeight", "oldRows", "Math", "trunc", "max", "overflowY", "scrollWidth", "setSelectionStart", "position", "value", "selectionStart", "selectionEnd", "<PERSON><PERSON><PERSON><PERSON>", "getTextarea", "textAreaTag", "textarea", "tagName", "allTextareas", "getElementsByTagName", "setPopShowFlag", "show", "isPopShowFlag", "setEditorKeyHandler", "Mentions", "_this", "onkeydown", "ev", "__awaiter", "preventDefault", "_a", "sent", "getProp", "propName", "getCursorXY", "format", "replace", "textArea", "inputorValue", "pos", "start_range", "slice", "end_range", "substring", "html", "div_mirror", "flag", "className", "innerHTML", "parentNode", "append", "offsetLeft", "scrollLeft", "offsetTop", "modalHelper", "focusDialog", "selector", "count", "htmlElement", "ele", "hasAttribute", "activeElement", "blur", "setTimeout", "focus", "getActiveElement", "destroyAllDialog", "querySelectorAll", "<PERSON><PERSON><PERSON><PERSON>", "Placement", "TriggerBoundyAdjustMode", "Overlay", "getFirst<PERSON><PERSON>d<PERSON><PERSON>", "triggerIsWrappedInDiv", "childNodes", "childElement", "setVerticalCalculation", "placement", "LeftTop", "RightTop", "triggerTop", "triggerHeight", "container", "trigger", "overlayHeight", "constraints", "reversePositionValue", "BottomLeft", "Bottom", "BottomRight", "verticalOffset", "Left", "Right", "TopLeft", "Top", "TopRight", "triggerBottom", "LeftBottom", "RightBottom", "setHorizontalCalculation", "triggerLeft", "triggerWidth", "overlayWidth", "horizontalOffset", "triggerRight", "containerDimension", "overlayDimension", "prototype", "removeHiddenClass", "end", "overlay", "indexOf", "start", "lastIndexOf", "substr", "classList", "calculateScrollBarSizes", "isContainerBody", "scrollbarSize", "horizontalHeight", "innerHeight", "documentElement", "clientHeight", "verticalWidth", "innerWidth", "clientWidth", "offsetWidth", "blazorId", "resizing", "bind", "mutating", "attributes", "characterData", "childList", "subtree", "attributeOldValue", "characterDataOldValue", "onScroll", "diff", "isTriggerFixed", "lastScrollPosition", "pageYOffset", "containerInfo", "overlayInfo", "selectedVerticalPosition", "sanitizedPosition", "getAdjustedBottom", "calculatePosition", "overlayPreset", "duringInit", "lastStyleMutation", "cssText", "contains", "applyLocation", "firstTime", "offsetParent", "recentPlacement", "getKeyElementDimensions", "restoreInitialPlacement", "calculationsToPerform", "getNominalPositions", "size", "adjustToContainerBoundaries", "sanitizeCalculatedPositions", "triggerPrefixCls", "add", "__assign", "zIndex", "getMaxZIndex", "selectedHorizontalPosition", "getAdjustedRight", "containerBoundarySize", "getContainerBoundarySize", "directionsToCalculate", "Set", "boundyAdjustMode", "None", "horizontalPosition", "getHorizontalPosition", "verticalPosition", "getVerticalPosition", "initialPlacement", "appliedStylePositionMap", "horizontal", "vertical", "verticalCalculation", "horizontalCalculation", "logToConsole", "extraMessage", "info", "parentInfo", "parentElement", "containerId", "absoluteTop", "triggerInfo", "absoluteLeft", "triggerHtml", "outerHTML", "class", "appliedCssPosition", "overlay_style_top", "overlay_style_bottom", "overlay_style_left", "overlay_style_right", "pageXOffset", "containerIsBody", "scrollbars", "overlayConstraints", "placment", "applyPlacement", "stringMach", "currentPlacement", "newPlacement", "getInfo", "triggerPosition", "clientTop", "clientLeft", "has", "adjustVerticalToContainerBoundaries", "adjustHorizontalToContainerBoundaries", "setBodyBoundayrSize", "getWindow", "scroll", "getScroll", "bodyBoundarySize", "verticalScrollBasedOnParent", "horizontalScrollBasedOnParent", "InScroll", "parentIsInsignificant", "getOverlayVisibleHeight", "visibleIn", "boundary", "getOverlayVisibleWidth", "positionCache", "selectedPositionCache", "placementCache", "horizontalCalculationCache", "visibleWidthBeforeAdjustment", "visibleWidthInBodyBeforeAdjustment", "visibleWidthAfterAdjustment", "visibleWidthInBodyAfterAdjustment", "overlayFitsContainer", "isContainerOverBody", "getHorizontalAdjustment", "verticalCalculationCache", "visibleHeightBeforeAdjustment", "visibleHeightInBodyBeforeAdjustment", "visibleHeightAfterAdjustment", "visibleHeightInBodyAfterAdjustment", "getVerticalAdjustment", "type", "endExpressedAsLeft", "endExpressedAsTop", "reverseVerticalPlacementMap", "reverseHorizontalPlacementMap", "arrowCenterPlacementMatch", "triggerBoundyAdjustMode", "findAncestorWithZIndex", "arrowPointAtCenter", "isFixedPosition", "overlay_1", "overlayHelper", "addOverlayToContainer", "overlaySelector", "triggerSelector", "containerSelector", "overlayTop", "overlayLeft", "overlayPresets", "overlayElement", "containerElement", "triggerElement", "addElementTo", "overlayRegistry", "updateOverlayPosition", "deleteOverlayFromContainer", "addPreventEnterOnOverlayVisible", "<PERSON><PERSON><PERSON><PERSON>", "preventKeyOnCondition", "removePreventEnterOnOverlayVisible", "tableHelper", "bindTableScroll", "bodyRef", "tableRef", "headerRef", "scrollX", "scrollY", "bindScroll", "SetScrollPositionClassName", "unbindTableScroll", "pingLeft", "pingRight", "abs", "uploadHelper", "addFileClickEventListener", "btn", "fileClickEvent", "removeFileClickEventListener", "stopPropagation", "fileId", "currentTarget", "nodeValue", "getElementById", "clearFile", "getFileInfo", "files", "fileInfo", "file", "objectUrl", "getObjectURL", "name", "objectURL", "undefined", "URL", "createObjectURL", "webkitURL", "uploadFile", "index", "data", "headers", "instance", "percentMethod", "successMethod", "errorMethod", "method", "formData", "FormData", "req", "XMLHttpRequest", "onreadystatechange", "readyState", "status", "responseText", "upload", "onprogress", "event", "percent", "floor", "loaded", "onerror", "open", "header", "setRequestHeader", "send", "throttle", "fn", "threshold", "timeout", "Date", "args", "_i", "arguments", "curTime", "clearTimeout", "apply", "eventMap", "defaultOptions", "inViewport", "<PERSON><PERSON>", "getContainerPos", "rect", "_container", "getBoundingClientRect", "bindDrag", "_trigger", "_options", "onMousedown", "onMouseup", "onMousemove", "onResize", "unbindDrag", "resetContainerStyle", "_style", "_isFirst", "dragInViewport", "_state", "isInDrag", "mX", "clientX", "mY", "clientY", "domMaxY", "domMaxX", "getAttribute", "domStartX", "domStartY", "newDomX", "nowX", "nowY", "disX", "disY", "newDomY", "margin", "paddingBottom", "parseInt", "assign", "enableDraggable", "dragger", "disableDraggable", "resetModalPosition", "triggerEvent", "eventType", "eventName", "evt", "createEvent", "initEvent", "dispatchEvent", "addDomEventListener", "callback", "k", "obj", "json", "v", "Node", "Window", "_id", "debounce", "addDomEventListenerToFirstChild", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeDomEventListener", "addPreventKeys", "inputElement", "keys", "map", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "check", "removePreventKeys", "func", "wait", "immediate", "context", "callNow", "manipulationHelper_1", "eventHelper_1", "domTypes", "absolutePosition", "getElementAbsolutePos", "round", "res", "viewportElement", "box", "getFirstChildDomInfo", "hasFocus", "getInnerText", "innerText", "from", "reduce", "node", "nodeName", "toLowerCase", "zIndexAsString", "Number", "isNaN", "getElementsInfo", "elements", "infos", "el", "enums", "cachedScrollBarSize", "scrollIds", "<PERSON><PERSON><PERSON><PERSON>", "addElementToBody", "delElementFromBody", "addElement", "elementSelector", "prepend", "parent", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "delElementFrom", "delElement", "setDomAttribute", "copyElement", "copyElementAsRichText", "copy", "selection", "getSelection", "range", "rangeCount", "removeAllRanges", "createRange", "selectNode", "addRange", "successful", "execCommand", "err", "navigator", "clipboard", "writeText", "then", "error", "fallbackCopyTextToClipboard", "select", "msg", "noScroll", "option", "FocusBehavior", "FocusAtLast", "HTMLElement", "preventScroll", "HTMLInputElement", "HTMLTextAreaElement", "FocusAndSelectAll", "FocusAtFirst", "setSelectionRange", "scrollTo", "scrollIntoView", "behavior", "block", "inline", "smoothScrollTo", "duration", "perTick", "to", "cancelAnimationFrame", "requestAnimationFrame", "targetPageY", "timer", "setInterval", "currentY", "speed", "ceil", "clearInterval", "invoke<PERSON>ab<PERSON>ey", "currInput", "inputs", "next", "disableBodyScroll", "oldBodyCache", "scrollBarSize", "oldBodyCacheStack", "getScrollBarSize", "styleHelper", "css", "hasScrollbar", "overflow", "addCls", "enableBodyScroll", "force", "pop", "_b", "_c", "removeCls", "fresh", "outer", "widthContained", "inner", "outerStyle", "pointerEvents", "visibility", "widthScroll", "__esModule", "State", "disposeObj", "objReferenceName", "getInstance", "clsName", "addClsToFirstChild", "removeClsFromFirstChild", "matchMedia", "query", "matches", "getStyle", "styleProp", "cssAttributes", "split", "cssAttribute", "attribute", "setProperty", "hasOwnProperty", "interop", "AntDesign", "defineProperty", "tinycolor", "hueStep", "saturationStep", "saturationStep2", "brightnessStep1", "brightnessStep2", "lightColorCount", "darkColorCount", "darkColorMap", "opacity", "toHsv", "_ref", "g", "b", "hsv", "rgbToHsv", "h", "s", "toHex", "_ref2", "concat", "rgbToHex", "getHue", "light", "hue", "getSaturation", "saturation", "toFixed", "getValue", "generate", "color", "opts", "patterns", "pColor", "inputToRGB", "colorString", "_hsv", "_colorString", "theme", "_ref3", "rgb1", "backgroundColor", "rgb2", "amount", "presetPrimaryColors", "red", "volcano", "orange", "gold", "yellow", "lime", "green", "cyan", "blue", "geekblue", "purple", "magenta", "grey", "presetPalettes", "presetDarkPalettes", "primary", "numberInputToObject", "parseIntFromHex", "convertHexToDecimal", "convertDecimalToHex", "rgbaToArgbHex", "rgbaToHex", "hsvToRgb", "hslToRgb", "rgbToHsl", "rgbToRgb", "util_1", "hue2rgb", "q", "d", "toString", "val", "bound01", "min", "l", "mod", "allow3Char", "hex", "pad2", "startsWith", "char<PERSON>t", "join", "allow4Char", "names", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "goldenrod", "gray", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavenderblush", "lavender", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "limegreen", "linen", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "rebeccapurple", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellowgreen", "isValidCSSUnit", "stringInputToObject", "conversion_1", "css_color_names_1", "rgb", "ok", "String", "convertToPercentage", "boundAlpha", "CSS_UNIT", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "matchers", "RegExp", "rgba", "hsl", "hsla", "hsva", "hex3", "hex6", "hex4", "hex8", "trim", "named", "match", "exec", "Boolean", "legacyRandom", "fromRatio", "index_1", "ratio", "newColor", "TinyColor", "random", "format_input_1", "isDark", "getBrightness", "isLight", "toRgb", "getLuminance", "RsRGB", "GsRGB", "BsRGB", "R", "pow", "G", "B", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "roundA", "isMonochrome", "toHsl", "toHsvString", "toHslString", "toHexString", "toHex8", "toHex8String", "toHexShortString", "allowShortChar", "toRgbString", "toPercentageRgb", "fmt", "toPercentageRgbString", "rnd", "to<PERSON>ame", "formatSet", "formattedString", "has<PERSON><PERSON><PERSON>", "toNumber", "clone", "lighten", "clamp01", "brighten", "darken", "tint", "mix", "shade", "desaturate", "saturate", "greyscale", "spin", "analogous", "results", "slices", "part", "ret", "complement", "monochromatic", "modification", "splitcomplement", "onBackground", "background", "fg", "bg", "triad", "polyad", "tetrad", "increment", "equals", "originalInput", "gradientType", "<PERSON><PERSON><PERSON><PERSON>", "m", "k2", "desc", "getOwnPropertyDescriptor", "writable", "configurable", "enumerable", "__exportStar", "default", "bounds", "getColorInfo", "bounds_1", "defineColor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "randomWithin", "seed", "bound", "sMin", "lowerBounds", "sMax", "bMin", "bMax", "saturationRange", "brightnessRange", "totalColors", "colors", "colorInput", "num", "namedColor", "find", "parsed", "luminosity", "H", "S", "s1", "v1", "s2", "v2", "mostReadable", "isReadable", "readability", "color1", "color2", "c1", "c2", "wcag2", "level", "readabilityLevel", "baseColor", "colorList", "bestColor", "bestScore", "includeFallbackColors", "colorList_1", "score", "to<PERSON><PERSON><PERSON><PERSON>", "firstColor", "secondColor", "hex8String", "secondHex8String", "isOnePointZero", "isPercentage", "isPercent"], "mappings": "AAAA,CAAA,SAAAA,EAAAC,EAAAC,EAAAC,GAAA,SAAAC,EAAAC,EAAAC,GAAA,GAAA,CAAAJ,EAAAG,GAAA,CAAA,GAAA,CAAAJ,EAAAI,GAAA,CAAA,IAAAE,EAAA,YAAA,OAAAC,SAAAA,QAAA,GAAA,CAAAF,GAAAC,EAAA,OAAAA,EAAAF,EAAA,CAAA,CAAA,EAAA,GAAAI,EAAA,OAAAA,EAAAJ,EAAA,CAAA,CAAA,EAAA,MAAAK,EAAA,IAAAC,MAAA,uBAAAN,EAAA,GAAA,GAAAO,KAAA,mBAAAF,CAAA,CAAAG,EAAAX,EAAAG,GAAA,CAAAS,QAAA,EAAA,EAAAb,EAAAI,GAAA,GAAAU,KAAAF,EAAAC,QAAA,SAAAd,GAAA,OAAAI,EAAAH,EAAAI,GAAA,GAAAL,IAAAA,CAAA,CAAA,EAAAa,EAAAA,EAAAC,QAAAd,EAAAC,EAAAC,EAAAC,CAAA,CAAA,CAAA,OAAAD,EAAAG,GAAAS,OAAA,CAAA,IAAA,IAAAL,EAAA,YAAA,OAAAD,SAAAA,QAAAH,EAAA,EAAAA,EAAAF,EAAAa,OAAAX,CAAA,GAAAD,EAAAD,EAAAE,EAAA,EAAA,OAAAD,CAAA,EAAA,CAAAa,EAAA,CAAA,SAAAT,EAAAU,EAAAJ,G,uDCAC,IAAAK,EAAAX,EAAA,2BAAA,EAED,SAAAY,KAKSA,EAAAC,OAAP,SAAcC,EAAaC,EAASC,GAKhCC,GALgCD,EAAA,KAAA,IAAAA,EAAA,CAAA,EAI9BA,GACS,IAAIE,iBAAiB,SAAAC,GAAa,OAAAP,EAAiBQ,iBAAiBD,EAAlCJ,CAAA,CAAqD,CAAvF,EAEA,IAAIG,iBAAiB,SAAAC,GAAa,OAAAJ,EAAAI,CAAA,CAAkB,CAApD,EAEbP,EAAiBS,kBAAkBC,IAAIR,EAAKG,CAA5C,CACD,EAEML,EAAAW,QAAP,SAAeT,EAAaU,EAASC,GAC7BR,EAAWL,EAAiBS,kBAAkBK,IAAIZ,CAAvC,EACbG,IACEU,EAAahB,EAAAiB,WAAcF,IAAIF,CAAlB,EACjBP,EAASM,QAAQI,EAAYF,CAA7B,EAEH,EAEMb,EAAAiB,WAAP,SAAkBf,GACVG,EAAWa,KAAKT,kBAAkBK,IAAIZ,CAA3B,EACbG,GACFA,EAASY,WAAT,CAEH,EAEMjB,EAAAmB,QAAP,SAAejB,GACbgB,KAAKD,WAAWf,CAAhB,EACAgB,KAAKT,kBAAL,OAA8BP,CAA9B,CACD,EAEcF,EAAAQ,iBAAf,SAAgCD,EAAWJ,GAEnCiB,EAAcC,KAAKC,UAAUf,CAAf,EACpBJ,EAAQoB,kBAAkB,SAAUH,CAApC,CACD,EAvCcpB,EAAAS,kBAAmD,IAAIe,IAF3D9B,EAAAM,iBA0CbA,C,gPC5CCyB,G,gBAAArC,EAAA,kBAAA,GACDsC,GADUC,EAAAjC,EAAA+B,EAAA,iBAAA,QAAA,EACVrC,EAAA,oBAAA,GAASuC,EAAAjC,EAAAgC,EAAA,kBAAA,C,yHCDR,IAAA3B,EAAAX,EAAA,2BAAA,EAGDwC,EAAA,aAOA,SAAAC,KAESA,EAAAC,0BAAP,WACE,MAAO,mBAAoBC,MAC5B,EAKMF,EAAA5B,OAAP,SAAcC,EAAKC,EAASC,GAKxBC,GALwBD,EAAA,KAAA,IAAAA,EAAA,CAAA,EAItBA,GACS,IAAI4B,eAAe,SAACC,EAAS5B,GAAa,OAAAwB,EAAeK,iBAAiBD,EAAS5B,EAAzCF,CAAA,CAA2D,CAArG,EAEA,IAAI6B,eAAe,SAACC,EAAS5B,GAAa,OAAAF,EAAQ8B,EAAR5B,CAAA,CAA0B,CAApE,EAEbwB,EAAeM,gBAAgBzB,IAAIR,EAAKG,CAAxC,CACD,EAEMwB,EAAAlB,QAAP,SAAeT,EAAaU,GACpBP,EAAWwB,EAAeM,gBAAgBrB,IAAIZ,CAAnC,EACbG,IACEU,EAAahB,EAAAiB,WAAcF,IAAIF,CAAlB,EACjBP,EAASM,QAAQI,CAAjB,EAEH,EAEMc,EAAAZ,WAAP,SAAkBf,GACVG,EAAWa,KAAKiB,gBAAgBrB,IAAIZ,CAAzB,EACbG,GACFA,EAASY,WAAT,CAEH,EAEMY,EAAAO,UAAP,SAAiBlC,EAAaU,GACtBP,EAAWa,KAAKiB,gBAAgBrB,IAAIZ,CAAzB,EAEbG,IACEU,EAAahB,EAAAiB,WAAcF,IAAIF,CAAlB,EACjBP,EAAS+B,UAAUrB,CAAnB,EAEH,EAEMc,EAAAV,QAAP,SAAejB,GACbgB,KAAKD,WAAWf,CAAhB,EACAgB,KAAKiB,gBAAL,OAA4BjC,CAA5B,CACD,EAEc2B,EAAAK,iBAAf,SAAgCD,EAAS5B,EAAUF,GACjD,IACQkC,EADJlC,IACIkC,EAAgB,IAAIC,MAC1BL,EAAQM,QAAQ,SAAAC,GACd,IACQC,EADJD,IACIC,EAAS,IAAIb,EACfY,EAAME,gBACRD,EAAOC,cAAgB,CACrBC,UAAWH,EAAME,cAAcC,UAC/BC,WAAYJ,EAAME,cAAcE,UAFX,GAMrBJ,EAAMK,iBACRJ,EAAOI,eAAiB,CACtBF,UAAWH,EAAMK,eAAeF,UAChCC,WAAYJ,EAAMK,eAAeD,UAFX,GAMtBJ,EAAMM,cACRL,EAAOK,YAAc,CACnBC,EAAGP,EAAMM,YAAYC,EACrBC,EAAGR,EAAMM,YAAYE,EACrBC,MAAOT,EAAMM,YAAYG,MACzBC,OAAQV,EAAMM,YAAYI,OAC1BC,IAAKX,EAAMM,YAAYK,IACvBC,MAAOZ,EAAMM,YAAYM,MACzBC,OAAQb,EAAMM,YAAYO,OAC1BC,KAAMd,EAAMM,YAAYQ,IARL,GAcvBjB,EAAckB,KAAKd,CAAnB,EAEH,CAlCD,EAoCMrB,EAAcC,KAAKC,UAAUe,CAAf,EACpBlC,EAAQoB,kBAAkB,SAAUH,CAApC,EAEH,EArFcS,EAAAM,gBAA+C,IAAIX,IAPvD9B,EAAAmC,eA8FbA,C,gPCxGA2B,G,6BAAApE,EAAA,yBAAA,GAEAqE,GAFS9B,EAAAjC,EAAA8D,EAAA,OAAA,EACT9D,EAAAgE,WAAAtE,EAAA,+BAAA,EACAA,EAAA,uBAAA,GACAuE,GADShC,EAAAjC,EAAA+D,EAAA,eAAA,EAAe9B,EAAAjC,EAAA+D,EAAA,UAAA,EAAU9B,EAAAjC,EAAA+D,EAAA,uBAAA,EAAuB9B,EAAAjC,EAAA+D,EAAA,aAAA,EACzDrE,EAAA,uBAAA,GACAwE,GADSjC,EAAAjC,EAAAiE,EAAA,aAAA,EACTvE,EAAA,6BAAA,GAWAyE,GAVElC,EAAAjC,EAAAkE,EAAA,eAAA,EACAjC,EAAAjC,EAAAkE,EAAA,YAAA,EACAjC,EAAAjC,EAAAkE,EAAA,aAAA,EACAjC,EAAAjC,EAAAkE,EAAA,gBAAA,EACAjC,EAAAjC,EAAAkE,EAAA,aAAA,EACAjC,EAAAjC,EAAAkE,EAAA,eAAA,EACAjC,EAAAjC,EAAAkE,EAAA,aAAA,EACAjC,EAAAjC,EAAAkE,EAAA,cAAA,EACAjC,EAAAjC,EAAAkE,EAAA,gBAAA,EAEFxE,EAAA,0BAAA,GAEA0E,GAFSnC,EAAAjC,EAAAmE,EAAA,iBAAA,EAAiBlC,EAAAjC,EAAAmE,EAAA,kBAAA,EAAkBlC,EAAAjC,EAAAmE,EAAA,oBAAA,EAE5CzE,EAAA,oBAAA,GAASuC,EAAAjC,EAAAoE,EAAA,WAAA,eAAA,EAETpE,EAAAqE,IAAA,SAAoBC,GAClBC,QAAQF,IAAIC,CAAZ,CACD,C,uRCrBA,IAAAP,EAAArE,EAAA,gBAAA,EAED,SAAA8E,KACSA,EAAAC,QAAP,SAAeC,GACTC,EAAMZ,EAAAa,cAAcxD,IAAIsD,CAAlB,EACNC,EACFZ,EAAAc,sBAAsBC,QAAQH,EAAII,SAAlC,EAEAhB,EAAAc,sBAAsBC,QAAQ,CAA9B,CAEH,EARU9E,EAAAwE,cASbA,C,yDCXC,SAAAQ,K,wCACUA,EAAAC,oBAAP,SAA2BC,EAAUC,GACjC,IAAMC,EAAgBC,SAASC,cAAc,GAAvB,EACtBF,EAAcG,KAAOJ,EACrBC,EAAcI,SAAWN,MAAAA,EAAAA,EAAY,GACrCE,EAAcK,MAAd,EACAL,EAAcM,OAAd,CACH,EAPS1F,EAAAgF,eAQdA,C,kNCRCW,G,gBAAAjG,EAAA,iBAAA,GACDkG,GADU3D,EAAAjC,EAAA2F,EAAA,eAAA,EACVjG,EAAA,iBAAA,GACAmG,GADS5D,EAAAjC,EAAA4F,EAAA,eAAA,EACTlG,EAAA,gBAAA,GACAoG,GADS7D,EAAAjC,EAAA6F,EAAA,cAAA,EACTnG,EAAA,kBAAA,GACAqG,GADS9D,EAAAjC,EAAA8F,EAAA,gBAAA,EACTpG,EAAA,kBAAA,GACAsG,GADS/D,EAAAjC,EAAA+F,EAAA,gBAAA,EACTrG,EAAA,eAAA,GACAuG,GADShE,EAAAjC,EAAAgG,EAAA,aAAA,EACTtG,EAAA,eAAA,GACAwG,GADSjE,EAAAjC,EAAAiG,EAAA,aAAA,EACTvG,EAAA,eAAA,GACAyG,GADSlE,EAAAjC,EAAAkG,EAAA,aAAA,EACTxG,EAAA,cAAA,GAASuC,EAAAjC,EAAAmG,EAAA,YAAA,C,sNCRR,SAAAC,K,oCACQA,EAAAC,qBAAP,SAA4BC,GAC1B,IAGMC,EAHFlB,SAASmB,cAAc,oBAAoBF,EAAS,IAApD,KAGEC,EAASlB,SAASC,cAAc,QAAvB,GACRmB,aAAa,MAAOH,CAA3B,EACAC,EAAOE,aAAa,iBAAkBH,CAAtC,EACAjB,SAASqB,KAAKC,YAAYJ,CAA1B,EACD,EATWvG,EAAAoG,WAUdA,C,2ECVC,IAAArC,EAAArE,EAAA,gBAAA,EACDoE,EAAApE,EAAA,kBAAA,EACAkH,EAAAlH,EAAA,mCAAA,EAEA,SAAAmH,KAESA,EAAAC,gBAAP,SAAuB5F,GACrB,IACI6F,EACApC,EAFJ,OAAKzD,IACD6F,EAAS,GACTpC,EAAMZ,EAAAa,cAAcxD,IAAIF,CAAlB,IAEV6F,EAAA,aAAyBpC,EAAIqC,cAAgB,EAEzC9F,EAAQ+F,cACVF,EAAA,WAAuBG,WAAWhG,EAAQ+F,aAAa,cAAhC,EACvBF,EAAA,WAAuBG,WAAWhG,EAAQ+F,aAAa,cAAhC,EACvBF,EAAA,cAA0BG,WAAWhG,EAAQ+F,aAAa,iBAAhC,EAC1BF,EAAA,aAAyBG,WAAWhG,EAAQ+F,aAAa,gBAAhC,EACzBF,EAAA,UAAsBG,WAAWhG,EAAQ+F,aAAa,aAAhC,GAEf5E,OAAO8E,mBACdJ,EAAA,WAAuBG,WAAW7B,SAAS+B,YAAYD,iBAAiBjG,EAAS,IAA/C,EAAqDmG,iBAAiB,aAAtE,CAAX,EACvBN,EAAA,WAAuBG,WAAW7B,SAAS+B,YAAYD,iBAAiBjG,EAAS,IAA/C,EAAqDmG,iBAAiB,aAAtE,CAAX,EACvBN,EAAA,cAA0BG,WAAW7B,SAAS+B,YAAYD,iBAAiBjG,EAAS,IAA/C,EAAqDmG,iBAAiB,gBAAtE,CAAX,EAC1BN,EAAA,aAAyBG,WAAW7B,SAAS+B,YAAYD,iBAAiBjG,EAAS,IAA/C,EAAqDmG,iBAAiB,eAAtE,CAAX,EACzBN,EAAA,UAAsBG,WAAW7B,SAAS+B,YAAYD,iBAAiBjG,EAAS,IAA/C,EAAqDmG,iBAAiB,YAAtE,CAAX,GAGpBC,OAAOC,GAAGC,IAAKT,EAAA,SAAf,IACFA,EAAA,UAAsB,GACpBO,OAAOC,GAAGC,IAAKT,EAAA,YAAf,IACFA,EAAA,aAAyB,GACpBA,GAzBc,IA0BtB,EAEMF,EAAAY,uBAAP,SAA8BvG,EAA8BwG,EAAiBC,EAAiBC,GAC5F,GAAKA,EAaH,OATA9D,EAAA+D,MAAMC,iBAAiB5G,EAAQ6G,IAAMH,EACrC9D,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,SAAW,WAAclB,EAAYoB,eAAe/G,EAASwG,EAASC,CAA7C,CAAwD,EAC1HzG,EAAQgH,iBAAiB,QAASpE,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,QAA3E,EACAnB,EAAAuB,OAAO5H,OAAOW,EAAQ6G,GAAK,UAAW,WACpClB,EAAYoB,eAAe/G,EAASwG,EAASC,CAA7C,CACD,EAAE,CAAA,CAFH,EAGAf,EAAAuB,OAAOlH,QAAQC,EAAQ6G,GAAK,UAAW7G,CAAvC,EACA2F,EAAYoB,eAAe/G,EAASwG,EAASC,CAA7C,EACAzG,EAAQkH,MAAMD,OAAS,OAChB3G,KAAKsF,gBAAgB5F,CAArB,EAZPM,KAAK6G,sBAAsBnH,CAA3B,CAcH,EAEM2F,EAAAwB,sBAAP,SAA6BnH,GAC3BA,EAAQoH,oBAAoB,QAASxE,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,QAA9E,EACAnB,EAAAuB,OAAOzF,UAAUxB,EAAQ6G,GAAK,UAAW7G,CAAzC,EACA4C,EAAA+D,MAAMC,iBAAiB5G,EAAQ6G,IAAM,KACrCjE,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,SAAW,IACrD,EAEMlB,EAAAoB,eAAP,SAAsB/G,EAA8BwG,EAAiBC,GACnE,IAGIY,EAEAC,EAMAC,EAGAC,EAdAC,EAAOnH,KAAKsF,gBAAgB5F,CAArB,EACNyH,IAEDJ,EAAYI,EAAA,WACZC,EAAeD,EAAA,WAAqBA,EAAA,cAAwBA,EAAA,UAAoBA,EAAA,aAChFH,EAAYtB,WAAWhG,EAAQkH,MAAM5E,MAAzB,EAEZqF,EAAU3H,EAAQuH,KACtBvH,EAAQuH,KAAOf,EACfxG,EAAQkH,MAAM5E,OAAS,OAEnBiF,EAAOK,KAAKC,MAAM7H,EAAQ8F,aAAeuB,CAAlC,EACXrH,EAAQuH,KAAOI,EAEXH,EAAY,EACLf,GAFXc,EAAOK,KAAKE,IAAItB,EAASe,CAAlB,IAMLvH,EAAQkH,MAAM5E,QADdkF,GAFAD,EAAOd,GAEaY,EAAYK,GACG,KACnC1H,EAAQkH,MAAMa,UAAY,YAI1B/H,EAAQkH,MAAM5E,QADdkF,EAAYD,EAAOF,EAAYK,GACI,KACnC1H,EAAQkH,MAAMa,UAAY,UAExBT,IAAcE,IACE5E,EAAA+D,MAAMC,iBAAiB5G,EAAQ6G,IACrClG,kBAAkB,oBAAqBX,EAAQgI,YAAaR,CAAxE,CAEH,EAEM7B,EAAAsC,kBAAP,SAAyBjI,EAASkI,GAChB,GAAZA,IACEzE,EAAwBZ,EAAAa,cAAcxD,IAAIF,CAAlB,IAEtBkI,GAAYzE,EAAI0E,MAAMnJ,SACxByE,EAAI2E,eAAiBF,EACrBzE,EAAI4E,aAAeH,EAI1B,EApGUpJ,EAAA6G,YAsGbA,C,u6CC1GC2C,G,wCAkEkBA,EAAAC,YAAf,SAA2BvI,GACvB,IAAMwI,EAAc,WAChBC,EAAWzI,EACf,GAAIA,EAAQ0I,SAAWF,EAAa,CAC5BG,EAAe3I,EAAQ4I,qBAAqBJ,CAA7B,EACnB,GAA2B,GAAvBG,EAAa3J,OACb,KAAM,oEAEVyJ,EAAWE,EAAa,EAC3B,CAED,OAAOF,CACV,EA1EaH,EAAAO,eAAiB,SAAUC,GACrCR,EAAeS,cAAgBD,CAClC,EAEaR,EAAAU,oBAAsB,SAAUC,EAAejJ,GAAzB,IAAAkJ,EAAA5I,KAEjBgI,EAAeC,YAAYvI,CAA3B,EACNmJ,UAAY,SAAOC,GAAE,OAAAC,EAAAH,EAAA,KAAA,EAAA,KAAA,EAAA,W,iDAE1B,OAAKZ,EAAeS,cACN,WAAVK,EAAG9J,IAAH,CAAA,EAAA,IACA8J,EAAGE,eAAH,EACA,CAAA,EAAML,EAAStI,kBAAkB,YAA3B,IAHyB,CAAA,G,cAG/B4I,EAAAC,KAAA,E,mBACiB,aAAVJ,EAAG9J,IAAH,CAAA,EAAA,IACP8J,EAAGE,eAAH,EACA,CAAA,EAAML,EAAStI,kBAAkB,YAA3B,I,cAAN4I,EAAAC,KAAA,E,mBAEe,SAAVJ,EAAG9J,IAAH,CAAA,EAAA,IACL8J,EAAGE,eAAH,EACA,CAAA,EAAML,EAAStI,kBAAkB,aAA3B,I,OAAN4I,EAAAC,KAAA,E,8BAZsB,CAe7B,CACJ,EAEalB,EAAAmB,QAAU,SAAUxL,EAAgByL,GAG9C,OAFepB,EAAeC,YAAYtK,CAA3B,EAECyL,EACnB,EAEapB,EAAAqB,YAAc,SAAU3J,GAErB,SAAT4J,EAAmBzB,GAEnB,OADAA,EAAQA,EAAM0B,QAAQ,aAAc,GAA5B,CAEX,CAJD,IAAIC,EAAWxB,EAAeC,YAAYvI,CAA3B,EAKX+J,EAAeD,EAAS3B,MACxB6B,EAAMF,EAAS1B,eACf6B,EAAcF,EAAaG,MAAM,EAAGF,CAAtB,EAEdG,GADqB,EAArBF,EAAYjL,SAAYiL,EAAcA,EAAYG,UAAU,EAAGH,EAAYjL,OAAS,CAA9C,GAC1B+K,EAAaG,MAAMF,CAAnB,GACZK,EAAOT,EAAOK,CAAP,EAIPK,GAFJD,GADAA,GAAQ,kBACAT,EAAOO,CAAP,EAEShG,SAASC,cAAc,KAAvB,GAKbmG,GAJJD,EAAWE,UAAY,uBACvBF,EAAWG,UAAYJ,EACvBP,EAASY,WAAWC,OAAOL,CAA3B,EAE4BA,EAAWhF,cAAc,MAAzB,GAIxB5C,EAAO6H,EAAKK,WAAad,EAASe,WAAa,GAC/CtI,EAAMgI,EAAKO,UAAYhB,EAASjG,UAAY,GAGhD,OADAyG,EAAW9F,OAAX,EACO,CAAC9B,EAAMH,EACjB,EAeL+F,GA/EC,SAAAA,KAAaxJ,EAAAwJ,eAAAA,C,4ECAb,IAAAzF,EAAArE,EAAA,gBAAA,EAED,SAAAuM,KACSA,EAAAC,YAAP,SAAmBC,EAAkBC,GAArC,IAIUC,EAJVjC,EAAA5I,KACM8K,GAD+B,KAAA,IAAAF,IAAAA,EAAA,GACZ/G,SAASmB,cAAc2F,CAAvB,GACnBG,IACEA,EAAIC,aAAa,UAAjB,EAEFF,OADIA,EAA2BhH,SAASmH,gBACxCH,EAAaI,KAAb,EAEAC,WAAW,WACTJ,EAAIK,MAAJ,EACY,IAAM5I,EAAAa,cAAcgI,iBAAd,IACJT,GACRC,EAAQ,IACVhC,EAAK8B,YAAYC,EAAUC,EAAQ,CAAnC,CAGL,EAAE,EARH,EAWL,EAEMH,EAAAY,iBAAP,WACExH,SAASyH,iBAAiB,iBAA1B,EACGjK,QAAQ,SAAA1D,GAAK,OAAAkG,SAASqB,KAAKqG,YAAY5N,EAA1ByM,UAAA,CAAuC,CADvD,CAED,EAxBU5L,EAAAiM,YAyBbA,C,8DCtBYe,EAgBAC,E,+MArBXlJ,G,uEAAArE,EAAA,gBAAA,GACDkH,EAAAlH,EAAA,mCAAA,EACAsC,EAAAtC,EAAA,sCAAA,EAyDAwN,IAtDYF,EAAAA,EAAAhN,EAAAgN,YAAAhN,EAAAgN,UAAS,KACnBA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,YAAA,GAAA,cACAA,EAAAA,EAAA,WAAA,IAAA,aACAA,EAAAA,EAAA,OAAA,IAAA,SACAA,EAAAA,EAAA,YAAA,IAAA,eAIUC,EAAAA,EAAAjN,EAAAiN,0BAAAjN,EAAAiN,wBAAuB,KACjCA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,SAAA,GAAA,WAsMOC,EAAAC,mBAAP,SAA0BjM,EAAsBkM,GAC9C,GAAIA,EAEF,IAAI,IAAI7N,EAAI,EAAGA,EAAI2B,EAAQmM,WAAWnN,OAAQX,CAAA,GAAK,CACjD,IAAM+N,EAAepM,EAAQmM,WAAW9N,GACxC,GAAI+N,EAAa3B,UACf,OAAO2B,CACV,CAEH,OAAOpM,CACR,EAEMgM,EAAAK,uBAAP,SAA8BC,EAAsBpE,GAClD,GAAiB,QAAbA,EACF,OAAQoE,GACN,KAAKR,EAAUS,QACf,KAAKT,EAAUU,SACb,OAAO,SAASC,EAAoBC,EAAuBC,EAA6BC,EAA2BC,EAAuBC,GACxI,MAAO,CACLvK,IAAKkK,EACLhK,OAAQuJ,EAAQe,qBAAqBN,EAAYE,EAAU7G,aAAc+G,CAAjE,CAFH,CAIR,EACH,KAAKf,EAAUkB,WACf,KAAKlB,EAAUmB,OACf,KAAKnB,EAAUoB,YACb,OAAO,SAAST,EAAoBC,EAAuBC,EAA6BC,EAA2BC,EAAuBC,GAClI5E,EAA6B,CACjC3F,IAAKkK,EAAaC,EAAgBI,EAAYK,cADb,EAInC,OADAjF,EAASzF,OAASuJ,EAAQe,qBAAqB7E,EAAS3F,IAAKoK,EAAU7G,aAAc+G,CAAnE,EACX3E,CACR,EACH,KAAK4D,EAAUsB,KACf,KAAKtB,EAAUuB,MACb,OAAO,SAASZ,EAAoBC,EAAuBC,EAA6BC,EAA2BC,EAAuBC,GAClI5E,EAA6B,CACjC3F,IAAKkK,EAAcC,EAAgB,EAAMG,EAAgB,CADxB,EAInC,OADA3E,EAASzF,OAASuJ,EAAQe,qBAAqB7E,EAAS3F,IAAKoK,EAAU7G,aAAc+G,CAAnE,EACX3E,CACR,CA3BL,CA8BF,GAAiB,WAAbA,EACF,OAAQoE,GACN,KAAKR,EAAUwB,QACf,KAAKxB,EAAUyB,IACf,KAAKzB,EAAU0B,SACb,OAAO,SAASC,EAAuBf,EAAuBC,EAA6BC,EAA2BC,EAAuBC,GACrI5E,EAA6B,CACjCzF,OAAQgL,EAAgBf,EAAgBI,EAAYK,cADnB,EAInC,OADAjF,EAAS3F,IAAMyJ,EAAQe,qBAAqB7E,EAASzF,OAAQkK,EAAU7G,aAAc+G,CAAtE,EACR3E,CACR,EACH,KAAK4D,EAAU4B,WACf,KAAK5B,EAAU6B,YACb,OAAO,SAASF,EAAuBf,EAAuBC,EAA6BC,EAA2BC,EAAuBC,GAK3I,MAJmC,CACjCrK,OAAQgL,EACRlL,IAAKyJ,EAAQe,qBAAqBU,EAAed,EAAU7G,aAAc+G,CAApE,CAF4B,CAKpC,CAnBL,CAwBF,OADAxJ,QAAQF,IAAI,6EAA8EmJ,EAAWpE,CAArG,EACO8D,EAAQK,uBAAuBP,EAAUkB,WAAY,KAArD,CACR,EAEMhB,EAAA4B,yBAAP,SAAgCtB,EAAsBpE,GACpD,GAAiB,SAAbA,EACF,OAAQoE,GACN,KAAKR,EAAUwB,QACf,KAAKxB,EAAUkB,WACb,OAAO,SAASa,EAAqBC,EAAsBnB,EAA6BC,EAA2BmB,EAAsBjB,GACvI,MAAO,CACLpK,KAAMmL,EACNrL,MAAOwJ,EAAQe,qBAAqBc,EAAalB,EAAU3E,YAAa+F,CAAjE,CAFF,CAIR,EACH,KAAKjC,EAAUuB,MACf,KAAKvB,EAAUU,SACf,KAAKV,EAAU6B,YACb,OAAO,SAASE,EAAqBC,EAAsBnB,EAA6BC,EAA2BmB,EAAsBjB,GACjI5E,EAA+B,CACnCxF,KAAMmL,EAAcC,EAAehB,EAAYkB,gBADZ,EAIrC,OADA9F,EAAS1F,MAAQwJ,EAAQe,qBAAqB7E,EAASxF,KAAMiK,EAAU3E,YAAa+F,CAAnE,EACV7F,CACR,EAEH,KAAK4D,EAAUyB,IACf,KAAKzB,EAAUmB,OACb,OAAO,SAASY,EAAqBC,EAAsBnB,EAA6BC,EAA2BmB,EAAsBjB,GACjI5E,EAA+B,CACnCxF,KAAMmL,EAAeC,EAAe,EAAMC,EAAe,CADtB,EAIrC,OADA7F,EAAS1F,MAAQwJ,EAAQe,qBAAqB7E,EAASxF,KAAMiK,EAAU3E,YAAa+F,CAAnE,EACV7F,CACR,CA5BL,CA+BF,GAAiB,UAAbA,EACF,OAAQoE,GACN,KAAKR,EAAU0B,SACf,KAAK1B,EAAUoB,YACb,OAAO,SAASe,EAAsBH,EAAsBnB,EAA6BC,EAA2BmB,EAAsBjB,GAKxI,MAJmC,CACjCtK,MAAOyL,EACPvL,KAAMsJ,EAAQe,qBAAqBkB,EAActB,EAAU3E,YAAa+F,CAAlE,CAF2B,CAKpC,EACH,KAAKjC,EAAUsB,KACf,KAAKtB,EAAUS,QACf,KAAKT,EAAU4B,WACb,OAAO,SAASO,EAAsBH,EAAsBnB,EAA6BC,EAA2BmB,EAAsBjB,GAClI5E,EAA+B,CACnC1F,MAAOyL,EAAeH,EAAehB,EAAYkB,gBADd,EAIrC,OADA9F,EAASxF,KAAOsJ,EAAQe,qBAAqB7E,EAAS1F,MAAOmK,EAAU3E,YAAa+F,CAApE,EACT7F,CACR,CAnBL,CAwBF,OADA7E,QAAQF,IAAI,+EAAgFmJ,EAAWpE,CAAvG,EACO8D,EAAQK,uBAAuBP,EAAUkB,WAAY,KAArD,CACR,EAUMhB,EAAAe,qBAAP,SAA4B7E,EAAkBgG,EAA4BC,GACxE,OAAOD,EAAqBhG,EAAWiG,CACxC,EAEOnC,EAAAoC,UAAAC,kBAAR,WACE,IAAIC,EAAMhO,KAAKiO,QAAQ/D,UAAUgE,QAAQ,SAA/B,EACNC,EAAQnO,KAAKiO,QAAQ/D,UAAUkE,YAAY,IAAKJ,CAAxC,EACC,GAATG,GAEgB,MADdjE,EAAYlK,KAAKiO,QAAQ/D,UAAUmE,OAAOF,EAAQ,EAAGH,CAAzC,IAEdhO,KAAKiO,QAAQK,UAAUpK,OAAOgG,CAA9B,CAGL,EAEOwB,EAAAoC,UAAAS,wBAAR,WACMvO,KAAKwO,gBACPxO,KAAKyO,cAAgB,CACnBC,iBAAkB7N,OAAO8N,YAAc9K,SAAS+K,gBAAgBC,aAChEC,cAAejO,OAAOkO,WAAalL,SAAS+K,gBAAgBI,WAFzC,EAMrBhP,KAAKyO,cAAgB,CACnBC,iBAAkB1O,KAAKqM,UAAUjF,aAAepH,KAAKqM,UAAUwC,aAC/DC,cAAe9O,KAAKqM,UAAU4C,YAAcjP,KAAKqM,UAAU2C,WAFxC,CAKxB,EAEOtD,EAAAoC,UAAArO,QAAR,WACE2F,EAAAuB,OAAO5H,OAAO,aAAaiB,KAAKkP,SAAYlP,KAAKmP,SAASC,KAAKpP,IAAnB,EAA0B,CAAA,CAAtE,EACAoF,EAAAuB,OAAOlH,QAAQ,aAAaO,KAAKkP,SAAYlP,KAAKqM,SAAlD,EACAjH,EAAAuB,OAAOlH,QAAQ,aAAaO,KAAKkP,SAAYlP,KAAKsM,OAAlD,EACA9L,EAAA1B,iBAASC,OAAO,WAAWiB,KAAKkP,SAAYlP,KAAKqP,SAASD,KAAKpP,IAAnB,EAA0B,CAAA,CAAtE,EACAQ,EAAA1B,iBAASW,QAAQ,WAAWO,KAAKkP,SAAYlP,KAAKsM,QAAS,CACzDgD,WAAY,CAAA,EACZC,cAAe,CAAA,EACfC,UAAW,CAAA,EACXC,QAAS,CAAA,EACTC,kBAAmB,CAAA,EACnBC,sBAAuB,CAAA,CANkC,CAA3D,GASI3P,KAAKwO,gBACP3N,OAGAb,KAAKqM,WAHE3F,iBAAiB,SAAU1G,KAAK4P,SAASR,KAAKpP,IAAnB,CAAlC,CAKH,EAEO0L,EAAAoC,UAAA8B,SAAR,WACE,IAEUC,EAFN7P,KAAK8P,eACH9P,KAAK+P,qBAAuBlP,OAAOmP,cAC/BH,EAAOhP,OAAOmP,YAAchQ,KAAK+P,mBACvC/P,KAAK4H,SAAS3F,KAAO4N,EACrB7P,KAAK4H,SAASzF,OAASuJ,EAAQe,qBAAqBzM,KAAK4H,SAAS3F,IAAKjC,KAAKiQ,cAAczK,aAAcxF,KAAKkQ,YAAYrB,YAAlG,EACe,QAAlC7O,KAAKmQ,0BACPnQ,KAAKoQ,kBAAkBnO,IAAMjC,KAAK4H,SAAS3F,IAC3CjC,KAAKiO,QAAQrH,MAAM3E,IAAMjC,KAAKoQ,kBAAkBnO,IAAM,OAEtDjC,KAAKoQ,kBAAkBjO,OAASnC,KAAKqQ,kBAAL,EAChCrQ,KAAKiO,QAAQrH,MAAMzE,OAASnC,KAAKoQ,kBAAkBjO,OAAS,MAE9DnC,KAAK+P,mBAAqBlP,OAAOmP,aAWnChQ,KAAKsQ,kBAAkB,CAAA,EAAM,CAAA,EAAOtQ,KAAKuQ,aAAzC,CAEH,EAEO7E,EAAAoC,UAAAqB,SAAR,SAAiBpO,EAAS5B,GAEpBa,KAAKwQ,WACPxQ,KAAKwQ,WAAa,CAAA,EAGpBxQ,KAAKsQ,kBAAkB,CAAA,EAAM,CAAA,EAAOtQ,KAAKuQ,aAAzC,CACD,EAUO7E,EAAAoC,UAAAuB,SAAR,SAAiBhQ,GACXW,KAAKwQ,WACPxQ,KAAKwQ,WAAa,CAAA,EAGhBxQ,KAAKyQ,oBAAsBzQ,KAAKsM,QAAQ1F,MAAM8J,UAChD1Q,KAAKyQ,kBAAoBzQ,KAAKsM,QAAQ1F,MAAM8J,QAC5C1Q,KAAKsQ,kBAAkB,CAAA,EAAM,CAAA,EAAOtQ,KAAKuQ,aAAzC,EAGH,EAEM7E,EAAAoC,UAAA7N,QAAP,WACEmF,EAAAuB,OAAO1G,QAAQ,aAAaD,KAAKkP,QAAjC,EACA1O,EAAA1B,iBAASmB,QAAQ,WAAWD,KAAKkP,QAAjC,EACIlP,KAAKqM,UAAUsE,SAAS3Q,KAAKiO,OAA7B,GACFjO,KAAKqM,UAAUd,YAAYvL,KAAKiO,OAAhC,GAGEjO,KAAKwO,gBACP3N,OAGAb,KAAKqM,WAHEvF,oBAAoB,SAAU9G,KAAK4P,QAA1C,CAKH,EAEMlE,EAAAoC,UAAAwC,kBAAP,SAAyBM,EAAwBC,EAAmBN,GAElE,IAF+CM,EAAA,KAAA,IAAAA,EAAA,CAAA,EAE1CA,IAAc7Q,KAAKiO,QAAQ6C,aAIhC,OAAKP,GAAkBvQ,KAAKsM,QAAQwE,cAMpC9Q,KAAK+P,mBAAqBlP,OAAOmP,YACjChQ,KAAK+Q,gBAAkB/Q,KAAKgM,UAC5BhM,KAAKuQ,cAAgBA,EAErBvQ,KAAKgR,wBAAwBH,CAA7B,EAEA7Q,KAAKiR,wBAAL,EAGAjR,KAAKkR,sBAAwBlR,KAAKmR,oBAAL,EACS,EAAlCnR,KAAKkR,sBAAsBE,MAC7BpR,KAAKqR,4BAAL,EAGFrR,KAAKsR,4BAAL,EAEIV,GACF5Q,KAAK4Q,cAAL,EAEK5Q,KAAKoQ,oBAxBLpQ,KAAKiO,QAAQK,UAAUqC,SAAS3Q,KAAKuR,iBAAmB,SAAxD,GACHvR,KAAKiO,QAAQK,UAAUkD,IAAIxR,KAAKuR,iBAAmB,SAAnD,EAEKvR,KAAK4H,SAsBf,EAWO8D,EAAAoC,UAAAwD,4BAAR,WACEtR,KAAKoQ,kBAAiBqB,EAAA,GAAQzR,KAAK4H,QAAb,EACtB5H,KAAKoQ,kBAAkBsB,OAASnP,EAAAa,cAAcuO,aAAd,EAChC3R,KAAKoQ,kBAAkBpE,UAAYhM,KAAKgM,UACA,SAApChM,KAAK4R,2BACP5R,KAAKoQ,kBAAkBlO,MAAQ,MAG/BlC,KAAKoQ,kBAAkBhO,KAAO,KAC9BpC,KAAKoQ,kBAAkBlO,MAAQlC,KAAK6R,iBAAL,GAGK,QAAlC7R,KAAKmQ,yBACPnQ,KAAKoQ,kBAAkBjO,OAAS,MAGhCnC,KAAKoQ,kBAAkBnO,IAAM,KAC7BjC,KAAKoQ,kBAAkBjO,OAASnC,KAAKqQ,kBAAL,EAEnC,EAUO3E,EAAAoC,UAAAqD,oBAAR,WACEnR,KAAK8R,sBAAwB9R,KAAK+R,yBAAL,EAC7B,IAAM/P,EAAShC,KAAK8R,sBAAsB3P,OAASnC,KAAK8R,sBAAsB7P,IACxEF,EAAQ/B,KAAK8R,sBAAsB5P,MAAQlC,KAAK8R,sBAAsB1P,KACtE4P,EAAwB,IAAIC,IA4BlC,OA1BIjS,KAAKkS,kBAAoBzG,EAAwB0G,MAAQpQ,EAAQ/B,KAAKkQ,YAAYlB,aAAehP,KAAKwO,gBAChE,SAApCxO,KAAK4R,2BACP5R,KAAK4H,SAASxF,KAAO,EAErBpC,KAAK4H,SAAS1F,MAAQ,GAGlBkQ,EAAqBpS,KAAKqS,sBAAL,EAC3BrS,KAAK4H,SAASxF,KAAOgQ,EAAmBhQ,KACxCpC,KAAK4H,SAAS1F,MAAQkQ,EAAmBlQ,MACzC8P,EAAsBR,IAAI,YAA1B,GAGExR,KAAKkS,kBAAoBzG,EAAwB0G,MAAQnQ,EAAShC,KAAKkQ,YAAYrB,cAAgB7O,KAAKwO,gBACpE,QAAlCxO,KAAKmQ,yBACPnQ,KAAK4H,SAAS3F,IAAM,EAEpBjC,KAAK4H,SAASzF,OAAS,GAInBmQ,EAAmBtS,KAAKuS,oBAAL,EACzBvS,KAAK4H,SAAS3F,IAAMqQ,EAAiBrQ,IACrCjC,KAAK4H,SAASzF,OAASmQ,EAAiBnQ,OACxC6P,EAAsBR,IAAI,UAA1B,GAEKQ,CACR,EAQOtG,EAAAoC,UAAAmD,wBAAR,WACMjR,KAAKgM,YAAchM,KAAKwS,mBAC1BxS,KAAKgM,UAAYhM,KAAKwS,iBACtBxS,KAAK4R,2BAA6BlG,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAoD0G,WACtF1S,KAAKmQ,yBAA2BzE,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAoD2G,SACpF3S,KAAK4S,oBAAsBlH,EAAQK,uBAAuB/L,KAAKgM,UAAWhM,KAAKmQ,wBAApD,EAC3BnQ,KAAK6S,sBAAwBnH,EAAQ4B,yBAAyBtN,KAAKgM,UAAWhM,KAAK4R,0BAAtD,EAEhC,EAOOlG,EAAAoC,UAAAgF,aAAR,SAAqBC,GAAA,KAAA,IAAAA,IAAAA,EAAA,IACnBhQ,QAAQF,IAAIkQ,EAAe,qBAAsB/S,KAAK4H,SACpD,QACA,CACEsH,SAAUlP,KAAKkP,SACf7C,UAAW,CACT2G,KAAMhT,KAAKiQ,cACXgD,WAAY,CACVpE,aAAc7O,KAAKqM,UAAU6G,cAAcrE,aAC3CG,YAAahP,KAAKqM,UAAU6G,cAAclE,YAC1CzE,WAAYvK,KAAKqM,UAAU6G,cAAc3I,WACzChH,UAAWvD,KAAKqM,UAAU6G,cAAc3P,SAJ9B,EAMZ4P,YAAanT,KAAKqM,UAAU9F,GAC5BuL,sBAAuB9R,KAAK8R,qBATnB,EAWXxF,QAAS,CACP8G,YAAapT,KAAKqT,YAAYD,YAC9BE,aAActT,KAAKqT,YAAYC,aAC/BzE,aAAc7O,KAAKqT,YAAYxE,aAC/BG,YAAahP,KAAKqT,YAAYrE,YAC9B5H,aAAcpH,KAAKqT,YAAYjM,aAC/B6H,YAAajP,KAAKqT,YAAYpE,YAC9BiD,iBAAkBlS,KAAKkS,iBAEvBqB,YAAavT,KAAKsM,QAAQkH,UAC1BjC,iBAAkBvR,KAAKuR,gBAVhB,EAYTtD,QAAS,CACPY,aAAc7O,KAAKkQ,YAAYrB,aAC/BG,YAAahP,KAAKkQ,YAAYlB,YAC9B5H,aAAcpH,KAAKkQ,YAAY9I,aAC/B6H,YAAajP,KAAKkQ,YAAYjB,YAC9BwE,MAAOzT,KAAKiO,QAAQ/D,UACpBwJ,mBAAoB,CAClBC,kBAAmB3T,KAAKiO,QAAQrH,MAAM3E,IACtC2R,qBAAsB5T,KAAKiO,QAAQrH,MAAMzE,OACzC0R,mBAAoB7T,KAAKiO,QAAQrH,MAAMxE,KACvC0R,oBAAqB9T,KAAKiO,QAAQrH,MAAM1E,KAJtB,CANb,EAaTrB,OAAQ,CACN8N,YAAa9N,OAAO8N,YACpBI,WAAYlO,OAAOkO,WACnBgF,YAAalT,OAAOkT,YACpB/D,YAAanP,OAAOmP,WAJd,EAMRpB,gBAAiB,CACfC,aAAchL,SAAS+K,gBAAgBC,aACvCG,YAAanL,SAAS+K,gBAAgBI,YACtCgF,gBAAiBhU,KAAKwO,eAHP,EAKjByF,WAAYjU,KAAKyO,cACjB8B,cAAevQ,KAAKuQ,cACpB2D,mBAAoBlU,KAAKkU,mBACzBtM,SAAU5H,KAAK4H,SACfwI,kBAAmBpQ,KAAKoQ,kBACxB+D,SAAU,CACT3B,iBAAkBxS,KAAKwS,iBACvBzB,gBAAiB/Q,KAAK+Q,gBACtB/E,UAAWhM,KAAKgM,UAChB4F,2BAA4B5R,KAAK4R,2BACjCzB,yBAA0BnQ,KAAKmQ,wBALtB,CAtDZ,CAFF,CAiED,EAcOzE,EAAAoC,UAAA+D,iBAAR,WACE,OAAI7R,KAAKwO,gBACAxO,KAAK4H,SAAS1F,OAASlC,KAAKiQ,cAAcvI,YAAc7G,OAAOkO,YAClE/O,KAAKyO,cAAcK,cAElB9O,KAAK4H,SAAS1F,KACtB,EAcOwJ,EAAAoC,UAAAuC,kBAAR,WACE,OAAIrQ,KAAKwO,gBACAxO,KAAK4H,SAASzF,QAAUnC,KAAKiQ,cAAczK,aAAe3E,OAAO8N,aACpE3O,KAAKyO,cAAcC,iBAElB1O,KAAK4H,SAASzF,MACtB,EAEOuJ,EAAAoC,UAAA8C,cAAR,WAC0C,SAApC5Q,KAAK4R,4BACP5R,KAAKiO,QAAQrH,MAAMxE,KAAOpC,KAAKoQ,kBAAkBhO,KAAO,KACxDpC,KAAKiO,QAAQrH,MAAM1E,MAAQ,UAE3BlC,KAAKiO,QAAQrH,MAAM1E,MAAQlC,KAAKoQ,kBAAkBlO,MAAQ,KAC1DlC,KAAKiO,QAAQrH,MAAMxE,KAAO,SAGU,QAAlCpC,KAAKmQ,0BACPnQ,KAAKiO,QAAQrH,MAAM3E,IAAMjC,KAAKoQ,kBAAkBnO,IAAM,KACtDjC,KAAKiO,QAAQrH,MAAMzE,OAAS,UAE5BnC,KAAKiO,QAAQrH,MAAMzE,OAASnC,KAAKoQ,kBAAkBjO,OAAS,KAC5DnC,KAAKiO,QAAQrH,MAAM3E,IAAM,SAG3BjC,KAAKoU,eAAL,CACD,EAEO1I,EAAAoC,UAAAsG,eAAR,WACE,IAEQC,EACAlG,EAGJmG,EAIEC,EAVFvU,KAAK+Q,kBAAoB/Q,KAAKgM,YAC5BsI,EAAgB,KAAA,EACdD,EAAgBrU,KAAKuR,iBAAgB,cACrCpD,EAAQnO,KAAKiO,QAAQ/D,UAAUgE,QAAQmG,CAA/B,EACRrG,EAAMhO,KAAKiO,QAAQ/D,UAAUgE,QAAQ,IAAKC,EAAQkG,EAAW3V,MAAvD,EAEV4V,EADW,GAATnG,EACiBnO,KAAKiO,QAAQ/D,UAAUmE,OAAOF,EAAOH,EAAIG,CAAzC,EAEAzC,EAAQ+G,wBAAwB7S,IAAII,KAAKwS,gBAAzC,EAAA,MAEjB+B,EAAeF,EAAa3I,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAA,MAChChM,KAAKiO,QAAQK,UAAU/E,QAAQ+K,EAAkBC,CAAjD,EAEH,EAYO7I,EAAAoC,UAAAkD,wBAAR,SAAgCH,GACzBA,IACH7Q,KAAKiQ,cAAgB1N,EAAAa,cAAcoR,QAAQxU,KAAKqM,SAA3B,EACrBrM,KAAKuO,wBAAL,GAEFvO,KAAKqT,YAAc9Q,EAAAa,cAAcoR,QAAQxU,KAAKsM,OAA3B,EACnBtM,KAAKkQ,YAAc3N,EAAAa,cAAcoR,QAAQxU,KAAKiO,OAA3B,CACpB,EAQOvC,EAAAoC,UAAAyE,oBAAR,WAuBE,OApBAvS,KAAKyU,gBAAgBzS,OAA0C,GAAjChC,KAAKqT,YAAYjM,aAAoBpH,KAAKqT,YAAYjM,aAClFpH,KAAKqT,YAAYxE,aACf7O,KAAKuQ,eACPvQ,KAAKyU,gBAAgBxS,IAAMjC,KAAKqT,YAAYD,YAAcpT,KAAKuQ,cAAczO,EAC7E9B,KAAKyU,gBAAgBzS,OAAS,GAE9BhC,KAAKyU,gBAAgBxS,IAAMjC,KAAKiQ,cAAc1M,UAAYvD,KAAKqT,YAAYD,YACvEpT,KAAKiQ,cAAcmD,YAAcpT,KAAKiQ,cAAcyE,UAE1D1U,KAAKyU,gBAAgBrB,YAAcpT,KAAKqT,YAAYD,YAEd,QAAlCpT,KAAKmQ,yBACInQ,KAAK4S,oBAAoB5S,KAAKyU,gBAAgBxS,IAAKjC,KAAKyU,gBAAgBzS,OAAQhC,KAAKiQ,cAC9FjQ,KAAKqT,YAAarT,KAAKkQ,YAAYrB,aAAc7O,KAAKkU,kBAD7C,GAIXlU,KAAKyU,gBAAgBtS,OAASnC,KAAKiQ,cAAczK,aAAexF,KAAKyU,gBAAgBxS,IAAMjC,KAAKyU,gBAAgBzS,OACrGhC,KAAK4S,oBAAoB5S,KAAKyU,gBAAgBtS,OAAQnC,KAAKyU,gBAAgBzS,OAAQhC,KAAKiQ,cACjGjQ,KAAKqT,YAAarT,KAAKkQ,YAAYrB,aAAc7O,KAAKkU,kBAD7C,EAId,EAQOxI,EAAAoC,UAAAuE,sBAAR,WAwBE,OArBArS,KAAKyU,gBAAgB1S,MAAwC,GAAhC/B,KAAKqT,YAAYpE,YAAmBjP,KAAKqT,YAAYpE,YAAcjP,KAAKqT,YAAYrE,YAG7GhP,KAAKuQ,eACPvQ,KAAKyU,gBAAgBrS,KAAOpC,KAAKqT,YAAYC,aAAetT,KAAKuQ,cAAc1O,EAC/E7B,KAAKyU,gBAAgB1S,MAAQ,GAE7B/B,KAAKyU,gBAAgBrS,KAAOpC,KAAKiQ,cAAc1F,WAAavK,KAAKqT,YAAYC,aACzEtT,KAAKiQ,cAAcqD,aAAetT,KAAKiQ,cAAc0E,WAE3D3U,KAAKyU,gBAAgBnB,aAAetT,KAAKqT,YAAYC,aAEb,SAApCtT,KAAK4R,2BACI5R,KAAK6S,sBAAsB7S,KAAKyU,gBAAgBrS,KAAMpC,KAAKyU,gBAAgB1S,MAAO/B,KAAKiQ,cAChGjQ,KAAKqT,YAAarT,KAAKkQ,YAAYlB,YAAahP,KAAKkU,kBAD5C,GAIXlU,KAAKyU,gBAAgBvS,MAAQlC,KAAKiQ,cAAcvI,YAAc1H,KAAKyU,gBAAgBrS,KAAOpC,KAAKyU,gBAAgB1S,MACpG/B,KAAK6S,sBAAsB7S,KAAKyU,gBAAgBvS,MAAOlC,KAAKyU,gBAAgB1S,MAAO/B,KAAKiQ,cACjGjQ,KAAKqT,YAAarT,KAAKkQ,YAAYlB,YAAahP,KAAKkU,kBAD5C,EAId,EAQOxI,EAAAoC,UAAAuD,4BAAR,WACMrR,KAAKkS,mBAAqBzG,EAAwB0G,OAIlDnS,KAAKkR,sBAAsB0D,IAAI,UAA/B,GACF5U,KAAK6U,oCAAL,EAGE7U,KAAKkR,sBAAsB0D,IAAI,YAA/B,IACF5U,KAAK8U,sCAAL,CAEH,EAEOpJ,EAAAoC,UAAAiH,oBAAR,WACI,IAAMlU,EAAS0B,EAAAa,cAAc4R,UAAd,EACTC,EAAS1S,EAAAa,cAAc8R,UAAd,EACflV,KAAKmV,iBAAmB,CACtBlT,IAAMgT,EAAOnT,EACbM,KAAM6S,EAAOpT,EACbK,MAAOrB,EAAOkO,WAAakG,EAAOpT,EAClCM,OAAQtB,EAAO8N,YAAcsG,EAAOnT,CAJd,CAM3B,EAUO4J,EAAAoC,UAAAiE,yBAAR,WACE,IAmBQqD,EAEAC,EAGFxG,EACAG,EAMFzL,EAQAgH,EAvCJ,OAAIvK,KAAKkS,mBAAqBzG,EAAwB6J,UAC/CtV,KAAKwO,iBACRxO,KAAK+U,oBAAL,EAEK,CACH3S,KAAM,EACNF,MAAOlC,KAAKiQ,cAAcvI,YAC1BzF,IAAK,EACLE,OAAQnC,KAAKiQ,cAAczK,YAJxB,IAOTxF,KAAK+U,oBAAL,EAEI/U,KAAKwO,gBACAxO,KAAKmV,kBAKNC,EAA8B,EAF9BG,EAAsE,IAA9CvV,KAAKqM,UAAU6G,cAAcrE,cACT,IAA7C7O,KAAKqM,UAAU6G,cAAclE,cAE7BhP,KAAKqM,UAAU6G,cAAcrE,aAAe7O,KAAKiQ,cAAcpB,aAC9DwG,EAAgC,CAACE,GAClCvV,KAAKqM,UAAU6G,cAAclE,YAAchP,KAAKiQ,cAAcjB,YAK/DzE,EADAhH,EADAyL,EADAH,EAAY,KAAA,EAqBT,CACL5M,IAAMsB,GAjBJ6R,GACFvG,EAAe7O,KAAKqM,UAAU6G,cAAcrE,aAChC7O,KAAKqM,UAAU6G,gBAE3BrE,EAAe7O,KAAKiQ,cAAcpB,aACtB7O,KAAKiQ,gBAHwB1M,UAgBzCpB,OAAQoB,EAAYsL,EACpBzM,KAAMmI,GAXJ8K,GACFrG,EAAchP,KAAKqM,UAAU6G,cAAclE,YAC9BhP,KAAKqM,UAAU6G,gBAE5BlE,EAAchP,KAAKiQ,cAAcjB,YACpBhP,KAAKiQ,gBAHwB1F,WAU1CrI,MAAOqI,EAAayE,CAJf,GAOV,EAKOtD,EAAAoC,UAAA0H,wBAAR,SAAgCC,GAC9B,IAAIC,EAIFzT,EAFgB,cAAdwT,GACFC,EAAW1V,KAAK8R,sBACV9R,KAAKyU,gBAAgBxS,MAE3ByT,EAAW1V,KAAKmV,iBACVnV,KAAKyU,gBAAgBrB,aAG7B,MAAsC,QAAlCpT,KAAKmQ,yBACAuF,EAASvT,QAAUF,EAAMjC,KAAKyU,gBAAgBzS,QAE9CC,EAAMyT,EAASzT,GAEzB,EAKOyJ,EAAAoC,UAAA6H,uBAAR,SAA+BF,GAC7B,IAAIC,EAIFtT,EAFgB,cAAdqT,GACFC,EAAW1V,KAAK8R,sBACT9R,KAAKyU,gBAAgBrS,OAE5BsT,EAAW1V,KAAKmV,iBACTnV,KAAKyU,gBAAgBnB,cAG9B,MAAwC,SAApCtT,KAAK4R,2BACA8D,EAASxT,OAASE,EAAOpC,KAAKyU,gBAAgB1S,OAE9CK,EAAOsT,EAAStT,IAE1B,EAOOsJ,EAAAoC,UAAAgH,sCAAR,WACE,IACQc,EACAC,EACAC,EACAC,EACAC,EAGJC,EAOIC,EAGJC,EAlBCnW,KAAKoW,qBAAqB,aAAcpW,KAAK4H,SAASxF,KAAMpC,KAAK4H,SAAS1F,KAA1E,IACG0T,EAAanE,EAAA,GAAyBzR,KAAK4H,QAA9B,EACbiO,EAAwB7V,KAAK4R,2BAC7BkE,EAAiB9V,KAAKgM,UACtB+J,EAA6B/V,KAAK6S,sBAClCmD,EAA+BhW,KAAK2V,uBAAuB,WAA5B,EACjCM,EAAkC,KAAA,EAEpCA,EADEjW,KAAKqW,oBAC8BrW,KAAK2V,uBAAuB,MAA5B,EAEAK,EAGvChW,KAAKsW,wBAAL,EAEMJ,EAA8BlW,KAAK2V,uBAAuB,WAA5B,EAChCQ,EAAiC,KAAA,EAQjCF,GAAqCE,EAPrCnW,KAAKqW,oBAC6BrW,KAAK2V,uBAAuB,MAA5B,EAEAO,IAKK,EAApCC,GAC0E,GAA1EA,EAAoCF,GAEvCD,EAA+BE,GAA6D,EAA9BA,KAChElW,KAAK4H,SAAWgO,EAChB5V,KAAK4R,2BAA6BiE,EAClC7V,KAAKgM,UAAY8J,EACjB9V,KAAK6S,sBAAwBkD,EAGlC,EAOOrK,EAAAoC,UAAA+G,oCAAR,WACE,IACQe,EACAC,EACAC,EACAS,EACAC,EAGJC,EAOIC,EAGJC,EAlBC3W,KAAKoW,qBAAqB,WAAYpW,KAAK4H,SAAS3F,IAAKjC,KAAK4H,SAASzF,MAAvE,IACGyT,EAAanE,EAAA,GAAyBzR,KAAK4H,QAA9B,EACbiO,EAAwB7V,KAAKmQ,yBAC7B2F,EAAiB9V,KAAKgM,UACtBuK,EAA2BvW,KAAK4S,oBAChC4D,EAAgCxW,KAAKwV,wBAAwB,WAA7B,EAClCiB,EAAmC,KAAA,EAErCA,EADEzW,KAAKqW,oBAC+BrW,KAAKwV,wBAAwB,MAA7B,EAEAgB,EAGxCxW,KAAK4W,sBAAL,EAEMF,EAA+B1W,KAAKwV,wBAAwB,WAA7B,EACjCmB,EAAkC,KAAA,EAQlCF,GAAsCE,EAPtC3W,KAAKqW,oBAC8BrW,KAAKwV,wBAAwB,MAA7B,EAEAkB,IAKK,EAArCC,GAC4E,GAA5EA,EAAqCF,GAExCD,EAAgCE,GAA+D,EAA/BA,KAClE1W,KAAK4H,SAAWgO,EAChB5V,KAAKmQ,yBAA2B0F,EAChC7V,KAAKgM,UAAY8J,EACjB9V,KAAK4S,oBAAsB2D,EAGhC,EAEO7K,EAAAoC,UAAAsI,qBAAR,SAA6BS,EAAiC1I,EAAeH,GAC3E,MAAa,eAAT6I,GACIC,EAAqB3I,EAAQnO,KAAKkQ,YAAYlB,YAC7ChP,KAAK8R,sBAAsB1P,MAAQ+L,GACnCA,GAASnO,KAAK8R,sBAAsB5P,OACpClC,KAAK8R,sBAAsB1P,MAAQ0U,GACnCA,GAAsB9W,KAAK8R,sBAAsB5P,QAEpD6U,EAAoB5I,EAAQnO,KAAKkQ,YAAYrB,aAC5C7O,KAAK8R,sBAAsB7P,KAAOkM,GAClCA,GAASnO,KAAK8R,sBAAsB3P,QACpCnC,KAAK8R,sBAAsB7P,KAAO8U,GAClCA,GAAqB/W,KAAK8R,sBAAsB3P,OACxD,EAMOuJ,EAAAoC,UAAA8I,sBAAR,WACE5W,KAAKgM,UAAYN,EAAQsL,4BAA4BpX,IAAII,KAAKgM,SAA7C,EAAwDhM,KAAKmQ,wBAA7D,EACjBnQ,KAAKmQ,yBAA2BzE,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAoD2G,SACpF3S,KAAK4S,oBAAsBlH,EAAQK,uBAAuB/L,KAAKgM,UAAWhM,KAAKmQ,wBAApD,EAC3B,IAAMmC,EAAmBtS,KAAKuS,oBAAL,EACzBvS,KAAK4H,SAAS3F,IAAMqQ,EAAiBrQ,IACrCjC,KAAK4H,SAASzF,OAASmQ,EAAiBnQ,MACzC,EAMOuJ,EAAAoC,UAAAwI,wBAAR,WACEtW,KAAKgM,UAAYN,EAAQuL,8BAA8BrX,IAAII,KAAKgM,SAA/C,EAA0DhM,KAAK4R,0BAA/D,EACjB5R,KAAK4R,2BAA6BlG,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAoD0G,WACtF1S,KAAK6S,sBAAwBnH,EAAQ4B,yBAAyBtN,KAAKgM,UAAWhM,KAAK4R,0BAAtD,EAC7B,IAAMQ,EAAqBpS,KAAKqS,sBAAL,EAC3BrS,KAAK4H,SAASxF,KAAOgQ,EAAmBhQ,KACxCpC,KAAK4H,SAAS1F,MAAQkQ,EAAmBlQ,KAC1C,EAphCgBwJ,EAAA+G,wBAEf,IAAInS,IAAI,CACN,CAACkL,EAAUwB,QAAe,CAAE0F,WAAY,OAAQC,SAAU,SAAUc,MAAO,SAAjD,GAC1B,CAACjI,EAAUyB,IAAe,CAAEyF,WAAY,OAAQC,SAAU,SAAUc,MAAO,KAAjD,GAC1B,CAACjI,EAAU0B,SAAe,CAAEwF,WAAY,QAASC,SAAU,SAAUc,MAAO,UAAlD,GAC1B,CAACjI,EAAUsB,KAAe,CAAE4F,WAAY,QAASC,SAAU,MAAOc,MAAO,MAA/C,GAC1B,CAACjI,EAAUS,QAAe,CAAEyG,WAAY,QAASC,SAAU,MAAOc,MAAO,SAA/C,GAC1B,CAACjI,EAAU4B,WAAe,CAAEsF,WAAY,QAASC,SAAU,SAAUc,MAAO,YAAlD,GAC1B,CAACjI,EAAUuB,MAAe,CAAE2F,WAAY,OAAQC,SAAU,MAAOc,MAAO,OAA9C,GAC1B,CAACjI,EAAUU,SAAe,CAAEwG,WAAY,OAAQC,SAAU,MAAOc,MAAO,UAA9C,GAC1B,CAACjI,EAAU6B,YAAe,CAAEqF,WAAY,OAAQC,SAAU,SAAUc,MAAO,aAAjD,GAC1B,CAACjI,EAAUkB,WAAe,CAAEgG,WAAY,OAAQC,SAAU,MAAOc,MAAO,YAA9C,GAC1B,CAACjI,EAAUmB,OAAe,CAAE+F,WAAY,OAAQC,SAAU,MAAOc,MAAO,QAA9C,GAC1B,CAACjI,EAAUoB,YAAe,CAAE8F,WAAY,QAASC,SAAU,MAAOc,MAAO,aAA/C,GAZ5B,EAea/H,EAAAsL,4BACb,IAAI1W,IAAI,CACN,CAACkL,EAAUwB,QAAe,SAACpF,GAAqB,OAAA4D,EAAAkB,UAAoB,GACpE,CAAClB,EAAUyB,IAAe,SAACrF,GAAqB,OAAA4D,EAAAmB,MAAgB,GAChE,CAACnB,EAAU0B,SAAe,SAACtF,GAAqB,OAAA4D,EAAAoB,WAAqB,GACrE,CAACpB,EAAUsB,KAAe,SAAClF,GAAqB,MAAa,QAAbA,EAAqB4D,EAAU4B,WAAa5B,EAA5CS,OAA6D,GAC7G,CAACT,EAAUS,QAAe,SAACrE,GAAqB,OAAA4D,EAAA4B,UAAoB,GACpE,CAAC5B,EAAU4B,WAAe,SAACxF,GAAqB,OAAA4D,EAAAS,OAAiB,GACjE,CAACT,EAAUuB,MAAe,SAACnF,GAAqB,MAAa,QAAbA,EAAqB4D,EAAU6B,YAAc7B,EAA7CU,QAA+D,GAC/G,CAACV,EAAUU,SAAe,SAACtE,GAAqB,OAAA4D,EAAA6B,WAAqB,GACrE,CAAC7B,EAAU6B,YAAe,SAACzF,GAAqB,OAAA4D,EAAAU,QAAkB,GAClE,CAACV,EAAUkB,WAAe,SAAC9E,GAAqB,OAAA4D,EAAAwB,OAAiB,GACjE,CAACxB,EAAUmB,OAAe,SAAC/E,GAAqB,OAAA4D,EAAAyB,GAAa,GAC7D,CAACzB,EAAUoB,YAAe,SAAChF,GAAqB,OAAA4D,EAAA0B,QAAkB,GAZpE,EAeaxB,EAAAuL,8BACb,IAAI3W,IAAI,CACN,CAACkL,EAAUwB,QAAe,SAACpF,GAAqB,OAAA4D,EAAA0B,QAAkB,GAClE,CAAC1B,EAAUyB,IAAS,SAACrF,GAAqB,MAAa,SAAbA,EAAsB4D,EAAU0B,SAAW1B,EAA3CwB,OAA4D,GACtG,CAACxB,EAAU0B,SAAe,SAACtF,GAAqB,OAAA4D,EAAAwB,OAAiB,GACjE,CAACxB,EAAUsB,KAAe,SAAClF,GAAqB,OAAA4D,EAAAuB,KAAe,GAC/D,CAACvB,EAAUS,QAAe,SAACrE,GAAqB,OAAA4D,EAAAU,QAAkB,GAClE,CAACV,EAAU4B,WAAe,SAACxF,GAAqB,OAAA4D,EAAA6B,WAAqB,GACrE,CAAC7B,EAAUuB,MAAe,SAACnF,GAAqB,OAAA4D,EAAAsB,IAAc,GAC9D,CAACtB,EAAUU,SAAe,SAACtE,GAAqB,OAAA4D,EAAA4B,UAAoB,GACpE,CAAC5B,EAAU6B,YAAe,SAACzF,GAAqB,OAAA4D,EAAAS,OAAiB,GACjE,CAACT,EAAUkB,WAAe,SAAC9E,GAAqB,OAAA4D,EAAAoB,WAAqB,GACrE,CAACpB,EAAUmB,OAAe,SAAC/E,GAAqB,MAAa,SAAbA,EAAsB4D,EAAUoB,YAAcpB,EAA9CkB,UAAkE,GAClH,CAAClB,EAAUoB,YAAe,SAAChF,GAAqB,OAAA4D,EAAAkB,UAAoB,GAZtE,EAeahB,EAAAwL,0BACb,IAAI5W,IAAI,CACN,CAACkL,EAAUwB,QAAexB,EAAUyB,KACpC,CAACzB,EAAUyB,IAAezB,EAAUyB,KACpC,CAACzB,EAAU0B,SAAe1B,EAAUyB,KACpC,CAACzB,EAAUsB,KAAetB,EAAUsB,MACpC,CAACtB,EAAUS,QAAeT,EAAUsB,MACpC,CAACtB,EAAU4B,WAAe5B,EAAUsB,MACpC,CAACtB,EAAUuB,MAAevB,EAAUuB,OACpC,CAACvB,EAAUU,SAAeV,EAAUuB,OACpC,CAACvB,EAAU6B,YAAe7B,EAAUuB,OACpC,CAACvB,EAAUkB,WAAelB,EAAUmB,QACpC,CAACnB,EAAUmB,OAAenB,EAAUmB,QACpC,CAACnB,EAAUoB,YAAepB,EAAUmB,QAZtC,EAm+BJjB,GAh6BE,SAAAA,EAAYwD,EACVjB,EAAyB5B,EAAwBC,EAAsBN,EACvEmL,EAAkDvL,EAAgC2F,EAClF2C,GApBMlU,KAAAwQ,WAAa,CAAA,EAKbxQ,KAAAyU,gBAAkH,GAGlHzU,KAAAqW,oBAAsB,CAAA,EA4RtBrW,KAAAyQ,kBAAoB,GA/Q1BzQ,KAAKkP,SAAWA,EAChBlP,KAAKiO,QAAUA,EAIfjO,KAAKiQ,cAAgB1N,EAAAa,cAAcoR,QAAQnI,CAAtB,EACrBrM,KAAKqM,UAAYA,EACjBrM,KAAKwO,gBAAkBnC,IAAcxI,SAASqB,KAC9ClF,KAAKuO,wBAAL,EACKvO,KAAKwO,kBACRxO,KAAKqW,oBAA6E,EAAvD9T,EAAAa,cAAcgU,uBAAuBpX,KAAKqM,SAA1C,GAG7BrM,KAAKiO,QAAQrH,MAAM8J,QAAU1Q,KAAKiO,QAAQrH,MAAM8J,QAAQnH,QAAQ,iBAAkB,EAArD,EAC7BvJ,KAAKiO,QAAQrH,MAAM3E,IAAM,MACzBjC,KAAK+N,kBAAL,EAKA/N,KAAKsM,QAAUZ,EAAQC,mBAAmBW,EAASV,CAApC,EACf5L,KAAKuR,iBAAmBA,EACpB2C,EAAmBmD,mBACrBrX,KAAKgM,UAAYN,EAAQwL,0BAA0BtX,IAAIoM,CAAtC,EAEjBhM,KAAKgM,UAAYA,EAEnBhM,KAAKwS,iBAAmBxS,KAAKgM,UAC7BhM,KAAKkS,iBAAmBiF,EACxBnX,KAAKkU,mBAAqBA,EAE1BlU,KAAK4H,SAAW,CAAE8J,OAAQ,CAAV,EAChB1R,KAAK4R,2BAA6BlG,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAoD0G,WACtF1S,KAAKmQ,yBAA2BzE,EAAQ+G,wBAAwB7S,IAAII,KAAKgM,SAAzC,EAAoD2G,SAEpF3S,KAAK4S,oBAAsBlH,EAAQK,uBAAuB/L,KAAKgM,UAAWhM,KAAKmQ,wBAApD,EAC3BnQ,KAAK6S,sBAAwBnH,EAAQ4B,yBAAyBtN,KAAKgM,UAAWhM,KAAK4R,0BAAtD,EAC7B5R,KAAK8P,eAAiBvN,EAAAa,cAAckU,gBAAgBtX,KAAKsM,OAAnC,EACtBtM,KAAKP,QAAL,CACD,CAjKUjB,EAAAkN,QAAAA,C,gLC3DZ,IAAAnJ,EAAArE,EAAA,gBAAA,EACDqZ,EAAArZ,EAAA,WAAA,EACAoE,EAAApE,EAAA,kBAAA,EAEA,SAAAsZ,KAGSA,EAAAC,sBAAP,SAA6BvI,EAC3BwI,EAAiBC,EAAiB3L,EAAuB4L,EACzDT,EAAkDvL,EAAgC2F,EAClF1E,EAAwBa,EAA0B2J,EAClDQ,EAAqBC,GAErB,IAaIC,EAbEC,EAAiBzV,EAAAa,cAAcxD,IAAI8X,CAAlB,EACjBO,EAAmB1V,EAAAa,cAAcxD,IAAIgY,CAAlB,EACnBM,EAAiB3V,EAAAa,cAAcxD,IAAI+X,CAAlB,EAEvB,GAAI,CAACpV,EAAAc,sBAAsB8U,aAAaT,EAAiBO,CAApD,EAMH,OALAlV,QAAQF,IAAI,kCAAmC,CAC5C0O,iBAAkBA,EAClBmG,gBAAiBA,EACjBO,iBAAkBA,CAH0B,CAA/C,EAKO,MAILJ,GAAcC,KAChBC,EAAiB,CAAElW,EAAGiW,EAAahW,EAAG+V,CAArB,GASb5J,EAAU,IAAIsJ,EAAA7L,QAAQwD,EAAU8I,EAAgBC,EAAkBC,EAAgBlM,EAAWmL,EAAyBvL,EAAuB2F,EANpG,CAC7C1E,eAAgBA,EAChBa,iBAAkBA,EAClB2J,mBAAoBA,CAHyB,CAM/B,EAIhB,OAFArX,KAAKoY,gBAAgBlJ,GAAYjB,GAElBqC,kBAAkB,CAAA,EAAO,CAAA,EAAMyH,CAAvC,CACR,EAGMP,EAAAa,sBAAP,SAA6BnJ,EAAkBwI,EAAiBC,EAAiB3L,EAAuB4L,EACtGT,EAAkDvL,EAAgC2F,EAClF1E,EAAwBa,EAA0B2J,EAClDQ,EAAqBC,GACrB,IAAM7J,EAAUjO,KAAKoY,gBAAgBlJ,GACrC,OAAIjB,EAKKA,EAAQqC,kBAAkB,CAAA,EAAO,CAAA,EAHlCuH,GAAcC,EACC,CAAEjW,EAAGiW,EAAahW,EAAG+V,CAArB,EAFH,KAAA,CAIX,EAKAL,EAAcC,sBAAsBvI,EAAUwI,EAAiBC,EAAiB3L,EAAY4L,EAAkBT,EAAyBvL,EAAuB2F,EACrK1E,EAAgBa,EAAkB2J,EAClCQ,EAAYC,CAFL,CAIV,EAEMN,EAAAc,2BAAP,SAAkCpJ,GAChC,IAAMjB,EAAUjO,KAAKoY,gBAAgBlJ,GACjCjB,IACFA,EAAQhO,QAAR,EACA,OAAOD,KAAKoY,gBAAgBlJ,GAE/B,EAEMsI,EAAAe,gCAAP,SAAuC7Y,EAASsY,GAC9C,IACQ7U,EADJzD,GAAWsY,IACP7U,EAAmBZ,EAAAa,cAAcxD,IAAIF,CAAlB,KAEvB4C,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,iBAAmB,SAAC5I,GAAM,OAAA4E,EAAAiW,YAAYC,sBAAsB9a,EAAG,QAAS,WAAM,OAAA,OAAAqa,EAAelH,YAAnE,CAAA,CAAyF,EAC5J3N,EAAIuD,iBAAiB,UAAWpE,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,iBAAkB,CAAA,CAA3F,EAGL,EAEMiR,EAAAkB,mCAAP,SAA0ChZ,GACxC,IACQyD,EADJzD,IACIyD,EAAmBZ,EAAAa,cAAcxD,IAAIF,CAAlB,KAEvByD,EAAI2D,oBAAoB,UAAWxE,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,gBAA5E,EACAjE,EAAA+D,MAAMG,sBAAsB9G,EAAQ6G,GAAK,iBAAmB,KAGjE,EAvFMiR,EAAAY,gBAA6C,GADzC5Z,EAAAgZ,cAyFbA,C,+FC7FC,SAAAmB,K,qCACQA,EAAAC,gBAAP,SAAuBC,EAASC,EAAUC,EAAWC,EAASC,GAC5DJ,EAAQK,WAAa,WACfF,GACFL,EAAYQ,2BAA2BN,EAASC,CAAhD,EAEEG,IACFF,EAAUxO,WAAasO,EAAQtO,WAElC,EAGDW,WAAW,WACV2N,GAAWA,EAAQK,WAAR,CACX,EAAE,GAFH,EAIAL,EAAQnS,kBAAoBmS,EAAQnS,iBAAiB,SAAUmS,EAAQK,UAA3C,EAC5BrY,OAAO6F,iBAAiB,SAAUmS,EAAQK,UAA1C,CACD,EAEMP,EAAAS,kBAAP,SAAyBP,GACnBA,IACFA,EAAQ/R,qBAAuB+R,EAAQ/R,oBAAoB,SAAU+R,EAAQK,UAA9C,EAC/BrY,OAAOiG,oBAAoB,SAAU+R,EAAQK,UAA7C,EAEH,EAEMP,EAAAQ,2BAAP,SAAkCN,EAASC,GAEzC,IAAIvO,EAAasO,EAAQtO,WACrB7C,EAAcmR,EAAQnR,YACtBsH,EAAc6J,EAAQ7J,YAEtBqK,EAAW,CAAA,EACXC,EAAY,CAAA,EAEX5R,GAAesH,GAA8B,GAAftH,EAEjC4R,EADAD,EAAW,CAAA,EAGU,GAAd9O,EAEP+O,EAAY,EADZD,EAAW,CAAA,GAGJ/R,KAAKiS,IAAI7R,GAAe6C,EAAayE,EAArC,GAAsD,EAE7DqK,EAAW,EADXC,EAAY,CAAA,GAKZA,EADAD,EAAW,CAAA,EAIbA,EAAWP,EAASxK,UAAUkD,IAAI,qBAAvB,EAAgDsH,EAASxK,UAAUpK,OAAO,qBAA1B,EAC3DoV,EAAYR,EAASxK,UAAUkD,IAAI,sBAAvB,EAAiDsH,EAASxK,UAAUpK,OAAO,sBAA1B,CAC9D,EAvDW1F,EAAAma,YAwDdA,C,uCCjDA,SAAAa,K,sCACSA,EAAAC,0BAAP,SAAiCC,GAC3BA,EAAIhT,kBACNgT,EAAIhT,iBAAiB,QAAS8S,EAAaG,cAA3C,CAEH,EAEMH,EAAAI,6BAAP,SAAoCF,GAClCA,EAAI5S,oBAAoB,QAAS0S,EAAaG,cAA9C,CACD,EAEcH,EAAAG,eAAf,SAA8Bhc,GAC5BA,EAAEkc,gBAAF,EACMC,EAAUnc,EAAEoc,cAAkCzK,WAAW,eAAe0K,UAC9DnW,SAASoW,eAAeH,CAAxB,EACR7V,MAAR,CACD,EAEMuV,EAAAU,UAAP,SAAiBxa,GACfA,EAAQuF,aAAa,OAAQ,OAA7B,EACAvF,EAAQmI,MAAQ,GAChBnI,EAAQuF,aAAa,OAAQ,MAA7B,CACD,EAEMuU,EAAAW,YAAP,SAAmBza,GACjB,GAAIA,EAAQ0a,OAAgC,EAAvB1a,EAAQ0a,MAAM1b,OAAY,CAE7C,IADA,IAAI2b,EAAWjZ,MAAA,EACNrD,EAAI,EAAGA,EAAI2B,EAAQ0a,MAAM1b,OAAQX,CAAA,GAAK,CAC7C,IAAIuc,EAAO5a,EAAQ0a,MAAMrc,GACnBwc,EAAYva,KAAKwa,aAAaF,CAAlB,EAClBD,EAAShY,KAAK,CACZqB,SAAU4W,EAAKG,KACfrJ,KAAMkJ,EAAKlJ,KACXsJ,UAAWH,EACX1D,KAAMyD,EAAKzD,IAJC,CAAd,CAMD,CAED,OAAOwD,CACR,CACF,EAEcb,EAAAgB,aAAf,SAA4BF,GAC1B,IAAI3W,EAAM,KAMV,OALkBgX,MAAd9Z,OAAO+Z,IACTjX,EAAM9C,OAAO+Z,IAAIC,gBAAgBP,CAA3B,EACuBK,MAApB9Z,OAAOia,YAChBnX,EAAM9C,OAAOia,UAAUD,gBAAgBP,CAAjC,GAED3W,CACR,EAEM6V,EAAAuB,WAAP,SAAkBrb,EAASsb,EAAOC,EAAMC,EAASpB,EAAQnW,EAAK8W,EAAMU,EAAUC,EAAeC,EAAeC,EAAaC,GACvH,IAAIC,EAAW,IAAIC,SACfnB,EAAO5a,EAAQ0a,MAAMY,GACrB5J,EAAOkJ,EAAKlJ,KAEhB,GADAoK,EAASnR,OAAOoQ,EAAMH,CAAtB,EACY,MAARW,EACF,IAAK,IAAIjc,KAAOic,EACdO,EAASnR,OAAOrL,EAAKic,EAAKjc,EAA1B,EAGJ,IAAM0c,EAAM,IAAIC,eAoBhB,GAnBAD,EAAIE,mBAAqB,WACA,IAAnBF,EAAIG,aAEFH,EAAII,OAAS,KAAoB,IAAbJ,EAAII,OAE1BX,EAAS9a,kBAAkBib,EAAaxB,EAAQ4B,EAAIK,YAApD,EAGFZ,EAAS9a,kBAAkBgb,EAAevB,EAAQ4B,EAAIK,YAAtD,EAEH,EACDL,EAAIM,OAAOC,WAAa,SAAUC,GAC5BC,EAAU7U,KAAK8U,MAAMF,EAAMG,OAASjL,EAAO,GAAjC,EACd+J,EAAS9a,kBAAkB+a,EAAetB,EAAQqC,CAAlD,CACD,EACDT,EAAIY,QAAU,SAAU3e,GACtBwd,EAAS9a,kBAAkBib,EAAaxB,EAAQ,OAAhD,CACD,EACD4B,EAAIa,KAAKhB,EAAQ5X,EAAK,CAAA,CAAtB,EACe,MAAXuX,EACF,IAAK,IAAIsB,KAAUtB,EACjBQ,EAAIe,iBAAiBD,EAAQtB,EAAQsB,EAArC,EAGJd,EAAIgB,KAAKlB,CAAT,CACD,EAxFUhd,EAAAgb,aAyFbA,C,wHC/FiB,SAAXmD,EAAYC,EAAIC,GAAA,KAAA,IAAAA,IAAAA,EAAA,KAClB,IAAIC,EACA3O,EAAQ,CAAC,IAAI4O,KACjB,OAAO,W,IAAA,IAAAnU,EAAA5I,KAAUgd,EAAA,GAAAC,EAAA,EAAAA,EAAAC,UAAAxe,OAAAue,CAAA,GAAAD,EAAAC,GAAAC,UAAAD,GACb,IAAoBE,EAAU,CAAC,IAAIJ,KAEnClc,OAAOuc,aAAaN,CAApB,EACuBD,GAAnBM,EAAUhP,GAEVyO,EAAGS,MALOrd,KAKQgd,CAAlB,EACA7O,EAAQgP,GAIRL,EAAUjc,OAAOqK,WAAW,WAExB0R,EAAGS,MAAMzU,EAAMoU,CAAf,CACH,EAAEH,CAHO,CAKjB,CACJ,CApBD,IAsBMS,EAAW,IAAIhd,IAEfid,EAAiB,CACnBC,WAAY,CAAA,CADO,EAIvBC,GAwBIA,EAAA3P,UAAA4P,gBAAA,WACI,IAAMC,EAAO3d,KAAK4d,WAAWC,sBAAhB,EACb,MAAO,CACHzb,KAAMub,EAAKvb,KACXH,IAAK0b,EAAK1b,GAFP,CAIV,EAgGDwb,EAAA3P,UAAAgQ,SAAA,WACI,IAAMxR,EAAUtM,KAAK+d,SACfpe,EAAUK,KAAKge,SAErB1R,EAAQ5F,iBAAiB,YAAa1G,KAAKie,YAAa,CAAA,CAAxD,EACApd,OAAO6F,iBAAiB,UAAW1G,KAAKke,UAAW,CAAA,CAAnD,EACAra,SAAS6C,iBAAiB,YAAa1G,KAAKme,WAA5C,EACIxe,EAAQ6d,YACR3c,OAAO6F,iBAAiB,SAAU1G,KAAKoe,SAAU,CAAA,CAAjD,CAEP,EAEDX,EAAA3P,UAAAuQ,WAAA,WACoBre,KAAK+d,SAEbjX,oBAAoB,YAAa9G,KAAKie,YAAa,CAAA,CAA3D,EACApd,OAAOiG,oBAAoB,UAAW9G,KAAKke,UAAW,CAAA,CAAtD,EACAra,SAASiD,oBAAoB,YAAa9G,KAAKme,WAA/C,EACIne,KAAKge,SAASR,YACd3c,OAAOiG,oBAAoB,SAAU9G,KAAKoe,SAAU,CAAA,CAApD,CAEP,EAEDX,EAAA3P,UAAAwQ,oBAAA,WACwB,OAAhBte,KAAKue,SACLve,KAAKwe,SAAW,CAAA,EAChBxe,KAAK4d,WAAW3Y,aAAa,QAASjF,KAAKue,MAA3C,EAEP,EACLd,GAlJI,SAAAA,EAAYnR,EAAsBD,EAAwBoS,GAA1D,IAAA7V,EAAA5I,KAPQA,KAAA+d,SAAwB,KACxB/d,KAAA4d,WAA0B,KAC1B5d,KAAAge,SAAgB,KAChBhe,KAAA0e,OAAc,KACd1e,KAAAwe,SAAoB,CAAA,EACpBxe,KAAAue,OAAiB,KAyBzBve,KAAAie,YAAc,SAACtgB,GACX,IAAM0I,EAAQuC,EAAK8V,OAKbzV,GAJN5C,EAAMsY,SAAW,CAAA,EACjBtY,EAAMuY,GAAKjhB,EAAEkhB,QACbxY,EAAMyY,GAAKnhB,EAAEohB,QACbnW,EAAKgV,WAAWhX,MAAMgB,SAAW,WACXgB,EAAK8U,gBAAL,GAAdtb,EAAI6G,EAAA7G,KAAEH,EAAGgH,EAAAhH,IAEb2G,EAAK4V,WAELnY,EAAM2Y,QAAUnb,SAAS+K,gBAAgBC,aACnCjG,EAAKgV,WAAWxW,aAAe,EACrCf,EAAM4Y,QAAUpb,SAAS+K,gBAAgBI,YACnCpG,EAAKgV,WAAW3O,YAAc,EACpC5I,EAAM2Y,QAAU3Y,EAAM2Y,QAAU,EAAI,EAAI3Y,EAAM2Y,QAC9C3Y,EAAM4Y,QAAU5Y,EAAM4Y,QAAU,EAAI,EAAI5Y,EAAM4Y,QAE9CrW,EAAKgV,WAAWhX,MAAMxE,KAAOA,EAAO,KACpCwG,EAAKgV,WAAWhX,MAAM3E,IAAMA,EAAM,KAE7B2G,EAAK2V,SACN3V,EAAK2V,OAAS3V,EAAKgV,WAAWsB,aAAa,OAA7B,GAGlBtW,EAAK4V,SAAW,CAAA,GAGpBnY,EAAM8Y,UAAY/c,EAClBiE,EAAM+Y,UAAYnd,CACrB,EAEDjC,KAAAke,UAAY,SAACvgB,GACT,IAAM0I,EAAQuC,EAAK8V,OAIbzV,GAFN5C,EAAMsY,SAAW,CAAA,EAEK/V,EAAK8U,gBAAL,GAAdtb,EAAI6G,EAAA7G,KAAEH,EAAGgH,EAAAhH,IACjBoE,EAAM8Y,UAAY/c,EAClBiE,EAAM+Y,UAAYnd,CACrB,EAEDjC,KAAAme,YAAcxB,EAAS,SAAChf,GACpB,IAOQ0hB,EAPFhZ,EAAQuC,EAAK8V,OACfrY,EAAMsY,WACFW,EAAO3hB,EAAEkhB,QACTU,EAAO5hB,EAAEohB,QACTS,EAAOF,EAAOjZ,EAAMuY,GACpBa,EAAOF,EAAOlZ,EAAMyY,GAEpBO,EAAUhZ,EAAM8Y,UAAYK,EAC5BE,EAAUrZ,EAAM+Y,UAAYK,EAC5B7W,EAAKoV,SAASR,aACV6B,EAAU,EACVA,EAAU,EAELA,EAAUhZ,EAAM4Y,UACrBI,EAAUhZ,EAAM4Y,SAEhBS,EAAU,EACVA,EAAU,EAELA,EAAUrZ,EAAM2Y,UACrBU,EAAUrZ,EAAM2Y,UAGxBpW,EAAKgV,WAAWhX,MAAMgB,SAAW,WACjCgB,EAAKgV,WAAWhX,MAAM+Y,OAAS,IAC/B/W,EAAKgV,WAAWhX,MAAMgZ,cAAgB,IACtChX,EAAKgV,WAAWhX,MAAMxE,KAAOid,EAAU,KACvCzW,EAAKgV,WAAWhX,MAAM3E,IAAMyd,EAAU,KAE7C,EAAE,EA9BW,EA8BPtQ,KAAKpP,IA9BE,EAgCdA,KAAAoe,SAAWzB,EAAS,SAAChf,GACjB,IAAM0I,EAAQuC,EAAK8V,OAEnBrY,EAAM2Y,QAAUnb,SAAS+K,gBAAgBC,aACnCjG,EAAKgV,WAAWxW,aAAe,EACrCf,EAAM4Y,QAAUpb,SAAS+K,gBAAgBI,YACnCpG,EAAKgV,WAAW3O,YAAc,EACpC5I,EAAM2Y,QAAU3Y,EAAM2Y,QAAU,EAAI,EAAI3Y,EAAM2Y,QAC9C3Y,EAAM4Y,QAAU5Y,EAAM4Y,QAAU,EAAI,EAAI5Y,EAAM4Y,QAC9C5Y,EAAM+Y,UAAYS,SAASjX,EAAKgV,WAAWhX,MAAM3E,GAA/B,EAClBoE,EAAM8Y,UAAYU,SAASjX,EAAKgV,WAAWhX,MAAMxE,IAA/B,EACdiE,EAAM+Y,UAAY/Y,EAAM2Y,SACJ,EAAhB3Y,EAAM2Y,UACNpW,EAAKgV,WAAWhX,MAAM3E,IAAMoE,EAAM2Y,QAAU,MAGhD3Y,EAAM8Y,UAAY9Y,EAAM4Y,UACxBrW,EAAKgV,WAAWhX,MAAMxE,KAAOiE,EAAM4Y,QAAU,KAEpD,EAAE,EAnBQ,EAmBJ7P,KAAKpP,IAnBD,EA/FPA,KAAK+d,SAAWzR,EAChBtM,KAAK4d,WAAavR,EAClBrM,KAAKge,SAAWlY,OAAOga,OAAO,GAAIvC,EAAgB,CAC9CC,WAAYiB,CADkC,CAAlC,EAGhBze,KAAK0e,OAAS,CACVC,SAAU,CAAA,EACVC,GAAI,EACJE,GAAI,EACJK,UAAW,EACXC,UAAW,CALD,CAOjB,CA8JI5gB,EAAAuhB,gBAvBT,SAAyBzT,EAAsBD,EAAwBoS,GAAA,KAAA,IAAAA,IAAAA,EAAA,CAAA,GACnE,IAAIuB,EAAU1C,EAAS1d,IAAI0M,CAAb,EACT0T,IACDA,EAAU,IAAIvC,EAAQnR,EAASD,EAAWoS,CAAhC,EACVnB,EAAS9d,IAAI8M,EAAS0T,CAAtB,GAEJA,EAAQlC,SAAR,CACH,EAgByBtf,EAAAyhB,iBAd1B,SAA0B3T,IAChB0T,EAAU1C,EAAS1d,IAAI0M,CAAb,IAEZ0T,EAAQ3B,WAAR,CAEP,EAS2C7f,EAAA0hB,mBAP5C,SAA4B5T,IAClB0T,EAAU1C,EAAS1d,IAAI0M,CAAb,IAEZ0T,EAAQ1B,oBAAR,CAEP,C,4EC/MA,IAAA/b,EAAArE,EAAA,WAAA,EACDoE,EAAApE,EAAA,kBAAA,EAEA,SAAAsa,KACSA,EAAA2H,aAAP,SAAoBzgB,EAA2B0gB,EAAmBC,GAG1DC,EAAMzc,SAAS0c,YAAYH,CAArB,EAEZ,OADAE,EAAIE,UAAUH,CAAd,EACO3gB,EAAQ+gB,cAAcH,CAAtB,CACR,EAEM9H,EAAAkI,oBAAP,SAA2BhhB,EAAS2gB,EAAmBrX,EAAyB/J,GAC7D,SAAX0hB,EAAW3D,GACf,IACS4D,EADHC,EAAM,GACZ,IAASD,KAAK5D,EACF,mBAAN4D,IACFC,EAAID,GAAK5D,EAAK4D,IAGlB,IAAME,EAAO3gB,KAAKC,UAAUygB,EAAK,SAACD,EAAGG,GACnC,OAAIA,aAAaC,KAAa,OAC1BD,aAAaE,OAAe,SACzBF,CACR,EAAE,GAJU,EAMb7V,WAAW,WAAcjM,EAAQoB,kBAAkB,SAAUygB,CAApC,CAA2C,EAAE,CAAtE,EACuB,CAAA,IAAnB9X,GACFgU,EAAKhU,eAAL,CAEH,CAjBD,IAmBM7F,EAAMZ,EAAAa,cAAcxD,IAAIF,CAAlB,EACNV,EAASqhB,EAAS,IAAIphB,EAAQiiB,IAGlC/d,EAAI,KAAKnE,GADO,WAAdqhB,EACgBrgB,KAAKmhB,SAAS,WAAM,OAAAR,EAAS,CAAE5R,WAAYlO,OAAOkO,WAAYJ,YAAa9N,OAAvD8N,WAAS,CAAT,CAA4E,EAAE,IAAK,CAAA,CAAvG,EAEAgS,EAGpBxd,EAAI,KAAKnE,GAASC,EACjBkE,EAAoBuD,iBAAiB2Z,EAAWld,EAAI,KAAKnE,EAAzD,CACF,EAEMwZ,EAAA4I,gCAAP,SAAuC1hB,EAAS2gB,EAAWrX,EAAgB/J,GACnEkE,EAAMZ,EAAAa,cAAcxD,IAAIF,CAAlB,EAERyD,GAAOA,EAAIke,mBACbrhB,KAAK0gB,oBAAoBvd,EAAIke,kBAAmBhB,EAAWrX,EAAgB/J,CAA3E,CAEH,EAEMuZ,EAAA8I,uBAAP,SAA8B5hB,EAAS2gB,EAAmBphB,GAClDkE,EAAMZ,EAAAa,cAAcxD,IAAIF,CAAlB,EACNV,EAASqhB,EAAS,IAAIphB,EAAQiiB,IAEhC/d,GACFA,EAAI2D,oBAAoBuZ,EAAWld,EAAI,KAAKnE,EAA5C,CAGH,EAEMwZ,EAAA+I,eAAP,SAAsBC,EAAcC,GAApC,IAEUte,EAFVyF,EAAA5I,KACMwhB,IACIre,EAAMZ,EAAAa,cAAcxD,IAAI4hB,CAAlB,EACZC,EAAOA,EAAKC,IAAI,SAAU7f,GAAK,OAAOA,EAAE8f,YAAF,CAAkB,CAAjD,EACPrf,EAAA+D,MAAMG,sBAAsBgb,EAAajb,GAAK,WAAa,SAAC5I,GAAM,OAAAiL,EAAKgZ,YAAYjkB,EAAjB8jB,CAAA,CAAyB,EAC1Fte,EAAoBuD,iBAAiB,UAAWpE,EAAA+D,MAAMG,sBAAsBgb,EAAajb,GAAK,WAAY,CAAA,CAA1G,EAEJ,EAEMiS,EAAAC,sBAAP,SAA6B9a,EAAkBqB,EAAa6iB,GAC1D,GAAIlkB,EAAEqB,IAAI2iB,YAAN,IAAwB3iB,EAAI2iB,YAAJ,GAAqBE,EAAA,EAE/C,OADAlkB,EAAEqL,eAAF,EACO,CAAA,CAEV,EAEMwP,EAAAsJ,kBAAP,SAAyBN,GACvB,IACQre,EADJqe,IACIre,EAAMZ,EAAAa,cAAcxD,IAAI4hB,CAAlB,KAETre,EAAoB2D,oBAAoB,UAAWxE,EAAA+D,MAAMG,sBAAsBgb,EAAajb,GAAK,UAAjG,EACDjE,EAAA+D,MAAMG,sBAAsBgb,EAAajb,GAAK,WAAa,KAGhE,EAEciS,EAAA2I,SAAf,SAAwBY,EAAMC,EAAMC,GAApC,IACMnF,EADNlU,EAAA5I,KAEE,OAAO,W,IAAC,IAAAgd,EAAA,GAAAC,EAAA,EAAAA,EAAAC,UAAAxe,OAAAue,CAAA,GAAAD,EAAAC,GAAAC,UAAAD,GACN,IAAMiF,EAAUtZ,EAKVuZ,EAAUF,GAAa,CAACnF,EAC9BM,aAAaN,CAAb,EACAA,EAAU5R,WANI,WACZ4R,EAAU,KACLmF,GAAWF,EAAK1E,MAAMzU,EAAMoU,CAAjB,CACjB,EAG2BgF,CAAlB,EACNG,GAASJ,EAAK1E,MAAM6E,EAASlF,CAApB,CACd,CACF,EAEcxE,EAAAoJ,YAAf,SAA2BjkB,EAAkB8jB,GAC3C,GAA0C,CAAC,IAAvCA,EAAKvT,QAAQvQ,EAAEqB,IAAI2iB,YAAN,CAAb,EAEF,OADAhkB,EAAEqL,eAAF,EACO,CAAA,CAEV,EA1GUxK,EAAAga,YA2GbA,C,uPC9GC3Z,G,gBAAAX,EAAA,cAAA,GACDkkB,GADU3hB,EAAAjC,EAAAK,EAAA,aAAA,eAAA,EACVX,EAAA,sBAAA,GACAmkB,GADS5hB,EAAAjC,EAAA4jB,EAAA,qBAAA,uBAAA,EACTlkB,EAAA,eAAA,GAASuC,EAAAjC,EAAA6jB,EAAA,aAAA,EACT7jB,EAAA8jB,SAAApkB,EAAA,SAAA,C,kHCDA,SAAA4B,K,oCACSA,EAAAkV,UAAP,WACE,MAAO,CACLjG,WAAYlO,OAAOkO,WACnBJ,YAAa9N,OAAO8N,WAFf,CAIR,EAEM7O,EAAAF,IAAP,SAAWF,GACT,GAAKA,GAEE,GAAuB,UAAnB,OAAOA,EAAsB,CACtC,GAAgB,WAAZA,EACA,OAAOmB,OACJ,GAAgB,aAAZnB,EACT,OAAOmE,SAETnE,EAAUmE,SAASmB,cAActF,CAAvB,CACX,CAAA,MARCA,EAAUmE,SAASqB,KASrB,OAAOxF,CACR,EAEMI,EAAA0U,QAAP,SAAe9U,GACb,IACKG,GAAAA,EADYG,KAAKJ,IAAIF,CAAT,IAEF,GAET6iB,EAAmBviB,KAAKwiB,sBAAsB3iB,CAA3B,EAkBzB,MAjBiC,CAC/B2K,UAAW3K,EAAW2K,WAAa,EACnCF,WAAYzK,EAAWyK,YAAc,EACrC2E,YAAapP,EAAWoP,aAAe,EACvC7H,aAAcvH,EAAWuH,cAAgB,EACzC5B,aAAc3F,EAAW2F,cAAgB,EACzCkC,YAAa7H,EAAW6H,aAAe,EACvC6C,WAAY1K,EAAW0K,YAAc,EACrChH,UAAW1D,EAAW0D,WAAa,EACnCmR,UAAW7U,EAAW6U,WAAa,EACnCC,WAAY9U,EAAW8U,YAAc,EACrC9F,aAAchP,EAAWgP,cAAgB,EACzCG,YAAanP,EAAWmP,aAAe,EACvClH,eAAgBjI,EAAWiI,gBAAkB,EAC7CsL,YAAa9L,KAAKmb,MAAMF,EAAiBzgB,CAA5B,EACbwR,aAAchM,KAAKmb,MAAMF,EAAiB1gB,CAA5B,CAfiB,CAkBlC,EAEM/B,EAAA0iB,sBAAP,SAA6B9iB,GAC3B,IAQU6K,EACAhH,EATJmf,EAAyB,CAC7B7gB,EAAG,EACHC,EAAG,CAF0B,EAe/B,OAXgB,OAAZpC,GACEA,EAAQme,wBACJ8E,EAAkB9e,SAAS+K,gBAC3BgU,EAAMljB,EAAQme,sBAAR,EACNtT,EAAaoY,EAAgBpY,WAC7BhH,EAAYof,EAAgBpf,UAElCmf,EAAI7gB,EAAI+gB,EAAIxgB,KAAOmI,EACnBmY,EAAI5gB,EAAI8gB,EAAI3gB,IAAMsB,GAGfmf,CACR,EAEM5iB,EAAA+d,sBAAP,SAA6Bne,GAC3B,IAAMG,EAAaG,KAAKJ,IAAIF,CAAT,EACnB,OAAIG,GAAcA,EAAWge,sBAGpB,CACL9b,OAHI4b,EAAO9d,EAAWge,sBAAX,GAGC9b,MACZC,OAAQ2b,EAAK3b,OACbC,IAAK0b,EAAK1b,IACVC,MAAOyb,EAAKzb,MACZC,OAAQwb,EAAKxb,OACbC,KAAMub,EAAKvb,KACXP,EAAG8b,EAAK9b,EACRC,EAAG6b,EAAK7b,CARH,EAWF,IACR,EAEMhC,EAAA+iB,qBAAP,SAA4BnjB,GACpBG,EAAaG,KAAKJ,IAAIF,CAAT,EACnB,OAAIG,EACEA,EAAWwhB,kBACNrhB,KAAKwU,QAAQ3U,EAAWwhB,iBAAxB,EAEArhB,KAAKwU,QAAQ3U,CAAb,EAGJ,IACR,EAEMC,EAAAsL,iBAAP,WAGE,OAFgBvH,SAASmH,cACNkU,aAAa,IAArB,GAA8B,EAE1C,EAEMpf,EAAAoV,UAAP,WACE,MAAO,CAAErT,EAAGhB,OAAOkT,YAAajS,EAAGjB,OAAOmP,WAAnC,CACR,EAEMlQ,EAAAgjB,SAAP,SAAgBnY,GACRxH,EAAMnD,KAAKJ,IAAI+K,CAAT,EACZ,OAAQ9G,SAASmH,gBAAkB7H,CACpC,EAEMrD,EAAAijB,aAAP,SAAoBrjB,GACZyD,EAAMnD,KAAKJ,IAAIF,CAAT,EACZ,OAAIyD,EAAYA,EAAI6f,UACb,IACR,EAEMljB,EAAA6R,aAAP,WACE,OAAOvQ,MAAM6hB,KAAKpf,SAASyH,iBAAiB,GAA1B,CAAX,EAA2C4X,OAAO,SAACxlB,EAAGC,GAAM,OAAA2J,KAAKE,IAAI9J,EAAG,CAACmD,OAAO8E,iBAAiBhI,CAAxB,EAA2B+T,QAAxC,CAAA,CAAoD,EAAE,CAAlH,CACR,EAEM5R,EAAAwX,gBAAP,SAAuB5X,GAErB,IADA,IAAIyjB,EAAOnjB,KAAKJ,IAAIF,CAAT,EACJyjB,GAAwC,SAAhCA,EAAKC,SAASC,YAAd,GAAwC,CACrD,GAAiF,UAA7ExiB,OAAO8E,iBAAiBwd,CAAxB,EAA8Btd,iBAAiB,UAA/C,EAA2Dwd,YAA3D,EAAwF,MAAO,CAAA,EACnGF,EAAOA,EAAK/Y,UACb,CACD,MAAO,CAAA,CACR,EAEMtK,EAAAsX,uBAAP,SAA8B1X,GAI5B,IAHA,IAEIgS,EAFAyR,EAAOnjB,KAAKJ,IAAIF,CAAT,EAGJyjB,GAAwC,SAAhCA,EAAKC,SAASC,YAAd,GAAwC,CAGrD,GAFAC,EAAiBziB,OAAO8E,iBAAiBwd,CAAxB,EAA8BzR,OAC/CA,EAAS6R,OAAO1D,SAASyD,CAAhB,EACL,CAACC,OAAOC,MAAM9R,CAAb,EACH,OAAOA,EAETyR,EAAOA,EAAK/Y,UACb,CACD,OAAO,IACR,EAEMtK,EAAA2jB,gBAAP,SAAuBC,GAAvB,IAAA9a,EAAA5I,KACM2jB,EAAQ,GAKZ,OAJAD,EAASriB,QAAQ,SAAAuiB,GACfD,EAAMC,EAAGrd,IAAMqC,EAAK4L,QAAQoP,CAAb,CAChB,CAFD,EAIOD,CACR,EA1JUnlB,EAAAsB,WA2JbA,C,mFC7JC,IAAAyC,EAAArE,EAAA,WAAA,EACDuE,EAAAvE,EAAA,gBAAA,EACAoE,EAAApE,EAAA,kBAAA,EACA2lB,EAAA3lB,EAAA,UAAA,EAGI4lB,EAA0CnJ,KAAAA,EACxCoJ,EAAY,IAAIzjB,IAEtB,SAAA0jB,KACSA,EAAAC,iBAAP,SAAwBvkB,GACtBmE,SAASqB,KAAKC,YAAYzF,CAA1B,CACD,EAEMskB,EAAAE,mBAAP,SAA0BxkB,GACxBmE,SAASqB,KAAKqG,YAAY7L,CAA1B,CACD,EAEMskB,EAAA7L,aAAP,SAAoBgM,EAAYC,EAAiBC,GAAA,KAAA,IAAAA,IAAAA,EAAA,CAAA,GAC3CC,EAAS/hB,EAAAa,cAAcxD,IAAIwkB,CAAlB,EACb,GAAIE,GAAUH,EAAY,CACxB,GAAIG,aAAkBtD,MAAQmD,aAAsBnD,KAGlD,OAFIqD,EAASC,EAAOC,aAAaJ,EAAYG,EAAOE,UAAvC,EACRF,EAAOnf,YAAYgf,CAAnB,EACE,CAAA,EAEPphB,QAAQF,IAAI,0BAA2ByhB,EAAQH,CAA/C,CAEH,CACD,MAAO,CAAA,CACR,EAEMH,EAAAS,eAAP,SAAsBC,EAAYN,GAC5BE,EAAS/hB,EAAAa,cAAcxD,IAAIwkB,CAAlB,EACTE,GAAUI,GACZJ,EAAO/Y,YAAYmZ,CAAnB,CAEH,EAEMV,EAAAW,gBAAP,SAAuBjlB,EAAS4P,GAC9B,IAAInM,EAAmBZ,EAAAa,cAAcxD,IAAIF,CAAlB,EACvB,GAAIyD,EACF,IAAK,IAAInE,KAAOsQ,EACdnM,EAAI8B,aAAajG,EAAKsQ,EAAWtQ,EAAjC,CAGL,EAEMglB,EAAAY,YAAP,SAAmBllB,GACZM,KAAK6kB,sBAAsBnlB,CAA3B,GACHM,KAAK8kB,KAAKplB,EAAQsjB,SAAlB,CAEH,EAEcgB,EAAAa,sBAAf,SAAqCnlB,GACnC,IAAIqlB,EAAYlhB,SAASmhB,aAAT,EAIZC,GAHuB,EAAvBF,EAAUG,YACZH,EAAUI,gBAAV,EAEUthB,SAASuhB,YAAT,GACZH,EAAMI,WAAW3lB,CAAjB,EACAqlB,EAAUO,SAASL,CAAnB,EACA,IACE,IAAIM,EAAa1hB,SAAS2hB,YAAY,MAArB,EAEjB,OADAT,EAAUI,gBAAV,EACOI,CAIR,CAHC,MAAOE,GAEP,OADAV,EAAUI,gBAAV,EACO,CAAA,CACR,CACF,EAEMnB,EAAAc,KAAP,SAAYhiB,GACL4iB,UAAUC,UAIfD,UAAUC,UAAUC,UAAU9iB,CAA9B,EAAoC+iB,KAAK,WACvC9iB,QAAQF,IAAI,6CAAZ,CACD,EAAE,SAAU4iB,GACX1iB,QAAQ+iB,MAAM,+BAAgCL,CAA9C,CACD,CAJD,EAHEzlB,KAAK+lB,4BAA4BjjB,CAAjC,CAQH,EAEckhB,EAAA+B,4BAAf,SAA2CjjB,GACzC,IAAI0G,EAAW3F,SAASC,cAAc,UAAvB,EACf0F,EAAS3B,MAAQ/E,EAGjB0G,EAAS5C,MAAM3E,IAAM,IACrBuH,EAAS5C,MAAMxE,KAAO,IACtBoH,EAAS5C,MAAMgB,SAAW,QAE1B/D,SAASqB,KAAKC,YAAYqE,CAA1B,EACAA,EAAS2B,MAAT,EACA3B,EAASwc,OAAT,EAEA,IACE,IACIC,EADapiB,SAAS2hB,YAAY,MAArB,EACM,aAAe,eACtCziB,QAAQF,IAAI,sCAAwCojB,CAApD,CAGD,CAFC,MAAOR,GACP1iB,QAAQ+iB,MAAM,iCAAkCL,CAAhD,CACD,CAED5hB,SAASqB,KAAKqG,YAAY/B,CAA1B,CACD,EAEMwa,EAAA7Y,MAAP,SAAaR,EAAUub,EAA2BC,GAA3B,KAAA,IAAAD,IAAAA,EAAA,CAAA,GAA2B,KAAA,IAAAC,IAAAA,EAA8BtC,EAAMuC,cAAcC,aAClG,IAAIljB,EAAMZ,EAAAa,cAAcxD,IAAI+K,CAAlB,EACV,GAAI,EAAExH,aAAemjB,aACnB,MAAM,IAAIjoB,MAAM,qCAAV,EAMR,GAJA8E,EAAIgI,MAAM,CACRob,cAAeL,CADP,CAAV,EAII/iB,aAAeqjB,kBAAoBrjB,aAAesjB,oBACpD,OAAQN,GACN,KAAKtC,EAAMuC,cAAcM,kBACvBvjB,EAAI6iB,OAAJ,EACA,MACF,KAAKnC,EAAMuC,cAAcO,aACvBxjB,EAAIyjB,kBAAkB,EAAG,CAAzB,EACA,MACF,KAAK/C,EAAMuC,cAAcC,YACvBljB,EAAIyjB,kBAAkB,CAAC,EAAG,CAAC,CAA3B,CARJ,CAYH,EAGM5C,EAAA/Y,KAAP,SAAYN,GACNxH,EAAMZ,EAAAa,cAAcxD,IAAI+K,CAAlB,EACNxH,GACFA,EAAI8H,KAAJ,CAEH,EAEM+Y,EAAA6C,SAAP,SAAgBlc,EAA4BuI,GACpCxT,EAAU6C,EAAAa,cAAcxD,IAAI+K,CAAlB,EACZuI,GAAiBxT,GAAWA,aAAmB4mB,YACjDpT,EAAc3P,UAAY7D,EAAQ8K,UACzB9K,GAAWA,aAAmB4mB,aACrC5mB,EAAQonB,eAAe,CAAEC,SAAU,SAAUC,MAAO,UAAWC,OAAQ,OAAhD,CAAvB,CAEL,EAEMjD,EAAAkD,eAAP,SAAsBvc,EAA4BuI,EAA4BiU,GAC5E,IAiBMC,EAhBFC,EADY9kB,EAAAa,cAAcxD,IAAI+K,CAAlB,EACCH,UACbuZ,EAAUnkB,IAAIsT,CAAd,GACAoU,qBAAqBvD,EAAUnkB,IAAIsT,CAAd,CAArB,EAGAiU,GAAY,EACZpD,EAAUvkB,IACN0T,EACAqU,sBAAsB,WAClBrU,EAAc3P,UAAY8jB,CAC7B,CAFD,CAFJ,GAUED,GADaC,EAAKnU,EAAc3P,WACR4jB,EAAY,GAE1CpD,EAAUvkB,IACN0T,EACAqU,sBAAsB,WAClBrU,EAAc3P,WAAa6jB,EACvBlU,EAAc3P,YAAc8jB,GAC5BrD,EAAmBkD,eAAevc,EAAUuI,EAAeiU,EAAW,EAAtE,CAEP,CALD,CAFJ,EASD,EAEMnD,EAAA1gB,QAAP,SAAekkB,GACb,IAAMC,EAAQC,YAAY,WACxB,IAAMC,EAAW9jB,SAAS+K,gBAAgBrL,WAAaM,SAASqB,KAAK3B,UAE/DqkB,EAAQtgB,KAAKugB,MADYF,EAAdH,EAAyBA,EAAcG,EAAWA,EAAWH,GAC3C,EAArB,EACVG,IAAaH,EACfM,cAAcL,CAAd,EAEA5mB,OAAOgmB,SAAS,EAAiBc,EAAdH,EAAyBG,EAAWC,EAAQD,EAAWC,CAA1E,CAEH,EAAE,EATW,CAUf,EAGM5D,EAAA+D,aAAP,WAEE,GAAuC,UAAnCC,EADYnkB,SAASmH,eACX5C,QAAQib,YAAlB,EAGF,IAFA,IAAI4E,EAASpkB,SAASyE,qBAAqB,OAA9B,EACT0f,EAAYnkB,SAASmH,cAChBjN,EAAI,EAAGA,EAAIkqB,EAAOvpB,OAAQX,CAAA,GACjC,GAAIkqB,EAAOlqB,IAAMiqB,EAAW,CAC1B,IAAIE,EAAOD,EAAOlqB,EAAI,GAClBmqB,GAAQA,EAAK/c,OACf+c,EAAK/c,MAAL,EAEF,KACD,CAGN,EAEM6Y,EAAAmE,kBAAP,WACE,IAAIjjB,EAAOrB,SAASqB,KACdkjB,EAAe,GAKfC,GAJN,CAAC,WAAY,QAAS,YAAYhnB,QAAQ,SAACrC,GACzCopB,EAAappB,GAAOkG,EAAK0B,MAAM5H,EAChC,CAFD,EAGAsD,EAAA+D,MAAMiiB,kBAAkBjmB,KAAK+lB,CAA7B,EACsBpoB,KAAKuoB,iBAAL,GACtB9lB,EAAA+lB,YAAYC,IAAIvjB,EACd,CACE0C,SAAY,WACZ7F,MAAS/B,KAAK0oB,aAAL,GAAuC,EAAhBL,EAAoB,eAAeA,EAAa,MAAQ,KACxFM,SAAY,QAHd,CADF,EAMAlmB,EAAA+lB,YAAYI,OAAO/kB,SAASqB,KAAM,sBAAlC,CACD,EAEM8e,EAAA6E,iBAAP,SAAwBC,GAClBA,IACAxmB,EAAA+D,MAAMiiB,kBAAoB,I,MAE1BF,EAAgD,EAAjC9lB,EAAA+D,MAAMiiB,kBAAkB5pB,OAAa4D,EAAA+D,MAAMiiB,kBAAkBS,IAAxB,EAAgC,GAGxFtmB,EAAA+lB,YAAYC,IAAI5kB,SAASqB,KACvB,CACE0C,SAAU,OAAAqB,EAAEmf,EAAA,UAAwBnf,EAAI,KACxClH,MAAO,OAAAinB,EAAEZ,EAAA,OAAqBY,EAAI,KAClCL,SAAU,OAAAM,EAAEb,EAAA,UAAwBa,EAAI,IAH1C,CADF,EAMAxmB,EAAA+lB,YAAYU,UAAUrlB,SAASqB,KAAM,sBAArC,CACD,EAEM8e,EAAA0E,aAAe,WACpB,IAAIC,EAAW9kB,SAASqB,KAAK0B,MAAM+hB,SACnC,OAAIA,CAAAA,GAAyB,WAAbA,IACT9kB,SAASqB,KAAKM,cAAgB3E,OAAO8N,aAAe9K,SAAS+K,gBAAgBC,aACrF,EAUOmV,EAAAuE,iBAAmB,SAACY,GAC1B,IASQC,EAgBAC,EAzBR,OAD0B,KAAA,IAAAF,IAAAA,EAAA,CAAA,GACF,aAApB,OAAOtlB,SACF,GAGLslB,CAAAA,GAAiCxO,KAAAA,IAAxBmJ,KACLwF,EAAQzlB,SAASC,cAAc,KAAvB,GACR8C,MAAM7E,MAAQ,OACpBunB,EAAM1iB,MAAM5E,OAAS,SAGfunB,GADAH,EAAQvlB,SAASC,cAAc,KAAvB,GACW8C,OAEdgB,SAAW,WACtB2hB,EAAWtnB,IAAM,IACjBsnB,EAAWnnB,KAAO,IAClBmnB,EAAWC,cAAgB,OAC3BD,EAAWE,WAAa,SACxBF,EAAWxnB,MAAQ,QACnBwnB,EAAWvnB,OAAS,QACpBunB,EAAWZ,SAAW,SAEtBS,EAAMjkB,YAAYmkB,CAAlB,EAEAzlB,SAASqB,KAAKC,YAAYikB,CAA1B,EAEMC,EAAiBC,EAAMra,YAC7Bma,EAAMxiB,MAAM+hB,SAAW,SAGnBU,KAFAK,EAAcJ,EAAMra,eAGtBya,EAAcN,EAAMpa,aAGtBnL,SAASqB,KAAKqG,YAAY6d,CAA1B,EACAtF,EAAsBuF,EAAiBK,GAElC5F,EACR,EA/RUtlB,EAAAwlB,mBAgSbA,C,gGCzSA,aAEAxlB,EAAAmrB,WAAA,CAAA,C,+ECFavD,EAAA5nB,EAAA4nB,gBAAA5nB,EAAA4nB,cAAa,KAKxBA,EAAA,YAAA,GAAA,cAIAA,EAAAA,EAAA,aAAA,GAAA,eAIAA,EAAAA,EAAA,kBAAA,GAAA,mB,8ECHAwD,EAAA9b,UAAA+b,WAAA,SAAWC,GACT,OAAO9pB,KAAKsG,iBAAiBwjB,EAC9B,EAWMF,EAAAG,YAAP,WAIE,OAHK/pB,KAAKmb,WACRnb,KAAKmb,SAAW,IAAIyO,GAEf5pB,KAAKmb,QACb,EAzBH,IAAAyO,EA0BAA,EARE,SAAAA,IAdA5pB,KAAAsG,iBAA2C,GAU3CtG,KAAAwG,sBAAyD,GAEzDxG,KAAAsoB,kBAAoB,EAEK,CAlBd9pB,EAAAorB,MAAAA,EA4BAprB,EAAA6H,MAAQujB,EAAMG,YAAN,C,4EC/BpB,IAAAlrB,EAAAX,EAAA,kBAAA,EAED,SAAAsqB,KACSA,EAAAI,OAAP,SAAcje,EAA4BT,G,IACpCxK,EAAUb,EAAAiB,WAAcF,IAAI+K,CAAlB,EACVjL,IACuB,UAArB,OAAOwK,EACTxK,EAAQ4O,UAAUkD,IAAItH,CAAtB,GAEAjB,EAAAvJ,EAAQ4O,WAAUkD,IAAG6L,MAAApU,EAAIiB,CAAzB,EAGL,EAEMse,EAAAU,UAAP,SAAiBve,EAA4Bqf,G,IACvCtqB,EAAUb,EAAAiB,WAAcF,IAAI+K,CAAlB,EACVjL,IACqB,UAAnB,OAAOsqB,EACTtqB,EAAQ4O,UAAUpK,OAAO8lB,CAAzB,GAEA/gB,EAAAvJ,EAAQ4O,WAAUpK,OAAMmZ,MAAApU,EAAI+gB,CAA5B,EAGL,EAEMxB,EAAAyB,mBAAP,SAA0BvqB,EAA2BwK,GAC/CrK,EAAahB,EAAAiB,WAAcF,IAAIF,CAAlB,EACbG,GAAcA,EAAWwhB,mBAC3BxhB,EAAWwhB,kBAAkB/S,UAAUkD,IAAItH,CAA3C,CAEH,EAEMse,EAAA0B,wBAAP,SAA+BxqB,EAA2BwK,GACpDrK,EAAahB,EAAAiB,WAAcF,IAAIF,CAAlB,EACbG,GAAcA,EAAWwhB,mBAC3BxhB,EAAWwhB,kBAAkB/S,UAAUpK,OAAOgG,CAA9C,CAEH,EAEMse,EAAA2B,WAAP,SAAkBC,GAChB,OAAOvpB,OAAOspB,WAAWC,CAAlB,EAAyBC,OACjC,EAEM7B,EAAA8B,SAAP,SAAgB5qB,EAAS6qB,GACvB,OAAI7qB,EAAQ+F,aACH/F,EAAQ+F,aAAa8kB,GACrB1pB,OAAO8E,iBACP9B,SAAS+B,YAAYD,iBAAiBjG,EAAS,IAA/C,EAAqDmG,iBAAiB0kB,CAAtE,EADJ,KAAA,CAEN,EAGM/B,EAAAC,IAAP,SAAW/oB,EAAsB+a,EAAuB5S,GACtD,GADsD,KAAA,IAAAA,IAAAA,EAAA,MAClC,UAAhB,OAAO4S,EACT,GAAc,OAAV5S,EAGF,IAFA,IACI2iB,EADQ/P,EACcgQ,MAAM,GAAZ,EACX1sB,EAAI,EAAGA,EAAIysB,EAAc9rB,OAAQX,CAAA,GAAK,CAC7C,IAAI2sB,EAAeF,EAAczsB,GAC5B2sB,IACDC,EAAYD,EAAaD,MAAM,GAAnB,EAChB/qB,EAAQkH,MAAMgkB,YAAYD,EAAU,GAAIA,EAAU,EAAlD,EACD,MAGHjrB,EAAQkH,MAAMgkB,YAAYnQ,EAAM5S,CAAhC,OAEA,IAAK,IAAI7I,KAAOyb,EACVA,EAAKoQ,eAAe7rB,CAApB,GACFU,EAAQkH,MAAMgkB,YAAY5rB,EAAKyb,EAAKzb,EAApC,CAIP,EAtEUR,EAAAgqB,YAuEbA,C,4ECzECsC,EAAA5sB,EAAA,0BAAA,EAQD2C,OAAOkqB,UAAY,CACjBD,QAAOA,CADU,C,sDCRnB,aAEAhlB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EAEA,IAAAojB,EAAA/sB,EAAA,iBAAA,EAEAgtB,EAAA,EAEAC,EAAA,IAEAC,EAAA,IAEAC,EAAA,IAEAC,EAAA,IAEAC,EAAA,EAEAC,EAAA,EAGAC,EAAA,CAAA,CACAzQ,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,EACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,EACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,EAAA,CACA1Q,MAAA,EACA0Q,QAAA,GACA,GAGA,SAAAC,EAAAC,GACA,IAAAluB,EAAAkuB,EAAAluB,EACAmuB,EAAAD,EAAAC,EACAC,EAAAF,EAAAE,EACAC,EAAAd,EAAAe,SAAAtuB,EAAAmuB,EAAAC,CAAA,EACA,MAAA,CACAG,EAAA,IAAAF,EAAAE,EACAC,EAAAH,EAAAG,EACAnL,EAAAgL,EAAAhL,CACA,CACA,CAIA,SAAAoL,EAAAC,GACA,IAAA1uB,EAAA0uB,EAAA1uB,EACAmuB,EAAAO,EAAAP,EACAC,EAAAM,EAAAN,EACA,MAAA,IAAAO,OAAApB,EAAAqB,SAAA5uB,EAAAmuB,EAAAC,EAAA,CAAA,CAAA,CAAA,CACA,CAeA,SAAAS,EAAAR,EAAAhuB,EAAAyuB,GAIAC,EADA,IAAAnlB,KAAAmb,MAAAsJ,EAAAE,CAAA,GAAA3kB,KAAAmb,MAAAsJ,EAAAE,CAAA,GAAA,IACAO,EAAAllB,KAAAmb,MAAAsJ,EAAAE,CAAA,EAAAf,EAAAntB,EAAAuJ,KAAAmb,MAAAsJ,EAAAE,CAAA,EAAAf,EAAAntB,EAEAyuB,EAAAllB,KAAAmb,MAAAsJ,EAAAE,CAAA,EAAAf,EAAAntB,EAAAuJ,KAAAmb,MAAAsJ,EAAAE,CAAA,EAAAf,EAAAntB,EASA,OANA0uB,EAAA,EACAA,GAAA,IACA,KAAAA,IACAA,GAAA,KAGAA,CACA,CAEA,SAAAC,EAAAX,EAAAhuB,EAAAyuB,GAEA,OAAA,IAAAT,EAAAE,GAAA,IAAAF,EAAAG,EACAH,EAAAG,GAcA,GARAS,EADAH,EACAT,EAAAG,EAAAf,EAAAptB,EACAA,IAAAytB,EACAO,EAAAG,EAAAf,EAEAY,EAAAG,EAAAd,EAAArtB,KAKA4uB,EAAA,IAKAA,EADAH,GAAAzuB,IAAAwtB,GAAA,GAAAoB,EACA,GAGAA,GAAA,MACAA,EAAA,KAGApJ,OAAAoJ,EAAAC,QAAA,CAAA,CAAA,EACA,CAEA,SAAAC,EAAAd,EAAAhuB,EAAAyuB,GAIA3kB,EADA2kB,EACAT,EAAAhL,EAAAsK,EAAAttB,EAEAguB,EAAAhL,EAAAuK,EAAAvtB,EAOA,OAJA,EAAA8J,IACAA,EAAA,GAGA0b,OAAA1b,EAAA+kB,QAAA,CAAA,CAAA,CACA,CAEA,SAAAE,EAAAC,GAKA,IAJA,IAAAC,EAAA,EAAA9P,UAAAxe,QAAAic,KAAAA,IAAAuC,UAAA,GAAAA,UAAA,GAAA,GACA+P,EAAA,GACAC,EAAAjC,EAAAkC,WAAAJ,CAAA,EAEAhvB,EAAAwtB,EAAA,EAAAxtB,EAAAA,EAAAA,EAAA,CACA,IAAAguB,EAAAJ,EAAAuB,CAAA,EACAE,EAAAjB,EAAAlB,EAAAkC,WAAA,CACAlB,EAAAM,EAAAR,EAAAhuB,EAAA,CAAA,CAAA,EACAmuB,EAAAQ,EAAAX,EAAAhuB,EAAA,CAAA,CAAA,EACAgjB,EAAA8L,EAAAd,EAAAhuB,EAAA,CAAA,CAAA,CACA,CAAA,CAAA,EACAkvB,EAAA5qB,KAAA+qB,CAAA,CACA,CAEAH,EAAA5qB,KAAA8pB,EAAAe,CAAA,CAAA,EAEA,IAAA,IAAAjQ,EAAA,EAAAA,GAAAuO,EAAAvO,GAAA,EAAA,CACA,IAAAoQ,EAAA1B,EAAAuB,CAAA,EAEAI,EAAAnB,EAAAlB,EAAAkC,WAAA,CACAlB,EAAAM,EAAAc,EAAApQ,CAAA,EACAiP,EAAAQ,EAAAW,EAAApQ,CAAA,EACA8D,EAAA8L,EAAAQ,EAAApQ,CAAA,CACA,CAAA,CAAA,EAEAgQ,EAAA5qB,KAAAirB,CAAA,CACA,CAGA,MAAA,SAAAN,EAAAO,MACA9B,EAAA/J,IAAA,SAAA8L,GACA,IA7GAC,EA6GAzS,EAAAwS,EAAAxS,MACA0Q,EAAA8B,EAAA9B,QAEA,OADAS,GA/GAsB,EA+GAxC,EAAAkC,WAAAH,EAAAU,iBAAA,SAAA,EA/GAC,EA+GA1C,EAAAkC,WAAAF,EAAAjS,EAAA,EA/GA4S,EA+GA,IAAAlC,EA9GAntB,GAAA,IACA,CACAb,GAAAiwB,EAAAjwB,EAAA+vB,EAAA/vB,GAAAa,EAAAkvB,EAAA/vB,EACAmuB,GAAA8B,EAAA9B,EAAA4B,EAAA5B,GAAAttB,EAAAkvB,EAAA5B,EACAC,GAAA6B,EAAA7B,EAAA2B,EAAA3B,GAAAvtB,EAAAkvB,EAAA3B,CACA,EAyGA,CAEA,CAAA,EAGAmB,CACA,CAEA,IAAAY,EAAA,CACAC,IAAA,UACAC,QAAA,UACAC,OAAA,UACAC,KAAA,UACAC,OAAA,UACAC,KAAA,UACAC,MAAA,UACAC,KAAA,UACAC,KAAA,UACAC,SAAA,UACAC,OAAA,UACAC,QAAA,UACAC,KAAA,SACA,EACAC,EAAA,GACAC,EAAA,GAWAd,GAVAhoB,OAAA2b,KAAAoM,CAAA,EAAAxsB,QAAA,SAAArC,GACA2vB,EAAA3vB,GAAA8tB,EAAAe,EAAA7uB,EAAA,EACA2vB,EAAA3vB,GAAA6vB,QAAAF,EAAA3vB,GAAA,GAEA4vB,EAAA5vB,GAAA8tB,EAAAe,EAAA7uB,GAAA,CACAuuB,MAAA,OACAG,gBAAA,SACA,CAAA,EACAkB,EAAA5vB,GAAA6vB,QAAAD,EAAA5vB,GAAA,EACA,CAAA,EACA2vB,EAAAb,KACAC,EAAAY,EAAAZ,QACAE,EAAAU,EAAAV,KACAD,EAAAW,EAAAX,OACAE,EAAAS,EAAAT,OACAC,EAAAQ,EAAAR,KACAC,EAAAO,EAAAP,MACAC,EAAAM,EAAAN,KAEAE,EAAAI,EAAAJ,SACAC,EAAAG,EAAAH,OACAC,EAAAE,EAAAF,QACAC,EAAAC,EAAAD,KAEAlwB,EAAA8vB,KANAK,EAAAL,KAOA9vB,EAAA6vB,KAAAA,EACA7vB,EAAA+vB,SAAAA,EACA/vB,EAAAsuB,SAAAA,EACAtuB,EAAAyvB,KAAAA,EACAzvB,EAAA4vB,MAAAA,EACA5vB,EAAAkwB,KAAAA,EACAlwB,EAAA2vB,KAAAA,EACA3vB,EAAAiwB,QAAAA,EACAjwB,EAAAwvB,OAAAA,EACAxvB,EAAAowB,mBAAAA,EACApwB,EAAAmwB,eAAAA,EACAnwB,EAAAqvB,oBAAAA,EACArvB,EAAAgwB,OAAAA,EACAhwB,EAAAsvB,IAAAA,EACAtvB,EAAAuvB,QAAAA,EACAvvB,EAAA0vB,OAAAA,C,8CC9PA,aACApoB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAAswB,oBAAAtwB,EAAAuwB,gBAAAvwB,EAAAwwB,oBAAAxwB,EAAAywB,oBAAAzwB,EAAA0wB,cAAA1wB,EAAA2wB,UAAA3wB,EAAA8tB,SAAA9tB,EAAA4wB,SAAA5wB,EAAAwtB,SAAAxtB,EAAA6wB,SAAA7wB,EAAA8wB,SAAA9wB,EAAA+wB,SAAA,KAAA,EACA,IAAAC,EAAAtxB,EAAA,QAAA,EAwDA,SAAAuxB,EAAAlxB,EAAAmxB,EAAA7xB,GAOA,OANAA,EAAA,IACAA,GAAA,GAEA,EAAAA,GACAA,EAAAA,EAEAA,EAAA,EAAA,EACAU,EAAA,EAAAV,GAAA6xB,EAAAnxB,GAEAV,EAAA,GACA6xB,EAEA7xB,EAAA,EAAA,EACAU,GAAAmxB,EAAAnxB,IAAA,EAAA,EAAAV,GAAA,EAEAU,CACA,CAwJA,SAAA0wB,EAAAU,GACA,OAAAroB,KAAAmb,MAAA,IAAA/c,WAAAiqB,CAAA,CAAA,EAAAC,SAAA,EAAA,CACA,CAQA,SAAAb,EAAAc,GACA,OAAAhQ,SAAAgQ,EAAA,EAAA,CACA,CA7NArxB,EAAA+wB,SAPA,SAAA7xB,EAAAmuB,EAAAC,GACA,MAAA,CACApuB,EAAA,KAAA,EAAA8xB,EAAAM,SAAApyB,EAAA,GAAA,EACAmuB,EAAA,KAAA,EAAA2D,EAAAM,SAAAjE,EAAA,GAAA,EACAC,EAAA,KAAA,EAAA0D,EAAAM,SAAAhE,EAAA,GAAA,CACA,CACA,EAwCAttB,EAAA8wB,SAjCA,SAAA5xB,EAAAmuB,EAAAC,GACApuB,GAAA,EAAA8xB,EAAAM,SAAApyB,EAAA,GAAA,EACAmuB,GAAA,EAAA2D,EAAAM,SAAAjE,EAAA,GAAA,EACAC,GAAA,EAAA0D,EAAAM,SAAAhE,EAAA,GAAA,EACA,IAAAtkB,EAAAF,KAAAE,IAAA9J,EAAAmuB,EAAAC,CAAA,EACAiE,EAAAzoB,KAAAyoB,IAAAryB,EAAAmuB,EAAAC,CAAA,EACAG,EAAA,EACAC,EAAA,EACA8D,GAAAxoB,EAAAuoB,GAAA,EACA,GAAAvoB,IAAAuoB,EAEA9D,EADAC,EAAA,MAGA,CACA,IAAAyD,EAAAnoB,EAAAuoB,EACA7D,EAAA,GAAA8D,EAAAL,GAAA,EAAAnoB,EAAAuoB,GAAAJ,GAAAnoB,EAAAuoB,GACA,OAAAvoB,GACA,KAAA9J,EACAuuB,GAAAJ,EAAAC,GAAA6D,GAAA9D,EAAAC,EAAA,EAAA,GACA,MACA,KAAAD,EACAI,GAAAH,EAAApuB,GAAAiyB,EAAA,EACA,MACA,KAAA7D,EACAG,GAAAvuB,EAAAmuB,GAAA8D,EAAA,CAIA,CACA1D,GAAA,CACA,CACA,MAAA,CAAAA,EAAAA,EAAAC,EAAAA,EAAA8D,EAAAA,CAAA,CACA,EAgDAxxB,EAAA6wB,SAtBA,SAAApD,EAAAC,EAAA8D,GACA,IAeAtyB,EACAmuB,EACAC,EAEA,OAhBAG,GAAA,EAAAuD,EAAAM,SAAA7D,EAAA,GAAA,EACAC,GAAA,EAAAsD,EAAAM,SAAA5D,EAAA,GAAA,EACA8D,GAAA,EAAAR,EAAAM,SAAAE,EAAA,GAAA,EACA,IAAA9D,EAIAxuB,EADAouB,EADAD,EAAAmE,GAOAtyB,EAAA+xB,EADAlxB,EAAA,EAAAyxB,GADAN,EAAAM,EAAA,GAAAA,GAAA,EAAA9D,GAAA8D,EAAA9D,EAAA8D,EAAA9D,GAEAwD,EAAAzD,EAAA,EAAA,CAAA,EACAJ,EAAA4D,EAAAlxB,EAAAmxB,EAAAzD,CAAA,EACAH,EAAA2D,EAAAlxB,EAAAmxB,EAAAzD,EAAA,EAAA,CAAA,GAEA,CAAAvuB,EAAA,IAAAA,EAAAmuB,EAAA,IAAAA,EAAAC,EAAA,IAAAA,CAAA,CACA,EAuCAttB,EAAAwtB,SA/BA,SAAAtuB,EAAAmuB,EAAAC,GACApuB,GAAA,EAAA8xB,EAAAM,SAAApyB,EAAA,GAAA,EACAmuB,GAAA,EAAA2D,EAAAM,SAAAjE,EAAA,GAAA,EACAC,GAAA,EAAA0D,EAAAM,SAAAhE,EAAA,GAAA,EACA,IAAAtkB,EAAAF,KAAAE,IAAA9J,EAAAmuB,EAAAC,CAAA,EACAiE,EAAAzoB,KAAAyoB,IAAAryB,EAAAmuB,EAAAC,CAAA,EACAG,EAAA,EACAlL,EAAAvZ,EACAmoB,EAAAnoB,EAAAuoB,EACA7D,EAAA,IAAA1kB,EAAA,EAAAmoB,EAAAnoB,EACA,GAAAA,IAAAuoB,EACA9D,EAAA,MAEA,CACA,OAAAzkB,GACA,KAAA9J,EACAuuB,GAAAJ,EAAAC,GAAA6D,GAAA9D,EAAAC,EAAA,EAAA,GACA,MACA,KAAAD,EACAI,GAAAH,EAAApuB,GAAAiyB,EAAA,EACA,MACA,KAAA7D,EACAG,GAAAvuB,EAAAmuB,GAAA8D,EAAA,CAIA,CACA1D,GAAA,CACA,CACA,MAAA,CAAAA,EAAAA,EAAAC,EAAAA,EAAAnL,EAAAA,CAAA,CACA,EAuBAviB,EAAA4wB,SAfA,SAAAnD,EAAAC,EAAAnL,GACAkL,EAAA,GAAA,EAAAuD,EAAAM,SAAA7D,EAAA,GAAA,EACAC,GAAA,EAAAsD,EAAAM,SAAA5D,EAAA,GAAA,EACAnL,GAAA,EAAAyO,EAAAM,SAAA/O,EAAA,GAAA,EACA,IAAAhjB,EAAAuJ,KAAA8U,MAAA6P,CAAA,EAEA1tB,EAAAwiB,GAAA,EAAAmL,GACAwD,EAAA3O,GAAA,GAAA/iB,EAFAiuB,EAAAluB,GAEAmuB,GAMA,MAAA,CAAAxuB,EAAA,IAHA,CAAAqjB,EAAA2O,EAAAnxB,EAAAA,EAAAV,EAFAkjB,GAAA,GAAA,EAAA/iB,GAAAkuB,GAEAnL,GAAAkP,EADAlyB,EAAA,GAIA8tB,EAAA,IAFA,CAAAhuB,EAAAkjB,EAAAA,EAAA2O,EAAAnxB,EAAAA,GAAA0xB,GAEAnE,EAAA,IADA,CAAAvtB,EAAAA,EAAAV,EAAAkjB,EAAAA,EAAA2O,GAAAO,EACA,CACA,EAuBAzxB,EAAA8tB,SAfA,SAAA5uB,EAAAmuB,EAAAC,EAAAoE,GAOA,OANAC,EAAA,EACA,EAAAX,EAAAY,MAAA9oB,KAAAmb,MAAA/kB,CAAA,EAAAkyB,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAA9oB,KAAAmb,MAAAoJ,CAAA,EAAA+D,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAA9oB,KAAAmb,MAAAqJ,CAAA,EAAA8D,SAAA,EAAA,CAAA,GAGAM,GACAC,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,GACAH,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,GACAH,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,EACAH,EAAA,GAAAG,OAAA,CAAA,EAAAH,EAAA,GAAAG,OAAA,CAAA,EAAAH,EAAA,GAAAG,OAAA,CAAA,EAEAH,EAAAI,KAAA,EAAA,CACA,EA0BA/xB,EAAA2wB,UAjBA,SAAAzxB,EAAAmuB,EAAAC,EAAA1tB,EAAAoyB,GAQA,OAPAL,EAAA,EACA,EAAAX,EAAAY,MAAA9oB,KAAAmb,MAAA/kB,CAAA,EAAAkyB,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAA9oB,KAAAmb,MAAAoJ,CAAA,EAAA+D,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAA9oB,KAAAmb,MAAAqJ,CAAA,EAAA8D,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAAnB,EAAA7wB,CAAA,CAAA,GAGAoyB,GACAL,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,GACAH,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,GACAH,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,GACAH,EAAA,GAAAE,WAAAF,EAAA,GAAAG,OAAA,CAAA,CAAA,EACAH,EAAA,GAAAG,OAAA,CAAA,EAAAH,EAAA,GAAAG,OAAA,CAAA,EAAAH,EAAA,GAAAG,OAAA,CAAA,EAAAH,EAAA,GAAAG,OAAA,CAAA,EAEAH,EAAAI,KAAA,EAAA,CACA,EAeA/xB,EAAA0wB,cATA,SAAAxxB,EAAAmuB,EAAAC,EAAA1tB,GAOA,MANA,EACA,EAAAoxB,EAAAY,MAAAnB,EAAA7wB,CAAA,CAAA,GACA,EAAAoxB,EAAAY,MAAA9oB,KAAAmb,MAAA/kB,CAAA,EAAAkyB,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAA9oB,KAAAmb,MAAAoJ,CAAA,EAAA+D,SAAA,EAAA,CAAA,GACA,EAAAJ,EAAAY,MAAA9oB,KAAAmb,MAAAqJ,CAAA,EAAA8D,SAAA,EAAA,CAAA,GAEAW,KAAA,EAAA,CACA,EAMA/xB,EAAAywB,oBAAAA,EAKAzwB,EAAAwwB,oBAHA,SAAA/C,GACA,OAAA8C,EAAA9C,CAAA,EAAA,GACA,EAMAztB,EAAAuwB,gBAAAA,EAQAvwB,EAAAswB,oBAPA,SAAA/B,GACA,MAAA,CACArvB,EAAAqvB,GAAA,GACAlB,GAAA,MAAAkB,IAAA,EACAjB,EAAA,IAAAiB,CACA,CACA,C,qCCxPA,aACAjnB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAAiyB,MAAA,KAAA,EAKAjyB,EAAAiyB,MAAA,CACAC,UAAA,UACAC,aAAA,UACAC,KAAA,UACAC,WAAA,UACAC,MAAA,UACAC,MAAA,UACAC,OAAA,UACAC,MAAA,UACAC,eAAA,UACA5C,KAAA,UACA6C,WAAA,UACAC,MAAA,UACAC,UAAA,UACAC,UAAA,UACAC,WAAA,UACAC,UAAA,UACAC,MAAA,UACAC,eAAA,UACAC,SAAA,UACAC,QAAA,UACAvD,KAAA,UACAwD,SAAA,UACAC,SAAA,UACAC,cAAA,UACAC,SAAA,UACAC,UAAA,UACAC,SAAA,UACAC,UAAA,UACAC,YAAA,UACAC,eAAA,UACAC,WAAA,UACAC,WAAA,UACAC,QAAA,UACAC,WAAA,UACAC,aAAA,UACAC,cAAA,UACAC,cAAA,UACAC,cAAA,UACAC,cAAA,UACAC,WAAA,UACAC,SAAA,UACAC,YAAA,UACAC,QAAA,UACAC,QAAA,UACAC,WAAA,UACAC,UAAA,UACAC,YAAA,UACAC,YAAA,UACAC,QAAA,UACAC,UAAA,UACAC,WAAA,UACAC,UAAA,UACA1F,KAAA,UACA2F,KAAA,UACAxF,MAAA,UACAyF,YAAA,UACAnF,KAAA,UACAoF,SAAA,UACAC,QAAA,UACAC,UAAA,UACAC,OAAA,UACAC,MAAA,UACAC,MAAA,UACAC,cAAA,UACAC,SAAA,UACAC,UAAA,UACAC,aAAA,UACAC,UAAA,UACAC,WAAA,UACAC,UAAA,UACAC,qBAAA,UACAC,UAAA,UACAC,WAAA,UACAC,UAAA,UACAC,UAAA,UACAC,YAAA,UACAC,cAAA,UACAC,aAAA,UACAC,eAAA,UACAC,eAAA,UACAC,eAAA,UACAC,YAAA,UACAnH,KAAA,UACAoH,UAAA,UACAC,MAAA,UACA/G,QAAA,UACAgH,OAAA,UACAC,iBAAA,UACAC,WAAA,UACAC,aAAA,UACAC,aAAA,UACAC,eAAA,UACAC,gBAAA,UACAC,kBAAA,UACAC,gBAAA,UACAC,gBAAA,UACAC,aAAA,UACAC,UAAA,UACAC,UAAA,UACAC,SAAA,UACAC,YAAA,UACAC,KAAA,UACAC,QAAA,UACAC,MAAA,UACAC,UAAA,UACA3I,OAAA,UACA4I,UAAA,UACAC,OAAA,UACAC,cAAA,UACAC,UAAA,UACAC,cAAA,UACAC,cAAA,UACAC,WAAA,UACAC,UAAA,UACAC,KAAA,UACAC,KAAA,UACAC,KAAA,UACAC,WAAA,UACA/I,OAAA,UACAgJ,cAAA,UACA1J,IAAA,UACA2J,UAAA,UACAC,UAAA,UACAC,YAAA,UACAC,OAAA,UACAC,WAAA,UACAC,SAAA,UACAC,SAAA,UACAC,OAAA,UACAC,OAAA,UACAC,QAAA,UACAC,UAAA,UACAC,UAAA,UACAC,UAAA,UACAC,KAAA,UACAC,YAAA,UACAC,UAAA,UACAC,IAAA,UACAC,KAAA,UACAC,QAAA,UACAC,OAAA,UACAC,UAAA,UACAC,OAAA,UACAC,MAAA,UACAC,MAAA,UACAC,WAAA,UACA/K,OAAA,UACAgL,YAAA,SACA,C,0BC5JA,aACApzB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAA26B,eAAA36B,EAAA46B,oBAAA56B,EAAA2uB,WAAA,KAAA,EAEA,IAAAkM,EAAAn7B,EAAA,cAAA,EACAo7B,EAAAp7B,EAAA,mBAAA,EACAsxB,EAAAtxB,EAAA,QAAA,EAgEAM,EAAA2uB,WA7CA,SAAAJ,GACA,IAIAiD,EAJAuJ,EAAA,CAAA77B,EAAA,EAAAmuB,EAAA,EAAAC,EAAA,CAAA,EACA1tB,EAAA,EACA8tB,EAAA,KAGAsN,EAAA,CAAA,EACAlwB,EAAA,CAAA,EA6BA,MAzBA,UAAA,OAFAyjB,EADA,UAAA,OAAAA,EACAqM,EAAArM,CAAA,EAEAA,KACAoM,EAAApM,EAAArvB,CAAA,GAAAy7B,EAAApM,EAAAlB,CAAA,GAAAsN,EAAApM,EAAAjB,CAAA,GACAyN,GAAA,EAAAF,EAAA9J,UAAAxC,EAAArvB,EAAAqvB,EAAAlB,EAAAkB,EAAAjB,CAAA,EACA0N,EAAA,CAAA,EACAlwB,EAAA,MAAAmwB,OAAA1M,EAAArvB,CAAA,EAAA2Q,OAAA,CAAA,CAAA,EAAA,OAAA,OAEA8qB,EAAApM,EAAAd,CAAA,GAAAkN,EAAApM,EAAAb,CAAA,GAAAiN,EAAApM,EAAAhM,CAAA,GACAmL,GAAA,EAAAsD,EAAAkK,qBAAA3M,EAAAb,CAAA,EACAnL,GAAA,EAAAyO,EAAAkK,qBAAA3M,EAAAhM,CAAA,EACAwY,GAAA,EAAAF,EAAAjK,UAAArC,EAAAd,EAAAC,EAAAnL,CAAA,EACAyY,EAAA,CAAA,EACAlwB,EAAA,OAEA6vB,EAAApM,EAAAd,CAAA,GAAAkN,EAAApM,EAAAb,CAAA,GAAAiN,EAAApM,EAAAiD,CAAA,IACA9D,GAAA,EAAAsD,EAAAkK,qBAAA3M,EAAAb,CAAA,EACA8D,GAAA,EAAAR,EAAAkK,qBAAA3M,EAAAiD,CAAA,EACAuJ,GAAA,EAAAF,EAAAhK,UAAAtC,EAAAd,EAAAC,EAAA8D,CAAA,EACAwJ,EAAA,CAAA,EACAlwB,EAAA,OAEAxD,OAAAgI,UAAA+c,eAAApsB,KAAAsuB,EAAA,GAAA,KACA3uB,EAAA2uB,EAAA3uB,GAGAA,GAAA,EAAAoxB,EAAAmK,YAAAv7B,CAAA,EACA,CACAo7B,GAAAA,EACAlwB,OAAAyjB,EAAAzjB,QAAAA,EACA5L,EAAA4J,KAAAyoB,IAAA,IAAAzoB,KAAAE,IAAA+xB,EAAA77B,EAAA,CAAA,CAAA,EACAmuB,EAAAvkB,KAAAyoB,IAAA,IAAAzoB,KAAAE,IAAA+xB,EAAA1N,EAAA,CAAA,CAAA,EACAC,EAAAxkB,KAAAyoB,IAAA,IAAAzoB,KAAAE,IAAA+xB,EAAAzN,EAAA,CAAA,CAAA,EACA1tB,EAAAA,CACA,CACA,EAGA,IAIAw7B,EAAA,MAAAvN,OAFA,uBAEA,OAAA,EAAAA,OAJA,gBAIA,GAAA,EAIAwN,EAAA,cAAAxN,OAAAuN,EAAA,YAAA,EAAAvN,OAAAuN,EAAA,YAAA,EAAAvN,OAAAuN,EAAA,WAAA,EACAE,EAAA,cAAAzN,OAAAuN,EAAA,YAAA,EAAAvN,OAAAuN,EAAA,YAAA,EAAAvN,OAAAuN,EAAA,YAAA,EAAAvN,OAAAuN,EAAA,WAAA,EACAG,EAAA,CACAH,SAAA,IAAAI,OAAAJ,CAAA,EACAL,IAAA,IAAAS,OAAA,MAAAH,CAAA,EACAI,KAAA,IAAAD,OAAA,OAAAF,CAAA,EACAI,IAAA,IAAAF,OAAA,MAAAH,CAAA,EACAM,KAAA,IAAAH,OAAA,OAAAF,CAAA,EACA/N,IAAA,IAAAiO,OAAA,MAAAH,CAAA,EACAO,KAAA,IAAAJ,OAAA,OAAAF,CAAA,EACAO,KAAA,uDACAC,KAAA,uDACAC,KAAA,uEACAC,KAAA,sEACA,EAKA,SAAApB,EAAArM,GAEA,GAAA,KADAA,EAAAA,EAAA0N,KAAA,EAAApX,YAAA,GACA3kB,OACA,MAAA,CAAA,EAEA,IAAAg8B,EAAA,CAAA,EACA,GAAApB,EAAA7I,MAAA1D,GACAA,EAAAuM,EAAA7I,MAAA1D,GACA2N,EAAA,CAAA,OAEA,GAAA,gBAAA3N,EACA,MAAA,CAAArvB,EAAA,EAAAmuB,EAAA,EAAAC,EAAA,EAAA1tB,EAAA,EAAAkL,OAAA,MAAA,EAMA,IAAAqxB,EAAAZ,EAAAR,IAAAqB,KAAA7N,CAAA,EACA,OAAA4N,EACA,CAAAj9B,EAAAi9B,EAAA,GAAA9O,EAAA8O,EAAA,GAAA7O,EAAA6O,EAAA,EAAA,GAEAA,EAAAZ,EAAAE,KAAAW,KAAA7N,CAAA,GAEA,CAAArvB,EAAAi9B,EAAA,GAAA9O,EAAA8O,EAAA,GAAA7O,EAAA6O,EAAA,GAAAv8B,EAAAu8B,EAAA,EAAA,GAEAA,EAAAZ,EAAAG,IAAAU,KAAA7N,CAAA,GAEA,CAAAd,EAAA0O,EAAA,GAAAzO,EAAAyO,EAAA,GAAA3K,EAAA2K,EAAA,EAAA,GAEAA,EAAAZ,EAAAI,KAAAS,KAAA7N,CAAA,GAEA,CAAAd,EAAA0O,EAAA,GAAAzO,EAAAyO,EAAA,GAAA3K,EAAA2K,EAAA,GAAAv8B,EAAAu8B,EAAA,EAAA,GAEAA,EAAAZ,EAAAhO,IAAA6O,KAAA7N,CAAA,GAEA,CAAAd,EAAA0O,EAAA,GAAAzO,EAAAyO,EAAA,GAAA5Z,EAAA4Z,EAAA,EAAA,GAEAA,EAAAZ,EAAAK,KAAAQ,KAAA7N,CAAA,GAEA,CAAAd,EAAA0O,EAAA,GAAAzO,EAAAyO,EAAA,GAAA5Z,EAAA4Z,EAAA,GAAAv8B,EAAAu8B,EAAA,EAAA,GAEAA,EAAAZ,EAAAS,KAAAI,KAAA7N,CAAA,GAEA,CACArvB,GAAA,EAAA27B,EAAAtK,iBAAA4L,EAAA,EAAA,EACA9O,GAAA,EAAAwN,EAAAtK,iBAAA4L,EAAA,EAAA,EACA7O,GAAA,EAAAuN,EAAAtK,iBAAA4L,EAAA,EAAA,EACAv8B,GAAA,EAAAi7B,EAAArK,qBAAA2L,EAAA,EAAA,EACArxB,OAAAoxB,EAAA,OAAA,MACA,GAEAC,EAAAZ,EAAAO,KAAAM,KAAA7N,CAAA,GAEA,CACArvB,GAAA,EAAA27B,EAAAtK,iBAAA4L,EAAA,EAAA,EACA9O,GAAA,EAAAwN,EAAAtK,iBAAA4L,EAAA,EAAA,EACA7O,GAAA,EAAAuN,EAAAtK,iBAAA4L,EAAA,EAAA,EACArxB,OAAAoxB,EAAA,OAAA,KACA,GAEAC,EAAAZ,EAAAQ,KAAAK,KAAA7N,CAAA,GAEA,CACArvB,GAAA,EAAA27B,EAAAtK,iBAAA4L,EAAA,GAAAA,EAAA,EAAA,EACA9O,GAAA,EAAAwN,EAAAtK,iBAAA4L,EAAA,GAAAA,EAAA,EAAA,EACA7O,GAAA,EAAAuN,EAAAtK,iBAAA4L,EAAA,GAAAA,EAAA,EAAA,EACAv8B,GAAA,EAAAi7B,EAAArK,qBAAA2L,EAAA,GAAAA,EAAA,EAAA,EACArxB,OAAAoxB,EAAA,OAAA,MACA,EAEAC,CAAAA,EAAAA,EAAAZ,EAAAM,KAAAO,KAAA7N,CAAA,IAEA,CACArvB,GAAA,EAAA27B,EAAAtK,iBAAA4L,EAAA,GAAAA,EAAA,EAAA,EACA9O,GAAA,EAAAwN,EAAAtK,iBAAA4L,EAAA,GAAAA,EAAA,EAAA,EACA7O,GAAA,EAAAuN,EAAAtK,iBAAA4L,EAAA,GAAAA,EAAA,EAAA,EACArxB,OAAAoxB,EAAA,OAAA,KACA,CAGA,CAMA,SAAAvB,EAAApM,GACA,OAAA8N,QAAAd,EAAAH,SAAAgB,KAAAnB,OAAA1M,CAAA,CAAA,CAAA,CACA,CAPAvuB,EAAA46B,oBAAAA,EAQA56B,EAAA26B,eAAAA,C,8EC5LA,aACArzB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAAs8B,aAAAt8B,EAAAu8B,UAAA,KAAA,EACA,IAAAC,EAAA98B,EAAA,SAAA,EACAsxB,EAAAtxB,EAAA,QAAA,EAgBAM,EAAAu8B,UAXA,SAAAE,EAAAjO,GACA,IAAAkO,EAAA,CACAx9B,GAAA,EAAA8xB,EAAAkK,qBAAAuB,EAAAv9B,CAAA,EACAmuB,GAAA,EAAA2D,EAAAkK,qBAAAuB,EAAApP,CAAA,EACAC,GAAA,EAAA0D,EAAAkK,qBAAAuB,EAAAnP,CAAA,CACA,EAIA,OAHAnR,KAAAA,IAAAsgB,EAAA78B,IACA88B,EAAA98B,EAAAmlB,OAAA0X,EAAA78B,CAAA,GAEA,IAAA48B,EAAAG,UAAAD,EAAAlO,CAAA,CACA,EAUAxuB,EAAAs8B,aAPA,WACA,OAAA,IAAAE,EAAAG,UAAA,CACAz9B,EAAA4J,KAAA8zB,OAAA,EACAvP,EAAAvkB,KAAA8zB,OAAA,EACAtP,EAAAxkB,KAAA8zB,OAAA,CACA,CAAA,CACA,C,kDC5BA,aACAt1B,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAAysB,UAAAzsB,EAAA28B,UAAA,KAAA,EACA,IAAA9B,EAAAn7B,EAAA,cAAA,EACAo7B,EAAAp7B,EAAA,mBAAA,EACAm9B,EAAAn9B,EAAA,gBAAA,EACAsxB,EAAAtxB,EAAA,QAAA,EACAi9B,GAsCAA,EAAArtB,UAAAwtB,OAAA,WACA,OAAAt7B,KAAAu7B,cAAA,EAAA,GACA,EACAJ,EAAArtB,UAAA0tB,QAAA,WACA,MAAA,CAAAx7B,KAAAs7B,OAAA,CACA,EAIAH,EAAArtB,UAAAytB,cAAA,WAEA,IAAAhC,EAAAv5B,KAAAy7B,MAAA,EACA,OAAA,IAAAlC,EAAA77B,EAAA,IAAA67B,EAAA1N,EAAA,IAAA0N,EAAAzN,GAAA,GACA,EAIAqP,EAAArtB,UAAA4tB,aAAA,WAEA,IAAAnC,EAAAv5B,KAAAy7B,MAAA,EAIAE,EAAApC,EAAA77B,EAAA,IACAk+B,EAAArC,EAAA1N,EAAA,IACAgQ,EAAAtC,EAAAzN,EAAA,IAEAgQ,EADAH,GAAA,OACAA,EAAA,MAIAr0B,KAAAy0B,KAAA,KAAAJ,GAAA,MAAA,GAAA,EAGAK,EADAJ,GAAA,OACAA,EAAA,MAIAt0B,KAAAy0B,KAAA,KAAAH,GAAA,MAAA,GAAA,EAGAK,EADAJ,GAAA,OACAA,EAAA,MAIAv0B,KAAAy0B,KAAA,KAAAF,GAAA,MAAA,GAAA,EAEA,MAAA,MAAAC,EAAA,MAAAE,EAAA,MAAAC,CACA,EAIAd,EAAArtB,UAAAouB,SAAA,WACA,OAAAl8B,KAAA5B,CACA,EAMA+8B,EAAArtB,UAAAquB,SAAA,SAAAC,GAGA,OAFAp8B,KAAA5B,GAAA,EAAAoxB,EAAAmK,YAAAyC,CAAA,EACAp8B,KAAAq8B,OAAA/0B,KAAAmb,MAAA,IAAAziB,KAAA5B,CAAA,EAAA,IACA4B,IACA,EAIAm7B,EAAArtB,UAAAwuB,aAAA,WAEA,OAAA,IADAt8B,KAAAu8B,MAAA,EAAArQ,CAEA,EAIAiP,EAAArtB,UAAA6d,MAAA,WACA,IAAAI,GAAA,EAAAsN,EAAArN,UAAAhsB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,CAAA,EACA,MAAA,CAAAG,EAAA,IAAAF,EAAAE,EAAAC,EAAAH,EAAAG,EAAAnL,EAAAgL,EAAAhL,EAAA3iB,EAAA4B,KAAA5B,CAAA,CACA,EAKA+8B,EAAArtB,UAAA0uB,YAAA,WACA,IAAAzQ,GAAA,EAAAsN,EAAArN,UAAAhsB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,CAAA,EACAG,EAAA3kB,KAAAmb,MAAA,IAAAsJ,EAAAE,CAAA,EACAC,EAAA5kB,KAAAmb,MAAA,IAAAsJ,EAAAG,CAAA,EACAnL,EAAAzZ,KAAAmb,MAAA,IAAAsJ,EAAAhL,CAAA,EACA,OAAA,IAAA/gB,KAAA5B,EAAA,OAAAiuB,OAAAJ,EAAA,IAAA,EAAAI,OAAAH,EAAA,KAAA,EAAAG,OAAAtL,EAAA,IAAA,EAAA,QAAAsL,OAAAJ,EAAA,IAAA,EAAAI,OAAAH,EAAA,KAAA,EAAAG,OAAAtL,EAAA,KAAA,EAAAsL,OAAArsB,KAAAq8B,OAAA,GAAA,CACA,EAIAlB,EAAArtB,UAAAyuB,MAAA,WACA,IAAArC,GAAA,EAAAb,EAAA/J,UAAAtvB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,CAAA,EACA,MAAA,CAAAG,EAAA,IAAAiO,EAAAjO,EAAAC,EAAAgO,EAAAhO,EAAA8D,EAAAkK,EAAAlK,EAAA5xB,EAAA4B,KAAA5B,CAAA,CACA,EAKA+8B,EAAArtB,UAAA2uB,YAAA,WACA,IAAAvC,GAAA,EAAAb,EAAA/J,UAAAtvB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,CAAA,EACAG,EAAA3kB,KAAAmb,MAAA,IAAAyX,EAAAjO,CAAA,EACAC,EAAA5kB,KAAAmb,MAAA,IAAAyX,EAAAhO,CAAA,EACA8D,EAAA1oB,KAAAmb,MAAA,IAAAyX,EAAAlK,CAAA,EACA,OAAA,IAAAhwB,KAAA5B,EAAA,OAAAiuB,OAAAJ,EAAA,IAAA,EAAAI,OAAAH,EAAA,KAAA,EAAAG,OAAA2D,EAAA,IAAA,EAAA,QAAA3D,OAAAJ,EAAA,IAAA,EAAAI,OAAAH,EAAA,KAAA,EAAAG,OAAA2D,EAAA,KAAA,EAAA3D,OAAArsB,KAAAq8B,OAAA,GAAA,CACA,EAKAlB,EAAArtB,UAAAqe,MAAA,SAAA+D,GAEA,OAAA,EAAAmJ,EAAA/M,UAAAtsB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,EADAoE,EAAA,KAAA,IAAAA,EAAA,CAAA,EACAA,CAAA,CACA,EAKAiL,EAAArtB,UAAA4uB,YAAA,SAAAxM,GAEA,MAAA,IAAAlwB,KAAAmsB,MADA+D,EAAA,KAAA,IAAAA,EAAA,CAAA,EACAA,CAAA,CACA,EAKAiL,EAAArtB,UAAA6uB,OAAA,SAAAnM,GAEA,OAAA,EAAA6I,EAAAlK,WAAAnvB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,EAAA9rB,KAAA5B,EADAoyB,EAAA,KAAA,IAAAA,EAAA,CAAA,EACAA,CAAA,CACA,EAKA2K,EAAArtB,UAAA8uB,aAAA,SAAApM,GAEA,MAAA,IAAAxwB,KAAA28B,OADAnM,EAAA,KAAA,IAAAA,EAAA,CAAA,EACAA,CAAA,CACA,EAKA2K,EAAArtB,UAAA+uB,iBAAA,SAAAC,GAEA,OADA,KAAA,IAAAA,IAAAA,EAAA,CAAA,GACA,IAAA98B,KAAA5B,EAAA4B,KAAA08B,YAAAI,CAAA,EAAA98B,KAAA48B,aAAAE,CAAA,CACA,EAIA3B,EAAArtB,UAAA2tB,MAAA,WACA,MAAA,CACA/9B,EAAA4J,KAAAmb,MAAAziB,KAAAtC,CAAA,EACAmuB,EAAAvkB,KAAAmb,MAAAziB,KAAA6rB,CAAA,EACAC,EAAAxkB,KAAAmb,MAAAziB,KAAA8rB,CAAA,EACA1tB,EAAA4B,KAAA5B,CACA,CACA,EAKA+8B,EAAArtB,UAAAivB,YAAA,WACA,IAAAr/B,EAAA4J,KAAAmb,MAAAziB,KAAAtC,CAAA,EACAmuB,EAAAvkB,KAAAmb,MAAAziB,KAAA6rB,CAAA,EACAC,EAAAxkB,KAAAmb,MAAAziB,KAAA8rB,CAAA,EACA,OAAA,IAAA9rB,KAAA5B,EAAA,OAAAiuB,OAAA3uB,EAAA,IAAA,EAAA2uB,OAAAR,EAAA,IAAA,EAAAQ,OAAAP,EAAA,GAAA,EAAA,QAAAO,OAAA3uB,EAAA,IAAA,EAAA2uB,OAAAR,EAAA,IAAA,EAAAQ,OAAAP,EAAA,IAAA,EAAAO,OAAArsB,KAAAq8B,OAAA,GAAA,CACA,EAIAlB,EAAArtB,UAAAkvB,gBAAA,WACA,SAAAC,EAAAp7B,GAAA,MAAA,GAAAwqB,OAAA/kB,KAAAmb,MAAA,KAAA,EAAA+M,EAAAM,SAAAjuB,EAAA,GAAA,CAAA,EAAA,GAAA,CAAA,CACA,MAAA,CACAnE,EAAAu/B,EAAAj9B,KAAAtC,CAAA,EACAmuB,EAAAoR,EAAAj9B,KAAA6rB,CAAA,EACAC,EAAAmR,EAAAj9B,KAAA8rB,CAAA,EACA1tB,EAAA4B,KAAA5B,CACA,CACA,EAIA+8B,EAAArtB,UAAAovB,sBAAA,WACA,SAAAC,EAAAt7B,GAAA,OAAAyF,KAAAmb,MAAA,KAAA,EAAA+M,EAAAM,SAAAjuB,EAAA,GAAA,CAAA,CAAA,CACA,OAAA,IAAA7B,KAAA5B,EACA,OAAAiuB,OAAA8Q,EAAAn9B,KAAAtC,CAAA,EAAA,KAAA,EAAA2uB,OAAA8Q,EAAAn9B,KAAA6rB,CAAA,EAAA,KAAA,EAAAQ,OAAA8Q,EAAAn9B,KAAA8rB,CAAA,EAAA,IAAA,EACA,QAAAO,OAAA8Q,EAAAn9B,KAAAtC,CAAA,EAAA,KAAA,EAAA2uB,OAAA8Q,EAAAn9B,KAAA6rB,CAAA,EAAA,KAAA,EAAAQ,OAAA8Q,EAAAn9B,KAAA8rB,CAAA,EAAA,KAAA,EAAAO,OAAArsB,KAAAq8B,OAAA,GAAA,CACA,EAIAlB,EAAArtB,UAAAsvB,OAAA,WACA,GAAA,IAAAp9B,KAAA5B,EACA,MAAA,cAEA,GAAA4B,EAAAA,KAAA5B,EAAA,GAIA,IADA,IAAA+xB,EAAA,KAAA,EAAAkJ,EAAA/M,UAAAtsB,KAAAtC,EAAAsC,KAAA6rB,EAAA7rB,KAAA8rB,EAAA,CAAA,CAAA,EACA7O,EAAA,EAAAhU,EAAAnD,OAAA/E,QAAAu4B,EAAA7I,KAAA,EAAAxT,EAAAhU,EAAAvK,OAAAue,CAAA,GAAA,CACA,IAAA+L,EAAA/f,EAAAgU,GAAAje,EAAAgqB,EAAA,GACA,GAAAmH,IADAnH,EAAA,GAEA,OAAAhqB,CAEA,CACA,MAAA,CAAA,CACA,EACAm8B,EAAArtB,UAAA8hB,SAAA,SAAAtmB,GACA,IAAA+zB,EAAAxC,QAAAvxB,CAAA,EAEAg0B,GADAh0B,EAAAA,MAAAA,EAAAA,EAAAtJ,KAAAsJ,OACA,CAAA,GACAi0B,EAAAv9B,KAAA5B,EAAA,GAAA,GAAA4B,KAAA5B,EAEA,OADAi/B,GAAAE,CAAAA,GAAAj0B,CAAAA,EAAA+mB,WAAA,KAAA,GAAA,SAAA/mB,GASA,QAAAA,IACAg0B,EAAAt9B,KAAA+8B,YAAA,GAEA,SAAAzzB,IACAg0B,EAAAt9B,KAAAk9B,sBAAA,GAEA,QAAA5zB,GAAA,SAAAA,IACAg0B,EAAAt9B,KAAA08B,YAAA,GAEA,SAAApzB,IACAg0B,EAAAt9B,KAAA08B,YAAA,CAAA,CAAA,GAEA,SAAApzB,IACAg0B,EAAAt9B,KAAA48B,aAAA,CAAA,CAAA,GAEA,SAAAtzB,IACAg0B,EAAAt9B,KAAA48B,aAAA,GAEA,SAAAtzB,IACAg0B,EAAAt9B,KAAAo9B,OAAA,GAEA,QAAA9zB,IACAg0B,EAAAt9B,KAAAy8B,YAAA,IAGAa,EADA,QAAAh0B,EACAtJ,KAAAw8B,YAAA,EAEAc,IAAAt9B,KAAA08B,YAAA,GAhCA,SAAApzB,GAAA,IAAAtJ,KAAA5B,EACA4B,KAAAo9B,OAAA,EAEAp9B,KAAA+8B,YAAA,CA8BA,EACA5B,EAAArtB,UAAA0vB,SAAA,WACA,OAAAl2B,KAAAmb,MAAAziB,KAAAtC,CAAA,GAAA,KAAA4J,KAAAmb,MAAAziB,KAAA6rB,CAAA,GAAA,GAAAvkB,KAAAmb,MAAAziB,KAAA8rB,CAAA,CACA,EACAqP,EAAArtB,UAAA2vB,MAAA,WACA,OAAA,IAAAtC,EAAAn7B,KAAA4vB,SAAA,CAAA,CACA,EAKAuL,EAAArtB,UAAA4vB,QAAA,SAAA9P,GACA,KAAA,IAAAA,IAAAA,EAAA,IACA,IAAAsM,EAAAl6B,KAAAu8B,MAAA,EAGA,OAFArC,EAAAlK,GAAApC,EAAA,IACAsM,EAAAlK,GAAA,EAAAR,EAAAmO,SAAAzD,EAAAlK,CAAA,EACA,IAAAmL,EAAAjB,CAAA,CACA,EAKAiB,EAAArtB,UAAA8vB,SAAA,SAAAhQ,GACA,KAAA,IAAAA,IAAAA,EAAA,IACA,IAAA2L,EAAAv5B,KAAAy7B,MAAA,EAIA,OAHAlC,EAAA77B,EAAA4J,KAAAE,IAAA,EAAAF,KAAAyoB,IAAA,IAAAwJ,EAAA77B,EAAA4J,KAAAmb,MAAAmL,CAAAA,EAAA,IAAA,GAAA,CAAA,CAAA,EACA2L,EAAA1N,EAAAvkB,KAAAE,IAAA,EAAAF,KAAAyoB,IAAA,IAAAwJ,EAAA1N,EAAAvkB,KAAAmb,MAAAmL,CAAAA,EAAA,IAAA,GAAA,CAAA,CAAA,EACA2L,EAAAzN,EAAAxkB,KAAAE,IAAA,EAAAF,KAAAyoB,IAAA,IAAAwJ,EAAAzN,EAAAxkB,KAAAmb,MAAAmL,CAAAA,EAAA,IAAA,GAAA,CAAA,CAAA,EACA,IAAAuN,EAAA5B,CAAA,CACA,EAMA4B,EAAArtB,UAAA+vB,OAAA,SAAAjQ,GACA,KAAA,IAAAA,IAAAA,EAAA,IACA,IAAAsM,EAAAl6B,KAAAu8B,MAAA,EAGA,OAFArC,EAAAlK,GAAApC,EAAA,IACAsM,EAAAlK,GAAA,EAAAR,EAAAmO,SAAAzD,EAAAlK,CAAA,EACA,IAAAmL,EAAAjB,CAAA,CACA,EAMAiB,EAAArtB,UAAAgwB,KAAA,SAAAlQ,GAEA,OAAA5tB,KAAA+9B,IAAA,QADAnQ,EAAA,KAAA,IAAAA,EAAA,GACAA,CAAA,CACA,EAMAuN,EAAArtB,UAAAkwB,MAAA,SAAApQ,GAEA,OAAA5tB,KAAA+9B,IAAA,QADAnQ,EAAA,KAAA,IAAAA,EAAA,GACAA,CAAA,CACA,EAMAuN,EAAArtB,UAAAmwB,WAAA,SAAArQ,GACA,KAAA,IAAAA,IAAAA,EAAA,IACA,IAAAsM,EAAAl6B,KAAAu8B,MAAA,EAGA,OAFArC,EAAAhO,GAAA0B,EAAA,IACAsM,EAAAhO,GAAA,EAAAsD,EAAAmO,SAAAzD,EAAAhO,CAAA,EACA,IAAAiP,EAAAjB,CAAA,CACA,EAKAiB,EAAArtB,UAAAowB,SAAA,SAAAtQ,GACA,KAAA,IAAAA,IAAAA,EAAA,IACA,IAAAsM,EAAAl6B,KAAAu8B,MAAA,EAGA,OAFArC,EAAAhO,GAAA0B,EAAA,IACAsM,EAAAhO,GAAA,EAAAsD,EAAAmO,SAAAzD,EAAAhO,CAAA,EACA,IAAAiP,EAAAjB,CAAA,CACA,EAKAiB,EAAArtB,UAAAqwB,UAAA,WACA,OAAAn+B,KAAAi+B,WAAA,GAAA,CACA,EAKA9C,EAAArtB,UAAAswB,KAAA,SAAAxQ,GACA,IAAAsM,EAAAl6B,KAAAu8B,MAAA,EACA9P,GAAAyN,EAAAjO,EAAA2B,GAAA,IAEA,OADAsM,EAAAjO,EAAAQ,EAAA,EAAA,IAAAA,EAAAA,EACA,IAAA0O,EAAAjB,CAAA,CACA,EAKAiB,EAAArtB,UAAAiwB,IAAA,SAAAhR,EAAAa,GACA,KAAA,IAAAA,IAAAA,EAAA,IACA,IAAAH,EAAAztB,KAAAy7B,MAAA,EACA9N,EAAA,IAAAwN,EAAApO,CAAA,EAAA0O,MAAA,EACAl9B,EAAAqvB,EAAA,IAOA,OAAA,IAAAuN,EANA,CACAz9B,GAAAiwB,EAAAjwB,EAAA+vB,EAAA/vB,GAAAa,EAAAkvB,EAAA/vB,EACAmuB,GAAA8B,EAAA9B,EAAA4B,EAAA5B,GAAAttB,EAAAkvB,EAAA5B,EACAC,GAAA6B,EAAA7B,EAAA2B,EAAA3B,GAAAvtB,EAAAkvB,EAAA3B,EACA1tB,GAAAuvB,EAAAvvB,EAAAqvB,EAAArvB,GAAAG,EAAAkvB,EAAArvB,CACA,CACA,CACA,EACA+8B,EAAArtB,UAAAuwB,UAAA,SAAAC,EAAAC,GACA,KAAA,IAAAD,IAAAA,EAAA,GACA,KAAA,IAAAC,IAAAA,EAAA,IACA,IAAArE,EAAAl6B,KAAAu8B,MAAA,EACAiC,EAAA,IAAAD,EACAE,EAAA,CAAAz+B,MACA,IAAAk6B,EAAAjO,GAAAiO,EAAAjO,GAAAuS,EAAAF,GAAA,GAAA,KAAA,IAAA,EAAAA,GACApE,EAAAjO,GAAAiO,EAAAjO,EAAAuS,GAAA,IACAC,EAAAp8B,KAAA,IAAA84B,EAAAjB,CAAA,CAAA,EAEA,OAAAuE,CACA,EAIAtD,EAAArtB,UAAA4wB,WAAA,WACA,IAAAxE,EAAAl6B,KAAAu8B,MAAA,EAEA,OADArC,EAAAjO,GAAAiO,EAAAjO,EAAA,KAAA,IACA,IAAAkP,EAAAjB,CAAA,CACA,EACAiB,EAAArtB,UAAA6wB,cAAA,SAAAL,GACA,KAAA,IAAAA,IAAAA,EAAA,GAOA,IANA,IAAAvS,EAAA/rB,KAAA2rB,MAAA,EACAM,EAAAF,EAAAE,EACAC,EAAAH,EAAAG,EACAnL,EAAAgL,EAAAhL,EACA2B,EAAA,GACAkc,EAAA,EAAAN,EACAA,CAAA,IACA5b,EAAArgB,KAAA,IAAA84B,EAAA,CAAAlP,EAAAA,EAAAC,EAAAA,EAAAnL,EAAAA,CAAA,CAAA,CAAA,EACAA,GAAAA,EAAA6d,GAAA,EAEA,OAAAlc,CACA,EACAyY,EAAArtB,UAAA+wB,gBAAA,WACA,IAAA3E,EAAAl6B,KAAAu8B,MAAA,EACAtQ,EAAAiO,EAAAjO,EACA,MAAA,CACAjsB,KACA,IAAAm7B,EAAA,CAAAlP,GAAAA,EAAA,IAAA,IAAAC,EAAAgO,EAAAhO,EAAA8D,EAAAkK,EAAAlK,CAAA,CAAA,EACA,IAAAmL,EAAA,CAAAlP,GAAAA,EAAA,KAAA,IAAAC,EAAAgO,EAAAhO,EAAA8D,EAAAkK,EAAAlK,CAAA,CAAA,EAEA,EAIAmL,EAAArtB,UAAAgxB,aAAA,SAAAC,GACA,IAAAC,EAAAh/B,KAAAy7B,MAAA,EACAwD,EAAA,IAAA9D,EAAA4D,CAAA,EAAAtD,MAAA,EACAW,EAAA4C,EAAA5gC,EAAA6gC,EAAA7gC,GAAA,EAAA4gC,EAAA5gC,GACA,OAAA,IAAA+8B,EAAA,CACAz9B,GAAAshC,EAAAthC,EAAAshC,EAAA5gC,EAAA6gC,EAAAvhC,EAAAuhC,EAAA7gC,GAAA,EAAA4gC,EAAA5gC,IAAAg+B,EACAvQ,GAAAmT,EAAAnT,EAAAmT,EAAA5gC,EAAA6gC,EAAApT,EAAAoT,EAAA7gC,GAAA,EAAA4gC,EAAA5gC,IAAAg+B,EACAtQ,GAAAkT,EAAAlT,EAAAkT,EAAA5gC,EAAA6gC,EAAAnT,EAAAmT,EAAA7gC,GAAA,EAAA4gC,EAAA5gC,IAAAg+B,EACAh+B,EAAAg+B,CACA,CAAA,CACA,EAIAjB,EAAArtB,UAAAoxB,MAAA,WACA,OAAAl/B,KAAAm/B,OAAA,CAAA,CACA,EAIAhE,EAAArtB,UAAAsxB,OAAA,WACA,OAAAp/B,KAAAm/B,OAAA,CAAA,CACA,EAKAhE,EAAArtB,UAAAqxB,OAAA,SAAAvhC,GAKA,IAJA,IAAAs8B,EAAAl6B,KAAAu8B,MAAA,EACAtQ,EAAAiO,EAAAjO,EACA1mB,EAAA,CAAAvF,MACAq/B,EAAA,IAAAzhC,EACAG,EAAA,EAAAA,EAAAH,EAAAG,CAAA,GACAwH,EAAAlD,KAAA,IAAA84B,EAAA,CAAAlP,GAAAA,EAAAluB,EAAAshC,GAAA,IAAAnT,EAAAgO,EAAAhO,EAAA8D,EAAAkK,EAAAlK,CAAA,CAAA,CAAA,EAEA,OAAAzqB,CACA,EAIA41B,EAAArtB,UAAAwxB,OAAA,SAAAvS,GACA,OAAA/sB,KAAA+8B,YAAA,IAAA,IAAA5B,EAAApO,CAAA,EAAAgQ,YAAA,CACA,EACA5B,GA9eA,SAAAA,EAAApO,EAAAC,GAKA,GAHA,KAAA,IAAAA,IAAAA,EAAA,KADAD,EAAA,KAAA,IAAAA,EAAA,GAIAA,aAAAoO,EAEA,OAAApO,EAEA,UAAA,OAAAA,IACAA,GAAA,EAAAsM,EAAAvK,qBAAA/B,CAAA,GAEA/sB,KAAAu/B,cAAAxS,EACA,IAAAwM,GAAA,EAAA8B,EAAAlO,YAAAJ,CAAA,EACA/sB,KAAAu/B,cAAAxS,EACA/sB,KAAAtC,EAAA67B,EAAA77B,EACAsC,KAAA6rB,EAAA0N,EAAA1N,EACA7rB,KAAA8rB,EAAAyN,EAAAzN,EACA9rB,KAAA5B,EAAAm7B,EAAAn7B,EACA4B,KAAAq8B,OAAA/0B,KAAAmb,MAAA,IAAAziB,KAAA5B,CAAA,EAAA,IACA4B,KAAAsJ,OAAA,OAAAL,EAAA+jB,EAAA1jB,QAAAL,EAAAswB,EAAAjwB,OACAtJ,KAAAw/B,aAAAxS,EAAAwS,aAKAx/B,KAAAtC,EAAA,IACAsC,KAAAtC,EAAA4J,KAAAmb,MAAAziB,KAAAtC,CAAA,GAEAsC,KAAA6rB,EAAA,IACA7rB,KAAA6rB,EAAAvkB,KAAAmb,MAAAziB,KAAA6rB,CAAA,GAEA7rB,KAAA8rB,EAAA,IACA9rB,KAAA8rB,EAAAxkB,KAAAmb,MAAAziB,KAAA8rB,CAAA,GAEA9rB,KAAAy/B,QAAAlG,EAAAC,EACA,CA4cAh7B,EAAA28B,UAAAA,EAOA38B,EAAAysB,UALA,SAAA8B,EAAAC,GAGA,OAAA,IAAAmO,EAFApO,EAAA,KAAA,IAAAA,EAAA,GAEAA,EADAC,EAAA,KAAA,IAAAA,EAAA,GACAA,CAAA,CACA,C,kGC9fA,aACAlnB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,C,0BCDA,aACA,IAAApH,EAAAT,MAAAA,KAAAS,kBAAAqF,OAAA/G,OAAA,SAAAjB,EAAA4hC,EAAA9e,EAAA+e,GACAhlB,KAAAA,IAAAglB,IAAAA,EAAA/e,GACA,IAAAgf,EAAA95B,OAAA+5B,yBAAAH,EAAA9e,CAAA,EACAgf,IAAA,QAAAA,EAAAF,EAAA/V,WAAAiW,CAAAA,EAAAE,UAAAF,CAAAA,EAAAG,gBACAH,EAAA,CAAAI,WAAA,CAAA,EAAApgC,IAAA,WAAA,OAAA8/B,EAAA9e,EAAA,CAAA,GAEA9a,OAAAklB,eAAAltB,EAAA6hC,EAAAC,CAAA,CACA,EAAA,SAAA9hC,EAAA4hC,EAAA9e,EAAA+e,GAEA7hC,EADA6hC,EAAAhlB,KAAAA,IAAAglB,EAAA/e,EACA+e,GAAAD,EAAA9e,EACA,GACAqf,EAAAjgC,MAAAA,KAAAigC,cAAA,SAAAP,EAAAlhC,GACA,IAAA,IAAAD,KAAAmhC,EAAA,YAAAnhC,GAAAuH,OAAAgI,UAAA+c,eAAApsB,KAAAD,EAAAD,CAAA,GAAAkC,EAAAjC,EAAAkhC,EAAAnhC,CAAA,CACA,EAEAy8B,GADAl1B,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACA3J,EAAA,SAAA,GACA+hC,EAAA/hC,EAAA,SAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,mBAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,eAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,gBAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,cAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,gBAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,UAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,cAAA,EAAAM,CAAA,EACAyhC,EAAA/hC,EAAA,cAAA,EAAAM,CAAA,EAEAA,EAAA0hC,QAAAlF,EAAA/P,S,4LC3BA,aACAnlB,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAA2hC,OAAA3hC,EAAA48B,OAAA,KAAA,EAIA,IAAAJ,EAAA98B,EAAA,SAAA,EA8HA,SAAAkiC,EAAA3T,GAEA,KAAAA,GAAAA,GAAA,MACAA,GAAA,KAEA,IAAA,IAAAxP,EAAA,EAAAojB,EAAA7hC,EAAA2hC,OAAAljB,EAAAojB,EAAA3hC,OAAAue,CAAA,GAAA,CACA,IACA8P,EAAAuT,EADAD,EAAApjB,EACA,EACA,GAAA8P,EAAAwT,UAAA9T,GAAAM,EAAAwT,SAAA,IAAA9T,GAAAM,EAAAwT,SAAA,GACA,OAAAxT,CAEA,CACA,MAAA1uB,MAAA,iBAAA,CACA,CACA,SAAAmiC,EAAAvb,EAAAwb,GACA,IAIAj5B,EAJA,OAAAmT,KAAAA,IAAA8lB,EACAn5B,KAAA8U,MAAA6I,EAAA,GAAA3d,KAAA8zB,OAAA,GAAAnW,EAAA,GAAA,EAAAA,EAAA,GAAA,GAGAzd,EAAAyd,EAAA,IAAA,EACA8K,EAAA9K,EAAA,IAAA,EAEAkY,GADAsD,GAAA,KAAAA,EAAA,OAAA,QACA,OACAn5B,KAAA8U,MAAA2T,EAAAoN,GAAA31B,EAAAuoB,EAAA,EACA,CACA,SAAAuQ,EAAAI,GACA,IAAAC,EAAAD,EAAAE,YAAA,GAAA,GACAC,EAAAH,EAAAE,YAAAF,EAAAE,YAAAliC,OAAA,GAAA,GACAoiC,EAAAJ,EAAAE,YAAAF,EAAAE,YAAAliC,OAAA,GAAA,GACAqiC,EAAAL,EAAAE,YAAA,GAAA,GACA,MAAA,CACAnmB,KAAAimB,EAAAjmB,KACA8lB,SAAAG,EAAAH,SACAK,YAAAF,EAAAE,YACAI,gBAAA,CAAAL,EAAAE,GACAI,gBAAA,CAAAH,EAAAC,EACA,CACA,CAhIAviC,EAAA48B,OAlCA,SAAAA,EAAAz7B,GAGA,GAAAgb,KAAAA,KAFAhb,EAAA,KAAA,IAAAA,EAAA,GAEAA,GAAAiL,OACA,OAAAjL,EAAAiL,MAAA,CACA,IAAAs2B,EAAAvhC,EAAAiL,MACAu2B,EAAA,GAEA,IADAxhC,EAAAiL,MAAA+P,KAAAA,EACAwmB,EAAAziC,OAAAwiC,GAIAvhC,EAAAiL,MAAA,KACAjL,EAAA8gC,OACA9gC,EAAA8gC,MAAA,GAEAU,EAAA9+B,KAAA+4B,EAAAz7B,CAAA,CAAA,EAGA,OADAA,EAAAiL,MAAAs2B,EACAC,CACA,CAEA,IAAAlV,EAaA,SAAAQ,EAAAgU,GAQA,OAFA/d,GAJAA,EAAA8d,EAmEA,SAAAY,GACA,IAAAC,EAAAxhB,SAAAuhB,EAAA,EAAA,EACA,GAAA,CAAA7d,OAAAC,MAAA6d,CAAA,GAAAA,EAAA,KAAA,EAAAA,EACA,MAAA,CAAAA,EAAAA,GAEA,GAAA,UAAA,OAAAD,EAAA,CAEA,IADAE,EAAA9iC,EAAA2hC,OAAAoB,KAAA,SAAA3jC,GAAA,OAAAA,EAAA6c,OAAA2mB,CAAA,CAAA,KAEArU,EAAAuT,EAAAgB,CAAA,GACAf,SACA,OAAAxT,EAAAwT,SAIA,IAAAiB,EADA,IAAAxG,EAAAG,UAAAiG,CAAA,GACA3B,QAEA,MAAA,CADAhT,EAAA+U,EAAA7V,MAAA,EAAAM,EACAQ,EAEA,CACA,MAAA,CAAA,EAAA,IACA,EAxFAA,CAAA,EACAgU,CAAA,GAGA,EACA,IAAA/d,EAEAA,CACA,EAtBA/iB,EAAA8sB,IAAA9sB,EAAA8gC,IAAA,EAEAvU,EAqBA,SAAAO,EAAA9sB,GACA,GAAA,eAAAA,EAAA8sB,IACA,OAAA,EAEA,GAAA,WAAA9sB,EAAA8hC,WACA,OAAAjB,EAAA,CAAA,EAAA,KAAA7gC,EAAA8gC,IAAA,EAEA,IACAE,GAAAK,EADAZ,EAAA3T,CAAA,EAAAuU,iBACA,GACAH,EAAAG,EAAA,GACA,OAAArhC,EAAA8hC,YACA,IAAA,SACAd,EAAA,GACA,MACA,IAAA,OACAA,EAAAE,EAAA,GACA,MACA,IAAA,QACAA,EAAA,EAIA,CACA,OAAAL,EAAA,CAAAG,EAAAE,GAAAlhC,EAAA8gC,IAAA,CACA,EA7CAxU,EAAAtsB,CAAA,EAGA+iB,EAAA,CAAAuJ,EAAAA,EAAAC,EAAAA,EAAAnL,EA2CA,SAAA2gB,EAAAC,EAAAhiC,GACA,IAAAmhC,EAkBA,SAAAY,EAAAC,GAEA,IADA,IAAAf,EAAAR,EAAAsB,CAAA,EAAAd,YACA7iC,EAAA,EAAAA,EAAA6iC,EAAAliC,OAAA,EAAAX,CAAA,GAAA,CACA,IAAA6jC,EAAAhB,EAAA7iC,GAAA,GACA8jC,EAAAjB,EAAA7iC,GAAA,GACA+jC,EAAAlB,EAAA7iC,EAAA,GAAA,GACAgkC,EAAAnB,EAAA7iC,EAAA,GAAA,GACA,GAAA6jC,GAAAD,GAAAA,GAAAG,EAGA,OAFApC,GAAAqC,EAAAF,IAAAC,EAAAF,IAEAD,GADAE,EAAAnC,EAAAkC,EAGA,CACA,OAAA,CACA,EAhCAF,EAAAC,CAAA,EACAZ,EAAA,IACA,OAAAphC,EAAA8hC,YACA,IAAA,OACAV,EAAAD,EAAA,GACA,MACA,IAAA,QACAA,GAAAC,EAAAD,GAAA,EACA,MACA,IAAA,SACAA,EAAA,EACAC,EAAA,GAIA,CACA,OAAAP,EAAA,CAAAM,EAAAC,GAAAphC,EAAA8gC,IAAA,CACA,EA9DAxU,EAAAC,EAAAvsB,CAAA,CACA,EAKA,OAJAgb,KAAAA,IAAAhb,EAAAy8B,QACA1Z,EAAAtkB,EAAAuB,EAAAy8B,OAGA,IAAApB,EAAAG,UAAAzY,CAAA,CACA,EAqIAlkB,EAAA2hC,OAAA,CACA,CACA1lB,KAAA,aACA8lB,SAAA,KACAK,YAAA,CACA,CAAA,EAAA,GACA,CAAA,IAAA,GAEA,EACA,CACAnmB,KAAA,MACA8lB,SAAA,CAAA,CAAA,GAAA,IACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,EACA,CACAnmB,KAAA,SACA8lB,SAAA,CAAA,GAAA,IACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,EACA,CACAnmB,KAAA,SACA8lB,SAAA,CAAA,GAAA,IACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,EACA,CACAnmB,KAAA,QACA8lB,SAAA,CAAA,GAAA,KACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,EACA,CACAnmB,KAAA,OACA8lB,SAAA,CAAA,IAAA,KACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,EACA,CACAnmB,KAAA,SACA8lB,SAAA,CAAA,IAAA,KACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,EACA,CACAnmB,KAAA,OACA8lB,SAAA,CAAA,IAAA,KACAK,YAAA,CACA,CAAA,GAAA,KACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,GAAA,IACA,CAAA,IAAA,IAEA,E,sCCxRA,aACA96B,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAAwjC,aAAAxjC,EAAAyjC,WAAAzjC,EAAA0jC,YAAA,KAAA,EACA,IAAAlH,EAAA98B,EAAA,SAAA,EASA,SAAAgkC,EAAAC,EAAAC,GACAC,EAAA,IAAArH,EAAAG,UAAAgH,CAAA,EACAG,EAAA,IAAAtH,EAAAG,UAAAiH,CAAA,EACA,OAAA96B,KAAAE,IAAA66B,EAAA3G,aAAA,EAAA4G,EAAA5G,aAAA,CAAA,EAAA,MACAp0B,KAAAyoB,IAAAsS,EAAA3G,aAAA,EAAA4G,EAAA5G,aAAA,CAAA,EAAA,IACA,CAeA,SAAAuG,EAAAE,EAAAC,EAAAG,GAEA,KAAA,IAAAA,IAAAA,EAAA,CAAAC,MAAA,KAAApxB,KAAA,OAAA,GADA,IAEAqxB,EAAAP,EAAAC,EAAAC,CAAA,EACA,QAAA,OAAAn5B,EAAAs5B,EAAAC,OAAAv5B,EAAA,OAAA,OAAA+f,EAAAuZ,EAAAnxB,MAAA4X,EAAA,UACA,IAAA,UACA,IAAA,WACA,OAAA,KAAAyZ,EACA,IAAA,UACA,OAAA,GAAAA,EACA,IAAA,WACA,OAAA,GAAAA,EACA,QACA,MAAA,CAAA,CACA,CACA,CA7BAjkC,EAAA0jC,YAAAA,EA8BA1jC,EAAAyjC,WAAAA,EAqCAzjC,EAAAwjC,aAnBA,SAAAA,EAAAU,EAAAC,EAAA3lB,GAKA,IAHA,IAAA4lB,EAAA,KACAC,EAAA,EACAC,GAHA9lB,EAAA,KAAA,IAAAA,EAAA,CAAA8lB,sBAAA,CAAA,EAAAN,MAAA,KAAApxB,KAAA,OAAA,EAGA4L,GAAA8lB,sBAAAN,EAAAxlB,EAAAwlB,MAAApxB,EAAA4L,EAAA5L,KACA6L,EAAA,EAAA8lB,EAAAJ,EAAA1lB,EAAA8lB,EAAArkC,OAAAue,CAAA,GAAA,CACA,IAAA8P,EAAAgW,EAAA9lB,GACA+lB,EAAAd,EAAAQ,EAAA3V,CAAA,EACA8V,EAAAG,IACAH,EAAAG,EACAJ,EAAA,IAAA5H,EAAAG,UAAApO,CAAA,EAEA,CACA,OAAAkV,EAAAS,EAAAE,EAAA,CAAAJ,MAAAA,EAAApxB,KAAAA,CAAA,CAAA,GAAA,CAAA0xB,EACAF,GAEA5lB,EAAA8lB,sBAAA,CAAA,EACAd,EAAAU,EAAA,CAAA,OAAA,QAAA1lB,CAAA,EACA,C,sCCpFA,aACAlX,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAAykC,WAAA,KAAA,EACA,IAAA5J,EAAAn7B,EAAA,cAAA,EACA88B,EAAA98B,EAAA,SAAA,EAeAM,EAAAykC,WAXA,SAAAC,EAAAC,GACA,IAAApW,EAAA,IAAAiO,EAAAG,UAAA+H,CAAA,EACAE,EAAA,KAAA,EAAA/J,EAAAnK,eAAAnC,EAAArvB,EAAAqvB,EAAAlB,EAAAkB,EAAAjB,EAAAiB,EAAA3uB,CAAA,EACAilC,EAAAD,EACA5D,EAAAzS,EAAAyS,aAAA,qBAAA,GAKA,OAJA2D,IACAjX,EAAA,IAAA8O,EAAAG,UAAAgI,CAAA,EACAE,EAAA,KAAA,EAAAhK,EAAAnK,eAAAhD,EAAAxuB,EAAAwuB,EAAAL,EAAAK,EAAAJ,EAAAI,EAAA9tB,CAAA,GAEA,8CAAAiuB,OAAAmT,EAAA,gBAAA,EAAAnT,OAAA+W,EAAA,eAAA,EAAA/W,OAAAgX,EAAA,GAAA,CACA,C,wDClBA,aAiDA,SAAAC,EAAA1lC,GACA,MAAA,UAAA,OAAAA,GAAA,CAAA,IAAAA,EAAAsQ,QAAA,GAAA,GAAA,IAAAxI,WAAA9H,CAAA,CACA,CAMA,SAAA2lC,EAAA3lC,GACA,MAAA,UAAA,OAAAA,GAAA,CAAA,IAAAA,EAAAsQ,QAAA,GAAA,CACA,CA1DApI,OAAAklB,eAAAxsB,EAAA,aAAA,CAAAqJ,MAAA,CAAA,CAAA,CAAA,EACArJ,EAAA4xB,KAAA5xB,EAAAk7B,oBAAAl7B,EAAAm7B,WAAAn7B,EAAA+kC,aAAA/kC,EAAA8kC,eAAA9kC,EAAAm/B,QAAAn/B,EAAAsxB,QAAA,KAAA,EAiCAtxB,EAAAsxB,QA5BA,SAAAlyB,EAAA4J,GAIA,IAAAg8B,EAAAD,EAFA3lC,EADA0lC,EAAA1lC,CAAA,EACA,OAEAA,CAAA,EAOA,OANAA,EAAA,MAAA4J,EAAA5J,EAAA0J,KAAAyoB,IAAAvoB,EAAAF,KAAAE,IAAA,EAAA9B,WAAA9H,CAAA,CAAA,CAAA,EAEA4lC,IACA5lC,EAAAiiB,SAAA4Z,OAAA77B,EAAA4J,CAAA,EAAA,EAAA,EAAA,KAGAF,KAAAiS,IAAA3b,EAAA4J,CAAA,EAAA,KACA,EAGA,MAAAA,GAIA5J,EAAA,EAAAA,EAAA4J,EAAAA,EAAA5J,EAAA4J,GAAA9B,WAAA+zB,OAAAjyB,CAAA,CAAA,EAKA5J,EAAA4J,EAAA9B,WAAA+zB,OAAAjyB,CAAA,CAAA,CAGA,EASAhJ,EAAAm/B,QAHA,SAAA9N,GACA,OAAAvoB,KAAAyoB,IAAA,EAAAzoB,KAAAE,IAAA,EAAAqoB,CAAA,CAAA,CACA,EAUArxB,EAAA8kC,eAAAA,EAQA9kC,EAAA+kC,aAAAA,EAYA/kC,EAAAm7B,WAPA,SAAAv7B,GAKA,OAJAA,EAAAsH,WAAAtH,CAAA,EAEAA,EADAolB,MAAAplB,CAAA,GAAAA,EAAA,GAAA,EAAAA,EACA,EAEAA,CACA,EAYAI,EAAAk7B,oBANA,SAAA97B,GACA,OAAAA,GAAA,EACA,GAAAyuB,OAAA,IAAA9I,OAAA3lB,CAAA,EAAA,GAAA,EAEAA,CACA,EASAY,EAAA4xB,KAHA,SAAAnyB,GACA,OAAA,IAAAA,EAAAS,OAAA,IAAAT,EAAAw7B,OAAAx7B,CAAA,CACA", "file": "ant-design-blazor.js", "sourcesContent": ["(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u=\"function\"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()", "﻿import { infoHelper as domInfoHelper } from '../modules/dom/infoHelper';\n\nexport class mutationObserver {\n  // @ts-ignore: TS2304: Cannot find name 'MutationObserver'\n  private static mutationObservers: Map<string, MutationObserver> = new Map<string, MutationObserver>();\n\n\n  static create(key: string, invoker, isDotNetInvoker: boolean = true) {\n    // @ts-ignore: TS2304: Cannot find name 'MutationObserver'\n    let observer;\n\n    if (isDotNetInvoker) {\n      observer = new MutationObserver(mutations => mutationObserver.observerCallback(mutations, invoker))\n    } else {\n      observer = new MutationObserver(mutations => invoker(mutations))\n    }\n    mutationObserver.mutationObservers.set(key, observer)\n  }\n\n  static observe(key: string, element, options?: MutationObserverInit) {\n    const observer = mutationObserver.mutationObservers.get(key);\n    if (observer) {\n      let domElement = domInfoHelper.get(element);\n      observer.observe(domElement, options);\n    }\n  }\n\n  static disconnect(key: string): void {\n    const observer = this.mutationObservers.get(key)\n    if (observer) {\n      observer.disconnect()\n    }\n  }\n\n  static dispose(key: string): void {\n    this.disconnect(key)\n    this.mutationObservers.delete(key)\n  }\n\n  private static observerCallback(mutations, invoker) {\n    //TODO: serialize a proper object (check resizeObserver.ts for sample)\n    const entriesJson = JSON.stringify(mutations)\n    invoker.invokeMethodAsync('Invoke', entriesJson)\n  }\n}", "﻿export { resizeObserver as resize } from './resizeObserver';\nexport { mutationObserver } from './mutationObserver';", "﻿import { infoHelper as domInfoHelper} from '../modules/dom/infoHelper';\nimport { boxSize, domRect } from '../modules/dom/types';\n\nclass ResizeObserverEntry {\n  borderBoxSize?: boxSize\n  contentBoxSize?: boxSize\n  contentRect?: domRect\n  target?: Element\n}\n\nexport class resizeObserver {\n\n  static isResizeObserverSupported(): boolean {\n    return \"ResizeObserver\" in window;\n  }\n\n  // @ts-ignore: TS2304: Cannot find name 'ResizeObserver'\n  private static resizeObservers: Map<string, ResizeObserver> = new Map<string, ResizeObserver>();\n\n  static create(key, invoker, isDotNetInvoker: boolean = true ) {\n    // @ts-ignore: TS2304: Cannot find name 'ResizeObserver'\n    let observer;\n        \n    if (isDotNetInvoker) {\n      observer = new ResizeObserver((entries, observer) => resizeObserver.observerCallBack(entries, observer, invoker));\n    } else {\n      observer = new ResizeObserver((entries, observer) => invoker(entries, observer));\n    }\n    resizeObserver.resizeObservers.set(key, observer)\n  }\n\n  static observe(key: string, element) {\n    const observer = resizeObserver.resizeObservers.get(key);\n    if (observer) {\n      let domElement = domInfoHelper.get(element);\n      observer.observe(domElement);\n    }\n  }\n\n  static disconnect(key: string): void {\n    const observer = this.resizeObservers.get(key)\n    if (observer) {\n      observer.disconnect()\n    }\n  }\n\n  static unobserve(key: string, element: Element): void {\n    const observer = this.resizeObservers.get(key)\n\n    if (observer) {\n      let domElement = domInfoHelper.get(element);\n      observer.unobserve(domElement)\n    }\n  }\n\n  static dispose(key: string): void {\n    this.disconnect(key)\n    this.resizeObservers.delete(key)\n  }\n\n  private static observerCallBack(entries, observer, invoker) {\n    if (invoker) {\n      const mappedEntries = new Array<ResizeObserverEntry>()\n      entries.forEach(entry => {        \n        if (entry) {\n          const mEntry = new ResizeObserverEntry()\n          if (entry.borderBoxSize) {\n            mEntry.borderBoxSize = {\n              blockSize: entry.borderBoxSize.blockSize,\n              inlineSize: entry.borderBoxSize.inlineSize\n            }\n          }\n\n          if (entry.contentBoxSize) {\n            mEntry.contentBoxSize = {\n              blockSize: entry.contentBoxSize.blockSize,\n              inlineSize: entry.contentBoxSize.inlineSize\n            }\n          }\n\n          if (entry.contentRect) {\n            mEntry.contentRect = {\n              x: entry.contentRect.x,\n              y: entry.contentRect.y,\n              width: entry.contentRect.width,\n              height: entry.contentRect.height,\n              top: entry.contentRect.top,\n              right: entry.contentRect.right,\n              bottom: entry.contentRect.bottom,\n              left: entry.contentRect.left\n            }\n\n          }\n\n          //mEntry.target = entry.target\n          mappedEntries.push(mEntry)\n        }\n      })\n\n      const entriesJson = JSON.stringify(mappedEntries)\n      invoker.invokeMethodAsync('Invoke', entriesJson)\n    }\n  }\n\n}", "export { state } from './modules/stateProvider';\nexport * as observable from './ObservableApi/observableApi';\nexport { domInfoHelper, domTypes, domManipulationHelper, eventHelper } from './modules/dom/exports';\nexport { styleHelper } from './modules/styleHelper';\nexport {\n  backtopHelper,\n  iconHelper,\n  inputHelper,\n  mentionsHelper,\n  modalHelper,\n  overlayHelper,\n  tableHelper,\n  uploadHelper,\n  downloadHelper\n} from './modules/components/export'\nexport { enableDraggable, disableDraggable, resetModalPosition } from \"./modules/dom/dragHelper\";\n\nexport { generate as generateColor } from \"@ant-design/colors\";\n\nexport function log(text) {\n  console.log(text);  \n}\n", "﻿import { domInfoHelper, domManipulationHelper } from '../dom/exports'\n\nexport class backtopHelper {\n  static backTop(target: string) {\n    let dom = domInfoHelper.get(target);\n    if (dom) {\n      domManipulationHelper.slideTo(dom.scrollTop);\n    } else {\n      domManipulationHelper.slideTo(0);\n    }\n  }\n}", "﻿export class downloadHelper {\n    static triggerFileDownload(fileName, url) {\n        const anchorElement = document.createElement('a');\n        anchorElement.href = url;\n        anchorElement.download = fileName ?? '';\n        anchorElement.click();\n        anchorElement.remove();\n    }\n}", "﻿export { backtopHelper } from './backtopHelper';\nexport { overlayHelper } from './overlayHelper';\nexport { uploadHelper } from './uploadHelper';\nexport { downloadHelper } from './downloadHelper';\nexport { mentionsHelper } from './mentionsHelper';\nexport { modalHelper } from './modalHelper';\nexport { inputHelper } from './inputHelper';\nexport { tableHelper } from './tableHelper';\nexport { iconHelper } from './iconHelper';", "﻿export class iconHelper {\n  static createIconFromfontCN(scriptUrl) {\n    if (document.querySelector(`[data-namespace=\"${scriptUrl}\"]`)) {\n      return;\n    }\n    const script = document.createElement(\"script\");\n    script.setAttribute(\"src\", scriptUrl);\n    script.setAttribute(\"data-namespace\", scriptUrl);\n    document.body.appendChild(script);\n  }\n}\n", "﻿import { domInfoHelper } from '../dom/exports'\nimport { state } from '../stateProvider';\nimport { resize } from '../../ObservableApi/observableApi';\n\nexport class inputHelper {\n\n  static getTextAreaInfo(element) {\n    if (!element) return null;\n    var result = {};\n    var dom = domInfoHelper.get(element);\n    if (!dom) return null;\n    result[\"scrollHeight\"] = dom.scrollHeight || 0;\n\n    if (element.currentStyle) {\n      result[\"lineHeight\"] = parseFloat(element.currentStyle[\"line-height\"]);\n      result[\"paddingTop\"] = parseFloat(element.currentStyle[\"padding-top\"]);\n      result[\"paddingBottom\"] = parseFloat(element.currentStyle[\"padding-bottom\"]);\n      result[\"borderBottom\"] = parseFloat(element.currentStyle[\"border-bottom\"]);\n      result[\"borderTop\"] = parseFloat(element.currentStyle[\"border-top\"]);\n    }\n    else if (window.getComputedStyle) {\n      result[\"lineHeight\"] = parseFloat(document.defaultView.getComputedStyle(element, null).getPropertyValue(\"line-height\"));\n      result[\"paddingTop\"] = parseFloat(document.defaultView.getComputedStyle(element, null).getPropertyValue(\"padding-top\"));\n      result[\"paddingBottom\"] = parseFloat(document.defaultView.getComputedStyle(element, null).getPropertyValue(\"padding-bottom\"));\n      result[\"borderBottom\"] = parseFloat(document.defaultView.getComputedStyle(element, null).getPropertyValue(\"border-bottom\"));\n      result[\"borderTop\"] = parseFloat(document.defaultView.getComputedStyle(element, null).getPropertyValue(\"border-top\"));\n    }\n    //Firefox can return this as NaN, so it has to be handled here like that.\n    if (Object.is(NaN, result[\"borderTop\"]))\n      result[\"borderTop\"] = 1;\n    if (Object.is(NaN, result[\"borderBottom\"]))\n      result[\"borderBottom\"] = 1;\n    return result;\n  }\n\n  static registerResizeTextArea(element: HTMLTextAreaElement, minRows: number, maxRows: number, objReference) {\n    if (!objReference) {\n      this.disposeResizeTextArea(element);\n    }\n    else {\n      state.objReferenceDict[element.id] = objReference;\n      state.eventCallbackRegistry[element.id + \"input\"] = function () { inputHelper.resizeTextArea(element, minRows, maxRows); }\n      element.addEventListener(\"input\", state.eventCallbackRegistry[element.id + \"input\"]);\n      resize.create(element.id + \"-resize\", () => {\n        inputHelper.resizeTextArea(element, minRows, maxRows);\n      }, false);\n      resize.observe(element.id + \"-resize\", element);\n      inputHelper.resizeTextArea(element, minRows, maxRows);\n      element.style.resize = 'none';\n      return this.getTextAreaInfo(element);\n    }\n  }\n\n  static disposeResizeTextArea(element: HTMLTextAreaElement) {\n    element.removeEventListener(\"input\", state.eventCallbackRegistry[element.id + \"input\"]);\n    resize.unobserve(element.id + \"-resize\", element);\n    state.objReferenceDict[element.id] = null;\n    state.eventCallbackRegistry[element.id + \"input\"] = null;\n  }\n\n  static resizeTextArea(element: HTMLTextAreaElement, minRows: number, maxRows: number) {\n    let dims = this.getTextAreaInfo(element);\n    if (!dims) return;\n\n    let rowHeight = dims[\"lineHeight\"];\n    let offsetHeight = dims[\"paddingTop\"] + dims[\"paddingBottom\"] + dims[\"borderTop\"] + dims[\"borderBottom\"];\n    let oldHeight = parseFloat(element.style.height);\n    //use rows attribute to evaluate real scroll height\n    let oldRows = element.rows;\n    element.rows = minRows;\n    element.style.height = 'auto';    \n    \n    var rows = Math.trunc(element.scrollHeight / rowHeight);\n    element.rows = oldRows;\n    rows = Math.max(minRows, rows);\n    var newHeight = 0;\n    if (rows > maxRows) {\n      rows = maxRows;\n\n      newHeight = (rows * rowHeight + offsetHeight);\n      element.style.height = newHeight + \"px\";\n      element.style.overflowY = \"visible\";\n    }\n    else {\n      newHeight = rows * rowHeight + offsetHeight;\n      element.style.height = newHeight + \"px\";\n      element.style.overflowY = \"hidden\";\n    }\n    if (oldHeight !== newHeight) {\n      let textAreaObj = state.objReferenceDict[element.id];\n      textAreaObj.invokeMethodAsync(\"ChangeSizeAsyncJs\", element.scrollWidth, newHeight);\n    }\n  }\n\n  static setSelectionStart(element, position: number) {\n    if (position >= 0) {\n      let dom: HTMLInputElement = domInfoHelper.get(element);\n      if (dom) {\n        if (position <= dom.value.length) {\n          dom.selectionStart = position;\n          dom.selectionEnd = position;\n        }\n      }\n    }\n  }\n\n}", "﻿export class mentionsHelper {\n\n    private static isPopShowFlag: boolean;\n\n    public static setPopShowFlag = function (show: boolean): void {\n        mentionsHelper.isPopShowFlag = show;\n    }\n\n    public static setEditorKeyHandler = function (Mentions: any, element: HTMLTextAreaElement): void {\n\n        var textArea = mentionsHelper.getTextarea(element);\n        textArea.onkeydown = async (ev): Promise<any> => {\n            //判断isPopShow不能用异步方法\n            if (!mentionsHelper.isPopShowFlag) return;\n            if (ev.key == \"ArrowUp\") {\n                ev.preventDefault();\n                await Mentions.invokeMethodAsync(\"PrevOption\");\n            } else if (ev.key == \"ArrowDown\") {\n                ev.preventDefault();\n                await Mentions.invokeMethodAsync(\"NextOption\");\n            }\n            else if (ev.key == \"Enter\") {\n                ev.preventDefault();\n                await Mentions.invokeMethodAsync(\"EnterOption\");\n            }\n            //其他按键在c#中处理\n        }\n    }\n\n    public static getProp = function (e: HTMLElement, propName: string): any {\n        var textArea = mentionsHelper.getTextarea(e);\n\n        return textArea[propName];\n    }\n\n    public static getCursorXY = function (element: HTMLElement) {\n        var textArea = mentionsHelper.getTextarea(element);\n        let format = function (value) {\n            value = value.replace(/<|>|`|\"|&/g, '?');\n            return value;\n        };\n        let inputorValue = textArea.value;\n        let pos = textArea.selectionStart;\n        let start_range = inputorValue.slice(0, pos);\n        if (start_range.length > 0) start_range = start_range.substring(0, start_range.length - 1);\n        let end_range = inputorValue.slice(pos);\n        let html = format(start_range);\n        html += \"<span>@</span>\";\n        html += format(end_range);\n\n        let div_mirror = document.createElement(\"div\");\n        div_mirror.className = \"ant-mentions-measure\"\n        div_mirror.innerHTML = html;\n        textArea.parentNode.append(div_mirror);\n\n        let flag: HTMLSpanElement = div_mirror.querySelector(\"span\");\n        //  let flagPos = flag.getBoundingClientRect();\n        //  let textAreaPos = textArea.getBoundingClientRect();\n        //  let bodyPos = document.body.getBoundingClientRect();\n        let left = flag.offsetLeft - textArea.scrollLeft + 16;\n        let top = flag.offsetTop - textArea.scrollTop + 16;\n\n        div_mirror.remove();\n        return [left, top];\n    };\n\n    private static getTextarea(element: HTMLElement) {\n        const textAreaTag = \"TEXTAREA\";\n        var textarea = element;\n        if (element.tagName != textAreaTag) {\n            var allTextareas = element.getElementsByTagName(textAreaTag);\n            if (allTextareas.length == 0) {\n                throw \"Mentions requires a textarea to be rendered, but none were found.\";\n            }\n            textarea = allTextareas[0] as HTMLTextAreaElement;\n        }\n\n        return textarea as HTMLTextAreaElement;\n    }\n}\n", "﻿import { domInfoHelper } from '../dom/exports'\n\nexport class modalHelper {\n  static focusDialog(selector: string, count: number = 0) {\n    let ele = <HTMLElement>document.querySelector(selector);\n    if (ele) {\n      if (ele.hasAttribute(\"disabled\")) {\n        let htmlElement = <HTMLElement>document.activeElement;\n        htmlElement?.blur();\n      } else {\n        setTimeout(() => {\n          ele.focus();\n          let curId = \"#\" + domInfoHelper.getActiveElement();\n          if (curId !== selector) {\n            if (count < 10) {\n              this.focusDialog(selector, count + 1);\n            }\n          }\n        }, 10);\n      }\n    }\n  }\n\n  static destroyAllDialog() {\n    document.querySelectorAll('.ant-modal-root')\n      .forEach(e => document.body.removeChild(e.parentNode));\n  }\n}", "﻿import { domInfoHelper, domTypes } from '../dom/exports'\nimport { resize } from '../../ObservableApi/observableApi';\nimport { mutationObserver as mutation } from '../../ObservableApi/mutationObserver';\n\n//Make sure the enum is identical as C# AntDesign.Placement enum\nexport enum Placement {\n  TopLeft       = 0,\n  Top           = 2,\n  TopRight      = 3,\n  Left          = 4,\n  LeftTop       = 5,\n  LeftBottom    = 6,\n  Right         = 7,\n  RightTop      = 8,\n  RightBottom   = 9,\n  BottomLeft    = 10,\n  Bottom        = 12,\n  BottomRight   = 13\n}\n\n//Make sure the enum is identical as C# AntDesign.TriggerBoundyAdjustMode enum\nexport enum TriggerBoundyAdjustMode {\n  None     = 0,\n  InView   = 1,\n  InScroll = 2\n}\n\ntype verticalPosition = {\n  top?: number,\n  bottom?: number\n}\n\ntype horizontalPosition = {\n  left?: number,\n  right?: number\n}\n\nexport type overlayConstraints = {\n  verticalOffset: number,\n  horizontalOffset: number,\n  arrowPointAtCenter: boolean\n}\nexport type coordinates = {\n  top?: number,\n  bottom?: number,\n  left?: number,\n  right?: number\n}\n\n\nexport type overlayPosition = {\n  top?: number,\n  bottom?: number,\n  left?: number,\n  right?: number,\n  zIndex: number,\n  placement?: Placement,\n}\n\nexport class Overlay {\n    private static appliedStylePositionMap: Map<Placement,\n        { horizontal: \"left\" | \"right\", vertical: \"top\" | \"bottom\", class: string }> =\n    new Map([\n      [Placement.TopLeft,       { horizontal: \"left\", vertical: \"bottom\", class: \"topLeft\"  }],\n      [Placement.Top,           { horizontal: \"left\", vertical: \"bottom\", class: \"top\" }],\n      [Placement.TopRight,      { horizontal: \"right\", vertical: \"bottom\", class: \"topRight\" }],\n      [Placement.Left,          { horizontal: \"right\", vertical: \"top\", class: \"left\" }],\n      [Placement.LeftTop,       { horizontal: \"right\", vertical: \"top\", class: \"leftTop\" }],\n      [Placement.LeftBottom,    { horizontal: \"right\", vertical: \"bottom\", class: \"leftBottom\" }],\n      [Placement.Right,         { horizontal: \"left\", vertical: \"top\", class: \"right\" }],\n      [Placement.RightTop,      { horizontal: \"left\", vertical: \"top\", class: \"rightTop\" }],\n      [Placement.RightBottom,   { horizontal: \"left\", vertical: \"bottom\", class: \"rightBottom\" }],\n      [Placement.BottomLeft,    { horizontal: \"left\", vertical: \"top\", class: \"bottomLeft\" }],\n      [Placement.Bottom,        { horizontal: \"left\", vertical: \"top\", class: \"bottom\" }],\n      [Placement.BottomRight,   { horizontal: \"right\", vertical: \"top\", class: \"bottomRight\" }],\n    ]);\n\n  private static reverseVerticalPlacementMap: Map<Placement, Function> =\n    new Map([\n      [Placement.TopLeft,       (position: string) => Placement.BottomLeft],\n      [Placement.Top,           (position: string) => Placement.Bottom],\n      [Placement.TopRight,      (position: string) => Placement.BottomRight],\n      [Placement.Left,          (position: string) => position === \"top\" ? Placement.LeftBottom : Placement.LeftTop],\n      [Placement.LeftTop,       (position: string) => Placement.LeftBottom],\n      [Placement.LeftBottom,    (position: string) => Placement.LeftTop],\n      [Placement.Right,         (position: string) => position === \"top\" ? Placement.RightBottom : Placement.RightTop],\n      [Placement.RightTop,      (position: string) => Placement.RightBottom],\n      [Placement.RightBottom,   (position: string) => Placement.RightTop],\n      [Placement.BottomLeft,    (position: string) => Placement.TopLeft],\n      [Placement.Bottom,        (position: string) => Placement.Top],\n      [Placement.BottomRight,   (position: string) => Placement.TopRight]\n    ]);\n    \n  private static reverseHorizontalPlacementMap: Map<Placement, Function> =\n    new Map([\n      [Placement.TopLeft,       (position: string) => Placement.TopRight],\n      [Placement.Top,     (position: string) => position === \"left\" ? Placement.TopRight : Placement.TopLeft],\n      [Placement.TopRight,      (position: string) => Placement.TopLeft],\n      [Placement.Left,          (position: string) => Placement.Right],\n      [Placement.LeftTop,       (position: string) => Placement.RightTop],\n      [Placement.LeftBottom,    (position: string) => Placement.RightBottom],\n      [Placement.Right,         (position: string) => Placement.Left],\n      [Placement.RightTop,      (position: string) => Placement.LeftBottom],\n      [Placement.RightBottom,   (position: string) => Placement.LeftTop],\n      [Placement.BottomLeft,    (position: string) => Placement.BottomRight],\n      [Placement.Bottom,        (position: string) => position === \"left\" ? Placement.BottomRight : Placement.BottomLeft],\n      [Placement.BottomRight,   (position: string) => Placement.BottomLeft]\n    ]);    \n    \n  private static arrowCenterPlacementMatch: Map<Placement, Placement> =\n    new Map([\n      [Placement.TopLeft,       Placement.Top],\n      [Placement.Top,           Placement.Top],\n      [Placement.TopRight,      Placement.Top],\n      [Placement.Left,          Placement.Left],\n      [Placement.LeftTop,       Placement.Left],\n      [Placement.LeftBottom,    Placement.Left],\n      [Placement.Right,         Placement.Right],\n      [Placement.RightTop,      Placement.Right],\n      [Placement.RightBottom,   Placement.Right],\n      [Placement.BottomLeft,    Placement.Bottom],\n      [Placement.Bottom,        Placement.Bottom],\n      [Placement.BottomRight,   Placement.Bottom]\n    ]);   \n    \n  private blazorId: string;\n  public overlay: HTMLDivElement;\n  private container: HTMLElement;\n  private trigger: HTMLElement;\n\n  private overlayInfo: domTypes.domInfo;\n  private containerInfo: domTypes.domInfo;\n  private triggerInfo: domTypes.domInfo;\n  \n  private containerBoundarySize: coordinates;\n  private bodyBoundarySize: coordinates;\n  \n  private placement: Placement;\n  private recentPlacement: Placement;\n  private initialPlacement?: Placement;\n\n  private triggerPrefixCls: string; \n\n  private boundyAdjustMode: TriggerBoundyAdjustMode\n  public position: overlayPosition;\n  public sanitizedPosition: overlayPosition;\n  \n  private overlayPreset: domTypes.position;\n\n  private verticalCalculation:\n    (triggerPosition: number, triggerHeight: number, container: domTypes.domInfo,\n    trigger: domTypes.domInfo, overlayHeight: number, constraints: overlayConstraints)\n      => verticalPosition;\n\n  private horizontalCalculation:\n    (triggerPosition: number, triggerWidth: number, container: domTypes.domInfo,\n    trigger: domTypes.domInfo, overlayWidth: number, constraints: overlayConstraints)\n        => horizontalPosition;\n\n  private overlayConstraints: overlayConstraints; \n  private duringInit = true;\n  private selectedVerticalPosition: \"top\" | \"bottom\";\n  private selectedHorizontalPosition: \"left\" | \"right\";\n  private calculationsToPerform: Set<\"horizontal\"|\"vertical\">;\n\n  private triggerPosition: coordinates & { absoluteTop?: number, absoluteLeft?: number, height?: number, width?: number } = { };  \n\n  private isContainerBody: boolean;\n  private isContainerOverBody = false;\n  private isTriggerFixed: boolean; //refers to trigger or any of its parent having \"position:fixed\"\n  private lastScrollPosition: number; //used only if isTriggerFixed === true\n\n  private scrollbarSize: {\n    horizontalHeight: number,\n    verticalWidth: number\n  }  \n\n  constructor(blazorId: string,\n    overlay: HTMLDivElement, container: HTMLElement, trigger: HTMLElement, placement: Placement, \n    triggerBoundyAdjustMode: TriggerBoundyAdjustMode, triggerIsWrappedInDiv: boolean, triggerPrefixCls: string,\n    overlayConstraints: overlayConstraints) {\n    this.blazorId = blazorId;\n    this.overlay = overlay;  \n    //containerInfo & scrollbars have to be obtained here, because after\n    //removal of classes, the overlay goes to left: -9999 what causes artificial \n    //scrollbars and viewport dimensions are changing\n    this.containerInfo = domInfoHelper.getInfo(container);\n    this.container = container;\n    this.isContainerBody = container === document.body;\n    this.calculateScrollBarSizes()\n    if (!this.isContainerBody) {\n      this.isContainerOverBody = domInfoHelper.findAncestorWithZIndex(this.container) > 0;\n    }\n\n    this.overlay.style.cssText = this.overlay.style.cssText.replace(\"display: none;\", \"\");\n    this.overlay.style.top = \"0px\"; //reset to prevent scrollbars if do not exist\n    this.removeHiddenClass()\n\n    //The trigger is actually wrapping div, which can have its own dimensions (coming from styles).\n    //So, first valid HTML element is picked and if there is none, the wrapping div is set as trigger.\n    //Triggers are always wrapped in div if the <ChildElement> instead of <Unbound> is used.\n    this.trigger = Overlay.getFirstValidChild(trigger, triggerIsWrappedInDiv);\n    this.triggerPrefixCls = triggerPrefixCls;\n    if (overlayConstraints.arrowPointAtCenter){      \n      this.placement = Overlay.arrowCenterPlacementMatch.get(placement);\n    } else {\n      this.placement = placement;\n    }\n    this.initialPlacement = this.placement;\n    this.boundyAdjustMode = triggerBoundyAdjustMode;    \n    this.overlayConstraints = overlayConstraints;\n\n    this.position = { zIndex: 0 };\n    this.selectedHorizontalPosition = Overlay.appliedStylePositionMap.get(this.placement).horizontal;\n    this.selectedVerticalPosition = Overlay.appliedStylePositionMap.get(this.placement).vertical;\n\n    this.verticalCalculation = Overlay.setVerticalCalculation(this.placement, this.selectedVerticalPosition);\n    this.horizontalCalculation = Overlay.setHorizontalCalculation(this.placement, this.selectedHorizontalPosition);\n    this.isTriggerFixed = domInfoHelper.isFixedPosition(this.trigger);\n    this.observe();\n  }\n\n  static getFirstValidChild(element: HTMLElement, triggerIsWrappedInDiv: boolean): HTMLElement {\n    if (triggerIsWrappedInDiv)\n    {      \n      for(let i = 0; i < element.childNodes.length; i++) {\n        const childElement = element.childNodes[i] as HTMLElement;\n        if (childElement.innerHTML)\n          return childElement;\n      }\n    }\n    return element\n  }\n\n  static setVerticalCalculation(placement: Placement, position: \"top\" | \"bottom\") {\n    if (position === \"top\") {\n      switch (placement) {\n        case Placement.LeftTop:\n        case Placement.RightTop:          \n          return function(triggerTop: number, triggerHeight: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayHeight: number, constraints: overlayConstraints) {               \n            return { \n              top: triggerTop,\n              bottom: Overlay.reversePositionValue(triggerTop, container.scrollHeight, overlayHeight) \n            };              \n          }; \n        case Placement.BottomLeft:\n        case Placement.Bottom:\n        case Placement.BottomRight:\n          return function(triggerTop: number, triggerHeight: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayHeight: number, constraints: overlayConstraints) { \n            const position: verticalPosition = {\n              top: triggerTop + triggerHeight + constraints.verticalOffset,\n            };\n            position.bottom = Overlay.reversePositionValue(position.top, container.scrollHeight, overlayHeight)\n            return position;\n          }; \n        case Placement.Left:\n        case Placement.Right:\n          return function(triggerTop: number, triggerHeight: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayHeight: number, constraints: overlayConstraints) {     \n            const position: verticalPosition = { \n              top: triggerTop + (triggerHeight / 2) - (overlayHeight / 2)\n            };\n            position.bottom = Overlay.reversePositionValue(position.top, container.scrollHeight, overlayHeight)\n            return position;\n          }; \n      }\n    }    \n    if (position === \"bottom\") {\n      switch (placement) {\n        case Placement.TopLeft:\n        case Placement.Top:\n        case Placement.TopRight:\n          return function(triggerBottom: number, triggerHeight: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayHeight: number, constraints: overlayConstraints) { \n            const position: verticalPosition = { \n              bottom: triggerBottom + triggerHeight + constraints.verticalOffset,\n            };\n            position.top = Overlay.reversePositionValue(position.bottom, container.scrollHeight, overlayHeight);\n            return position;\n          };           \n        case Placement.LeftBottom:\n        case Placement.RightBottom:\n          return function(triggerBottom: number, triggerHeight: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayHeight: number, constraints: overlayConstraints) {  \n            const position: verticalPosition = { \n              bottom: triggerBottom,\n              top: Overlay.reversePositionValue(triggerBottom, container.scrollHeight, overlayHeight)\n            };            \n            return position;                               \n          };   \n      }\n    }\n    //fallback - should not happen, but to avoid crashing scenarios, revert to BottomLeft\n    console.log(\"Error: setVerticalCalculation did not match, nothing selected!!! Fallback.\", placement, position);\n    return Overlay.setVerticalCalculation(Placement.BottomLeft, \"top\");\n  }\n\n  static setHorizontalCalculation(placement: Placement, position: \"left\" | \"right\") {\n    if (position === \"left\") {\n      switch (placement) {\n        case Placement.TopLeft:\n        case Placement.BottomLeft:\n          return function(triggerLeft: number, triggerWidth: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayWidth: number, constraints: overlayConstraints) {        \n            return { \n              left: triggerLeft,\n              right: Overlay.reversePositionValue(triggerLeft, container.scrollWidth, overlayWidth)\n            };              \n          }; \n        case Placement.Right:\n        case Placement.RightTop:\n        case Placement.RightBottom:\n          return function(triggerLeft: number, triggerWidth: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayWidth: number, constraints: overlayConstraints) {        \n            const position: horizontalPosition = {\n              left: triggerLeft + triggerWidth + constraints.horizontalOffset\n            };\n            position.right = Overlay.reversePositionValue(position.left, container.scrollWidth, overlayWidth)\n            return position;\n          };\n\n        case Placement.Top:\n        case Placement.Bottom:\n          return function(triggerLeft: number, triggerWidth: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayWidth: number, constraints: overlayConstraints) {        \n            const position: horizontalPosition = {\n              left: triggerLeft + (triggerWidth / 2) - (overlayWidth / 2)\n            };\n            position.right = Overlay.reversePositionValue(position.left, container.scrollWidth, overlayWidth)\n            return position;\n          };\n      }\n    }\n    if (position === \"right\") {\n      switch (placement) {\n        case Placement.TopRight:\n        case Placement.BottomRight:\n          return function(triggerRight: number, triggerWidth: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayWidth: number, constraints: overlayConstraints) {        \n            let position: horizontalPosition = {\n              right: triggerRight,\n              left: Overlay.reversePositionValue(triggerRight, container.scrollWidth, overlayWidth)\n            };            \n            return position;\n          };\n        case Placement.Left:\n        case Placement.LeftTop:\n        case Placement.LeftBottom:\n          return function(triggerRight: number, triggerWidth: number, container: domTypes.domInfo, trigger: domTypes.domInfo, overlayWidth: number, constraints: overlayConstraints) {\n            const position: horizontalPosition = {\n              right: triggerRight + triggerWidth + constraints.horizontalOffset\n            };\n            position.left = Overlay.reversePositionValue(position.right, container.scrollWidth, overlayWidth)\n            return position;\n          };          \n      }      \n    }\n    //fallback - should not happen, but to avoid crashing scenarios, revert to BottomLeft\n    console.log(\"Error: setHorizontalCalculation did not match, nothing selected!!! Fallback.\", placement, position);\n    return Overlay.setVerticalCalculation(Placement.BottomLeft, \"top\");    \n  }\n\n  /**\n   * Calculates reversed position. So for given left will return right,\n   * for top => bottom, etc.\n   * @param the value that needs to be reversed (left in scenario: left => right)\n   * @param for horizontal (left, right) container width & for vertical (top, bottom) container height\n   * @param for horizontal (left, right) overlay width & for vertical (top, bottom) overlay height\n   * @returns number\n   */\n  static reversePositionValue(position: number, containerDimension: number, overlayDimension: number) {    \n    return containerDimension - position - overlayDimension;\n  }\n\n  private removeHiddenClass() {    \n    let end = this.overlay.className.indexOf(\"-hidden\");\n    let start = this.overlay.className.lastIndexOf(\" \", end)\n    if (start >= 0) {\n      let className = this.overlay.className.substr(start + 1, end);\n      if (className !== \"\") {\n        this.overlay.classList.remove(className);\n      }\n    }\n  }\n\n  private calculateScrollBarSizes() {\n    if (this.isContainerBody) {\n      this.scrollbarSize = { \n        horizontalHeight: window.innerHeight - document.documentElement.clientHeight,\n        verticalWidth: window.innerWidth - document.documentElement.clientWidth\n      }\n    } \n    else {\n      this.scrollbarSize = { \n        horizontalHeight: this.container.offsetHeight - this.container.clientHeight, \n        verticalWidth: this.container.offsetWidth - this.container.clientWidth \n      }\n    }\n  }\n\n  private observe() {\n    resize.create(`container-${this.blazorId}`, this.resizing.bind(this), false);\n    resize.observe(`container-${this.blazorId}`, this.container);    \n    resize.observe(`container-${this.blazorId}`, this.trigger);    \n    mutation.create(`trigger-${this.blazorId}`, this.mutating.bind(this), false);\n    mutation.observe(`trigger-${this.blazorId}`, this.trigger, {\n      attributes: true,\n      characterData: false,\n      childList: false,\n      subtree: false,\n      attributeOldValue: false,\n      characterDataOldValue: false\n    });\n    \n    if (this.isContainerBody) {\n      window.addEventListener(\"scroll\", this.onScroll.bind(this));\n    }\n    else {\n      this.container.addEventListener(\"scroll\", this.onScroll.bind(this));\n    }\n  }  \n\n  private onScroll() {\n    if (this.isTriggerFixed) {\n      if (this.lastScrollPosition !== window.pageYOffset) {      \n        const diff = window.pageYOffset - this.lastScrollPosition; //positive -> down, negative -> up        \n        this.position.top += diff;\n        this.position.bottom = Overlay.reversePositionValue(this.position.top, this.containerInfo.scrollHeight, this.overlayInfo.clientHeight);      \n        if (this.selectedVerticalPosition === \"top\") {        \n          this.sanitizedPosition.top = this.position.top;\n          this.overlay.style.top = this.sanitizedPosition.top + \"px\";        \n        } else {\n          this.sanitizedPosition.bottom = this.getAdjustedBottom();\n          this.overlay.style.bottom = this.sanitizedPosition.bottom + \"px\";\n        }\n        this.lastScrollPosition = window.pageYOffset;\n      }\n    } else {\n      //Commented out code is a non-optimized calculation only if overlay stops fitting during scroll\n      //It misses active check for initialPlacement being different to current placement\n      // this.getKeyElementDimensions(false);\n      // this.containerBoundarySize = this.getContainerBoundarySize();\n      // if (!this.overlayFitsContainer(\"horizontal\", this.position.left, this.position.right)\n      //   || !this.overlayFitsContainer(\"vertical\", this.position.top, this.position.bottom)) {    \n      //     this.calculatePosition(true, false, this.overlayPreset)\n      // }    \n      this.calculatePosition(true, false, this.overlayPreset);\n    }\n  }\n\n  private resizing(entries, observer) {\n    //prevents from recalculation right on the spot during constructor run\n    if (this.duringInit) { \n      this.duringInit = false;\n      return;\n    }\n    this.calculatePosition(true, false, this.overlayPreset);\n  }\n\n  private lastStyleMutation = \"\";\n\n  /**\n   * Mutation observer will fire whenever trigger style changes. This is first and foremost\n   * to monitor position/size changes, so overlay can adjust itself to the new position.\n   * @param mutations \n   * @returns \n   */\n  private mutating(mutations) {\n    if (this.duringInit) { \n      this.duringInit = false;\n      return;\n    }    \n    if (this.lastStyleMutation !== this.trigger.style.cssText) {\n      this.lastStyleMutation = this.trigger.style.cssText;\n      this.calculatePosition(true, false, this.overlayPreset);\n    }\n    \n  }\n\n  public dispose(): void {    \n    resize.dispose(`container-${this.blazorId}`);\n    mutation.dispose(`trigger-${this.blazorId}`);\n    if (this.container.contains(this.overlay)) {\n      this.container.removeChild(this.overlay);\n    }\n\n    if (this.isContainerBody) {      \n      window.removeEventListener(\"scroll\", this.onScroll);\n    }\n    else {\n      this.container.removeEventListener(\"scroll\", this.onScroll);\n    }\n  }\n\n  public calculatePosition(applyLocation: boolean, firstTime = false, overlayPreset?: domTypes.position): overlayPosition {        \n    //check if hidden, if yes, no need to recalculate (only if not first time)\n    if (!firstTime && !this.overlay.offsetParent) {      \n      return;\n    }\n    //trigger no longer visible, hide\n    if (!overlayPreset && !this.trigger.offsetParent) {      \n      if (!this.overlay.classList.contains(this.triggerPrefixCls + \"-hidden\")) {\n        this.overlay.classList.add(this.triggerPrefixCls + \"-hidden\");\n      }\n      return this.position;\n    }\n    this.lastScrollPosition = window.pageYOffset;\n    this.recentPlacement = this.placement;\n    this.overlayPreset = overlayPreset;\n\n    this.getKeyElementDimensions(firstTime);\n\n    this.restoreInitialPlacement();\n\n    //add a very basic check - if overlay width exceeds container width, left defaults to 0     \n    this.calculationsToPerform = this.getNominalPositions();\n    if (this.calculationsToPerform.size > 0) {\n      this.adjustToContainerBoundaries();\n    }\n\n    this.sanitizeCalculatedPositions();    \n    //first positioning is applied by blazor - without it, a flicker is visible\n    if (applyLocation) {\n      this.applyLocation();\n    }    \n    return this.sanitizedPosition;\n  }\n\n  /**\n   * All variants of positions are stored during calculations, but only key positions are \n   * returned (so only left or right and only top or bottom).\n   * Also, bottom & right positions need to be recalculated, due to the fact that during \n   * calculations:\n   *  - bottom is represented as a value counting from top \n   *  - right is represented as a value counting from left\n   * Browsers use different reference for bottom & right.\n   */\n  private sanitizeCalculatedPositions() {\n    this.sanitizedPosition = { ...this.position};\n    this.sanitizedPosition.zIndex = domInfoHelper.getMaxZIndex();\n    this.sanitizedPosition.placement = this.placement;    \n    if (this.selectedHorizontalPosition === \"left\") {\n      this.sanitizedPosition.right = null;\n    }\n    else {\n      this.sanitizedPosition.left = null;\n      this.sanitizedPosition.right = this.getAdjustedRight();\n    }\n\n    if (this.selectedVerticalPosition === \"top\") {\n      this.sanitizedPosition.bottom = null;\n    }\n    else {\n      this.sanitizedPosition.top = null;\n      this.sanitizedPosition.bottom = this.getAdjustedBottom();\n    }\n  }\n\n  /**\n   * Gets first calculations of the overlay. For each direction, there is a single scenario\n   * when it is immediately known that no further calculation is needed:\n   * - for vertical direction - when overlay's height is larger than container vertical boundaries\n   * - for vertical direction - when overlay's width is larger than container horizontal boundaries\n   * These scenarios are ignored, when boundyAdjustMode === TriggerBoundyAdjustMode.None\n   * @returns collection containing directions that will be calculable (not final)\n   */\n  private getNominalPositions(): Set<\"horizontal\"|\"vertical\"> {\n    this.containerBoundarySize = this.getContainerBoundarySize();\n    const height = this.containerBoundarySize.bottom - this.containerBoundarySize.top;\n    const width = this.containerBoundarySize.right - this.containerBoundarySize.left;\n    const directionsToCalculate = new Set<\"horizontal\"|\"vertical\">();\n\n    if (this.boundyAdjustMode != TriggerBoundyAdjustMode.None && width < this.overlayInfo.clientWidth && this.isContainerBody) {\n      if (this.selectedHorizontalPosition === \"left\") {\n        this.position.left = 0;\n      } else {\n        this.position.right = 0;\n      }\n    } else {\n      const horizontalPosition = this.getHorizontalPosition();\n      this.position.left = horizontalPosition.left;\n      this.position.right = horizontalPosition.right;\n      directionsToCalculate.add(\"horizontal\");\n    }\n    //same for height exceeding container height - top defaults to 0   \n    if (this.boundyAdjustMode != TriggerBoundyAdjustMode.None && height < this.overlayInfo.clientHeight && this.isContainerBody) {      \n      if (this.selectedVerticalPosition === \"top\") {\n        this.position.top = 0;\n      } else {\n        this.position.bottom = 0;\n      }\n    } else {\n      \n      const verticalPosition = this.getVerticalPosition();\n      this.position.top = verticalPosition.top;\n      this.position.bottom = verticalPosition.bottom;      \n      directionsToCalculate.add(\"vertical\");\n    }\n    return directionsToCalculate;\n  }\n\n  /**\n   * Restore initial placement (and following connected variables & functions) on calculation.\n   * This never kicks in on first calculation. This is done because the overlay should always\n   * try to position itself to the initial placement. So on every recalculation initial settings\n   * (used during object creation) are reloaded.\n   */\n  private restoreInitialPlacement() {\n    if (this.placement !== this.initialPlacement) {\n      this.placement = this.initialPlacement;\n      this.selectedHorizontalPosition = Overlay.appliedStylePositionMap.get(this.placement).horizontal;\n      this.selectedVerticalPosition = Overlay.appliedStylePositionMap.get(this.placement).vertical;\n      this.verticalCalculation = Overlay.setVerticalCalculation(this.placement, this.selectedVerticalPosition);\n      this.horizontalCalculation = Overlay.setHorizontalCalculation(this.placement, this.selectedHorizontalPosition);\n    }\n  }\n\n  /**\n   * Very basic logging, useful during debugging.\n   * @param extraMessage \n   */\n  /* istanbul ignore next */\n  private logToConsole(extraMessage = \"\") {\n    console.log(extraMessage + \" Overlay position:\", this.position,\n      \"Input\",\n      { \n        blazorId: this.blazorId,        \n        container: {\n          info: this.containerInfo,\n          parentInfo: {\n            clientHeight: this.container.parentElement.clientHeight,\n            clientWidth: this.container.parentElement.clientWidth,\n            scrollLeft: this.container.parentElement.scrollLeft,\n            scrollTop: this.container.parentElement.scrollTop\n          },\n          containerId: this.container.id,        \n          containerBoundarySize: this.containerBoundarySize,\n        },\n        trigger: { \n          absoluteTop: this.triggerInfo.absoluteTop,\n          absoluteLeft: this.triggerInfo.absoluteLeft,\n          clientHeight: this.triggerInfo.clientHeight,\n          clientWidth: this.triggerInfo.clientWidth,\n          offsetHeight: this.triggerInfo.offsetHeight,\n          offsetWidth: this.triggerInfo.offsetWidth,\n          boundyAdjustMode: this.boundyAdjustMode,\n          //triggerType: this.triggerType,\n          triggerHtml: this.trigger.outerHTML,\n          triggerPrefixCls: this.triggerPrefixCls\n        },\n        overlay: { \n          clientHeight: this.overlayInfo.clientHeight,\n          clientWidth: this.overlayInfo.clientWidth,\n          offsetHeight: this.overlayInfo.offsetHeight,\n          offsetWidth: this.overlayInfo.offsetWidth,\n          class: this.overlay.className,\n          appliedCssPosition: {\n            overlay_style_top: this.overlay.style.top,\n            overlay_style_bottom: this.overlay.style.bottom,\n            overlay_style_left: this.overlay.style.left,\n            overlay_style_right: this.overlay.style.right\n          }\n        },\n        window: {\n          innerHeight: window.innerHeight,\n          innerWidth: window.innerWidth,\n          pageXOffset: window.pageXOffset,\n          pageYOffset: window.pageYOffset,\n        },        \n        documentElement: {\n          clientHeight: document.documentElement.clientHeight,\n          clientWidth: document.documentElement.clientWidth,\n          containerIsBody: this.isContainerBody,\n        },\n        scrollbars: this.scrollbarSize,\n        overlayPreset: this.overlayPreset,\n        overlayConstraints: this.overlayConstraints,\n        position: this.position,           \n        sanitizedPosition: this.sanitizedPosition,\n        placment: {\n         initialPlacement: this.initialPlacement,\n         recentPlacement: this.recentPlacement,\n         placement: this.placement,\n         selectedHorizontalPosition: this.selectedHorizontalPosition,\n         selectedVerticalPosition: this.selectedVerticalPosition\n        }\n      }\n    );    \n  }\n\n  /**\n   * Right in the class is calculated with assumption that it is just reversed Left. \n   * This works well for containers that are not body. When in body, then different Right \n   * calculation is executed. Example:\n   * In a document of width of 5000px, the first Left = 0 and the first Right = 0 as well \n   * (and respectively, max Left = 5000 and max Right = 5000). However, browsers are behaving \n   * differently. Left indeed is 0 until the document width (5000). Right however is different. \n   * Right = 0 means the point of original viewport most Right. So, if you viewport is 1000px \n   * wide, Right = 0 will mean same as Left = 1000. So to reach Left = 5000, Right has to \n   * be equal to -4000. \n   * @returns number - right position\n   */\n  private getAdjustedRight(): number {    \n    if (this.isContainerBody) {\n      return this.position.right - (this.containerInfo.scrollWidth - window.innerWidth)\n        - this.scrollbarSize.verticalWidth;\n    }\n    return this.position.right;    \n  }\n\n  /**\n   * Bottom in the class is calculated with assumption that it is just reversed Top. \n   * This works well for containers that are not body. When in body, then different Bottom\n   * calculation is executed. Example:\n   * In a document of height of 5000px, the first Top = 0 and the first Bottom = 0 as well \n   * (and respectively, max Top = 5000 and max Bottom = 5000). However, browsers are behaving \n   * differently. Top indeed is 0 until the document height (5000). Bottom however is different. \n   * Bottom = 0 means the point of original viewport most bottom. So, if you viewport is 1000px \n   * in height, Bottom = 0 will mean same as Top = 1000. So to reach Top = 5000, Bottom has to \n   * be equal to -4000. \n   * @returns number - bottom position\n   */\n  private getAdjustedBottom(): number {    \n    if (this.isContainerBody) {\n      return this.position.bottom - (this.containerInfo.scrollHeight - window.innerHeight)\n        - this.scrollbarSize.horizontalHeight;\n    }\n    return this.position.bottom;\n  }\n\n  private applyLocation() {\n    if (this.selectedHorizontalPosition === \"left\") {\n      this.overlay.style.left = this.sanitizedPosition.left + \"px\";\n      this.overlay.style.right = \"unset\";\n    } else {\n      this.overlay.style.right = this.sanitizedPosition.right + \"px\";\n      this.overlay.style.left = \"unset\";\n    }\n\n    if (this.selectedVerticalPosition === \"top\") {\n      this.overlay.style.top = this.sanitizedPosition.top + \"px\";\n      this.overlay.style.bottom = \"unset\";\n    } else {\n      this.overlay.style.bottom = this.sanitizedPosition.bottom + \"px\";\n      this.overlay.style.top = \"unset\";\n    }\n\n    this.applyPlacement();\n  }\n\n  private applyPlacement() {\n    if (this.recentPlacement !== this.placement) {\n      let currentPlacement: string;\n      const stringMach = `${this.triggerPrefixCls}-placement-`;\n      const start = this.overlay.className.indexOf(stringMach);\n      const end = this.overlay.className.indexOf(\" \", start + stringMach.length);\n      if (start >= 0) {\n        currentPlacement = this.overlay.className.substr(start, end-start);\n      } else {\n        currentPlacement = Overlay.appliedStylePositionMap.get(this.initialPlacement).class;\n      }\n      let newPlacement = stringMach + Overlay.appliedStylePositionMap.get(this.placement).class;\n      this.overlay.classList.replace(currentPlacement, newPlacement);\n    }\n  }\n\n  /**\n   * Loads all important dimensions of the key elements (container of the trigger, trigger & overlay)\n   * into domType.domInfo structures. This could be accessed directly, except absolute positions.\n   * Also simplifies mocking.\n   * @param firstTime - if this method is called first time, then no need to load information on\n   *  container, as it was already loaded in the constructor. This is due to the fact that first time,\n   *  when overlay is added it has default left set to -9999 which causes the scrollbars to \n   * appear (which will be gone by the time overlay becomes visible). Scrollbars change\n   *  dimensions, so often calculations were incorrect.\n   */\n  private getKeyElementDimensions(firstTime: boolean) {\n    if (!firstTime) {\n      this.containerInfo = domInfoHelper.getInfo(this.container);\n      this.calculateScrollBarSizes()\n    }    \n    this.triggerInfo = domInfoHelper.getInfo(this.trigger);\n    this.overlayInfo = domInfoHelper.getInfo(this.overlay);    \n  }\n\n  /**\n   * Calculates trigger top & bottom positions and trigger height and\n   * uses these to return nominal position values depending on placement and\n   * expected attachment point (top/bottom)\n   * @returns verticalPosition\n   */\n  private getVerticalPosition(): verticalPosition {\n    let position: verticalPosition;\n    //usually first offsetHeight is taken, as the measurement contains the borders\n    this.triggerPosition.height = this.triggerInfo.offsetHeight != 0 ? this.triggerInfo.offsetHeight \n    : this.triggerInfo.clientHeight;    \n    if (this.overlayPreset) {\n      this.triggerPosition.top = this.triggerInfo.absoluteTop + this.overlayPreset.y;\n      this.triggerPosition.height = 0;\n    } else {\n      this.triggerPosition.top = this.containerInfo.scrollTop + this.triggerInfo.absoluteTop\n        - this.containerInfo.absoluteTop - this.containerInfo.clientTop;\n    }\n    this.triggerPosition.absoluteTop = this.triggerInfo.absoluteTop;\n\n    if (this.selectedVerticalPosition === \"top\"){\n      position = this.verticalCalculation(this.triggerPosition.top, this.triggerPosition.height, this.containerInfo,\n        this.triggerInfo, this.overlayInfo.clientHeight, this.overlayConstraints);\n    }\n    else { //bottom\n      this.triggerPosition.bottom = this.containerInfo.scrollHeight - this.triggerPosition.top - this.triggerPosition.height;\n      position = this.verticalCalculation(this.triggerPosition.bottom, this.triggerPosition.height, this.containerInfo,\n        this.triggerInfo, this.overlayInfo.clientHeight, this.overlayConstraints);\n    }\n    return position;\n  }\n\n  /**\n   * Calculates trigger left & right positions and trigger width and\n   * uses these to return nominal position values depending on placement and\n   * expected attachment point (left/right)\n   * @returns verticalPosition\n   */\n  private getHorizontalPosition(): horizontalPosition {\n    let position: horizontalPosition;\n    //usually first offsetHeight is taken, as the measurement contains the borders    \n    this.triggerPosition.width = this.triggerInfo.offsetWidth != 0 ? this.triggerInfo.offsetWidth : this.triggerInfo.clientWidth;\n    \n    //let triggerLeft: number;\n    if (this.overlayPreset) {\n      this.triggerPosition.left = this.triggerInfo.absoluteLeft + this.overlayPreset.x;\n      this.triggerPosition.width = 0;\n    } else {\n      this.triggerPosition.left = this.containerInfo.scrollLeft + this.triggerInfo.absoluteLeft\n        - this.containerInfo.absoluteLeft - this.containerInfo.clientLeft;\n    }\n    this.triggerPosition.absoluteLeft = this.triggerInfo.absoluteLeft;\n\n    if (this.selectedHorizontalPosition === \"left\"){      \n      position = this.horizontalCalculation(this.triggerPosition.left, this.triggerPosition.width, this.containerInfo,\n        this.triggerInfo, this.overlayInfo.clientWidth, this.overlayConstraints);\n    }\n    else { //right\n      this.triggerPosition.right = this.containerInfo.scrollWidth - this.triggerPosition.left - this.triggerPosition.width;\n      position = this.horizontalCalculation(this.triggerPosition.right, this.triggerPosition.width, this.containerInfo,\n        this.triggerInfo, this.overlayInfo.clientWidth, this.overlayConstraints);\n    }\n    return position;    \n  }\n\n  /**\n   * Responsible for calling logic that handles situation when calculated overlay position\n   * is causing overlay to be partially rendered invisible. The goal is to adjust placement \n   * in such a way, so the overlay is fully visible.\n   * @returns void\n   */\n  private adjustToContainerBoundaries() {\n    if (this.boundyAdjustMode === TriggerBoundyAdjustMode.None) {\n      return;\n    }    \n\n    if (this.calculationsToPerform.has(\"vertical\")) {\n      this.adjustVerticalToContainerBoundaries();\n    }\n\n    if (this.calculationsToPerform.has(\"horizontal\")) {\n      this.adjustHorizontalToContainerBoundaries();\n    }\n  }\n\n  private setBodyBoundayrSize() {\n      const window = domInfoHelper.getWindow();      \n      const scroll = domInfoHelper.getScroll();\n      this.bodyBoundarySize = {\n        top : scroll.y,\n        left: scroll.x,\n        right: window.innerWidth + scroll.x,\n        bottom: window.innerHeight + scroll.y\n      };     \n  }\n\n  /**\n   * Retrieves information on current logical viewport (visible area). For \n   * InView this means actual viewport area (what you see in the browser - either the \n   * body or the scrolled to area in a container) or for InScroll this means total\n   * area of the container (or body).\n   * @returns coordinates - absolute values measuring from top = 0 and left = 0 (first \n   * pixels of the container)\n   */\n  private getContainerBoundarySize(): coordinates {    \n    if (this.boundyAdjustMode === TriggerBoundyAdjustMode.InScroll) {      \n      if (!this.isContainerBody) {\n        this.setBodyBoundayrSize();\n      }\n      return { \n          left: 0,\n          right: this.containerInfo.scrollWidth,\n          top: 0,\n          bottom: this.containerInfo.scrollHeight\n        };\n    }        \n    this.setBodyBoundayrSize();\n\n    if (this.isContainerBody) {       \n      return this.bodyBoundarySize;\n    } else {         \n      //special care is needed when evaluating viewport of the container\n      const parentIsInsignificant = this.container.parentElement.clientHeight === 0\n        || this.container.parentElement.clientWidth === 0;\n      const verticalScrollBasedOnParent = !parentIsInsignificant\n        && this.container.parentElement.clientHeight < this.containerInfo.clientHeight;\n      const horizontalScrollBasedOnParent = !parentIsInsignificant\n        && this.container.parentElement.clientWidth < this.containerInfo.clientWidth;\n\n      let clientHeight: number;\n      let clientWidth: number;\n      let scrollTop: number;\n      let scrollLeft: number;\n\n      if (verticalScrollBasedOnParent) {\n        clientHeight = this.container.parentElement.clientHeight;\n        scrollTop = this.container.parentElement.scrollTop;\n      } else {\n        clientHeight = this.containerInfo.clientHeight;\n        scrollTop = this.containerInfo.scrollTop;\n      }\n\n      if (horizontalScrollBasedOnParent) {\n        clientWidth = this.container.parentElement.clientWidth;clientWidth;\n        scrollLeft = this.container.parentElement.scrollLeft;\n      } else {\n        clientWidth = this.containerInfo.clientWidth;\n        scrollLeft = this.containerInfo.scrollLeft;\n      }\n          \n      return { \n        top : scrollTop,\n        bottom: scrollTop + clientHeight,\n        left: scrollLeft,\n        right: scrollLeft + clientWidth\n      };                \n    }  \n  }\n\n  /**\n   * Returns how much height of the overlay is visible in current viewport \n   */\n  private getOverlayVisibleHeight(visibleIn: \"container\" | \"body\"): number {\n    let boundary: coordinates;\n    let top: number;    \n    if (visibleIn === \"container\") {\n      boundary = this.containerBoundarySize;\n      top = this.triggerPosition.top;\n    } else {\n      boundary = this.bodyBoundarySize;\n      top = this.triggerPosition.absoluteTop;\n    }\n\n    if (this.selectedVerticalPosition === \"top\") {\n      return boundary.bottom - (top + this.triggerPosition.height);\n    } else {\n      return top - boundary.top;\n    }    \n  }\n\n  /**\n   * Returns how much width of the overlay is visible in current viewport \n   */\n  private getOverlayVisibleWidth(visibleIn: \"container\" | \"body\"): number {    \n    let boundary: coordinates;\n    let left: number;    \n    if (visibleIn === \"container\") {\n      boundary = this.containerBoundarySize;\n      left = this.triggerPosition.left;\n    } else {\n      boundary = this.bodyBoundarySize;\n      left = this.triggerPosition.absoluteLeft;\n    }\n\n    if (this.selectedHorizontalPosition === \"left\") {\n      return boundary.right - (left + this.triggerPosition.width);\n    } else {\n      return left - boundary.left;\n    }\n  }\n\n  /**\n   * Checks if current position actually fits in the container and if not, then reverses\n   * the placement. Then calculates which already calculated placements has the largest horizontal \n   * area of the overlay visible and picks the calculation with largest area.\n   */\n  private adjustHorizontalToContainerBoundaries() {\n    if (!this.overlayFitsContainer(\"horizontal\", this.position.left, this.position.right)) {\n      const positionCache: overlayPosition = { ...this.position };\n      const selectedPositionCache = this.selectedHorizontalPosition;\n      const placementCache = this.placement;\n      const horizontalCalculationCache = this.horizontalCalculation;\n      const visibleWidthBeforeAdjustment = this.getOverlayVisibleWidth(\"container\");\n      let visibleWidthInBodyBeforeAdjustment: number;\n      if (this.isContainerOverBody) {\n        visibleWidthInBodyBeforeAdjustment = this.getOverlayVisibleWidth(\"body\");\n      } else {\n        visibleWidthInBodyBeforeAdjustment = visibleWidthBeforeAdjustment\n      };      \n\n      this.getHorizontalAdjustment();\n\n      const visibleWidthAfterAdjustment = this.getOverlayVisibleWidth(\"container\");\n      let visibleWidthInBodyAfterAdjustment: number;\n      if (this.isContainerOverBody) {\n        visibleWidthInBodyAfterAdjustment = this.getOverlayVisibleWidth(\"body\");\n      } else {\n        visibleWidthInBodyAfterAdjustment = visibleWidthAfterAdjustment\n      };        \n\n      if (\n        !(visibleWidthInBodyBeforeAdjustment < visibleWidthInBodyAfterAdjustment \n          && visibleWidthInBodyAfterAdjustment > 0\n          && visibleWidthInBodyAfterAdjustment - visibleWidthInBodyBeforeAdjustment >= 0)        \n        ||\n        !(visibleWidthBeforeAdjustment < visibleWidthAfterAdjustment && visibleWidthAfterAdjustment > 0)) {        \n        this.position = positionCache;\n        this.selectedHorizontalPosition = selectedPositionCache;\n        this.placement = placementCache;\n        this.horizontalCalculation = horizontalCalculationCache;\n      }\n    }\n  }\n\n  /**\n   * Checks if current position actually fits in the container and if not, then reverses\n   * the placement. Then calculates which already calculated placements has the largest vertical \n   * area of the overlay visible and picks the calculation with largest area.\n   */\n  private adjustVerticalToContainerBoundaries() {\n    if (!this.overlayFitsContainer(\"vertical\", this.position.top, this.position.bottom)) {\n      const positionCache: overlayPosition = { ...this.position };\n      const selectedPositionCache = this.selectedVerticalPosition;\n      const placementCache = this.placement;\n      const verticalCalculationCache = this.verticalCalculation;\n      const visibleHeightBeforeAdjustment = this.getOverlayVisibleHeight(\"container\");\n      let visibleHeightInBodyBeforeAdjustment: number;\n      if (this.isContainerOverBody) {\n        visibleHeightInBodyBeforeAdjustment = this.getOverlayVisibleHeight(\"body\");\n      } else {\n        visibleHeightInBodyBeforeAdjustment = visibleHeightBeforeAdjustment\n      };\n\n      this.getVerticalAdjustment();      \n\n      const visibleHeightAfterAdjustment = this.getOverlayVisibleHeight(\"container\");\n      let visibleHeightInBodyAfterAdjustment: number;\n      if (this.isContainerOverBody) {\n        visibleHeightInBodyAfterAdjustment = this.getOverlayVisibleHeight(\"body\");\n      } else {\n        visibleHeightInBodyAfterAdjustment = visibleHeightAfterAdjustment\n      };          \n\n      if (\n        !(visibleHeightInBodyBeforeAdjustment < visibleHeightInBodyAfterAdjustment \n          && visibleHeightInBodyAfterAdjustment > 0 \n          && visibleHeightInBodyAfterAdjustment - visibleHeightInBodyBeforeAdjustment >= 0)\n        ||\n        !(visibleHeightBeforeAdjustment < visibleHeightAfterAdjustment && visibleHeightAfterAdjustment > 0)) {        \n        this.position = positionCache;\n        this.selectedVerticalPosition = selectedPositionCache;\n        this.placement = placementCache;\n        this.verticalCalculation = verticalCalculationCache;\n      }\n    }\n  }\n\n  private overlayFitsContainer(type: \"horizontal\" | \"vertical\", start: number, end: number): boolean {    \n    if (type === \"horizontal\") {\n      const endExpressedAsLeft = start + this.overlayInfo.clientWidth;\n      return this.containerBoundarySize.left <= start\n          && start <= this.containerBoundarySize.right //overlay left is between container left and right\n          && this.containerBoundarySize.left <= endExpressedAsLeft\n          && endExpressedAsLeft <= this.containerBoundarySize.right //and overlay right is between container left and right\n    }\n    const endExpressedAsTop = start + this.overlayInfo.clientHeight;\n    return this.containerBoundarySize.top <= start\n        && start <= this.containerBoundarySize.bottom //overlay top is between container top and bottom\n        && this.containerBoundarySize.top <= endExpressedAsTop \n        && endExpressedAsTop <= this.containerBoundarySize.bottom //and overlay bottom is between container top and bottom\n  }\n\n  /**\n   * Applies basic adjustment - switches verticaly placement (top -> bottom & bottom -> top) \n   * and recalculates based on the newly set placement \n   */\n  private getVerticalAdjustment() {\n    this.placement = Overlay.reverseVerticalPlacementMap.get(this.placement)(this.selectedVerticalPosition);\n    this.selectedVerticalPosition = Overlay.appliedStylePositionMap.get(this.placement).vertical;\n    this.verticalCalculation = Overlay.setVerticalCalculation(this.placement, this.selectedVerticalPosition);\n    const verticalPosition = this.getVerticalPosition();\n    this.position.top = verticalPosition.top;\n    this.position.bottom = verticalPosition.bottom;\n  }\n\n  /**\n   * Applies basic adjustment - switches horizontal placement (left -> right & right -> left)\n   * and recalculates based on the newly set placement\n   */\n  private getHorizontalAdjustment() {\n    this.placement = Overlay.reverseHorizontalPlacementMap.get(this.placement)(this.selectedHorizontalPosition);\n    this.selectedHorizontalPosition = Overlay.appliedStylePositionMap.get(this.placement).horizontal;\n    this.horizontalCalculation = Overlay.setHorizontalCalculation(this.placement, this.selectedHorizontalPosition);\n    const horizontalPosition = this.getHorizontalPosition();\n    this.position.left = horizontalPosition.left;\n    this.position.right = horizontalPosition.right;    \n  }  \n}", "﻿import { domInfoHelper, eventHelper, domManipulationHelper, domTypes } from '../dom/exports'\nimport { Placement, TriggerBoundyAdjustMode, overlayConstraints, overlayPosition, Overlay } from './overlay'\nimport { state } from '../stateProvider';\n\nexport class overlayHelper {\n  static overlayRegistry: { [key: string]: Overlay} = {};\n\n  static addOverlayToContainer(blazorId: string, \n    overlaySelector, triggerSelector, placement: Placement,  containerSelector: string,\n    triggerBoundyAdjustMode: TriggerBoundyAdjustMode, triggerIsWrappedInDiv: boolean, triggerPrefixCls: string,\n    verticalOffset: number, horizontalOffset: number, arrowPointAtCenter: boolean,    \n    overlayTop?: number, overlayLeft?: number\n  ): overlayPosition {      \n    const overlayElement = domInfoHelper.get(overlaySelector) as HTMLDivElement;    \n    const containerElement = domInfoHelper.get(containerSelector) as HTMLElement;\n    const triggerElement = domInfoHelper.get(triggerSelector) as HTMLElement;    \n\n    if (!domManipulationHelper.addElementTo(overlaySelector, containerElement)) {\n      console.log(\"Failed to add overlay. Details:\", {\n         triggerPrefixCls: triggerPrefixCls,\n         overlaySelector: overlaySelector,\n         containerElement: containerElement\n      } );\n      return null;\n    }    \n\n    let overlayPresets: domTypes.position;\n    if (overlayTop || overlayLeft) {\n      overlayPresets = { x: overlayLeft, y: overlayTop };\n    }\n\n    const overlayConstraints: overlayConstraints = {\n      verticalOffset: verticalOffset,\n      horizontalOffset: horizontalOffset,\n      arrowPointAtCenter: arrowPointAtCenter\n    };\n\n    const overlay = new Overlay(blazorId, overlayElement, containerElement, triggerElement, placement, triggerBoundyAdjustMode, triggerIsWrappedInDiv, triggerPrefixCls, overlayConstraints);   \n    //register object in store, so it can be retrieved during update/dispose\n    this.overlayRegistry[blazorId] = overlay;\n    \n    return overlay.calculatePosition(false, true, overlayPresets);\n  }\n\n\n  static updateOverlayPosition(blazorId: string, overlaySelector, triggerSelector, placement: Placement,  containerSelector: string,\n    triggerBoundyAdjustMode: TriggerBoundyAdjustMode, triggerIsWrappedInDiv: boolean, triggerPrefixCls: string,\n    verticalOffset: number, horizontalOffset: number, arrowPointAtCenter: boolean,  \n    overlayTop?: number, overlayLeft?: number): overlayPosition {\n    const overlay = this.overlayRegistry[blazorId];\n    if (overlay){\n      let overlayPresets: domTypes.position;\n        if (overlayTop || overlayLeft) {\n          overlayPresets = { x: overlayLeft, y: overlayTop };\n        }      \n      return overlay.calculatePosition(false, false, overlayPresets);      \n    } else {\n      //When page is slow, it may happen that rendering of an overlay may not happen, even if \n      //blazor thinks it did happen. In such a case, when overlay object is not found, just try\n      //to render it again.\n      return overlayHelper.addOverlayToContainer(blazorId, overlaySelector, triggerSelector, placement,  containerSelector,triggerBoundyAdjustMode, triggerIsWrappedInDiv, triggerPrefixCls, \n      verticalOffset, horizontalOffset, arrowPointAtCenter,  \n      overlayTop, overlayLeft);      \n    }    \n  }\n\n  static deleteOverlayFromContainer(blazorId: string) {\n    const overlay = this.overlayRegistry[blazorId];\n    if (overlay) {      \n      overlay.dispose();\n      delete this.overlayRegistry[blazorId];\n    }\n  }\n\n  static addPreventEnterOnOverlayVisible(element, overlayElement) {\n    if (element && overlayElement) {\n      const dom: HTMLElement = domInfoHelper.get(element);\n      if (dom) {\n        state.eventCallbackRegistry[element.id + \"keydown:Enter\"] = (e) => eventHelper.preventKeyOnCondition(e, \"enter\", () => overlayElement.offsetParent !== null);\n        dom.addEventListener(\"keydown\", state.eventCallbackRegistry[element.id + \"keydown:Enter\"], false);\n      }\n    }\n  }\n\n  static removePreventEnterOnOverlayVisible(element) {\n    if (element) {\n      const dom: HTMLElement = domInfoHelper.get(element);\n      if (dom) {\n        dom.removeEventListener(\"keydown\", state.eventCallbackRegistry[element.id + \"keydown:Enter\"]);\n        state.eventCallbackRegistry[element.id + \"keydown:Enter\"] = null; \n      }\n    }\n  }\n}\n\n", "﻿export class tableHelper {\n  static bindTableScroll(bodyRef, tableRef, headerRef, scrollX, scrollY) {\n    bodyRef.bindScroll = () => {\n      if (scrollX) {\n        tableHelper.SetScrollPositionClassName(bodyRef, tableRef);\n      }\n      if (scrollY) {\n        headerRef.scrollLeft = bodyRef.scrollLeft;\n      }\n    }\n\n    // direct setting classlist will not work, so delay 500ms for workaround\n    setTimeout(() => {\n     bodyRef && bodyRef.bindScroll();\n    }, 500);\n\n    bodyRef.addEventListener && bodyRef.addEventListener('scroll', bodyRef.bindScroll);\n    window.addEventListener('resize', bodyRef.bindScroll);\n  }\n\n  static unbindTableScroll(bodyRef) {\n    if (bodyRef) {\n      bodyRef.removeEventListener && bodyRef.removeEventListener('scroll', bodyRef.bindScroll);\n      window.removeEventListener('resize', bodyRef.bindScroll);\n    }\n  }\n\n  static SetScrollPositionClassName(bodyRef, tableRef) {\n\n    let scrollLeft = bodyRef.scrollLeft;\n    let scrollWidth = bodyRef.scrollWidth;\n    let clientWidth = bodyRef.clientWidth;\n\n    let pingLeft = false;\n    let pingRight = false;\n\n    if ((scrollWidth == clientWidth && scrollWidth != 0)) {\n      pingLeft = false;\n      pingRight = false;\n    }\n    else if (scrollLeft == 0) {\n      pingLeft = false;\n      pingRight = true;\n    }\n    else if (Math.abs(scrollWidth - (scrollLeft + clientWidth)) <= 1) {\n      pingRight = false;\n      pingLeft = true;\n    }\n    else {\n      pingLeft = true;\n      pingRight = true;\n    }\n\n    pingLeft ? tableRef.classList.add(\"ant-table-ping-left\") : tableRef.classList.remove(\"ant-table-ping-left\");\n    pingRight ? tableRef.classList.add(\"ant-table-ping-right\") : tableRef.classList.remove(\"ant-table-ping-right\");\n  }\n}", "﻿type fileInfo = {\n  fileName: string,\n  size: number,\n  objectURL: string,\n  type: string\n}\n\nexport class uploadHelper {\n  static addFileClickEventListener(btn: HTMLElement) {\n    if (btn.addEventListener) {\n      btn.addEventListener(\"click\", uploadHelper.fileClickEvent);\n    }\n  }\n\n  static removeFileClickEventListener(btn: HTMLElement) {\n    btn.removeEventListener(\"click\", uploadHelper.fileClickEvent);\n  }\n\n  private static fileClickEvent(e: MouseEvent) {\n    e.stopPropagation();\n    const fileId = (e.currentTarget as HTMLSpanElement).attributes[\"data-fileid\"].nodeValue;\n    const element = document.getElementById(fileId) as HTMLInputElement;\n    element.click();\n  }\n\n  static clearFile(element) {\n    element.setAttribute(\"type\", \"input\");\n    element.value = '';\n    element.setAttribute(\"type\", \"file\");\n  }\n\n  static getFileInfo(element: HTMLInputElement) {\n    if (element.files && element.files.length > 0) {\n      let fileInfo = Array<fileInfo>();\n      for (var i = 0; i < element.files.length; i++) {\n        let file = element.files[i];\n        const objectUrl = this.getObjectURL(file);\n        fileInfo.push({\n          fileName: file.name,\n          size: file.size,\n          objectURL: objectUrl,\n          type: file.type\n        });\n      }\n\n      return fileInfo;\n    }\n  }\n\n  private static getObjectURL(file: File): string {\n    var url = null;\n    if (window.URL != undefined) {\n      url = window.URL.createObjectURL(file);\n    } else if (window.webkitURL != undefined) {\n      url = window.webkitURL.createObjectURL(file);\n    }\n    return url;\n  }\n\n  static uploadFile(element, index, data, headers, fileId, url, name, instance, percentMethod, successMethod, errorMethod, method: string) {\n    let formData = new FormData();\n    var file = element.files[index];\n    var size = file.size;\n    formData.append(name, file);\n    if (data != null) {\n      for (var key in data) {\n        formData.append(key, data[key]);\n      }\n    }\n    const req = new XMLHttpRequest()\n    req.onreadystatechange = function () {\n      if (req.readyState === 4) {\n        // #1655 Any 2xx response code is okay\n        if (req.status < 200 || req.status > 299) {\n          // #2857 should get error raw response\n          instance.invokeMethodAsync(errorMethod, fileId, req.responseText);\n          return;\n        }\n        instance.invokeMethodAsync(successMethod, fileId, req.responseText);\n      }\n    }\n    req.upload.onprogress = function (event) {\n      var percent = Math.floor(event.loaded / size * 100);\n      instance.invokeMethodAsync(percentMethod, fileId, percent);\n    }\n    req.onerror = function (e) {\n      instance.invokeMethodAsync(errorMethod, fileId, \"error\");\n    }\n    req.open(method, url, true)\n    if (headers != null) {\n      for (var header in headers) {\n        req.setRequestHeader(header, headers[header]);\n      }\n    }\n    req.send(formData)\n  }\n}", "﻿\nconst throttle = (fn, threshold = 160) => {\n    let timeout;\n    var start = +new Date;\n    return function (...args) {\n        let context = this, curTime = +new Date() - 0;\n        //总是干掉事件回调\n        window.clearTimeout(timeout);\n        if (curTime - start >= threshold) {\n            //只执行一部分方法，这些方法是在某个时间段内执行一次\n            fn.apply(context, args);\n            start = curTime;\n        }\n        else {\n            //让方法在脱离事件后也能执行一次\n            timeout = window.setTimeout(() => {\n                //@ts-ignore\n                fn.apply(this, args);\n            }, threshold);\n        }\n    };\n}\n\nconst eventMap = new Map<HTMLElement, Dragger>();\n\nconst defaultOptions = {\n    inViewport: true\n}\n\nclass Dragger {\n\n    private _trigger: HTMLElement = null;\n    private _container: HTMLElement = null;\n    private _options: any = null;\n    private _state: any = null;\n    private _isFirst: boolean = true;\n    private _style: string = null;\n\n    constructor(trigger: HTMLElement, container: HTMLElement, dragInViewport: boolean) {\n        this._trigger = trigger;\n        this._container = container;\n        this._options = Object.assign({}, defaultOptions, {\n            inViewport: dragInViewport\n        });\n        this._state = {\n            isInDrag: false,\n            mX: 0, // mouse x\n            mY: 0, // mouse y\n            domStartX: 0, // on mousedown, the mouse x\n            domStartY: 0, // on mousedown, the mouse y            \n        }\n    }\n\n    getContainerPos() {\n        const rect = this._container.getBoundingClientRect();\n        return {\n            left: rect.left,\n            top: rect.top\n        }\n    }\n\n    onMousedown = (e) => {\n        const state = this._state;\n        state.isInDrag = true;\n        state.mX = e.clientX;\n        state.mY = e.clientY;\n        this._container.style.position = \"absolute\";\n        const { left, top } = this.getContainerPos();\n\n        if (this._isFirst) {\n\n            state.domMaxY = document.documentElement.clientHeight\n                - this._container.offsetHeight - 1;\n            state.domMaxX = document.documentElement.clientWidth\n                - this._container.offsetWidth - 1;\n            state.domMaxY = state.domMaxY < 0 ? 0 : state.domMaxY;\n            state.domMaxX = state.domMaxX < 0 ? 0 : state.domMaxX;\n\n            this._container.style.left = left + 'px';\n            this._container.style.top = top + 'px';\n\n            if (!this._style) {\n                this._style = this._container.getAttribute(\"style\");\n            }\n\n            this._isFirst = false;\n        }\n\n        state.domStartX = left;\n        state.domStartY = top;\n    }\n\n    onMouseup = (e) => {\n        const state = this._state;\n\n        state.isInDrag = false;\n\n        const { left, top } = this.getContainerPos();\n        state.domStartX = left;\n        state.domStartY = top;\n    }\n\n    onMousemove = throttle((e) => {\n        const state = this._state;\n        if (state.isInDrag) {\n            var nowX = e.clientX,\n                nowY = e.clientY,\n                disX = nowX - state.mX,\n                disY = nowY - state.mY;\n\n            var newDomX = state.domStartX + disX;\n            var newDomY = state.domStartY + disY;\n            if (this._options.inViewport) {\n                if (newDomX < 0) {\n                    newDomX = 0;\n                }\n                else if (newDomX > state.domMaxX) {\n                    newDomX = state.domMaxX;\n                }\n                if (newDomY < 0) {\n                    newDomY = 0;\n                }\n                else if (newDomY > state.domMaxY) {\n                    newDomY = state.domMaxY;\n                }\n            }\n            this._container.style.position = \"absolute\";\n            this._container.style.margin = \"0\";\n            this._container.style.paddingBottom = \"0\";\n            this._container.style.left = newDomX + \"px\";\n            this._container.style.top = newDomY + \"px\";\n        }\n    }, 10).bind(this);\n\n    onResize = throttle((e) => {\n        const state = this._state;\n\n        state.domMaxY = document.documentElement.clientHeight\n            - this._container.offsetHeight - 1;\n        state.domMaxX = document.documentElement.clientWidth\n            - this._container.offsetWidth - 1;\n        state.domMaxY = state.domMaxY < 0 ? 0 : state.domMaxY;\n        state.domMaxX = state.domMaxX < 0 ? 0 : state.domMaxX;\n        state.domStartY = parseInt(this._container.style.top);\n        state.domStartX = parseInt(this._container.style.left);\n        if (state.domStartY > state.domMaxY) {\n            if (state.domMaxY > 0) {\n                this._container.style.top = state.domMaxY + \"px\";\n            }\n        }\n        if (state.domStartX > state.domMaxX) {\n            this._container.style.left = state.domMaxX + \"px\";\n        }\n    }, 10).bind(this);\n\n    bindDrag() {\n        const trigger = this._trigger;\n        const options = this._options;\n\n        trigger.addEventListener(\"mousedown\", this.onMousedown, false);\n        window.addEventListener(\"mouseup\", this.onMouseup, false);\n        document.addEventListener(\"mousemove\", this.onMousemove);\n        if (options.inViewport) {\n            window.addEventListener(\"resize\", this.onResize, false);\n        }\n    }\n\n    unbindDrag() {\n        const trigger = this._trigger;\n\n        trigger.removeEventListener(\"mousedown\", this.onMousedown, false);\n        window.removeEventListener(\"mouseup\", this.onMouseup, false);\n        document.removeEventListener(\"mousemove\", this.onMousemove);\n        if (this._options.inViewport) {\n            window.removeEventListener(\"resize\", this.onResize, false);\n        }\n    }\n\n    resetContainerStyle() {\n        if (this._style !== null) {\n            this._isFirst = true;\n            this._container.setAttribute(\"style\", this._style);\n        }\n    }\n}\n\nfunction enableDraggable(trigger: HTMLElement, container: HTMLElement, dragInViewport: boolean = true) {\n    let dragger = eventMap.get(trigger);\n    if (!dragger) {\n        dragger = new Dragger(trigger, container, dragInViewport);\n        eventMap.set(trigger, dragger);\n    } \n    dragger.bindDrag();\n}\n\nfunction disableDraggable(trigger: HTMLElement) {\n    const dragger = eventMap.get(trigger);\n    if (dragger) {\n        dragger.unbindDrag();\n    }\n}\n\nfunction resetModalPosition(trigger: HTMLElement) {\n    const dragger = eventMap.get(trigger);\n    if (dragger) {\n        dragger.resetContainerStyle();\n    }\n}\n\nexport { enableDraggable, disableDraggable, resetModalPosition };", "﻿import { domInfoHelper } from './exports';\nimport { state } from '../stateProvider';\n\nexport class eventHelper {\n  static triggerEvent(element: HTMLInputElement, eventType: string, eventName: string) {\n    //TODO: replace with event constructors https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent\n    //Not used \n    const evt = document.createEvent(eventType);\n    evt.initEvent(eventName);\n    return element.dispatchEvent(evt);\n  }\n\n  static addDomEventListener(element, eventName: string, preventDefault: boolean, invoker: any) {\n    const callback = args => {\n      const obj = {};\n      for (let k in args) {\n        if (k !== 'originalTarget') { //firefox occasionally raises Permission Denied when this property is being stringified\n          obj[k] = args[k];\n        }\n      }\n      const json = JSON.stringify(obj, (k, v) => {\n        if (v instanceof Node) return 'Node';\n        if (v instanceof Window) return 'Window';\n        return v;\n      }, ' ');\n\n      setTimeout(function () { invoker.invokeMethodAsync('Invoke', json) }, 0);\n      if (preventDefault === true) {\n        args.preventDefault();\n      }\n    };\n\n    const dom = domInfoHelper.get(element);\n    const key = `${eventName}-${invoker._id}`;\n\n    if (eventName === 'resize') {\n      dom[`e_${key}`] = this.debounce(() => callback({ innerWidth: window.innerWidth, innerHeight: window.innerHeight }), 200, false);\n    } else {\n      dom[`e_${key}`] = callback;\n    }\n\n    dom[`i_${key}`] = invoker;\n    (dom as HTMLElement).addEventListener(eventName, dom[`e_${key}`]);\n  }\n\n  static addDomEventListenerToFirstChild(element, eventName, preventDefault, invoker) {\n    const dom = domInfoHelper.get(element);\n\n    if (dom && dom.firstElementChild) {\n      this.addDomEventListener(dom.firstElementChild, eventName, preventDefault, invoker);\n    }\n  }\n\n  static removeDomEventListener(element, eventName: string, invoker) {\n    const dom = domInfoHelper.get(element);\n    const key = `${eventName}-${invoker._id}`;\n\n    if (dom) {\n      dom.removeEventListener(eventName, dom[`e_${key}`]);\n      //dom[`i_${key}`].dispose();\n    }\n  }\n\n  static addPreventKeys(inputElement, keys: string[]) {\n    if (inputElement) {\n      const dom = domInfoHelper.get(inputElement);\n      keys = keys.map(function (x) { return x.toUpperCase(); })\n      state.eventCallbackRegistry[inputElement.id + \"keydown\"] = (e) => this.preventKeys(e, keys);\n      (dom as HTMLElement).addEventListener(\"keydown\", state.eventCallbackRegistry[inputElement.id + \"keydown\"], false);\n    }\n  }\n\n  static preventKeyOnCondition(e: KeyboardEvent, key: string, check: () => boolean) {\n    if (e.key.toUpperCase() === key.toUpperCase() && check()) {\n      e.preventDefault();\n      return false;\n    }\n  }\n\n  static removePreventKeys(inputElement) {\n    if (inputElement) {\n      const dom = domInfoHelper.get(inputElement);\n      if (dom) {\n        (dom as HTMLElement).removeEventListener(\"keydown\", state.eventCallbackRegistry[inputElement.id + \"keydown\"]);\n        state.eventCallbackRegistry[inputElement.id + \"keydown\"] = null;\n      }\n    }\n  }\n\n  private static debounce(func, wait, immediate) {\n    var timeout;\n    return (...args) => {\n      const context = this;\n      const later = () => {\n        timeout = null;\n        if (!immediate) func.apply(this, args);\n      };\n      const callNow = immediate && !timeout;\n      clearTimeout(timeout);\n      timeout = setTimeout(later, wait);\n      if (callNow) func.apply(context, args);\n    };\n  };\n\n  private static preventKeys(e: KeyboardEvent, keys: string[]) {\n    if (keys.indexOf(e.key.toUpperCase()) !== -1) {\n      e.preventDefault();\n      return false;\n    }\n  }\n}", "﻿export { infoHelper as domInfoHelper } from './infoHelper';\nexport { manipulationHelper as domManipulationHelper } from './manipulationHelper';\nexport { eventHelper } from './eventHelper';\nexport * as domTypes from './types';", "﻿import { domTypes } from './exports'\n\nexport class infoHelper {\n  static getWindow() {\n    return {\n      innerWidth: window.innerWidth,\n      innerHeight: window.innerHeight\n    };\n  }\n\n  static get(element: any) {\n    if (!element) {\n      element = document.body;\n    } else if (typeof element === 'string') {\n      if (element === 'window') {\n          return window;\n      } else if (element === 'document') {\n        return document;\n      }\n      element = document.querySelector(element!)\n    }\n    return element;\n  }\n\n  static getInfo(element: any): domTypes.domInfo {\n    let domElement = this.get(element);\n    if (!domElement) {\n      domElement = {};\n    }\n    const absolutePosition = this.getElementAbsolutePos(domElement);\n    const result: domTypes.domInfo = {\n      offsetTop: domElement.offsetTop || 0,\n      offsetLeft: domElement.offsetLeft || 0,\n      offsetWidth: domElement.offsetWidth || 0,\n      offsetHeight: domElement.offsetHeight || 0,\n      scrollHeight: domElement.scrollHeight || 0,\n      scrollWidth: domElement.scrollWidth || 0,\n      scrollLeft: domElement.scrollLeft || 0,\n      scrollTop: domElement.scrollTop || 0,\n      clientTop: domElement.clientTop || 0,\n      clientLeft: domElement.clientLeft || 0,\n      clientHeight: domElement.clientHeight || 0,\n      clientWidth: domElement.clientWidth || 0,\n      selectionStart: domElement.selectionStart || 0,\n      absoluteTop: Math.round(absolutePosition.y),\n      absoluteLeft: Math.round(absolutePosition.x)\n    };\n    return result;\n  }\n\n  static getElementAbsolutePos(element: any): domTypes.position {\n    const res: domTypes.position = {\n      x: 0,\n      y: 0\n    };\n    if (element !== null) {\n      if (element.getBoundingClientRect) {\n        const viewportElement = document.documentElement;\n        const box = element.getBoundingClientRect();\n        const scrollLeft = viewportElement.scrollLeft;\n        const scrollTop = viewportElement.scrollTop;\n\n        res.x = box.left + scrollLeft;\n        res.y = box.top + scrollTop;\n      }\n    }\n    return res;\n  }\n\n  static getBoundingClientRect(element: any): domTypes.domRect {\n    const domElement = this.get(element);\n    if (domElement && domElement.getBoundingClientRect) {\n      const rect = domElement.getBoundingClientRect();\n      // Fixes #1468. This wrapping is necessary for old browsers. Remove this when one day we no longer support them.\n      return {\n        width: rect.width,\n        height: rect.height,\n        top: rect.top,\n        right: rect.right,\n        bottom: rect.bottom,\n        left: rect.left,\n        x: rect.x,\n        y: rect.y\n      };\n    }\n    return null;\n  }\n\n  static getFirstChildDomInfo(element: any) {\n    const domElement = this.get(element);\n    if (domElement) {\n      if (domElement.firstElementChild) {\n        return this.getInfo(domElement.firstElementChild);\n      } else {\n        return this.getInfo(domElement);\n      }\n    }\n    return null;\n  }\n\n  static getActiveElement() {\n    const element = document.activeElement;\n    const id = element.getAttribute(\"id\") || \"\";\n    return id;\n  }\n\n  static getScroll() {\n    return { x: window.pageXOffset, y: window.pageYOffset };\n  }\n\n  static hasFocus(selector) {\n    const dom = this.get(selector);\n    return (document.activeElement === dom);\n  }\n\n  static getInnerText(element) {\n    const dom = this.get(element);\n    if (dom) return dom.innerText;\n    return null;\n  }\n\n  static getMaxZIndex(): number {\n    return Array.from(document.querySelectorAll(\"*\")).reduce((r, e) => Math.max(r, +window.getComputedStyle(e).zIndex || 0), 0)\n  }\n\n  static isFixedPosition(element) {\n    let node = this.get(element);\n    while (node && node.nodeName.toLowerCase() !== 'body') {\n      if (window.getComputedStyle(node).getPropertyValue('position').toLowerCase() === 'fixed') { return true; }\n      node = node.parentNode;\n    }\n    return false;\n  }\n\n  static findAncestorWithZIndex(element: HTMLElement): number {\n    let node = this.get(element);\n    let zIndexAsString: string;\n    let zIndex: number;\n    while (node && node.nodeName.toLowerCase() !== 'body') {\n      zIndexAsString = window.getComputedStyle(node).zIndex;\n      zIndex = Number.parseInt(zIndexAsString);\n      if (!Number.isNaN(zIndex)) {\n        return zIndex;\n      }\n      node = node.parentNode;\n    }\n    return null;\n  }\n\n  static getElementsInfo(elements: any[]): any {\n    let infos = {};\n    elements.forEach(el => {\n      infos[el.id] = this.getInfo(el);\n    })\n\n    return infos;\n  }\n}", "﻿import { domInfoHelper } from './exports'\nimport { styleHelper } from '../styleHelper'\nimport { state } from '../stateProvider'\nimport * as enums from '../enums';\n\n\nlet cachedScrollBarSize: number | undefined = undefined;\nconst scrollIds = new Map<HTMLElement, number>();\n\nexport class manipulationHelper {\n  static addElementToBody(element) {\n    document.body.appendChild(element);\n  }\n\n  static delElementFromBody(element) {\n    document.body.removeChild(element);\n  }\n\n  static addElementTo(addElement, elementSelector, prepend = false): boolean {\n    let parent = domInfoHelper.get(elementSelector);\n    if (parent && addElement) {\n      if (parent instanceof Node && addElement instanceof Node) {\n        if (prepend) parent.insertBefore(addElement, parent.firstChild);\n        else parent.appendChild(addElement);\n        return true;\n      } else {\n        console.log(\"does not implement node\", parent, addElement);\n      }\n    }\n    return false;\n  }\n\n  static delElementFrom(delElement, elementSelector) {\n    let parent = domInfoHelper.get(elementSelector);\n    if (parent && delElement) {\n      parent.removeChild(delElement);\n    }\n  }\n\n  static setDomAttribute(element, attributes) {\n    let dom: HTMLElement = domInfoHelper.get(element);\n    if (dom) {\n      for (let key in attributes) {\n        dom.setAttribute(key, attributes[key]);\n      }\n    }\n  }\n  \n  static copyElement(element) {\n    if (!this.copyElementAsRichText(element)) {\n      this.copy(element.innerText);\n    }\n  }\n\n  private static copyElementAsRichText(element) {\n    var selection = document.getSelection();\n    if (selection.rangeCount > 0) {\n      selection.removeAllRanges();\n    }\n    var range = document.createRange();\n    range.selectNode(element);\n    selection.addRange(range);\n    try {\n      var successful = document.execCommand('copy');\n      selection.removeAllRanges();\n      return successful;\n    } catch (err) {\n      selection.removeAllRanges();\n      return false;\n    }\n  }\n\n  static copy(text) {\n    if (!navigator.clipboard) {\n      this.fallbackCopyTextToClipboard(text);\n      return;\n    }\n    navigator.clipboard.writeText(text).then(function () {\n      console.log('Async: Copying to clipboard was successful!');\n    }, function (err) {\n      console.error('Async: Could not copy text: ', err);\n    });\n  }\n\n  private static fallbackCopyTextToClipboard(text) {\n    var textArea = document.createElement(\"textarea\");\n    textArea.value = text;\n\n    // Avoid scrolling to bottom\n    textArea.style.top = \"0\";\n    textArea.style.left = \"0\";\n    textArea.style.position = \"fixed\";\n\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n\n    try {\n      var successful = document.execCommand('copy');\n      var msg = successful ? 'successful' : 'unsuccessful';\n      console.log('Fallback: Copying text command was ' + msg);\n    } catch (err) {\n      console.error('Fallback: Oops, unable to copy', err);\n    }\n\n    document.body.removeChild(textArea);\n  }\n\n  static focus(selector, noScroll: boolean = false, option: enums.FocusBehavior = enums.FocusBehavior.FocusAtLast) {\n    let dom = domInfoHelper.get(selector);\n    if (!(dom instanceof HTMLElement))\n      throw new Error(\"Unable to focus on invalid element.\");\n\n    dom.focus({\n      preventScroll: noScroll\n    });\n\n    if (dom instanceof HTMLInputElement || dom instanceof HTMLTextAreaElement) {\n      switch (option) {\n        case enums.FocusBehavior.FocusAndSelectAll:\n          dom.select();\n          break;\n        case enums.FocusBehavior.FocusAtFirst:\n          dom.setSelectionRange(0, 0);\n          break;\n        case enums.FocusBehavior.FocusAtLast:\n          dom.setSelectionRange(-1, -1);\n          break;\n      }\n    }\n  }\n\n\n  static blur(selector) {\n    let dom = domInfoHelper.get(selector);\n    if (dom) {\n      dom.blur();\n    }\n  }\n\n  static scrollTo(selector: Element | string, parentElement?: HTMLElement) {\n    const element = domInfoHelper.get(selector);\n    if (parentElement && element && element instanceof HTMLElement) {\n      parentElement.scrollTop = element.offsetTop;\n    } else if (element && element instanceof HTMLElement) {\n        element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });\n    }    \n  }  \n\n  static smoothScrollTo(selector: Element | string, parentElement: HTMLElement, duration: number) {\n    const element = domInfoHelper.get(selector);\n    let to = element.offsetTop;\n    if (scrollIds.get(parentElement)) {\n        cancelAnimationFrame(scrollIds.get(parentElement)!);\n    }\n    // jump to target if duration zero\n    if (duration <= 0) {\n        scrollIds.set(\n            parentElement,\n            requestAnimationFrame(() => {\n                parentElement.scrollTop = to;\n            }),\n        );\n\n        return;\n    }\n    const difference = to - parentElement.scrollTop;\n    const perTick = (difference / duration) * 10;\n\n    scrollIds.set(\n        parentElement,\n        requestAnimationFrame(() => {\n            parentElement.scrollTop += perTick;\n            if (parentElement.scrollTop !== to) {\n                manipulationHelper.smoothScrollTo(selector, parentElement, duration - 10);\n            }\n        }),\n    );\n  }\n\n  static slideTo(targetPageY) {\n    const timer = setInterval(function () {\n      const currentY = document.documentElement.scrollTop || document.body.scrollTop;\n      const distance = targetPageY > currentY ? targetPageY - currentY : currentY - targetPageY;\n      const speed = Math.ceil(distance / 10);\n      if (currentY === targetPageY) {\n        clearInterval(timer);\n      } else {\n        window.scrollTo(0, targetPageY > currentY ? currentY + speed : currentY - speed);\n      }\n    }, 10);\n  }\n\n  //copied from https://www.telerik.com/forums/trigger-tab-key-when-enter-key-is-pressed\n  static invokeTabKey() {\n    var currInput = document.activeElement;\n    if (currInput.tagName.toLowerCase() == \"input\") {\n      var inputs = document.getElementsByTagName(\"input\");\n      var currInput = document.activeElement;\n      for (var i = 0; i < inputs.length; i++) {\n        if (inputs[i] == currInput) {\n          var next = inputs[i + 1];\n          if (next && next.focus) {\n            next.focus();\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  static disableBodyScroll() {\n    let body = document.body;\n    const oldBodyCache = {};\n    [\"position\", \"width\", \"overflow\"].forEach((key) => {\n      oldBodyCache[key] = body.style[key];\n    });\n    state.oldBodyCacheStack.push(oldBodyCache);\n    const scrollBarSize = this.getScrollBarSize();\n    styleHelper.css(body,\n      {\n        \"position\": \"relative\",\n        \"width\": this.hasScrollbar() && scrollBarSize > 0 ? `calc(100% - ${scrollBarSize}px)` : null,\n        \"overflow\": \"hidden\"\n      });\n    styleHelper.addCls(document.body, \"ant-scrolling-effect\");\n  }\n\n  static enableBodyScroll(force: boolean | undefined) {\n    if (force) {\n        state.oldBodyCacheStack = [];\n    }\n    let oldBodyCache = state.oldBodyCacheStack.length > 0 ? state.oldBodyCacheStack.pop() : {};\n    \n\n    styleHelper.css(document.body,\n      {\n        \"position\": oldBodyCache[\"position\"] ?? null,\n        \"width\": oldBodyCache[\"width\"] ?? null,\n        \"overflow\": oldBodyCache[\"overflow\"] ?? null\n      });\n    styleHelper.removeCls(document.body, \"ant-scrolling-effect\");\n  }\n\n  static hasScrollbar = () => {\n    let overflow = document.body.style.overflow;\n    if (overflow && overflow === \"hidden\") return false;\n    return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight);\n  }\n\n\n  /**\n   * getScrollBarSize\n   * source https://github.com/react-component/util/blob/master/src/getScrollBarSize.tsx\n   * \n   * @param fresh force get scrollBar size and don't use cache\n   * @returns \n   */\n   static getScrollBarSize = (fresh: boolean = false) => {\n    if (typeof document === \"undefined\") {\n      return 0;\n    }\n\n    if (fresh || cachedScrollBarSize === undefined) {\n      const inner = document.createElement(\"div\");\n      inner.style.width = \"100%\";\n      inner.style.height = \"200px\";\n\n      const outer = document.createElement(\"div\");\n      const outerStyle = outer.style;\n\n      outerStyle.position = \"absolute\";\n      outerStyle.top = \"0\";\n      outerStyle.left = \"0\";\n      outerStyle.pointerEvents = \"none\";\n      outerStyle.visibility = \"hidden\";\n      outerStyle.width = \"200px\";\n      outerStyle.height = \"150px\";\n      outerStyle.overflow = \"hidden\";\n\n      outer.appendChild(inner);\n\n      document.body.appendChild(outer);\n\n      const widthContained = inner.offsetWidth;\n      outer.style.overflow = \"scroll\";\n      let widthScroll = inner.offsetWidth;\n\n      if (widthContained === widthScroll) {\n        widthScroll = outer.clientWidth;\n      }\n\n      document.body.removeChild(outer);\n      cachedScrollBarSize = widthContained - widthScroll;\n    }\n    return cachedScrollBarSize;\n  };\n}", "\"use strict\";\n\nexports.__esModule = true;\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ==", "﻿export enum FocusBehavior {\n  /// <summary>\n  /// When focuses, cursor will move to the last character. \n  /// This is default behavior.\n  /// </summary>\n  FocusAtLast,\n  /// <summary>\n  /// When focuses, cursor will move to the first character\n  /// </summary>\n  FocusAtFirst,\n  /// <summary>\n  /// When focuses, the content will be selected\n  /// </summary>\n  FocusAndSelectAll\n}", "﻿import { eventCallback } from './dom/types'\n\n//Singleton \nexport class State {\n  private static instance: State;\n\n  //Stores references to dot net objects (components wrapped in DotNetObjectReference)\n  objReferenceDict: { [key: string]: any } = {};\n\n  //All object references must later be disposed by JS code or by .NET code.\n  disposeObj(objReferenceName) {\n    delete this.objReferenceDict[objReferenceName];\n  }\n\n  //Stores callback for events based on a key. Needed when\n  //Event needs to be removed - the callback can be retrieved and\n  //used to remove the event in question\n  eventCallbackRegistry: { [key: string]: eventCallback} = {};\n  \n  oldBodyCacheStack = [];\n\n  private constructor() { }\n\n  static getInstance() {\n    if (!this.instance) {\n      this.instance = new State();      \n    }\n    return this.instance;\n  }\n}\n\nexport const state = State.getInstance();", "﻿import { infoHelper as domInfoHelper } from './dom/infoHelper';\n\nexport class styleHelper {\n  static addCls(selector: Element | string, className: string | Array<string>) {\n    let element = domInfoHelper.get(selector);\n    if (element) {\n      if (typeof className === \"string\") {\n        element.classList.add(className);\n      } else {\n        element.classList.add(...className);\n      }\n    }\n  }\n\n  static removeCls(selector: Element | string, clsName: string | Array<string>) {\n    let element = domInfoHelper.get(selector);\n    if (element) {\n      if (typeof clsName === \"string\") {\n        element.classList.remove(clsName);\n      } else {\n        element.classList.remove(...clsName);\n      }\n    }\n  }\n\n  static addClsToFirstChild(element: Element | string, className: string): void {\n    var domElement = domInfoHelper.get(element);\n    if (domElement && domElement.firstElementChild) {\n      domElement.firstElementChild.classList.add(className);\n    }\n  }\n\n  static removeClsFromFirstChild(element: Element | string, className): void {\n    var domElement = domInfoHelper.get(element);\n    if (domElement && domElement.firstElementChild) {\n      domElement.firstElementChild.classList.remove(className);\n    }\n  }\n\n  static matchMedia(query: string): boolean {\n    return window.matchMedia(query).matches;\n  }\n\n  static getStyle(element, styleProp: string) {\n    if (element.currentStyle)\n      return element.currentStyle[styleProp];\n    else if (window.getComputedStyle)\n      return document.defaultView.getComputedStyle(element, null).getPropertyValue(styleProp);\n  }\n\n  //Referenced in Caret, class Mirror\n  static css(element: HTMLElement, name: string | object, value: string | null = null) {\n    if (typeof name === 'string') {\n      if (value === null) {\n        let style = name;\n        let cssAttributes = style.split(\";\");\n        for (let i = 0; i < cssAttributes.length; i++) {\n          let cssAttribute = cssAttributes[i];\n          if (!cssAttribute) continue;\n          let attribute = cssAttribute.split(\":\");\n          element.style.setProperty(attribute[0], attribute[1]);\n        }\n        return;\n      }\n      element.style.setProperty(name, value);\n    } else {\n      for (let key in name) {\n        if (name.hasOwnProperty(key)) {\n          element.style.setProperty(key, name[key]);\n        }\n      }\n    }\n  }\n}", "﻿import * as interop from \"./core/JsInterop/interop\";\n\ndeclare global {\n  interface Window {\n    AntDesign: any;\n  }\n}\n\nwindow.AntDesign = {\n  interop,\n};\n", "'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar tinycolor = require('@ctrl/tinycolor');\n\nvar hueStep = 2; // 色相阶梯\n\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\n\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\n\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\n\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\n\nvar lightColorCount = 5; // 浅色数量，主色上\n\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\n\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}]; // Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\n\nfunction toHsv(_ref) {\n  var r = _ref.r,\n      g = _ref.g,\n      b = _ref.b;\n  var hsv = tinycolor.rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n} // Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\n\n\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n      g = _ref2.g,\n      b = _ref2.b;\n  return \"#\".concat(tinycolor.rgbToHex(r, g, b, false));\n} // Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\n\n\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\n\nfunction getHue(hsv, i, light) {\n  var hue; // 根据色相不同，色相转向不同\n\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n\n  return hue;\n}\n\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n\n  var saturation;\n\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  } // 边界值修正\n\n\n  if (saturation > 1) {\n    saturation = 1;\n  } // 第一格的 s 限制在 0.06-0.1 之间\n\n\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n\n  return Number(saturation.toFixed(2));\n}\n\nfunction getValue(hsv, i, light) {\n  var value;\n\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n\n  if (value > 1) {\n    value = 1;\n  }\n\n  return Number(value.toFixed(2));\n}\n\nfunction generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = tinycolor.inputToRGB(color);\n\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(tinycolor.inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n\n  patterns.push(toHex(pColor));\n\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n\n    var _colorString = toHex(tinycolor.inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n\n    patterns.push(_colorString);\n  } // dark theme patterns\n\n\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n          opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(tinycolor.inputToRGB(opts.backgroundColor || '#141414'), tinycolor.inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n\n  return patterns;\n}\n\nvar presetPrimaryColors = {\n  red: '#F5222D',\n  volcano: '#FA541C',\n  orange: '#FA8C16',\n  gold: '#FAAD14',\n  yellow: '#FADB14',\n  lime: '#A0D911',\n  green: '#52C41A',\n  cyan: '#13C2C2',\n  blue: '#1890FF',\n  geekblue: '#2F54EB',\n  purple: '#722ED1',\n  magenta: '#EB2F96',\n  grey: '#666666'\n};\nvar presetPalettes = {};\nvar presetDarkPalettes = {};\nObject.keys(presetPrimaryColors).forEach(function (key) {\n  presetPalettes[key] = generate(presetPrimaryColors[key]);\n  presetPalettes[key].primary = presetPalettes[key][5]; // dark presetPalettes\n\n  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {\n    theme: 'dark',\n    backgroundColor: '#141414'\n  });\n  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];\n});\nvar red = presetPalettes.red;\nvar volcano = presetPalettes.volcano;\nvar gold = presetPalettes.gold;\nvar orange = presetPalettes.orange;\nvar yellow = presetPalettes.yellow;\nvar lime = presetPalettes.lime;\nvar green = presetPalettes.green;\nvar cyan = presetPalettes.cyan;\nvar blue = presetPalettes.blue;\nvar geekblue = presetPalettes.geekblue;\nvar purple = presetPalettes.purple;\nvar magenta = presetPalettes.magenta;\nvar grey = presetPalettes.grey;\n\nexports.blue = blue;\nexports.cyan = cyan;\nexports.geekblue = geekblue;\nexports.generate = generate;\nexports.gold = gold;\nexports.green = green;\nexports.grey = grey;\nexports.lime = lime;\nexports.magenta = magenta;\nexports.orange = orange;\nexports.presetDarkPalettes = presetDarkPalettes;\nexports.presetPalettes = presetPalettes;\nexports.presetPrimaryColors = presetPrimaryColors;\nexports.purple = purple;\nexports.red = red;\nexports.volcano = volcano;\nexports.yellow = yellow;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.numberInputToObject = exports.parseIntFromHex = exports.convertHexToDecimal = exports.convertDecimalToHex = exports.rgbaToArgbHex = exports.rgbaToHex = exports.rgbToHex = exports.hsvToRgb = exports.rgbToHsv = exports.hslToRgb = exports.rgbToHsl = exports.rgbToRgb = void 0;\nvar util_1 = require(\"./util\");\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nfunction rgbToRgb(r, g, b) {\n    return {\n        r: (0, util_1.bound01)(r, 255) * 255,\n        g: (0, util_1.bound01)(g, 255) * 255,\n        b: (0, util_1.bound01)(b, 255) * 255,\n    };\n}\nexports.rgbToRgb = rgbToRgb;\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nfunction rgbToHsl(r, g, b) {\n    r = (0, util_1.bound01)(r, 255);\n    g = (0, util_1.bound01)(g, 255);\n    b = (0, util_1.bound01)(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, l: l };\n}\nexports.rgbToHsl = rgbToHsl;\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nfunction hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = (0, util_1.bound01)(h, 360);\n    s = (0, util_1.bound01)(s, 100);\n    l = (0, util_1.bound01)(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\nexports.hslToRgb = hslToRgb;\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nfunction rgbToHsv(r, g, b) {\n    r = (0, util_1.bound01)(r, 255);\n    g = (0, util_1.bound01)(g, 255);\n    b = (0, util_1.bound01)(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, v: v };\n}\nexports.rgbToHsv = rgbToHsv;\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nfunction hsvToRgb(h, s, v) {\n    h = (0, util_1.bound01)(h, 360) * 6;\n    s = (0, util_1.bound01)(s, 100);\n    v = (0, util_1.bound01)(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [v, q, p, p, t, v][mod];\n    var g = [t, v, v, q, p, p][mod];\n    var b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\nexports.hsvToRgb = hsvToRgb;\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nfunction rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        (0, util_1.pad2)(Math.round(r).toString(16)),\n        (0, util_1.pad2)(Math.round(g).toString(16)),\n        (0, util_1.pad2)(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\nexports.rgbToHex = rgbToHex;\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        (0, util_1.pad2)(Math.round(r).toString(16)),\n        (0, util_1.pad2)(Math.round(g).toString(16)),\n        (0, util_1.pad2)(Math.round(b).toString(16)),\n        (0, util_1.pad2)(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\nexports.rgbaToHex = rgbaToHex;\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nfunction rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        (0, util_1.pad2)(convertDecimalToHex(a)),\n        (0, util_1.pad2)(Math.round(r).toString(16)),\n        (0, util_1.pad2)(Math.round(g).toString(16)),\n        (0, util_1.pad2)(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\nexports.rgbaToArgbHex = rgbaToArgbHex;\n/** Converts a decimal to a hex value */\nfunction convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\nexports.convertDecimalToHex = convertDecimalToHex;\n/** Converts a hex value to a decimal */\nfunction convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\nexports.convertHexToDecimal = convertHexToDecimal;\n/** Parse a base-16 hex value into a base-10 integer */\nfunction parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexports.parseIntFromHex = parseIntFromHex;\nfunction numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\nexports.numberInputToObject = numberInputToObject;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.names = void 0;\n// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexports.names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isValidCSSUnit = exports.stringInputToObject = exports.inputToRGB = void 0;\n/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nvar conversion_1 = require(\"./conversion\");\nvar css_color_names_1 = require(\"./css-color-names\");\nvar util_1 = require(\"./util\");\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nfunction inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = (0, conversion_1.rgbToRgb)(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = (0, util_1.convertToPercentage)(color.s);\n            v = (0, util_1.convertToPercentage)(color.v);\n            rgb = (0, conversion_1.hsvToRgb)(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = (0, util_1.convertToPercentage)(color.s);\n            l = (0, util_1.convertToPercentage)(color.l);\n            rgb = (0, conversion_1.hslToRgb)(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = (0, util_1.boundAlpha)(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\nexports.inputToRGB = inputToRGB;\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nfunction stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (css_color_names_1.names[color]) {\n        color = css_color_names_1.names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: (0, conversion_1.parseIntFromHex)(match[1]),\n            g: (0, conversion_1.parseIntFromHex)(match[2]),\n            b: (0, conversion_1.parseIntFromHex)(match[3]),\n            a: (0, conversion_1.convertHexToDecimal)(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: (0, conversion_1.parseIntFromHex)(match[1]),\n            g: (0, conversion_1.parseIntFromHex)(match[2]),\n            b: (0, conversion_1.parseIntFromHex)(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: (0, conversion_1.parseIntFromHex)(match[1] + match[1]),\n            g: (0, conversion_1.parseIntFromHex)(match[2] + match[2]),\n            b: (0, conversion_1.parseIntFromHex)(match[3] + match[3]),\n            a: (0, conversion_1.convertHexToDecimal)(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: (0, conversion_1.parseIntFromHex)(match[1] + match[1]),\n            g: (0, conversion_1.parseIntFromHex)(match[2] + match[2]),\n            b: (0, conversion_1.parseIntFromHex)(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\nexports.stringInputToObject = stringInputToObject;\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nfunction isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\nexports.isValidCSSUnit = isValidCSSUnit;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.legacyRandom = exports.fromRatio = void 0;\nvar index_1 = require(\"./index\");\nvar util_1 = require(\"./util\");\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nfunction fromRatio(ratio, opts) {\n    var newColor = {\n        r: (0, util_1.convertToPercentage)(ratio.r),\n        g: (0, util_1.convertToPercentage)(ratio.g),\n        b: (0, util_1.convertToPercentage)(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new index_1.TinyColor(newColor, opts);\n}\nexports.fromRatio = fromRatio;\n/** old random function */\nfunction legacyRandom() {\n    return new index_1.TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\nexports.legacyRandom = legacyRandom;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.tinycolor = exports.TinyColor = void 0;\nvar conversion_1 = require(\"./conversion\");\nvar css_color_names_1 = require(\"./css-color-names\");\nvar format_input_1 = require(\"./format-input\");\nvar util_1 = require(\"./util\");\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = (0, conversion_1.numberInputToObject)(color);\n        }\n        this.originalInput = color;\n        var rgb = (0, format_input_1.inputToRGB)(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = (0, util_1.boundAlpha)(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = (0, conversion_1.rgbToHsv)(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = (0, conversion_1.rgbToHsv)(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = (0, conversion_1.rgbToHsl)(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = (0, conversion_1.rgbToHsl)(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return (0, conversion_1.rgbToHex)(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return (0, conversion_1.rgbaToHex)(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round((0, util_1.bound01)(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round((0, util_1.bound01)(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + (0, conversion_1.rgbToHex)(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(css_color_names_1.names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = (0, util_1.clamp01)(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = (0, util_1.clamp01)(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = (0, util_1.clamp01)(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = (0, util_1.clamp01)(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\nexports.TinyColor = TinyColor;\n// kept for backwards compatability with v1\nfunction tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\nexports.tinycolor = tinycolor;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar index_1 = require(\"./index\");\n__exportStar(require(\"./index\"), exports);\n__exportStar(require(\"./css-color-names\"), exports);\n__exportStar(require(\"./readability\"), exports);\n__exportStar(require(\"./to-ms-filter\"), exports);\n__exportStar(require(\"./from-ratio\"), exports);\n__exportStar(require(\"./format-input\"), exports);\n__exportStar(require(\"./random\"), exports);\n__exportStar(require(\"./interfaces\"), exports);\n__exportStar(require(\"./conversion\"), exports);\n// kept for backwards compatability with v1\nexports.default = index_1.tinycolor;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bounds = exports.random = void 0;\n/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nvar index_1 = require(\"./index\");\nfunction random(options) {\n    if (options === void 0) { options = {}; }\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        var totalColors = options.count;\n        var colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    var h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    var s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    var v = pickBrightness(h, s, options);\n    var res = { h: h, s: s, v: v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new index_1.TinyColor(res);\n}\nexports.random = random;\nfunction pickHue(hue, seed) {\n    var hueRange = getHueRange(hue);\n    var res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    var saturationRange = getColorInfo(hue).saturationRange;\n    var sMin = saturationRange[0];\n    var sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    var bMin = getMinimumBrightness(H, S);\n    var bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    var lowerBounds = getColorInfo(H).lowerBounds;\n    for (var i = 0; i < lowerBounds.length - 1; i++) {\n        var s1 = lowerBounds[i][0];\n        var v1 = lowerBounds[i][1];\n        var s2 = lowerBounds[i + 1][0];\n        var v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            var m = (v2 - v1) / (s2 - s1);\n            var b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    var num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        var namedColor = exports.bounds.find(function (n) { return n.name === colorInput; });\n        if (namedColor) {\n            var color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        var parsed = new index_1.TinyColor(colorInput);\n        if (parsed.isValid) {\n            var hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (var _i = 0, bounds_1 = exports.bounds; _i < bounds_1.length; _i++) {\n        var bound = bounds_1[_i];\n        var color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    var max = range[1] || 1;\n    var min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    var rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    var sMin = bound.lowerBounds[0][0];\n    var sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    var bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    var bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexports.bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mostReadable = exports.isReadable = exports.readability = void 0;\nvar index_1 = require(\"./index\");\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nfunction readability(color1, color2) {\n    var c1 = new index_1.TinyColor(color1);\n    var c2 = new index_1.TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\nexports.readability = readability;\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nfunction isReadable(color1, color2, wcag2) {\n    var _a, _b;\n    if (wcag2 === void 0) { wcag2 = { level: 'AA', size: 'small' }; }\n    var readabilityLevel = readability(color1, color2);\n    switch (((_a = wcag2.level) !== null && _a !== void 0 ? _a : 'AA') + ((_b = wcag2.size) !== null && _b !== void 0 ? _b : 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\nexports.isReadable = isReadable;\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nfunction mostReadable(baseColor, colorList, args) {\n    if (args === void 0) { args = { includeFallbackColors: false, level: 'AA', size: 'small' }; }\n    var bestColor = null;\n    var bestScore = 0;\n    var includeFallbackColors = args.includeFallbackColors, level = args.level, size = args.size;\n    for (var _i = 0, colorList_1 = colorList; _i < colorList_1.length; _i++) {\n        var color = colorList_1[_i];\n        var score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new index_1.TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level: level, size: size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\nexports.mostReadable = mostReadable;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.toMsFilter = void 0;\nvar conversion_1 = require(\"./conversion\");\nvar index_1 = require(\"./index\");\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nfunction toMsFilter(firstColor, secondColor) {\n    var color = new index_1.TinyColor(firstColor);\n    var hex8String = '#' + (0, conversion_1.rgbaToArgbHex)(color.r, color.g, color.b, color.a);\n    var secondHex8String = hex8String;\n    var gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        var s = new index_1.TinyColor(secondColor);\n        secondHex8String = '#' + (0, conversion_1.rgbaToArgbHex)(s.r, s.g, s.b, s.a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\".concat(gradientType, \"startColorstr=\").concat(hex8String, \",endColorstr=\").concat(secondHex8String, \")\");\n}\nexports.toMsFilter = toMsFilter;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.pad2 = exports.convertToPercentage = exports.boundAlpha = exports.isPercentage = exports.isOnePointZero = exports.clamp01 = exports.bound01 = void 0;\n/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nfunction bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\nexports.bound01 = bound01;\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nfunction clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\nexports.clamp01 = clamp01;\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nfunction isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\nexports.isOnePointZero = isOnePointZero;\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nfunction isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\nexports.isPercentage = isPercentage;\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nfunction boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\nexports.boundAlpha = boundAlpha;\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nfunction convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\nexports.convertToPercentage = convertToPercentage;\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nfunction pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\nexports.pad2 = pad2;\n"]}