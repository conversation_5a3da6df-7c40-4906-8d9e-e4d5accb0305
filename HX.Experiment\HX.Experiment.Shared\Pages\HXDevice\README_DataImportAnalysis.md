# HX.Experiment 数据导入和数据分析功能实现

## 功能概述

在 `HX.Experiment\HX.Experiment.Shared\Pages\HXDevice\Detail.razor` 中新增了数据导入和数据分析功能，参考了HX.HRV项目中的相应实现。

## 新增的服务接口

### 1. IDataImportService & DataImportService
**位置**: 
- 接口: `HX.Experiment.Shared/Services/IDataImportService.cs`
- 实现: `HX.Experiment.Window/Services/DataImportService.cs`

**功能**:
- 从设备导入检测数据到服务器
- 检查设备数据状态
- 清理设备数据
- 提供导入进度反馈

**主要方法**:
- `ImportDataAsync(string deviceSn, string recordId)` - 导入数据
- `HasDataAsync(string deviceSn)` - 检查是否有数据
- `ClearDeviceDataAsync(string deviceSn)` - 清空设备数据
- `GetDeviceFileCountAsync(string deviceSn)` - 获取文件数量

### 2. IDataAnalysisService & DataAnalysisService
**位置**: 
- 接口: `HX.Experiment.Shared/Services/IDataAnalysisService.cs`
- 实现: `HX.Experiment.Window/Services/DataAnalysisService.cs`

**功能**:
- 启动数据分析任务
- 获取分析结果
- 导出分析报告
- 提供分析进度反馈

**主要方法**:
- `StartAnalysisAsync()` - 开始分析
- `GetAnalysisResultAsync()` - 获取分析结果
- `GetSegmentDataAsync()` - 获取时间段数据
- `ExportAnalysisResultAsync()` - 导出结果

## 新增的UI组件

### DataAnalysisDialog.razor
**位置**: `HX.Experiment.Shared/Pages/HXDevice/DataAnalysisDialog.razor`

**功能**:
- 数据分析向导对话框
- 三步式分析流程：选择时间段 → 执行分析 → 查看结果
- 支持时间段选择和分析名称设置
- 实时显示分析进度
- 分析结果展示和导出功能

## Detail.razor 页面更新

### 1. 快速操作区域新增按钮
- **数据导入**: 从设备导入检测数据到服务器，带进度显示
- **数据分析**: 打开数据分析对话框

### 2. 功能集成
- 数据导入进度显示
- 分析结果可视化
- 错误处理和用户反馈

## 数据模型

### ExperimentAnalysisResult
- 分析结果数据模型
- 包含心率变异性、皮肤电、情绪分析结果
- 支持图表和统计数据

### ExperimentSegmentData
- 时间段数据模型
- 包含PPG、EDA、IMU、HR数据点

### 事件参数类
- `DataImportProgressEventArgs` - 数据导入进度事件
- `DataAnalysisProgressEventArgs` - 数据分析进度事件
- `DataAnalysisCompletedEventArgs` - 数据分析完成事件

## API 接口依赖

### 数据导入相关
- `POST /api/v2/hx_experiment/TestRecord/UploadFile` - 上传检测文件

### 数据分析相关
- `POST /api/v2/hx_experiment/DataAnalysis/Add` - 创建分析记录
- `GET /api/v2/hx_experiment/DataAnalysis/Get` - 获取分析结果
- `GET /api/v2/hx_experiment/DataAnalysis/GetSegmentData` - 获取时间段数据
- `GET /api/v2/hx_experiment/DataAnalysis/Export/{analysisId}` - 导出分析结果

### WebSocket连接
- `ws://{host}/iot/hx_experiment/analysis/v1` - 分析进度和结果推送

## 与HX.HRV的对应关系

| HX.HRV功能 | HX.Experiment实现 | 说明 |
|------------|------------------|------|
| DeviceStatusCardContent数据导入 | DataImportService | 设备数据上传到服务器 |
| MainPage.xaml.cs的ExportToClient | DataImportService.ImportDataAsync | 文件复制和上传逻辑 |
| DataAnalysis.razor分析功能 | DataAnalysisDialog | 时间段选择和分析执行 |
| ALGDataAnalysisService | DataAnalysisService | 分析任务管理和结果处理 |

## 实现特点

### 1. 模块化设计
- 服务接口与实现分离
- UI组件可复用
- 事件驱动的进度反馈

### 2. 用户体验
- 实时进度显示
- 友好的错误提示
- 响应式设计

### 3. 扩展性
- 支持多种分析类型
- 可配置的分析参数
- 灵活的结果展示

## 使用流程

### 数据导入流程
1. 确保设备已连接并有数据
2. 点击"数据导入"按钮
3. 系统自动检查设备数据
4. 复制文件到临时目录
5. 上传文件到服务器
6. 清理临时文件和设备数据

### 数据分析流程
1. 点击"数据分析"按钮
2. 选择分析时间段和输入分析名称
3. 点击"开始分析"
4. 系统执行心率变异性和皮肤电分析
5. 显示分析结果和图表
6. 可导出分析报告

## 注意事项

1. **设备通信**: 需要根据实际设备协议调整命令格式
2. **API接口**: 需要后端实现对应的API接口
3. **WebSocket**: 需要配置分析服务的WebSocket连接
4. **文件路径**: 导入和导出路径需要根据实际环境调整
5. **权限管理**: 需要确保用户有相应的操作权限
6. **依赖注入**: 需要在DI容器中注册新的服务接口
