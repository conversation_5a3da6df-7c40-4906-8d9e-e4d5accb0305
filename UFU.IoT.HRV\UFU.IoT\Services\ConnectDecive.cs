﻿using Microsoft.Extensions.FileProviders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Services
{
    /// <summary>
    /// 已连接设备
    /// </summary>
    public class ConnectDevice
    {
        private int _msgId = (int)(DateTime.Now - DateTime.Now.Date).TotalSeconds * 100;
        private bool otaStart = false;
        /// <summary>
        /// 消息编号
        /// </summary>
        public int MsgId { get { return _msgId++; } }

        /// <summary>
        /// 连接对象
        /// </summary>
        public IConnect Connect { get; }
        /// <summary>
        /// 设备编号
        /// </summary>
        public string DeviceId { get; }
        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceModel Device { get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastDataTime { get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime ConnectTime { get; set; }
        /// <summary>
        /// 时间差，单位毫秒(服务器时间-设备时间)
        /// </summary>
        public double? TimeOffset { get; set; }
        /// <summary>
        /// 登录Token
        /// </summary>
        public DeviceTokenModel Token { get; set; }
        /// <summary>
        /// 是否需要更新数据库
        /// </summary>
        public bool NeedUpdate { get; set; }
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; }
        /// <summary>
        /// 查找值
        /// </summary>
        public int? FindValue { get; set; }
        /// <summary>
        /// OTA文件
        /// </summary>
        public IFileInfo OTAFile { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connect">连接对象</param>
        /// <param name="device">设备对象</param>
        public ConnectDevice(IConnect connect, DeviceModel device)
        {
            Connect = connect;
            Device = device;
            DeviceId = device.Id;
            //DeviceType = deviceType;
            //TypeId = deviceType.Id;
            LastDataTime = DateTime.Now;
            ConnectTime = DateTime.Now;
        }

        /// <summary>
        /// 开始OTA
        /// </summary>
        /// <param name="offset"></param>
        public void OTAStart(long offset)
        {
            if (otaStart)
            {
                return;
            }

            otaStart = true;

            using (var stream = OTAFile.CreateReadStream())
            {
                if (otaStart && offset < OTAFile.Length)
                {
                    long left = OTAFile.Length - offset;
                    var bufferLength = (int)Math.Min(2048, left);
                    var datas = new byte[bufferLength];
                    stream.Position = offset;
                    stream.Read(datas, 0, bufferLength);
                    var buffer = new byte[bufferLength + 1];
                    buffer[0] = 1;
                    Buffer.BlockCopy(datas, 0, buffer, 1, datas.Length);
                    if (Connect.SendBinary(buffer))
                    {
                        offset += bufferLength;
                        //Task.Delay(300).Wait();
                    }
                    Console.WriteLine($"[OTA][{Device.Id}] { (offset * 100.0 / OTAFile.Length).ToString("f2") }%");
                }
            }
            otaStart = false;
        }

        /// <summary>
        /// 停止OTA
        /// </summary>
        public void OTAStop()
        {
            otaStart = false;
        }

        /// <summary>
        /// OTA是否已经开始
        /// </summary>
        /// <returns></returns>
        public bool IsOTAStart()
        {
            return otaStart;
        }
    }
}
