﻿@page "/Client/SCI/DataAnalysis/{RecordId}"
@attribute [Permission("数据分析", Icon = "shuju", MenuOrder = "5")]
@inject IJSRuntime JS
@using System.Collections
@using System.Globalization
@using System.Net.WebSockets
@using System.Text
@using System.Text.Json
@using System.Text.Json.Nodes
@using HX.Base.Shared.Components
@using HX.HRV.SCI.Shared.Models
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.CSVDataModel
@using HX.HRV.Shared.Services
@using Masa.Blazor
@using Masa.Blazor.Presets
@using Microsoft.JSInterop
@using UFU.CoreFX.Models
@using UFU.CoreFX.Permission
@using UFU.CoreFX.Shared.Services
@using UFU.CoreFX.Utils
@implements IDisposable
@inject DeviceStateService DeviceStateService
<style>

    .hrv-report-fields {
        display: flex;
        flex-wrap: wrap; /* 支持换行布局 */
        border: 1px solid #ddd; /* 可选：边框 */
        background-color: #f9f9f9; /* 背景色 */
        padding: 0.5rem;
        font-size: 1.2rem;
        flex-flow: row;
        width: 100%;
        justify-content: space-between;
    }

    .field-item {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        align-content: stretch;
    }

    .hrv-report-label {
        font-weight: bold;
        color: #333;
        text-align: end;
    }

    .hrv-report-value {
        color: #555;
        text-align: left;
    }

    .data-analysis-table {

    }

    .data-analysis-table th,
    .data-analysis-table td {
        border-bottom: 1px solid #ddd;
        padding: 0.125em;
        width: 10em;
    }

    .data-analysis-table tr:first-child {
        border-top: 1px solid #ddd;
    }

    .data-analysis-table th {
        background-color: #e1ebf6;
        color: #333;
    }

    .section-title {
        font-weight: bold;
        color: #3982e3;
        text-align: left;
        font-size: 1.5rem;
        margin-top: 0.5rem;
    }

</Style>
<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<div style="display: flex;">
    <div class="hrv-report-fields">
        @if (_fields != null)
        {
            foreach (var field in _fields)
            {
                <div class="field-item">
                    <div class="hrv-report-label">@(field.Label)：</div>
                    <div class="hrv-report-value">@field.Value</div>
                </div>
            }
        }
    </div>
</div>
<MStepper Style="height: 100%;font-size: 20px" Value="_step">
    <MStepperHeader>
        <MStepperStep Step="1" Complete="_step>1">
            选择数据
        </MStepperStep>
        <MDivider></MDivider>
        <MStepperStep Step="2" Complete="_step>2">
            分析
        </MStepperStep>
    </MStepperHeader>

    <MStepperItems>
        <MStepperContent Step="1">
       <div Class="d-flex justify-center align-center">
                    <MContainer class="d-flex justify-center align-center" Style="width:60%">
                        <MLabel>分析时段:</MLabel>
                        <PDateDigitalClockPicker @bind-Value="SegmentStartDate"
                                                 Step="@TimeSpan.FromMinutes(1)"
                                                 TimeFormat="TimeFormat.Hr24">
                            <PDefaultDateTimePickerActivator Filled SoloInverted
                                                             Dense
                                                             Clearable/>
                        </PDateDigitalClockPicker>
                        -
                        <PDateDigitalClockPicker @bind-Value="SegmentEndDate"
                                                 Step="@TimeSpan.FromMinutes(1)"
                                                 TimeFormat="TimeFormat.Hr24">
                            <PDefaultDateTimePickerActivator Filled SoloInverted
                                                             Dense
                                                             Clearable/>
                        </PDateDigitalClockPicker>
                        @* <div class="d-flex justify-center align-center"> *@
                        @*     <MLabel Class="ml-2">分段时长(分钟):</MLabel>  *@
                        @*     <div class="d-flex justify-center align-center" style="width: 100px;"> *@
                        @*         <MSelect Id="sex" Items="SpaceTimes" *@
                        @*                  @bind-Value="_spaceTime" *@
                        @*                  ItemText="u => u.ToString()" *@
                        @*                  SingleLine *@
                        @*                  Dense *@
                        @*                  HideDetails="true" *@
                        @*                  Solo *@
                        @*                  SoloInverted *@
                        @*                  ItemValue="u => u"> *@
                        @*         </MSelect> *@
                        @*     </div> *@
                        @* </div> *@
                        <MButton Type="primary" Style="margin-left: 10px" 
                                 OnClick="async ()=>await OnDateClick()">
                            确定
                        </MButton>
                        <div >
                            <MButton Color="primary" Style="margin-left: 10px;"
                                     OnClick="OnAnalysisClick">分析
                            </MButton>
                        </div>
                    </MContainer>
                </div>
            <div style="display: flex">
                <div style="flex:5">
                    <div style="display: flex;padding:  0em 2rem;width: 100% ;justify-content: center;flex-direction: column;  ">
                        <div style="width:100%;height: 650px">
                            <HXEchartSegmentLazyComponent
                                OnDataZoom="@OnEchartDataZoom"
                                @ref="_echartSegmentComponent"
                            />
                        </div>
                        @* <div style="display: flex;width: 100% ;justify-content:center;align-items: center; "> *@
                        @*     <MButton Large class="mr-2 white--text" *@
                        @*              Color="blue-grey" *@
                        @*              LeftIconName="mdi-chevron-left" *@
                        @*              OnClick="PreCallback"> *@
                        @*         上一时间段 *@
                        @*     </MButton> *@
                        @*     <div class="d-flex flex-column justify-space-between"> *@
                        @*         <MLabel Class="blue--text text--accent-1"> *@
                        @*             所选时间段： @DataZoomStartDateTime.ToString("yyyy-MM-dd HH:mm:ss") - @DataZoomEndDateTime.ToString("yyyy-MM-dd HH:mm:ss") *@
                        @*         </MLabel> *@
                        @*         <MLabel Class="mt-2 blue--text text--darken-4"> *@
                        @*             显示时间段： @StartDateTime.ToString("yyyy-MM-dd HH:mm:ss") - @EndDateTime.ToString("yyyy-MM-dd HH:mm:ss") *@
                        @*         </MLabel> *@
                        @* *@
                        @*     </div> *@
                        @*     <MButton Large class="ml-2 white--text" *@
                        @*              Color="blue-grey" *@
                        @*              RightIconName="mdi-chevron-right" *@
                        @*              OnClick="NextCallback"> *@
                        @*         下一时间段 *@
                        @*     </MButton> *@
                        @* </div> *@
                        @* *@
                    </div>
                </div>
            </div>
        </MStepperContent>
        <MStepperContent Step="2">

            @if (Statistics != null)
            {
                <MTabs
                    @bind-Value="_SelectTable"
                    Centered
                    SliderColor="1f73d2">
                    <MTab Value=@("心率变异性分析")>
                        @("心率变异性分析")
                    </MTab>
                    <MTab Value=@("皮肤电分析")>
                        @("皮肤电分析")
                    </MTab>
                </MTabs>
                <MTabsItems Value="@_SelectTable">
                    <MTabItem Value="@("心率变异性分析")">
                        <div style="display: flex">
                            
                            <div style=" display: flex; flex-flow: row; ">
                            @if (isShowEDA)
                            {
                                <img src="@Statistics?.HRImage" alt=""/>
                            }
                            @* @if (HRChartOption != null) *@
                                @* { *@
                                @*     $1$ <MECharts Option="@HRChartOption" #1# *@
                                @*     $1$           Width="800" Height="600"> #1# *@
                                @*     $1$ </MECharts> #1# *@
                                @*     *@
                                @* } *@
                            </div>
                            <div style="display: flex; ">
                                <div style=" flex: 2;display: flex; ">
                                    <div style="display: flex; flex-flow: column;font-size: 18px">
                                        <div>
                                            <div class="section-title">Time Domain</div>
                                            <table class="data-analysis-table">
                                                <tr>
                                                    @foreach (var reportData in ReportDataModel.timeDomain)
                                                    {
                                                        <td style="font-weight: bold">@reportData.Name </td>
                                                    }
                                                </tr>
                                                <tr>
                                                    @foreach (var reportData in ReportDataModel.timeDomain)
                                                    {
                                                        <td>@Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key) @reportData.Unit.Replace("-", "")</td>
                                                    }
                                                </tr>
                                            </table>
                                        </div>
                                        <div>
                                            <div class="section-title">Frequency domain</div>
                                            <table class="data-analysis-table">
                                                @{
                                                    var size = 7;
                                                    var totalPage = (ReportDataModel.frequencyDomain.Length + size - 1) / size;

                                                    for (var i = 0; i < totalPage; i++)
                                                    {
                                                        <tr>
                                                            @for (var j = 0; j < size; j++)
                                                            {
                                                                var index = i * size + j;
                                                                if (index < ReportDataModel.frequencyDomain.Length)
                                                                {
                                                                    <td style="font-weight: bold">@ReportDataModel.frequencyDomain[index].Name </td>
                                                                }
                                                            }
                                                        </tr>
                                                        <tr>
                                                            @for (var j = 0; j < size; j++)
                                                            {
                                                                var index = i * size + j;
                                                                if (index < ReportDataModel.frequencyDomain.Length)
                                                                {
                                                                    <td style="font-size: 0.85rem">@(Statistics?.StatisticsDictionary.GetValueOrDefault(ReportDataModel.frequencyDomain[index].Key) + ReportDataModel.frequencyDomain[index].Unit.Replace("-", ""))</td>
                                                                }
                                                            }
                                                        </tr>
                                                    }
                                                }

                                            </table>
                                        </div>
                                        <div>
                                            <div class="section-title">Nonlinear</div>
                                            <table class="data-analysis-table">
                                                <tr>
                                                    @foreach (var reportData in ReportDataModel.nonlinearAnalysis)
                                                    {
                                                        <td style="font-weight: bold">@reportData.Name </td>
                                                    }
                                                </tr>
                                                <tr style="font-size: 0.85rem">
                                                    @foreach (var reportData in ReportDataModel.nonlinearAnalysis)
                                                    {
                                                        <td>@(Statistics?.StatisticsDictionary.GetValueOrDefault(reportData.Key) + reportData.Unit.Replace("-", ""))</td>
                                                    }
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </MTabItem>
                    <MTabItem Value="@("皮肤电分析")">
                        <div style="display: flex">
                            <div style="display: flex;flex-direction: column;height: 700px">
                                @if (isShowEDA)
                                {
                                    @* <MECharts Option="EDAChartOption" *@
                                    @*           Width="800" Height="320"> *@
                                    @* </MECharts> *@
                                    @* <MECharts Option="SCRChartOption" *@
                                    @*           Width="800" Height="320"> *@
                                    @* </MECharts> *@
                                <img src="@EdaImage"   alt=""/>
                                <img src="@ScrImage"   alt=""/>
                                }
                            </div>
                            <div style="display: flex;font-size: 16px ">
                                <div style="display: flex;padding-top: 1rem; flex-flow: column;">
                                    <div>
                                        <div class="section-title">Time Domain</div>
                                       
                                            <table class="data-analysis-table">
                                                <tr>
                                                    @foreach (var item in EDATimeDomain)
                                                    {
                                                        <td style="font-weight: bold">@item </td>
                                                    }
                                                </tr>
                                                @foreach (var timeDomain in EdaTimeDomain)
                                                {
                                                    <tr>
                                                        <td style="font-size: 0.85rem">@timeDomain.Name</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Mean</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Max</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Min</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.SD</td>
                                                        <td style="font-size: 0.85rem">@timeDomain.Var</td>
                                                    </tr>
                                                }
                                            </table>
                                    </div>
                                    <div>
                                        <div class="section-title">SCR</div>
                                        @if (EdaSCRResults != null)
                                        {
                                            <table class="data-analysis-table"
                                                   style="max-height: 240px;overflow-y:scroll; display: block;">
                                                <tr>
                                                    @foreach (var item in ScrTimeDomain)
                                                    {
                                                        <td style="font-weight: bold">@item </td>
                                                    }
                                                </tr>
                                                @foreach (var item in EdaSCRResults)
                                                {
                                                    <tr>
                                                        <td style="font-size: 0.85rem">@item.SCR_OnsetsTime</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_PeaksTime</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_RiseTime</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_Amplitude</td>
                                                        <td style="font-size: 0.85rem">@item.SCR_RecoveryTime</td>
                                                    </tr>
                                                }
                                            </table>
                                        }
                                    </div>
                                    <div>
                                        <div class="section-title">Event Related</div>
                                        @if (EventKey != null)
                                        {
                                            <table class="data-analysis-table"
                                                   style="max-height: 240px;overflow-y:scroll; display: block;">
                                                <tr>
                                                    @foreach (var item in EventKey)
                                                    {
                                                        <td style="font-weight: bold">@item </td>
                                                    }
                                                </tr>
                                                @foreach (var item in EdaEventResults ?? new List<EdaEventResult>())
                                                {
                                                    <tr>
                                                        <td style="font-size: 0.85rem">@item.event_id</td>
                                                        <td style="font-size: 0.85rem">@item.event_label</td>
                                                        <td style="font-size: 0.85rem">@item.event_OnsetTime</td>
                                                        <td style="font-size: 0.85rem">@item.nSCR</td>
                                                        <td style="font-size: 0.85rem">@item.Latency</td>
                                                        <td style="font-size: 0.85rem">@item.Amplitude_Summary</td>
                                                        <td style="font-size: 0.85rem">@item.Average_SCL</td>
                                                        <td style="font-size: 0.85rem">@item.Average_RiseTime</td>
                                                        <td style="font-size: 0.85rem">@item.Average_RecoveryTime</td>
                                                    </tr>
                                                }
                                            </table>
                                        }
                                    </div>

                                </div>
                            </div>
                        </div>
                    </MTabItem>
                </MTabsItems>
            }
            <div style="width: 100%;display: flex; flex-flow: row;justify-content: flex-end;margin-bottom: 1rem;">
                <MButton
                    OnClick="PreviousStep">
                    上一步
                </MButton>
                <MButton Color="primary" Style="margin-left: 10px"
                         OnClick="OnExportClick">导出
                </MButton>
            </div>
        </MStepperContent>
    </MStepperItems>
</MStepper>

<PModal
    @bind-Value="isShowName"
    Persistent
    Title="请输入分析名称"
    Width="500"
    OnSave="HandleOnSave"
    OnCancel="HandleOnCancel">
    <MRow>
        <MCol Cols="12">
            <MTextField @bind-Value="Name"
                        Label="分析名称"
                        Placeholder="请输入分析名称"
                        Dense
                        Outlined/>
        </MCol>
    </MRow>
</PModal>

@code {
    private int _step = 1;

    private class Field
    {
        public string Label { get; set; }
        public string Value { get; set; }
    }

    public bool isShowName { get; set; }
    private string Name { get; set; }
    public bool isLoadPPG { get; set; } = true;
    [Parameter] public string RecordId { get; set; }
    private PatientModel patientModel = new();
    private ReportDataStatistics Statistics { get; set; }
    private PatientRecordModel _patientRecordModel = new();
    [Inject] StateService _stateService { get; set; }
    [Inject] public InternalHttpClientService InternalHttpClientService { get; set; }

    private async Task HandleOnSave(ModalActionEventArgs args)
    {
        isShowName = true;
        await OnAnalysisButtonClick();
    }

    private void HandleOnCancel()
    {
        DeviceStateService.OnMessageReceived -= HandlerReceiveDataFromSocket;
        Name = string.Empty;
        isShowName = false;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitRecordDataAsync();
            _fields = new[]
            {
                new Field { Label = "被试编号", Value = patientModel.OutpatientNumberString },
                new Field { Label = "姓名", Value = patientModel.Name },
                new Field { Label = "报告编号", Value = _patientRecordModel.RecordCode },
                new Field { Label = "开始采集时间", Value = _patientRecordModel.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss") },
                new Field { Label = "结束采集时间", Value = _patientRecordModel.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss") },
                new Field { Label = "数据长度", Value = _patientRecordModel.CollectEndTime - _patientRecordModel.CollectStartTime > TimeSpan.Zero ? (_patientRecordModel.CollectEndTime - _patientRecordModel.CollectStartTime).ToString(@"hh\:mm\:ss", CultureInfo.InvariantCulture) : "" }
            };
            await ConnectWebSocketAsync();
        }
        await base.OnAfterRenderAsync(firstRender);
    }


    private ClientWebSocket ClientSocket { get; set; }
    private DateTime SegmentStartDate { get; set; }
    private DateTime SegmentEndDate { get; set; }

    private async Task UpdateSegmentData()
    {
        PopupService.ShowProgressCircular();
        var json = new
        {
            recordId = RecordId,
            startTime = SegmentStartDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            endTime = SegmentEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff")
        };
        _echartSegmentComponent?.ClearData();
        var msgStr = JsonTool.SerializeIgnoreNull(json);
        await ClientSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
            CancellationToken.None);

    }

    protected override void OnInitialized()
    {
      
        base.OnInitialized();
    }


    private CancellationTokenSource _cancellationTokenSource;

    private List<EDACSVDataModel> EDADatas = new();

    private void HandlerReceiveDataFromSocket(JsonNode algJsonNode)
    {
        try
        {
            if (algJsonNode["Function"]?.ToString() == "FromALG")
            {
                _cancellationTokenSource?.Cancel();
                record = algJsonNode["Record"].Deserialize<DataModel<DataAnalysisRecordModel>>();
                Statistics = record.Data.Statistics;
               
                isShowName = false;
                _step = 2;
                if (algJsonNode["Category"]?.ToString() == "GSR")
                {
                   
                    try
                    {
                        EdaImage = record.Data.EdaImageUrl;
                        ScrImage = record.Data.SCL_SCRImageUrl;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }

                    EdaTimeDomain = record.Data.EdaTimeDomain;
                    EdaSCRResults = record.Data.EdaSCRResults;
                    EdaEventResults = record.Data.EdaEventResults;
                    isShowEDA = true;
                    PopupService.EnqueueSnackbarAsync("皮肤电分析完成", AlertTypes.Success);
                    PopupService.HideProgressCircular();
                }
                else
                {
                    
                    PopupService.EnqueueSnackbarAsync("心率变异性分析完成", AlertTypes.Success);
                }

                try
                {
                    InvokeAsync(StateHasChanged);
                }
                catch (Exception e)
                {
                    Console.WriteLine("HandlerReceiveDataFromSocket InvokeAsync Error:" + e);
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"HandlerReceiveDataFromSocket:{e}");
            throw;
        }
    }

    public EDADataCalculateModel SclDaCalculateModel { get; set; }

    public EDADataCalculateModel ScrDaCalculateModel { get; set; }

    public EDADataCalculateModel EdaDaCalculateModel { get; set; }

    private Dictionary<string, EDADataCalculateModel>
        EDADataCalculateModels = new();

    private readonly string[] EDATimeDomain =
    {
        "Name",
        "Mean",
        "MAX",
        "MIN",
        "Standard Deviation",
        "Variance"
    };


    private readonly string[] ScrTimeDomain =
    {
        "Onset Time",
        "Peak Time",
        "Rise Time",
        "Amp",
        "Half Deacy Time"
    };


    private readonly string[] EventKey =
    {
        "Event Group",
        "Event Name ",
        //  "Event Desription",
        "Event Time",
        "nSCR",
        "Latency",
        "Amplitude Summary",
        "SCL",
        "AVerage Rise Time",
        "AVerage Half Decay Time"
    };

    private async Task InitRecordDataAsync()
    {
        var res = await _stateService
            .GetAsJsonAsync<DataModel<PatientRecordModel>>(
                "/api/v2.0/HRV_HX/PatientRecord/GetPatientRecordModelDetailById", new Dictionary<string, string>
                {
                    { "id", RecordId }
                });
        _patientRecordModel = res?.Data?.Data;
        patientModel = _patientRecordModel.Patient;
        SegmentStartDate = _patientRecordModel.CollectStartTime;
        SegmentEndDate = _patientRecordModel.CollectEndTime;
    }

    // public DateTime StartDateTime { get; set; }

    private IEnumerable<Field> _fields;
    private async Task HandleDataZoom((float startIndex, float endIndex) indices)
    {
        Console.WriteLine("DataZoom: " + indices.startIndex + " - " + indices.endIndex);
        StartIndex = indices.startIndex;
        EndIndex = indices.endIndex;
        var timeSpan = _patientRecordModel.CollectEndTime - _patientRecordModel.CollectStartTime;
        var startIndex = timeSpan.TotalMilliseconds * indices.startIndex;
        var endIndex = timeSpan.TotalMilliseconds * indices.endIndex;
        var newStartTime = _patientRecordModel.CollectStartTime.AddMilliseconds(startIndex);
        var newEndTime = _patientRecordModel.CollectStartTime.AddMilliseconds(endIndex);
        SegmentStartDate = newStartTime;
        SegmentEndDate = newEndTime;
    }
    private int _chartType = 1;
    // private int _spaceTime = 5;
    private DateTime maxDate = DateTime.MaxValue;
    private DataModel<DataAnalysisRecordModel> record;
    private DataAnalysisRecordModel RecordModel => record.Data;
    private StringNumber? _SelectTable = "心率变异性分析";

    private bool isShowEDA;
    private HXEchartSegmentLazyComponent _echartSegmentComponent;
    private bool isShowSCR;

    private async Task OnDateClick()
    {
        
        if(SegmentStartDate>SegmentEndDate)
        {
            await PopupService.EnqueueSnackbarAsync("开始时间必须小于结束时间", AlertTypes.Error);
            return;
        }

        if (SegmentStartDate < _patientRecordModel.CollectStartTime)
        {
            await     PopupService.EnqueueSnackbarAsync("开始时间必须大于采集开始时间", AlertTypes.Error);
            return;
        }
        if (SegmentEndDate > _patientRecordModel.CollectEndTime)
        {
            await PopupService.EnqueueSnackbarAsync("结束时间必须小于采集结束时间", AlertTypes.Error);
            return;
        }
        // StartDateTime = SegmentStartDate;
        // EndDateTime = SegmentStartDate.AddMinutes(_spaceTime);
        await _echartSegmentComponent.ClearData();
        await UpdateSegmentData();
    }

    private async Task OnAnalysisButtonClick()
    {
        
        // if(DataZoomStartDateTime==default || DataZoomEndDateTime==default)
        // {
        //     await PopupService.EnqueueSnackbarAsync("请选择时间段", AlertTypes.Error);
        //     return;
        // }

        if (SegmentEndDate - SegmentStartDate > TimeSpan.FromHours(1))
        {
            await PopupService.EnqueueSnackbarAsync("时间段不能大于1小时", AlertTypes.Error);
            return;
        }
        
        var data = new
        {
            Function = "ToALG",
            Data = new
            {
                RecordId,
                StartTime = SegmentStartDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                EndTime = SegmentEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                Name
            }
        };
        var msgStr = JsonTool.SerializeIgnoreNull(data);
        try
        {
            await DeviceStateService.ClientSocket.SendAsync(new ArraySegment<byte>(Encoding.UTF8.GetBytes(msgStr)), WebSocketMessageType.Text, true,
                CancellationToken.None);
        }
        catch (Exception e)
        {
            await PopupService.EnqueueSnackbarAsync(e.Message, AlertTypes.Error);
        }

        PopupService.ShowProgressCircular();
        // 启动取消操作的计时器
        _cancellationTokenSource = new CancellationTokenSource();
        // 启动计时器任务
        _ = Task.Delay(1000 * 30*5, _cancellationTokenSource.Token).ContinueWith(t =>
        {
            if (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                PopupService.HideProgressCircular();
                PopupService.EnqueueSnackbarAsync("分析操作超时", AlertTypes.Error);
                _cancellationTokenSource.Cancel();
            }
        }, TaskContinuationOptions.OnlyOnRanToCompletion);
    }

    private IPopupService PopupService => DeviceStateService.PopupService;
    private float EndIndex { get; set; }
    private float StartIndex { get; set; }
   
    public void Dispose()
    {
        _cancellationTokenSource?.Cancel(); // 确保在销毁时取消计时器
        DeviceStateService.OnMessageReceived -= HandlerReceiveDataFromSocket;
        _cts.Cancel();
        _cts.Dispose();
        if (ClientSocket is { State: WebSocketState.Open })
        {
            try
            {
                ClientSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Component disposing", CancellationToken.None).Wait();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during Dispose closing WebSocket: {ex.Message}");
            }
        }

        ClientSocket?.Dispose();
        ClientSocket = null;
    }

    [Inject] HttpClient HttpClient { get; set; }

    private async Task OnExportClick()
    {
        if (record == null)
        {
            await PopupService.EnqueueSnackbarAsync("请先进行数据分析", AlertTypes.Warning);
            return;
        }

        _ = Task.Run(async () =>
        {
            PopupService.ShowProgressCircular();
            await JS.InvokeVoidAsync("AutoPrintPdf", $"/client/DataAnalysis/ReportDetail/{record.Id}");
            await Task.Delay(10000);
            PopupService.HideProgressCircular();
        });
        await JS.InvokeVoidAsync("downloadFileFromUrl", $"api/v2.0/SCI/DataAnalysisRecord/DownloadStatic/{record.Id}");
    }

    private void NextStep(int step)
    {
        _step = 2;
    }

    private void PreviousStep()
    {
        _step = 1;
    }

    // private async Task PreCallback()
    // {
    //     StartDateTime = StartDateTime.AddMinutes(-_spaceTime);
    //     EndDateTime=EndDateTime.AddMinutes(_spaceTime);
    //     await UpdateSegmentData();
    // }
    //
    // private async Task NextCallback()
    // {
    //     StartDateTime = StartDateTime.AddMinutes(_spaceTime);
    //     EndDateTime=EndDateTime.AddMinutes(_spaceTime);
    //     await UpdateSegmentData();
    // }

private object HRChartOption { get; set; }

    private void GenerateChartOption(List<int> data)
    {
        HRChartOption= new
        {
            Animation = false,
            Title = new
            {
                text = "心率",
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            Grid = new
            {
                left = "3%",
                right = "10%",
                top = "20%",
                bottom = "3%",
                containLabel = true
            },
            xAxis = new
            {
                type = "category",
                Show = false
            },
            yAxis = new
            {
                type = "value"
            },
            series = new[]
            {
                new
                {
                    data,
                    type = "line",
                    ShowSymbol = false
                }
            }
        };
    }


    private object EDAChartOption { get; set; } = new { };
    private object SCRChartOption { get; set; } = new { };
    public string? EdaImage { get; set; }
    public string? ScrImage { get; set; }
    public List<EdaTimeDomain> EdaTimeDomain { get; set; } = new List<EdaTimeDomain>();
    public List<EdaSCRResult> EdaSCRResults { get; set; }
    public List<EdaEventResult> EdaEventResults { get; set; }

    /// <summary>
    ///     构建EDA图表
    /// </summary>
    /// <returns></returns>
    private void GenerateEDAChartOption()
    {
        var series = new List<object>();

        //从EDADatas中生成Series

        var seriesEdaData = EDADatas.Select(m => new object[]
        {
            m.Date.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            m.EDA
        }).ToList();


        var seriesScr = EDADatas.Select(m => new object[]
        {
            m.Date.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            m.SCR
        }).ToList();

        var seriesScl = EDADatas.Select(m =>
            new object[]
            {
                m.Date.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                m.SCL
            }).ToList();

        series.Add(new
        {
            sampling = "lttb",
            large = true,
            Name = "EDA",
            type = "line",
            data = seriesEdaData,
            yAxisIndex = 1,
            showSymbol = false,
            markLine = new
            {
                symbol = new[] { "none" },
                label = new
                {
                    show = true
                },
                lineStyle = new
                {
                    color = "red",
                    width = 2
                }
            }
        });
        // series.Add(new
        // {
        //     Name = "SCR",
        //     type = "line",
        //     data = seriesScr,
        //     yAxisIndex = 0,
        //     showSymbol = false,
        //     markLine = new
        //     {
        //         symbol = new string[] { "none" },
        //         label = new
        //         {
        //             show = true
        //         },
        //         lineStyle = new
        //         {
        //             color = "red",
        //             width = 2
        //         }
        //     }
        // });

        series.Add(new
        {
            Name = "SCL",
            type = "line",
            data = seriesScl,
            showSymbol = false,
            yAxisIndex = 0,
            markLine = new
            {
                symbol = new[] { "none" },
                label = new
                {
                    show = true
                },
                lineStyle = new
                {
                    color = "red",
                    width = 2
                }
            }
        });

        EDAChartOption = new
        {
            Animation = false,
            Title = new
            {
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            tooltip = new
            {
                trigger = "axis"
            },
            legend = new
            {
                type = "plain"
            },
            xAxis = new
            {
                Type = "time"
            },
            yAxis = new object[]
            {
                new
                {
                    type = "value"
                },
                new
                {
                    type = "value"
                }
            },
            series
        };
        var markdedData =record.Data.EdaSCRResults.Select(m =>
            {
               var isParsed = TimeOnly.TryParseExact(m.SCR_OnsetsTime, "HH:mm:ss.fff", CultureInfo.InvariantCulture, DateTimeStyles.None, out var time);
               var data = record.Data.StartTime;
               var date =data.Date;
               var dateTime = new DateTime(date.Year, date.Month, date.Day, time.Hour, time.Minute, time.Second, time.Millisecond);
               return new
               {
                   xAxis = dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff"), 
                   label = new
                   {
                       formatter = m.SCR_OnsetsTime,
                       position = "end",
                       padding = new[] { 0, 0, 0, 0 }
                   }
               };
            }
        ).ToList();
        

        SCRChartOption = new
        {
            Animation = false,
            Title = new
            {
                TextStyle = new
                {
                    FontSize = 12
                }
            },
            tooltip = new
            {
                trigger = "axis"
            },
            legend = new
            {
                type = "plain"
            },
            xAxis = new
            {
                Type = "time"
            },
            yAxis = new
            {
                type = "value"
            },
            series = new
            {
                Name = "SCR",
                type = "line",
                data = seriesScr,
                showSymbol = false,
                markLine = new
                {
                    symbol = new[] { "none" },
                    label = new
                    {
                        show = true
                    },
                    lineStyle = new
                    {
                        color = "red",
                        width = 2
                    },
                    data = markdedData
                }
            }
        };
    }
    private void OnEchartDataZoom((float startIndex, float endIndex) arg)
    {
        var allTimeTicks =_patientRecordModel.CollectEndTime-_patientRecordModel.CollectStartTime;
        var start = (allTimeTicks.TotalSeconds * arg.startIndex/100);
        var end = allTimeTicks.TotalSeconds * arg.endIndex/100;
        SegmentStartDate = _patientRecordModel.CollectStartTime.AddSeconds(start );
        SegmentEndDate =  _patientRecordModel.CollectStartTime.AddSeconds(end );
        InvokeAsync(StateHasChanged);
    }


    private async Task ConnectWebSocketAsync()
    {
        // 如果已经有连接或者正在连接，则不再重复
        if (ClientSocket != null && (ClientSocket.State == WebSocketState.Open || ClientSocket.State == WebSocketState.Connecting))
        {
            return;
        }
        ClientSocket = new ClientWebSocket();
        // 启动一个独立的任务来处理 WebSocket 连接和接收
        _ = Task.Run(async () =>
        {
            var currentUri = _stateService.NavigationManager.ToAbsoluteUri(_stateService.NavigationManager.Uri);
            var webSocketUrl = $"ws://{currentUri.Authority}/iot/hx_hrv_sci/v1?token={_stateService.Token}";
            var serverUri = new Uri(webSocketUrl);
            try
            {
                Console.WriteLine($"Attempting to connect to WebSocket: {serverUri}");
                await ClientSocket.ConnectAsync(serverUri, _cts.Token);
                await UpdateSegmentData();
                Console.WriteLine($"WebSocket connected: {ClientSocket.State}");
                await ReceiveWebSocketDataAsync();
            }
            catch (OperationCanceledException)
            {
                // Task 被取消，这是预期行为，例如组件被销毁
                Console.WriteLine("WebSocket connection attempt was cancelled.");
            }
            catch (WebSocketException wsEx)
            {
                Console.WriteLine($"WebSocket connection error: {wsEx.Message}. Code: {wsEx.WebSocketErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during WebSocket connection: {ex.Message}");
                await InvokeAsync(() =>
                    PopupService.EnqueueSnackbarAsync($"连接时发生未知错误: {ex.Message}", AlertTypes.Error));
            }
            finally
            {
                // 确保在任务结束时关闭和释放 WebSocket 资源
                if (ClientSocket != null && (ClientSocket.State == WebSocketState.Open || ClientSocket.State == WebSocketState.CloseSent))
                {
                    try
                    {
                        await ClientSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Connection closed by client", CancellationToken.None);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error closing WebSocket: {ex.Message}");
                    }
                }

                ClientSocket?.Dispose();
                ClientSocket = null;
                Console.WriteLine("WebSocket client disposed.");
            }
        }, _cts.Token); 
    }

    private async Task ReceiveWebSocketDataAsync()
    {
        var buffer = new byte[8192]; 

        while (ClientSocket != null && ClientSocket.State == WebSocketState.Open && !_cts.IsCancellationRequested)
        {
            try
            {
                WebSocketReceiveResult result;
                using var ms = new MemoryStream();
                do
                {
                    _cts.Token.ThrowIfCancellationRequested();
                    result = await ClientSocket.ReceiveAsync(new ArraySegment<byte>(buffer), _cts.Token);
                    ms.Write(buffer, 0, result.Count);
                } while (!result.EndOfMessage); // 循环接收直到完整消息

                if (result.MessageType == WebSocketMessageType.Close)
                {
                    Console.WriteLine($"WebSocket received close message: {result.CloseStatusDescription}");
                    await ClientSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Server requested close", _cts.Token);
                    break;
                }

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var realData = Encoding.UTF8.GetString(ms.ToArray());
                    await ProcessReceivedData(realData);
                }
                else if (result.MessageType == WebSocketMessageType.Binary)
                {
                    Console.WriteLine("Received binary data, skipping for now.");
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("WebSocket data reception was cancelled.");
                break; 
            }
            catch (WebSocketException wsEx)
            {
                await InvokeAsync(() =>
                    PopupService.EnqueueSnackbarAsync($"WebSocket 接收错误: {wsEx.Message}", AlertTypes.Error));
                break; 
            }
            catch (JsonException jsonEx)
            {
                await InvokeAsync(() =>
                    PopupService.EnqueueSnackbarAsync($"数据解析失败: {jsonEx.Message}", AlertTypes.Error));
            }
            catch (Exception ex)
            {
                await InvokeAsync(() =>
                    PopupService.EnqueueSnackbarAsync($"接收时发生未知错误: {ex.Message}", AlertTypes.Error));
                break;
            }
        }
    }

    private async Task ProcessReceivedData(string realData)
    {
        try
        {
            var segmentData = JsonNode.Parse(realData);
            if (segmentData == null) return; 
            var type = segmentData["Type"]?.GetValue<string>();
            var data = segmentData["data"]?.AsArray()?.GetValues<string>()?.ToList();
            var isEnd =segmentData["IsEnd"]?.GetValue<bool>()==true;
            if (segmentData.AsObject().ContainsKey("NoData"))
            {
                await PopupService.EnqueueSnackbarAsync($"所选时间段无{type}数据", AlertTypes.Warning);
            }
            else
            {
                if (type == "PPG")
                {
                    var reData = data?.Select(m => m.Split(",")).ToList();
                   await _echartSegmentComponent.UpdateChartData(reData, isEnd, multiplierindex: 0);
                }
                else if (type == "EDA")
                {
                    var reData = data?.Select(m => m.Split(",")).ToList();
                   await _echartSegmentComponent.UpdateChartData(reData, isEnd, multiplierindex: 1);
                }
            }
            if (isEnd)
            {
                PopupService.HideProgressCircular();
                StateHasChanged();
            }

            
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"JSON parsing error in ProcessReceivedData: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing received data: {ex.Message}");
            Console.WriteLine(ex.StackTrace); // 打印堆栈跟踪以便调试
        }
    }
    private readonly CancellationTokenSource _cts = new();

    private void OnAnalysisClick()
    {
        DeviceStateService.OnMessageReceived += HandlerReceiveDataFromSocket;
        if (SegmentEndDate - SegmentStartDate > TimeSpan.FromHours(1))
        {
            PopupService.EnqueueSnackbarAsync("分析时段不能超过1小时", AlertTypes.Warning);
            return;
        }
        isShowName = true;
    }

}
