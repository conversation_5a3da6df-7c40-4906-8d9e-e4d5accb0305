{"Logging": {"LogLevel": {"Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore.Authentication": "Warning"}}}, "AllowedHosts": "*", "Cors": {"Origins": "http://localhost:8080", "AllowCredentials": true}, "Permission": {"EnableDataPermission": false, "EnableFunctionPermission": true, "EnableVersionPermission": false}, "ConnectionStrings": {"DefaultDbContext": "User ID=postgres;Password=******************;Host=database;Port=5432;Database=HX.Experiment;Pooling=true;"}, "Redis": {"Enable": false, "ConnectionString": "127.0.0.1:6379,password=xBarvRgVJEEmBbe2"}, "Blazor": {"Enable": true, "Layout": "_HXExperimentLayout", "RenderMode": "Server"}, "DefaultUrl": {"Home": "/", "Login": "/Core/Auth/Login", "Register": "/"}, "MQTT": {"Port": 5168}, "Version": "v1.0.0", "AlgorithmProxy": {"Host": "hx_alg", "Port": 80}, "HrvVariable": {"SysName": "教学产品"}}