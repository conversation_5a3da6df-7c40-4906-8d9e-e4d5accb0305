﻿@using System.Globalization
@using HX.HRV.SCI.Shared.Models
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using HX.HRV.Shared.Pages.Client.Dialog
@using UFU.IoT.Shared.Models
@implements IDisposable
@inject StateService StateService
@inherits HRV.Shared.Pages.Client.Dialog.DeviceStatusDialog
<MRow Style="width: 100%;" Justify="JustifyTypes.SpaceBetween">
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">当前被试:</MLabel>
		<MLabel>@DeviceStatusData.RecordModel?.Patient?.Name</MLabel>
	</MCol>
	<MCol Cols="3">
		<MLabel Class="font-weight-bold">倒计时:</MLabel>
		<MLabel>@DeviceStatusData.CollectTime.ToString(@"mm\:ss")</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">连接方式:</MLabel>
		<MLabel>@DeviceStatusData.ConnectionType</MLabel>
	</MCol>
	<MCol Cols="2">
		<MLabel Class="font-weight-bold">状态:</MLabel>
		<MLabel>@DeviceStatusData.DeviceStatus</MLabel>
	</MCol>
	<MCol Cols="12">
		<MSwitch TValue="bool" @bind-Value="isOrg" Label="@($"PPG降噪: {isOrg}")">
		</MSwitch>
	</MCol>
</MRow>
<MRow Style="width: 100%;" Justify="JustifyTypes.SpaceBetween">
	<MCol Cols="12">
		<span style="display: inline-flex;">
			@if (_tags != null)
			{
				@foreach (var tag in _tags)
				{
					<MButton
						OnClick="@(e => Callback(@DeviceStatusData?.Device?.Id, @tag?.Data?.Name))"
						OnClickStopPropagation
						XSmall
						Class="ml-2"
						Color="#faad14">
						@tag?.Data?.Name
					</MButton>
				}
			}  
		</span>
	</MCol>
</MRow>
@if (DeviceStatusData.DeviceStatus == EnumDeviceStatus.检测中)
{
	<MRow Style="display: flex;justify-content: center;">

		<MCol Cols="12" Style="@(GetnoneStyle())">
			<DeviceStatusDialogChartComponent Take="500 * 5"
			                                  ChartTitle="PPG"
			                                  LineColor="#354de5"
			                                  DataKey="ORGPPG"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12" Style="@(GetVisibilityStyle())">
			<DeviceStatusDialogChartComponent Take="500 * 5"
			                                  ChartTitle="PPG"
			                                  LineColor="#9ac5fa"
			                                  DataKey="PPG"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12">
			<DeviceStatusDialogChartComponent Take="60"
			                                  ChartTitle="HR(bpm)"
			                                  LineColor="#aac57f"
			                                  DataKey="Bmp"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false" IsSmooth="true">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12">
			<DeviceStatusDialogChartComponent Take="60"
			                                  ChartTitle="SpO2(%)"
			                                  LineColor="#3896bb"
			                                  DataKey="SPO2"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12">
			<DeviceStatusDialogChartComponent Take="50 * 11"
			                                  ChartTitle="GSR(μS)"
			                                  LineColor="#9988d5"
			                                  DataKey="EDA"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		@* <MCol Cols="12"> *@
		@* 	<DeviceStatusDialogChartComponent Take="50 * 11" *@
		@* 	                                  ChartTitle="SCR(μS)" *@
		@* 	                                  LineColor="#e3ad90" *@
		@* 	                                  DataKey="SCR" *@
		@* 	                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false" *@
		@* 	                                  IsSmooth="false"> *@
		@* 	</DeviceStatusDialogChartComponent> *@
		@* </MCol> *@
		@* <MCol Cols="12"> *@
		@* 	<DeviceStatusDialogChartComponent Take="50 * 10" *@
		@* 	                                  ChartTitle="SCL(μS)" *@
		@* 	                                  LineColor="#73c5b7" *@
		@* 	                                  DataKey="SCL" *@
		@* 	                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false" *@
		@* 	                                  IsSmooth="false"> *@
		@* 	</DeviceStatusDialogChartComponent> *@
		@* *@
		@* </MCol> *@
		<MCol Cols="12">
			<DeviceStatusDialogChartComponent Take="50 * 10"
			                                  ChartTitle="G(m/s^2)"
			                                  DataKey="IMU" DeviceId="@DeviceStatusData.Device.Id"
			                                  LineColor="#d9f1e6"
			                                  DisplayNameAction="@(model =>
			                                                     {
				                                                     var lastData = model.ImuDatas.Select(m => m.LastOrDefault())?.ToArray();
				                                                     if (lastData?.Any() != true || lastData.Length < 3)
				                                                     {
					                                                     return 0d.ToString("F4");
				                                                     }

				                                                     var accx = lastData[0];
				                                                     var accy = lastData[1];
				                                                     var accz = lastData[2];
				                                                     var total = CalculateTotalAcceleration(accx, accy, accz);
				                                                     return total.ToString("F4");
			                                                     })" IsMultiple="true" IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
		<MCol Cols="12">
			<DeviceStatusDialogChartComponent Take="60" ChartTitle="AP(Pa)" LineColor="#e6dacb"
			                                  DataKey="@nameof(HXDataType.AP)"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
			<DeviceStatusDialogChartComponent Take="60" ChartTitle="LIGHT(LUX)" LineColor="#7aa386"
			                                  DataKey="@nameof(HXDataType.LIGHT)" DeviceId="@DeviceStatusData.Device.Id"
			                                  IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
			<DeviceStatusDialogChartComponent Take="60" ChartTitle="TEMPAIR(℃)" LineColor="#94bca6"
			                                  DataKey="@nameof(HXDataType.TEMPAIR)"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
			<DeviceStatusDialogChartComponent Take="60" ChartTitle="TEMPSKIN(℃)" LineColor="#e6dacb"
			                                  DataKey="@nameof(HXDataType.TEMPSKIN)"
			                                  DeviceId="@DeviceStatusData.Device.Id" IsMultiple="false"
			                                  IsSmooth="false">
			</DeviceStatusDialogChartComponent>
		</MCol>
	</MRow>
}

@code {
	private List<DataModel<EventTagModel>> _tags = new();

	public double CalculateTotalAcceleration(int adcX, int adcY, int adcZ)

	{
		const double scaleFactor = 0.000061 * 9.80665; // 合并系数

		double accelX = adcX * scaleFactor;

		double accelY = adcY * scaleFactor;

		double accelZ = adcZ * scaleFactor;

		return Math.Sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ);
	}


	private async Task InitEventAsync()
	{
		var url = $"/api/v2/SCI/EventTag/List";
		var result = await StateService.GetAsJsonAsync<List<DataModel<EventTagModel>>>(url);
		if (result is { Data: not null })
		{
			_tags = result.Data?.Take(5)?.ToList();
		}
	}

	private async Task Callback(string id, string mark)
	{
		await StateService.PostAsJsonAsync<bool>("api/v2/SCI/EventTag/Mark", null, new Dictionary<string, string>()
		{
			{ "deviceIds", id },
			{ "mark", mark }
		});
		await PopupService.EnqueueSnackbarAsync("打标成功", AlertTypes.Success, timeout: 300);
	}

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await InitEventAsync(); 
	}

}