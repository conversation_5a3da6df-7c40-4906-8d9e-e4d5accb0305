﻿using Microsoft.AspNetCore.Mvc;
using HX.Encrypt;
using HX.Encrypt.Models;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;

namespace HX.HRV.Web.Areas.HRV_HX.Controllers;
[Area("HRV_HX")]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[area]/[controller]/[action]")]
public class RSARegisterController: Controller
{
    public RSARegisterController()
    {
        
    }
    
    [HttpGet]
    [Permission("注册服务/获取设备ID", IsAPI = true, AllowAnonymous = true)]
    public string GetDeviceId()
    {
        return RsaRegister.GetDeviceId();
    }

    [HttpPost()]
    [Permission("注册服务/添加", IsAPI = true, AllowAnonymous = true)]
    public Task<Result<LicenseData>> UploadVerifyContent([FromBody] string content)
    {
        var res = new Result<LicenseData>();
        try
        {
            RsaRegister.WriteLicenseKey(content);
        }
        catch (Exception e)
        {
            res.Success = false;
            res.Message = e.Message;
            return Task.FromResult(res);
        }
        if (RsaRegister.VerifyAndParseLicenseKey(out var licenseData))
        {
            res.Data = licenseData;
            return Task.FromResult(res);;
        }
        else
        {
            res.Success = false;
            res.Message = "激活码验证失败！";
            return Task.FromResult(res);
        }
    }
    
    [HttpGet()]
    [Permission("注册服务/获取", IsAPI = true, AllowAnonymous = true)]
    public Result<LicenseData> GetRegisterData()
    {
        var res = new Result<LicenseData>();
        if (RsaRegister.VerifyAndParseLicenseKey(out var licenseData))
        {
            res.Data = licenseData;
            return res; 
        }
        res.Success = false;
        return res;
    }
    
    
    
}