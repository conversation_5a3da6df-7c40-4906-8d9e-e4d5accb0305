@page "/class-manager"
@using System.Net
@using System.Text.Json
@using HX.Experiment.Shared.Model
@using Masa.Blazor
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
@using UFU.CoreFX.Utils
@using UFU.IoT.Models

@inject StateService state
@inject IPopupService PopupService

<MCard Class="pa-6" Style="background:#f5f5f5;margin:auto;">
	<MRow Align="@AlignTypes.Center" Justify="@JustifyTypes.SpaceBetween" Class="mb-6">
		<MCol>
			<div class="d-flex align-center">
				<MIcon Size="36" Color="primary" Class="mr-3">mdi-school</MIcon>
				<div>
					<h2 class="font-weight-bold mb-0">班级管理</h2>
					<p class="text-caption grey--text mb-0">管理班级信息和学生数据</p>
				</div>
			</div>
		</MCol>
		<MCol Class="d-flex justify-end">
			<MButton
				Color="primary"
				Class="mr-3"
				OnClick="ShowCreateClassDialog"
				Elevation="2"
				Large>
				<MIcon Left>mdi-plus</MIcon>
				创建班级
			</MButton>
			<MButton
				Color="success"
				OnClick="ImportStudents"
				Elevation="2"
				Large
				Outlined>
				<MIcon Left>mdi-file-excel</MIcon>
				导入学生
			</MButton>
		</MCol>
	</MRow>

	<MRow>
		<MCol Xl="3" Sm="12">
			<MCard Class="elevation-4 rounded-lg">
				<MCardTitle Class="d-flex align-center pa-4 primary white--text">
					<MIcon Class="mr-2" Color="white">mdi-format-list-bulleted</MIcon>
					<span class="font-weight-medium">班级列表</span>
				</MCardTitle>

				<MCardText Class="pa-0">
					@if (classes.Any())
					{
						<MList Dense>
							@foreach (var classItem in classes)
							{
								<MListItem
									OnClick="async () => await SwicthClass(classItem)"
									Class="@(selectedClass?.Id == classItem.Id ? "primary lighten-5 primary--text" : "")"
									style="@($"border-left: 4px solid {(selectedClass?.Id == classItem.Id ? "var(--v-primary-base)" : "transparent")};")">

									<MListItemIcon>
										<MIcon Color="@(selectedClass?.Id == classItem.Id ? "primary" : "grey")">
											mdi-school
										</MIcon>
									</MListItemIcon>

									<MListItemContent>
										<MListItemTitle Class="font-weight-medium">
											@classItem.Data?.Name
										</MListItemTitle>
										<MListItemSubtitle>
											@if (!string.IsNullOrEmpty(classItem.Data?.Year))
											{
												<span>@(classItem.Data.Year)级</span>
											}
											@if (!string.IsNullOrEmpty(classItem.Data?.Major))
											{
												<span class="ml-2">@classItem.Data.Major</span>
											}
										</MListItemSubtitle>
									</MListItemContent>

									<MListItemAction>
										<MTooltip Bottom>
											<ActivatorContent>
												<MButton
													Icon
													Small
													Color="primary"
													OnClick="() => HandleEditClassClick(classItem)"
													OnClickStopPropagation>
													<MIcon>mdi-pencil</MIcon>
												</MButton>
											</ActivatorContent>
											<ChildContent>
												<span>编辑班级</span>
											</ChildContent>
										</MTooltip>

										<MTooltip Bottom>
											<ActivatorContent>
												<MButton
													Icon
													Small
													Color="error"
													Class="ml-2"
													OnClick="() => DeleteFormDataById(classItem.Id, _classFormId)"
													OnClickStopPropagation>
													<MIcon>mdi-delete</MIcon>
												</MButton>
											</ActivatorContent>
											<ChildContent>
												<span>删除班级</span>
											</ChildContent>
										</MTooltip>
									</MListItemAction>
								</MListItem>
							}
						</MList>
					}
					else
					{
						<div class="text-center pa-8">
							<MIcon Size="64" Color="grey lighten-2">mdi-school-outline</MIcon>
							<p class="text-h6 grey--text mt-4">暂无班级</p>
							<p class="text-caption grey--text">点击上方"创建班级"按钮开始添加</p>
						</div>
					}
				</MCardText>
			</MCard>
		</MCol>

		<MCol xs="8" Xl="9" Sm="12">
			@if (selectedClass != null)
			{
				<MCard Class="elevation-4 rounded-lg">
					<MCardTitle Class="d-flex align-center justify-space-between pa-4 success white--text">
						<div class="d-flex align-center">
							<MIcon Class="mr-2" Color="white">mdi-account-group</MIcon>
							<div>
								<span class="font-weight-medium">学生列表</span>
								<div class="text-caption mt-1">
									@selectedClass.Data?.Name
									@if (Students?.Any() == true)
									{
										<span class="ml-2">(@Students.Count 人)</span>
									}
								</div>
							</div>
						</div>
						<MButton
							Color="white"
							Class="success--text"
							OnClick="@HandleAddStudentOnClick"
							Elevation="0">
							<MIcon Left>mdi-plus</MIcon>
							添加学生
						</MButton>
					</MCardTitle>

					<MCardText Class="pa-0">
						@if (Students?.Any() == true)
						{
							<MDataTable
								Headers="_headers"
								Items="Students"
								Page="@_page"
								ItemsPerPage="@_itemsPerPage"
								OnOptionsUpdate="@HandleOnOptionsUpdate"
								ServerItemsLength="@_total"
								Class="elevation-0"
								HideDefaultHeader="false">
							</MDataTable>
						}
						else
						{
							<div class="text-center pa-12">
								<MIcon Size="64" Color="grey lighten-2">mdi-account-group-outline</MIcon>
								<p class="text-h6 grey--text mt-4">暂无学生</p>
								<p class="text-caption grey--text mb-4">该班级还没有学生信息</p>
								<MButton
									Color="success"
									OnClick="@HandleAddStudentOnClick"
									Class="mr-3">
									<MIcon Left>mdi-plus</MIcon>
									添加学生
								</MButton>
								<MButton
									Color="primary"
									OnClick="ImportStudents"
									Outlined>
									<MIcon Left>mdi-file-excel</MIcon>
									批量导入
								</MButton>
							</div>
						}
					</MCardText>
				</MCard>
			}
			else
			{
				<MCard Class="elevation-4 rounded-lg">
					<MCardText Class="pa-12 text-center">
						<MIcon Size="96" Color="grey lighten-1">mdi-school-outline</MIcon>
						<p class="text-h5 grey--text mt-6 mb-2">欢迎使用班级管理系统</p>
						<p class="text-body-1 grey--text mb-6">请从左侧选择一个班级查看学生信息，或创建新的班级开始管理</p>
						<MButton
							Color="primary"
							Large
							OnClick="ShowCreateClassDialog">
							<MIcon Left>mdi-plus</MIcon>
							创建第一个班级
						</MButton>
					</MCardText>
				</MCard>
			}
		</MCol>
	</MRow>
</MCard>

<!-- 创建班级对话框 -->
<MDialog @bind-Value="showEditClassDialog" MaxWidth="600" Persistent>
	<MCard Class="elevation-8">
		<MForm Model="EditClass" AutoLabel>
			<MCardTitle Class="d-flex align-center pa-6 primary white--text">
				<MIcon Class="mr-3" Color="white">mdi-school</MIcon>
				<span class="text-h5 font-weight-medium">
					@(string.IsNullOrEmpty(EditClass?.Id) ? "创建新班级" : "编辑班级信息")
				</span>
			</MCardTitle>

			<MCardText Class="pa-6">
				<MRow>
					<MCol Cols="12">
						<MAlert Type="AlertTypes.Info" Class="mb-4" Dense>
							<MIcon Left>mdi-information</MIcon>
							请填写班级的基本信息，带 * 号的为必填项
						</MAlert>
					</MCol>
				</MRow>

				<MRow>
					<MCol Cols="12">
						<MTextField
							Required
							@bind-Value="EditClass.Data.Name"
							Label="班级名称"
							Outlined
							Dense
							PrependInnerIcon="mdi-account-group"
							Placeholder="例如：计算机科学2024级1班"
							Rules="@(new List<Func<string, StringBoolean>> { value => !string.IsNullOrEmpty(value) ? true : "班级名称不能为空" })"
							Class="mb-3"/>
					</MCol>
				</MRow>

				<MRow>
					<MCol Cols="6">
						<MTextField
							@bind-Value="EditClass.Data.Year"
							Label="入学年份"
							Outlined
							Dense
							PrependInnerIcon="mdi-calendar"
							Placeholder="例如：2024"
							Type="number"
							Class="mb-3"/>
					</MCol>
					<MCol Cols="6">
						<MTextField
							@bind-Value="EditClass.Data.Major"
							Label="专业名称"
							Outlined
							Dense
							PrependInnerIcon="mdi-book-open-variant"
							Placeholder="例如：计算机科学与技术"
							Class="mb-3"/>
					</MCol>
				</MRow>

				<MRow>
					<MCol Cols="12">
						<MTextarea
							@bind-Value="EditClass.Data.Description"
							Label="描述/备注"
							Outlined
							Dense
							PrependInnerIcon="mdi-text"
							Placeholder="请输入班级的详细描述或备注信息..."
							Rows="3"
							Class="mb-3"/>
					</MCol>
				</MRow>
			</MCardText>

			<MDivider></MDivider>

			<MCardActions Class="pa-6">
				<MSpacer/>
				<MButton
					Color="grey"
					Text
					OnClick="(_) => showEditClassDialog = false"
					Class="mr-3">
					<MIcon Left>mdi-close</MIcon>
					取消
				</MButton>
				<MButton
					Type="submit"
					Color="primary"
					OnClick="HandleEditClass"
					Elevation="2">
					<MIcon Left>mdi-content-save</MIcon>
					@(string.IsNullOrEmpty(EditClass?.Id) ? "创建班级" : "保存修改")
				</MButton>
			</MCardActions>
		</MForm>
	</MCard>
</MDialog>



<!-- 编辑学生对话框 -->
<MDialog @bind-Value="showEditStudentDialog" MaxWidth="600" Persistent>
	<MCard Class="elevation-8">
		<MCardTitle Class="d-flex align-center pa-6 success white--text">
			<MIcon Class="mr-3" Color="white">mdi-account-plus</MIcon>
			<span class="text-h5 font-weight-medium">
				@(string.IsNullOrEmpty(editStudent?.Id) ? "添加新学生" : "编辑学生信息")
			</span>
		</MCardTitle>

		<MCardText Class="pa-6">
			<MRow>
				<MCol Cols="12">
					<MAlert Type="AlertTypes.Info" Class="mb-4" Dense>
						<MIcon Left>mdi-information</MIcon>
						请填写学生的基本信息，所有字段均为必填项
					</MAlert>
				</MCol>
			</MRow>

			@if (selectedClass != null)
			{
				<MRow>
					<MCol Cols="12">
						<MCard Class="mb-4" Outlined>
							<MCardText Class="py-3">
								<div class="d-flex align-center">
									<MIcon Class="mr-2" Color="primary">mdi-school</MIcon>
									<span class="font-weight-medium">所属班级：</span>
									<span class="ml-2 primary--text">@selectedClass.Data?.Name</span>
								</div>
							</MCardText>
						</MCard>
					</MCol>
				</MRow>
			}

			<MRow>
				<MCol Cols="12">
					<MTextField
						Required
						@bind-Value="editStudent.Data.Name"
						Label="学生姓名"
						Outlined
						Dense
						PrependInnerIcon="mdi-account"
						Placeholder="请输入学生的真实姓名"
						Rules="@(new List<Func<string, StringBoolean>> { value => !string.IsNullOrEmpty(value) ? true : "学生姓名不能为空" })"
						Class="mb-3"/>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12">
					<MTextField
						Required
						@bind-Value="editStudent.Data.CardId"
						Label="学号/身份证号"
						Outlined
						Dense
						PrependInnerIcon="mdi-card-account-details"
						Placeholder="请输入学号或身份证号"
						Rules="@(new List<Func<string, StringBoolean>> { value => !string.IsNullOrEmpty(value) ? true : "学号/身份证号不能为空" })"
						Class="mb-3"/>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12">
					<MTextField
						@bind-Value="editStudent.Data.UserId"
						Label="用户ID（可选）"
						Outlined
						Dense
						PrependInnerIcon="mdi-account-key"
						Placeholder="关联的用户账号ID"
						Hint="用于关联系统用户账号，可留空"
						PersistentHint
						Class="mb-3"/>
				</MCol>
			</MRow>
		</MCardText>

		<MDivider></MDivider>

		<MCardActions Class="pa-6">
			<MSpacer/>
			<MButton
				Color="grey"
				Text
				OnClick="() => showEditStudentDialog = false"
				Class="mr-3">
				<MIcon Left>mdi-close</MIcon>
				取消
			</MButton>
			<MButton
				Color="success"
				OnClick="@HandleEditStudent"
				Elevation="2">
				<MIcon Left>mdi-content-save</MIcon>
				@(string.IsNullOrEmpty(editStudent?.Id) ? "添加学生" : "保存修改")
			</MButton>
		</MCardActions>
	</MCard>
</MDialog>

<!-- Excel导入对话框 -->
<MDialog @bind-Value="showImportDialog" MaxWidth="800">
	<MCard>
		<MCardTitle Class="d-flex align-center">
			<MIcon Class="mr-2">mdi-file-excel</MIcon>
			Excel批量导入学生
		</MCardTitle>
		<MCardText>
			<MRow>
				<MCol Cols="12">
					<MAlert Type="AlertTypes.Info" Class="mb-4">
						<div>
							<strong>导入说明：</strong>
							<ul class="mt-2">
								<li>支持.xlsx和.xls格式的Excel文件</li>
								<li>文件大小不能超过10MB</li>
								<li>Excel文件第一行必须是标题行：姓名、学号/身份证号、用户ID</li>
								<li>姓名和学号/身份证号为必填项</li>
							</ul>
						</div>
					</MAlert>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12" Class="d-flex align-center mb-4">
					<MButton Color="secondary" OnClick="DownloadTemplate" Class="mr-4">
						<MIcon Left>mdi-download</MIcon>
						下载模板
					</MButton>
					<span class="text-caption">建议先下载模板文件，按照格式填写学生信息</span>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12">
					<InputFile
						accept=".xlsx,.xls"
						OnChange="OnFileSelected"
						class="form-control"
						style="margin-bottom: 16px;">
					</InputFile>
				</MCol>
			</MRow>

			@if (isValidating)
			{
				<MRow>
					<MCol Cols="12" Class="text-center">
						<MProgressCircular Indeterminate Color="primary" Class="mr-2"></MProgressCircular>
						正在验证文件格式...
					</MCol>
				</MRow>
			}

			@if (validationResult != null)
			{
				<MRow>
					<MCol Cols="12">
						@if (validationResult.IsValid)
						{
							<MAlert Type="AlertTypes.Success" Class="mb-4">
								文件格式验证通过！预览前10条数据：
							</MAlert>

							@if (validationResult.PreviewStudents.Any())
							{
								<MDataTable
									Headers="_previewHeaders"
									Items="validationResult.PreviewStudents"
									ItemsPerPage="10"
									HideDefaultFooter
									Class="elevation-1">
								</MDataTable>
							}
						}
						else
						{
							<MAlert Type="AlertTypes.Error" Class="mb-4">
								<div>
									<strong>文件格式验证失败：</strong>
									<ul class="mt-2">
										@foreach (var error in validationResult.ErrorMessages)
										{
											<li>@error</li>
										}
									</ul>
								</div>
							</MAlert>
						}
					</MCol>
				</MRow>
			}

			@if (importResult != null)
			{
				<MRow>
					<MCol Cols="12">
						@if (importResult.Success)
						{
							<MAlert Type="AlertTypes.Success" Class="mb-4">
								<div>
									<strong>导入完成！</strong>
									<p>成功导入：@importResult.SuccessCount 条记录</p>
									@if (importResult.FailureCount > 0)
									{
										<p>失败：@importResult.FailureCount 条记录</p>
									}
								</div>
							</MAlert>
						}
						else
						{
							<MAlert Type="AlertTypes.Error" Class="mb-4">
								<div>
									<strong>导入失败：</strong>
									<ul class="mt-2">
										@foreach (var error in importResult.ErrorMessages)
										{
											<li>@error</li>
										}
									</ul>
								</div>
							</MAlert>
						}

						@if (importResult.FailedStudents.Any())
						{
							<MExpansionPanels>
								<MExpansionPanel>
									<MExpansionPanelHeader>
										查看失败记录详情 (@importResult.FailedStudents.Count 条)
									</MExpansionPanelHeader>
									<MExpansionPanelContent>
										<MDataTable
											Headers="_failedHeaders"
											Items="importResult.FailedStudents"
											ItemsPerPage="10"
											Class="elevation-1">
										</MDataTable>
									</MExpansionPanelContent>
								</MExpansionPanel>
							</MExpansionPanels>
						}
					</MCol>
				</MRow>
			}
		</MCardText>
		<MCardActions>
			<MSpacer/>
			<MButton Color="grey" OnClick="() => showImportDialog = false">取消</MButton>
			<MButton
				Color="primary"
				OnClick="ImportFile"
				Disabled="@(selectedFile == null || validationResult?.IsValid != true || isImporting)"
				Loading="isImporting">
				@if (isImporting)
				{
					<span>导入中...</span>
				}
				else
				{
					<span>开始导入</span>
				}
			</MButton>
		</MCardActions>
	</MCard>
</MDialog>



@code {

	private List<DataTableHeader<DataModel<StudentInfo>>> _headers => new()
	{
		new()
		{
			Text = "序号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel => studentDataModel.Id
		},
		new()
		{
			Text = "学生姓名",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel => studentDataModel?.Data.Name
		},
		new()
		{
			Text = "学号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel => studentDataModel.Data?.CardId
		},
		new()
		{
			Text = "操作",
			Align = DataTableHeaderAlign.Center,
			Sortable = false,
			Width = "120",
			CellRender = studentDataModel =>
			{
				RenderFragment renderFragment = @<div class="d-flex justify-center">
					                                <MTooltip Bottom>
						                                <ActivatorContent>
							                                <MButton
								                                Icon
								                                Small
								                                Color="primary"
								                                Class="mr-2"
								                                OnClick="() => HandleEditStudentOnClick(studentDataModel)">
								                                <MIcon>mdi-pencil</MIcon>
							                                </MButton>
						                                </ActivatorContent>
						                                <ChildContent>
							                                <span>编辑学生</span>
						                                </ChildContent>
					                                </MTooltip>

					                                <MTooltip Bottom>
						                                <ActivatorContent>
							                                <MButton
								                                Icon
								                                Small
								                                Color="error"
								                                OnClick="() => DeleteFormDataById(studentDataModel.Id, _studentFormId)">
								                                <MIcon>mdi-delete</MIcon>
							                                </MButton>
						                                </ActivatorContent>
						                                <ChildContent>
							                                <span>删除学生</span>
						                                </ChildContent>
					                                </MTooltip>
				                                </div>
					;
				return renderFragment;
			}
		},
	};

	private List<DataTableHeader<StudentPreviewInfo>> _previewHeaders => new()
	{
		new()
		{
			Text = "行号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.RowNumber.ToString()
		},
		new()
		{
			Text = "姓名",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.Name
		},
		new()
		{
			Text = "学号/身份证号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.CardId
		},
		new()
		{
			Text = "用户ID",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.UserId
		}
	};

	private List<DataTableHeader<FailedStudentInfo>> _failedHeaders => new()
	{
		new()
		{
			Text = "行号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.RowNumber.ToString()
		},
		new()
		{
			Text = "姓名",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.Name
		},
		new()
		{
			Text = "学号/身份证号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.CardId
		},
		new()
		{
			Text = "错误原因",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.ErrorReason
		}
	};

}

<style>
	.rounded-lg {
		border-radius: 12px !important;
	}

	.elevation-4 {
		box-shadow: 0 4px 8px rgba(0,0,0,0.12), 0 2px 4px rgba(0,0,0,0.08) !important;
	}

	.elevation-8 {
		box-shadow: 0 8px 16px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1) !important;
	}

	.v-list-item:hover {
		background-color: rgba(0,0,0,0.04) !important;
	}

	.v-list-item.primary.lighten-5 {
		background-color: rgba(25, 118, 210, 0.08) !important;
	}

	.v-data-table > .v-data-table__wrapper > table > thead > tr > th {
		background-color: #f8f9fa !important;
		font-weight: 600 !important;
		color: #495057 !important;
	}

	.v-data-table > .v-data-table__wrapper > table > tbody > tr:hover {
		background-color: rgba(0,0,0,0.02) !important;
	}

	.v-card--outlined {
		border: 1px solid rgba(0,0,0,0.12) !important;
	}

	.primary.white--text .v-card__title {
		background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
	}

	.success.white--text .v-card__title {
		background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
	}
</style>
