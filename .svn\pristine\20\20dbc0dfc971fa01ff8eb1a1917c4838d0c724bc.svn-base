﻿@using System.Text.Json.Nodes
@using HX.HRV.Shared.Models.ViewModel
@using HX.HRV.Shared.Services
@using Microsoft.AspNetCore.Mvc
@using Microsoft.JSInterop
@using UFU.IoT.Shared.Models
@implements IDisposable

<div style="height: 200px; margin-top:10px;border-radius: 10px;background: #fff;"
class="d-flex align-center justify-space-between">
	<div style="height: 100%;width: 200px; background-color:@(LineColor);border-radius:  10px 0 0 10px "
	class="d-flex flex-column align-center justify-center ">
		<div class="text-center">@ChartTitle</div>
		<div class="text-center">@GetShowName()</div>
	</div> 
	<div id="@ChartId" style="width: 900px; height:  200px;"></div>
</div>

@code {
	private string ChartId { get; set; } = Guid.NewGuid().ToString(); // 图表唯一 ID
	[Parameter] public string ChartTitle { get; set; } // 图表标题
	[Parameter] public string LineColor { get; set; } // 折线颜色
	[Inject] private IJSRuntime Js { get; set; }
	[Parameter] public int Take { get; set; } = 500; 
	[Parameter] public string DeviceId { get; set; }
	private DeviceStatusViewModel DeviceStatusData => DeviceStateService.GetDeviceStatusViewModel(DeviceId);
	[Inject] private DeviceStateService DeviceStateService { get; set; }
	[Parameter] public bool IsSmooth { get; set; }
	[Parameter] public Func<DeviceStatusViewModel, string> DisplayNameAction { get; set; } = null;
	private bool _isInitialized;
	public string ShowValue { get; set; }

	private string GetShowName()
	{
		if (DisplayNameAction != null)
		{
			return
				DisplayNameAction(DeviceStatusData);
		}
		else
		{
			return
				ShowValue;
		}
	}

	[Parameter] public string DataKey { get; set; }

	[Parameter] public bool IsMultiple { get; set; }

	protected override void OnAfterRender(bool firstRender)
	{
		base.OnAfterRender(firstRender);
		if (!firstRender) return;
		Js.InvokeVoidAsync("RealTimeEchart.InitChart", new List<short>[] { new(), new(), new(), }, ChartId, Take, false
			, IsMultiple, LineColor);
		_isInitialized = true;
	}

	private void OnReceiveData(JsonNode jsonNode)
	{
		Task.Run(async () =>
		{
			try
			{
				if (!_isInitialized) return;
				if (jsonNode.AsObject().ContainsKey(DataKey))
				{
					if (jsonNode["IMU"] is JsonArray imu)
					{
						var imuDatas = new List<List<short>>();
						foreach (JsonArray imuJsonValue in imu)
						{
							var imuData = imuJsonValue.GetValues<short>();
							imuDatas.Add(imuData.ToList());
						}
						 Js.InvokeVoidAsync("RealTimeEchart.UpdateChartData",
							ChartId, imuDatas);
					}
					
					else
					{
						if (jsonNode.AsObject().ContainsKey(DataKey) )
						{
							
							
							float showValue=0f;
							if(jsonNode[DataKey] is JsonArray jsonArray)
							{
								var tempData = jsonArray.GetValues<float>()?.ToList();
								showValue=tempData?.LastOrDefault()??0;
								Console.WriteLine($"KEY:{DataKey},Value:{ShowValue}");
								// var part1 = tempData.Take(tempData.Count / 2).ToList();
								//  Js.InvokeVoidAsync("RealTimeEchart.UpdateChartData",
								// 	ChartId, part1);
								//
								//  var part2 = tempData.Skip(tempData.Count / 2).ToList();
								// 
								 Js.InvokeVoidAsync("RealTimeEchart.UpdateChartData",
									ChartId, tempData);
							}
							else if(jsonNode[DataKey] is JsonValue jsonValue)
							{
								var tempData = jsonValue.GetValue<float>();
								showValue=tempData;
								 Js.InvokeVoidAsync("RealTimeEchart.UpdateChartData",
									ChartId, tempData);
							}

							if (new[] { "HR", "EDA" }.Contains(DataKey))
							{
								ShowValue = showValue.ToString("F0");
								InvokeAsync(StateHasChanged);
							}
							else
							{
								ShowValue = showValue.ToString("F2");
								InvokeAsync(StateHasChanged);
							}
						}
						
					}
				}
			}
			catch (Exception e)
			{
				Console.WriteLine(e.Message);
			}
		});
	}

	protected override void OnInitialized()
	{
		DeviceStatusData.OnReceiveData += OnReceiveData;
		base.OnInitialized();
	}

	public void Dispose()
	{
		Js.InvokeVoidAsync("RealTimeEchart.DisposeChart", ChartId);
		if (DeviceStatusData != null)
		{
			DeviceStatusData.OnReceiveData -= OnReceiveData;
		}
	}

}