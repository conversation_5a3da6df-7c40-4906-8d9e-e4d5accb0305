﻿@page "/client/sci/patient-record/list"
@using HX.HRV.Shared.Models
@using HX.HRV.Shared.Pages.Client.Dialog
@using HX.HRV.Shared.Services
@using Masa.Blazor
@using UFU.CoreFX.Shared.Models
@using UFU.IoT.Shared.Utils
@using Masa.Blazor.Presets
@using Microsoft.JSInterop
@using UFU.CoreFX.Models
@using UFU.CoreFX.Permission
@using UFU.CoreFX.Shared.Services
@attribute [Permission(MenuName = "数据管理", Name = "多参数生理及行为研究平台/数据管理", Icon = "shuju", MenuOrder = "4", IsMenu = true)]
<style>
	.customer-input .m-input__prepend-outer .m-label {
		width: 120px;
	}
	.m-data-table__wrapper{
		height: 90%;
		overflow: auto;
	}
</style>

<iframe id="hiddeniframe" style="position: absolute; top: -9999px;height:1000px;width:1000px"></iframe>
<div style="height: 100%">
	<MForm Class="pa-2" Style="height: 15%;width: 98%;">
		<MRow Class="search-row">
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MTextField Clearable Class="customer-input" @bind-Value="_queryPatientModel.CardId"
				            Solo
				            Dense>
					<PrependContent>
						<MLabel Class="mr-2">身份证号:</MLabel>
					</PrependContent>
				</MTextField>
			</MCol>
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MTextField Clearable Solo
				            Dense Class="customer-input" @bind-Value="_queryPatientModel.OutpatientNumberString">
					<PrependContent>
						<MLabel Class="mr-2">被试编号:</MLabel>
					</PrependContent>
				</MTextField>
			</MCol>
			<MCol Cols="3" Class="d-flex align-center justify-center">
				<MMenu @bind-Value="_isShowDatePicker"
				       CloseOnContentClick="false"
				       Transition="scale-transition"
				       OffsetY
				       Context="DatePickerContext"
				       MinWidth="@("auto")">
					<ActivatorContent>
						<MTextField Value="FormatDate()"
						            Solo
						            Dense Class="customer-input"
						            Readonly
						            @attributes="DatePickerContext.Attrs">
							<PrependContent>
								<MLabel Class="mr-2">检测时间:</MLabel>
							</PrependContent>
						</MTextField>

					</ActivatorContent>
					<ChildContent>
						<MDatePicker @bind-Value="_dates"
						             NoTitle
						             Range
						             Scrollable>
							<MSpacer></MSpacer>
							<MButton Text
							         Color="primary"
							         OnClick="() => { _isShowDatePicker = false; _dates = new List<DateOnly>(); }">
								取消
							</MButton>
							<MButton Text
							         Color="primary"
							         OnClick="() => { _isShowDatePicker = false; }">
								确认
							</MButton>
						</MDatePicker>
					</ChildContent>
				</MMenu>
			</MCol>

		</MRow>
		<MRow Class="ma-2 d-flex align-start justify-end">
			<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;"
			         Class="customer-button blue ml-4" OnClick="InitDataList">
				搜索
			</MButton>
			@if (_hasExportPermission)
			{
				<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;" Class="amber ml-4" Rounded
				         OnClick="() => AddExportRecordAsync()">
					批量导出
				</MButton>
				<MButton Style="height: 3rem;max-width: 9rem;width: 8rem;font-size: x-large;" Class="amber ml-4" Rounded
				         OnClick="ShowTaskDetail">
					导出任务
				</MButton>
			}

		</MRow>
	</MForm>
	<MDataTable Style="height: 84%;" OnOptionsUpdate="@HandleOnOptionsUpdate"
	            TItem="PatientRecordModel"
	            Headers="_headers"
	            SingleSelect="false"
	            ShowSelect
	            ItemKey="r => r.Id"
	            Items="PatientRecordModels"
	            Stripe
	            @bind-Selected="_selected"
	            DisableSort="true"
	            ItemsPerPage="_options.ItemsPerPage"
	            Loading="_loading"
	            FooterProps="@(props =>
	                         {
		                         props.ShowFirstLastPage = false;
		                         props.ShowCurrentPage = true;
		                         props.PageText = @"共{2}条数据";
	                         })"
	            ServerItemsLength="@_total">
		<HeaderColContent Context="header">
			<MLabel Style="font-weight: 500;
font-size: 1.05rem;
color: #28333E;">@header.Text</MLabel>
		</HeaderColContent>
		<ItemColContent Context="item">
			@if ( item.Header.Value == "actions")
			{
				<div class="d-flex align-center justify-center">
					@if (_hasDeletePatientRecordPermission&&item.Item.BuildReportStatus == BuildReportStatus.Completed)
					{
						<MButton Style="font-size: 1.25rem;padding: 0" OnClick="() => PrintReport(item.Item.Id)"
						         Color="blue" Class="text-decoration-underline" Plain>
							查看报告
						</MButton>
					}
					<MButton Style="font-size: 1.25rem;padding: 0"
					         OnClick="() =>NavigateToDataPlayback(item.Item.Id)" Color="blue"
					         Class="text-decoration-underline" Plain>
						回放
					</MButton>
					<MButton Style="font-size: 1.25rem;padding: 0"
					         OnClick=" async () =>await NavigateToDataAnalysis(item.Item.Id)" Color="blue"
					         Class="text-decoration-underline" Plain>
						分析
					</MButton>
					<MButton Style="font-size: 1.25rem;padding: 0"
					         OnClick="() => { DetailDialogData = item.Item; ShowRecordDetail(); }" Color="blue"
					         Class="text-decoration-underline" Plain>
						详情
					</MButton>

					@if (_hasDeletePatientRecordPermission)
					{
						<MButton Style="font-size: 1.25rem;padding: 0"
						         OnClick="async () => { await AddExportRecordAsync(item.Item.Id); }" Color="blue"
						         Class="text-decoration-underline" Plain>
							导出
						</MButton>
						<MButton Style="font-size: 1.25rem;padding: 0"
						         OnClick="async () => await DeleteConfirmAsync(item.Item.Id)" Color="red"
						         Class="text-decoration-underline" Plain>
							删除
						</MButton>
					}
				</div>
			}
			else
			{
				<div style="display: flex;align-items: center;justify-content: center;min-width: 2rem;">
					<MLabel Style="font-size: 1rem;"> @item.Value</MLabel>
				</div>
			}
		</ItemColContent>
	</MDataTable>
</div>


@*导出任务列表*@
@if (_isShowTaskDialog)
{
	<PModal Width="942"
	        Persistent="false"
	        @bind-Value="_isShowTaskDialog">
		<TitleContent>
			<div style="flex-grow: 1;text-align: right">导出任务</div>
		</TitleContent>
		<ChildContent>
			<ExportRecordDialog @ref="exportRecordDialog" StartTaskId="@StartTaskId" CloseDialog="CloseTaskDetail">
			</ExportRecordDialog>
		</ChildContent>
	</PModal>
}
@*检测记录详情*@
<PModal Width="700"
        @bind-Value="_isShowDetailDialog">
	<TitleContent>
		<div style="flex-grow: 1;text-align: right">@DetailDialogData.Patient?.Name</div>
	</TitleContent>
	<ChildContent>
		<MRow>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">检测开始时间:</MLabel>
				<MLabel>@DetailDialogData.CollectStartTime</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">检测结束时间:</MLabel>
				<MLabel>@DetailDialogData.CollectEndTime</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">被试编号:</MLabel>
				<MLabel>@DetailDialogData.Patient?.OutpatientNumberString</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">身份证号:</MLabel>
				<MLabel>@DetailDialogData.Patient?.CardId</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">年龄:</MLabel>
				<MLabel>@DetailDialogData.Patient?.Age 岁</MLabel>
			</MCol>
			@*体重*@
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">体重:</MLabel>
				<MLabel>@DetailDialogData.Patient?.Weight KG</MLabel>
			</MCol>

			<MCol Cols="6">
				<MLabel Class="font-weight-bold">性别:</MLabel>
				<MLabel>@DetailDialogData.Patient?.Sex.ToString()</MLabel>
			</MCol>
			@*终端*@
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">终端:</MLabel>
				<MLabel>@DetailDialogData.DeviceId</MLabel>
			</MCol>
			@*报告编号*@
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">报告编号:</MLabel>
				<MLabel>@DetailDialogData.RecordCode</MLabel>
			</MCol>
			<MCol Cols="6">
				<MLabel Class="font-weight-bold">被试来源:</MLabel>
				<MLabel>@DetailDialogData.Patient?.Source</MLabel>
			</MCol>
		</MRow>
	</ChildContent>
</PModal>

@code {
	[Inject] private IJSRuntime js { get; set; }
	private bool _isShowTaskDialog;
	private bool _isShowDetailDialog;
	private int _total;
	private bool isRender = false;

	private List<DateOnly> _dates = new()
	{
	};


	private IEnumerable<string> _selected = new List<string>();

	private string FormatDate()
	{
		if (_dates.Count >= 2)
		{
			return _dates[0].ToString("yyyy-MM-dd") + "至" + _dates[1].ToString("yyyy-MM-dd");
		}

		return string.Empty;
	}


	private bool _loading = false;
	private bool isShowAddPatient = false;
	private PatientRecordModel DetailDialogData { get; set; } = new();

	[CascadingParameter(Name = "PopupService")]
	IPopupService PopupService { get; set; }

	private Sex[] Sexes { get; } = new[]
	{
		Sex.男,
		Sex.女
	};

	private DataOptions _options = new(1, 10);
	private bool _isShowDatePicker;
	private ExportRecordDialog exportRecordDialog;
	private List<PatientRecordModel> PatientRecordModels { get; set; } = new();

	private List<DataTableHeader<PatientRecordModel>> _headers => new()
	{
		new()
		{
			Text = "检测时间",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.CollectStartTime), CellRender = (e) => e.CollectStartTime.ToString("yyyy-MM-dd HH:mm:ss")
		},
		new()
		{
			Text = "结束时间",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.CollectEndTime), CellRender = (e) => e.CollectEndTime.ToString("yyyy-MM-dd HH:mm:ss")
		},
		new()
		{
			Text = "姓名", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.Name),
			CellRender = (e) => e.Patient?.Name
		},
		new()
		{
			Text = "被试编号",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.OutpatientNumberString),
			CellRender = (e) => e.Patient?.OutpatientNumberString
		},
		new()
		{
			Text = "身份证号", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.CardId),
			CellRender = (e) => e.Patient?.CardId
		},
		new()
		{
			Text = "年龄",
			Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.Age),
			CellRender = (e) => e.Patient?.Age?.ToString() ?? ""
		},
		new()
		{
			Text = "性别", Align = DataTableHeaderAlign.Center,
			CellRender = (e) => e.Patient?.Sex?.ToString()
		},
		new()
		{
			Text = "来源", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.Patient.Source),
			CellRender = (e) => e.Patient?.Source
		},
		new()
		{
			Text = "报告编号", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.RecordCode),
			CellRender = (e) => e.RecordCode
		},
		new()
		{
			Text = "报告状态", Align = DataTableHeaderAlign.Center,
			Value = nameof(PatientRecordModel.BuildReportStatusName),
			CellRender = (e) => e.BuildReportStatus.GetDisplayName()
		},
		new()
		{
			Text = "操作",
			Value = "actions",
			Sortable = false,
			Align = DataTableHeaderAlign.Center,
		}
	};


	private async Task HandleOnOptionsUpdate(DataOptions options)
	{
		_options = options;
		await InitDataList();
	}

	private PatientModel _queryPatientModel { get; set; } = new();
	private PatientRecordModel _queryPatientRecordModel { get; set; } = new();
	[Inject] InternalHttpClientService _internalHttpClientService { get; set; }
	[Inject] StateService _stateService { get; set; }
	private string StartTaskId { get; set; }

	/// <summary>
	/// 路由数据
	/// </summary>
	[CascadingParameter]
	public AuthRouteData AuthRouteData { get; set; }

	private bool _hasDeletePatientRecordPermission { get; set; }
	private bool _hasExportPermission { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await InitDataList();
		_hasDeletePatientRecordPermission = AuthRouteData.CurrentModule
			?.Functions.Any(m => m.Name == "心率变异性数据管理接口/删除" || m.Name == "心率变异性/数据管理") ?? false;
		_hasExportPermission = AuthRouteData.CurrentModule
			?.Functions.Any(m => m.Name == "心率变异性导出任务接口/列表") ?? false;
		await base.OnInitializedAsync();
	}


	protected override void OnAfterRender(bool firstRender)
	{
		if (!firstRender)
		{
			isRender = true;
		}

		base.OnAfterRender(firstRender);
	}

	/// <summary>
	/// 初始化列表数据
	/// </summary>
	/// <returns></returns>
	private async Task InitDataList()
	{
		_loading = true;
		var queryParam = new Dictionary<string, string>
		{
			{ nameof(PatientModel.Age), _queryPatientModel.ToString() },
			{ nameof(PatientRecordModel.DeviceId), _queryPatientRecordModel.DeviceId },
			{ nameof(PatientModel.CardId), _queryPatientModel.CardId },
			{ nameof(PatientModel.Source), _queryPatientModel.Source },
			{ nameof(PatientModel.OutpatientNumberString), _queryPatientModel.OutpatientNumberString },
			{ nameof(PatientModel.Sex), _queryPatientModel.Sex.ToString() },
			{ "page", _options.Page.ToString() },
			{nameof(PatientRecordModel.CollectStartTime), _dates.Count > 0 ? _dates[0].ToString("yyyy-MM-dd") : ""},
			{nameof(PatientRecordModel.CollectEndTime), _dates.Count > 1 ? _dates[1].ToString("yyyy-MM-dd") : ""},
			{ "pageSize", _options.ItemsPerPage.ToString() }
		};
		var result = await _internalHttpClientService.GetPatientRecordModelListByParams(queryParam);
		PatientRecordModels = result?.Data?.Select(m => m.Data)?.ToList();
		_total = result.Page?.TotalCount ?? 0;
		_loading = false;
	}

	/// <summary>
	/// 删除
	/// </summary>
	private async Task DeleteConfirmAsync(string id)
	{
		var confirmed = await PopupService.ConfirmAsync(
			"警告",
			"将永久删除，无法找回，确认删除??",
			AlertTypes.Warning);
		if (confirmed)
		{
			var deleteResult = await _internalHttpClientService.DeletePatientRecordAsync(id);
			await PopupService.EnqueueSnackbarAsync(deleteResult ? "删除成功！" : "删除失败！", AlertTypes.Success);
			await InitDataList();
		}
	}

	private void ShowTaskDetail()
	{
		StartTaskId = string.Empty;
		_isShowTaskDialog = true;
	}

	private void CloseTaskDetail()
	{
		_isShowTaskDialog = false;
	}

	private void ShowRecordDetail()
	{
		_isShowDetailDialog = true;
	}

	private void CloseRecordDetail()
	{
		_isShowDetailDialog = false;
	}

	/// <summary>
	///  添加导出记录
	/// </summary>
	/// <returns></returns>
	private async Task AddExportRecordAsync(string id = null)
	{
		var selected = !string.IsNullOrEmpty(id) ? new List<string> { id } : _selected.ToList();

		if (!selected.Any())
		{
			await PopupService.EnqueueSnackbarAsync("请选择需要导出的记录", AlertTypes.Warning);
			return;
		}

		var export = new HxExportTaskModel()
		{
			ExportCount = selected?.Count() ?? 0,
			ExportStatus = EnumExportTaskStatus.UnStart,
			Progress = 0,
			PatientRecordIds = selected?.ToList(),
		};
		//新增
		var result = await _stateService
			.PostAsJsonAsync<DataModel<HxExportTaskModel>>("/api/v2.0/HRV_HX/HxExportTask/Add",
				export);
		if (result.Success)
		{
			PopupService.ShowProgressCircular();
			// StartTaskId= export.Id;
			await Task.Run(async () =>
			{
				ShowTaskDetail();
				await PopupService.EnqueueSnackbarAsync("添加导出任务成功", AlertTypes.Success, closeable: true, timeout: 500);
			});
			PopupService.HideProgressCircular();
		}
		else
		{
			await PopupService.EnqueueSnackbarAsync($"添加导出任务失败,{result.Message}", AlertTypes.Error);
		}
	}

	private void PrintReport(string id)
	{
		_stateService.NavigationManager.NavigateTo($"/client/report/report-detail/{id}");
	}


	/// <summary>
	///  数据分析
	/// </summary>
	/// <param name="id"></param>
	private async Task NavigateToDataAnalysis(string id)
	{

		_loading = true;
		var queryParam = new Dictionary<string, string>
		{
			{ "recordCodeOrId", _queryPatientRecordModel.RecordCode },
			{ "pageSize", "1"}
		};
		var result = await _stateService
			.GetAsJsonAsync<List<DataModel<PatientRecordModel>>>(
				"/api/v2.0/SCI/DataAnalysisRecord/List", queryParam);
		if (result.Success && result.Data?.Count > 0)
		{
			_stateService.NavigationManager.NavigateTo($"/Client/SCI/DataAnalysis/list?recordId={id}");
		}
		else
		{
			_stateService.NavigationManager.NavigateTo($"/Client/SCI/DataAnalysis/{id}");
		}

	}

	/// <summary>
	/// 数据回放
	/// </summary>
	/// <param name="id"></param>
	private void NavigateToDataPlayback(string id)
	{
		_stateService.NavigationManager.NavigateTo($"/Client/SCI/DataPlayback/{id}");
	}


}