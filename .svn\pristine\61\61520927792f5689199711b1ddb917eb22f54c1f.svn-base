﻿@page "/client/account/list"
@using System.ComponentModel.DataAnnotations
@using HX.HRV.Shared.Services
@using Microsoft.Extensions.Configuration

@attribute [Permission(MenuName = "账号管理",Name = "心率变异性/账号管理",Icon = "zhang<PERSON>",MenuOrder = "6",IsMenu = true)]

<style>
    .customer-input{
        height:40px;
        border-radius: 19px;
        border: none;
        text-indent: 12px
    }
    .customer-input .m-input__slot{
        height: 32px;
    }

    .customer-input  .m-input__prepend-outer .m-label{
        width: 80px;
        text-align: end;
    }
    .search-row .m-label m-label{
        width: 100px;
        text-align: end;
    }
    .customer-button {
        background: linear-gradient(0deg, #008ef7, #1e9fff);
        border-radius: 28px;
        color: white;
        margin-left: 4px;
     }
    .customer-button .m-btn__content{
        color: white;
    }

    input:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px white inset !important;
        -webkit-text-fill-color: #000 !important;
    }
    .m-dialog--active{
        border-radius: 29px;
    }
    .customer-input .m-input__prepend-outer .m-label {
        font-size: 1.25rem;
    }
</style>

<MForm Class="ma-8">
    <MRow Class="search-row">
        <MCol Cols="3" Class="d-flex align-center justify-center">
            <MLabel Class="mr-2">关键词:</MLabel>
            <MTextField Clearable Class="customer-input" @bind-Value="Keywords"
                        Solo
                        Dense>
            </MTextField>
        </MCol>
        <MCol Cols="2" Class="d-flex align-start justify-start">
            <MButton Class="customer-button"  Style="    height: 3rem;
    max-width: 9rem;
    width: 8rem;
    font-size: x-large;
" OnClick="InitDataList">搜索</MButton>
            <MButton Class="customer-button" Style="    height: 3rem;
    max-width: 9rem;
    width: 8rem;
    font-size: x-large;
" OnClick="AddAccountCallback">添加账号</MButton>
        </MCol>
    </MRow>
</MForm>
<MCard Class="ma-8">
    <MDataTable Stripe
                OnOptionsUpdate="@HandleOnOptionsUpdate"
                TItem="UserOrganModel"
                Loading="_isLoading"
                Headers="_headers"
                Items="UserModelList"
                ServerItemsLength="_total"
                ItemsPerPage="_options.ItemsPerPage"
                FooterProps="@(props => { props.ShowFirstLastPage = false; props.ShowCurrentPage = false; props.PageText = @"共{2}条数据"; props.DisableItemsPerPage = true; })">
        <HeaderColContent Context="header">
            <MLabel Style="font-weight: 400;
font-size: 1.25rem;
color: #28333E;">@header.Text</MLabel>
        </HeaderColContent>
        <ItemColContent Context="itemContext">
            @if (itemContext.Header.Value == "actions")
            {
                <div class="d-flex align-center justify-center">
                    <MButton Style="font-size: 1.25rem" Color="blue" Class="text-decoration-underline" OnClick="() => EditAccountCallback(itemContext.Item)" Plain>
                        编辑账号
                    </MButton>
                    <MButton Style="font-size: 1.25rem" OnClick="async () => await ResetPasswordAsync(itemContext.Item.UserId)" Color="blue" Class="text-decoration-underline" Plain>
                        重置密码
                    </MButton>
                    <MButton Style="font-size: 1.25rem" OnClick="async () => await DeleteConfirmAsync(itemContext.Item.UserId)" Color="blue" Class="text-decoration-underline" Plain>
                        删除
                    </MButton>
                </div>
            }
            else
            {
                <MLabel Style="font-size: 1.25rem"> @itemContext.Value</MLabel>
            }
        </ItemColContent>
    </MDataTable>
</MCard>
<PModal
Width="500"
Title="@DialogTitle"
OnCancel="CloseAddUserDialog"
@bind-Value="isShowAddUser">
    <ChildContent >
        <MForm Model="DialogData" OnValidSubmit="HandleOnValidSubmit" EnableI18n>
            <MRow Class="mt-4">
                <MCol Cols="12">
                    <MTextField Disabled="@(!string.IsNullOrEmpty(DialogData.Id))" Clearable Class="customer-input" Dense="true" Solo="true" TValue="string" @bind-Value="DialogData.Name">
                        <PrependContent >
                            <MLabel >账号:</MLabel>
                        </PrependContent>
                    </MTextField>
                </MCol>
                <MCol Cols="12">
                    <MTextField Clearable Class="customer-input" Dense="true" Solo="true" TValue="string" @bind-Value="DialogData.RealName">
                        <PrependContent >
                            <MLabel >姓名:</MLabel>
                        </PrependContent>
                    </MTextField>
                </MCol>
                <MCol Cols="12" Class="d-flex  justify-start align-center">
                    <MLabel Style="width: 80px;text-align: end;font-size: 1.25rem;" Class="mr-2">角色:</MLabel>
                    <MButtonGroup
                    Class="customer-input"
                    Dense="true"
                    Mandatory
                    @bind-Value="DialogData.RoleId">
                        @foreach (var role in RoleModels)
                        {
                            <MButton Class="rounded-pill"
                            Style="font-size: 1.25rem;
                                                 width: 104px;
                                                 height: 38px;
                                                 background: #FFFFFF;
                                                 box-shadow: 0px 2px 48px 3px rgba(101,162,231,0.24);
                                                 border-radius: 19px;" Value="@role.Id">
                                @role.Name
                            </MButton>
                        }
                    </MButtonGroup>
                </MCol>

                <MCol Cols="12">
                    <MTextField Clearable Type="password" Class="customer-input" Dense="true" Solo="true" TValue="string" @bind-Value="DialogData.Password">
                        <PrependContent >
                            <MLabel >密码:</MLabel>
                        </PrependContent>
                    </MTextField>
                </MCol>
            </MRow>
            <MCardActions>

                <MRow Class="mt-6" Justify="JustifyTypes.End">
                    <MCol Cols="5">
                        <MButton Height="40" Class="dialog-button cancel" Color="#fff" OnClick="CloseAddUserDialog">取消</MButton>
                    </MCol>
                    <MCol Cols="5">
                        <MButton Height="40" Type="submit" Class="dialog-button confirm">
                            确认
                        </MButton>
                    </MCol>
                </MRow>

            </MCardActions>
        </MForm>
    </ChildContent>
</PModal>



@code {
    private bool isShowAddUser = false;
    private bool IsShowResetDialogData = false;
    public bool _isLoading = false;
    [Inject] IPopupService PopupService { get; set; }
    private SimpleUserModel DialogData { get; set; }
    private DataOptions _options = new(1, 10);
    private List<UserOrganModel> UserModelList { get; set; } = new();
    private List<RoleModel> RoleModels { get; set; } = new();
    private string DialogTitle { get; set; }

    private class SimpleUserModel
    {
        public string Id { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空"),
         StringLength(200, MinimumLength = 6, ErrorMessage = "密码长度必须大于等于6位,小于等于200位"),
         Display(Name = "密码")]
        public string Password { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "账号不能为空"),
         StringLength(50, ErrorMessage = "账号长度必须小于等于50位"),
         Display(Name = "账号")]
        public string Name { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        public StringNumber RoleId { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        [StringLength(50, ErrorMessage = "姓名长度必须小于等于50位"),
         Required(ErrorMessage = "姓名不能为空"),
         Display(Name = "姓名")]
        public string RealName { get; set; }

        public SimpleUserModel()
        {
        }

        public SimpleUserModel(UserOrganModel userModel)
        {
            Id = userModel.UserId;
            Name = userModel.Name;
            RoleId=userModel.Roles.FirstOrDefault();
            RealName = userModel.User?.Nikename;
            Password = userModel.User?.Password;
        }
    }

    private List<DataTableHeader<UserOrganModel>> _headers => new()
    {
        new()
        {
            Text = "姓名",
            Align = DataTableHeaderAlign.Start,
            Sortable = false,
            Value = nameof(UserOrganModel.User.RealName),
            CellRender = user => user.User?.RealName
        },
        new()
        {
            Text = "账号", Value = nameof(UserOrganModel.User.Name),
            CellRender = user => user.User?.Name
        },

        new()
        {
            Text = "操作",
            Value = "actions",
            Sortable = false,
            Width = "100px",
            Align = DataTableHeaderAlign.Center,
        }
    };

    private async Task HandleOnOptionsUpdate(DataOptions options)
    {
        _options = options;
        await InitDataList();
    }

    private int _total;
    [Inject] InternalHttpClientService _internalHttpClientService { get; set; }
    [Inject] StateService _StateService { get; set; }
    public string Keywords { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _options.ItemsPerPage = 15;
        await InitRoleList();
        await InitDataList();
        await base.OnInitializedAsync();
    }

    /// <summary>
    /// 初始化列表数据
    /// </summary>
    private async Task InitDataList()
    {
        _isLoading = true;
        var queryParam = new Dictionary<string, string>
        {
            { "keywords", Keywords },
            { "page", _options.Page.ToString() },
            { "pageSize", _options.ItemsPerPage.ToString() }
        };
        var result = await _StateService
            .GetAsJsonAsync<List<UserOrganModel>>($"/api/v2/HRV_HX/OrgUser/OrganUserList", queryParam);
        UserModelList = result.Data ?? new();
        _total = result?.Page?.TotalCount ?? 0;
        _isLoading = false;
    }

    /// <summary>
    /// 初始化角色列表
    /// </summary>
    private async Task InitRoleList()
    {
        RoleModels = await _internalHttpClientService.GetRoleList();
        //排除管理员
        RoleModels = RoleModels.Where(r => r.Name!="管理员").ToList();
    }

    private async Task DeleteConfirmAsync(string id)
    {
        var confirmed = await PopupService.ConfirmAsync(
            "警告",
            "删除后将无法恢复，确认删除吗?",
            AlertTypes.Warning);
        if (confirmed)
        {
            var deleteResult = await _internalHttpClientService.DeleteUserAsync(id);
            await PopupService.EnqueueSnackbarAsync(deleteResult ? "删除成功！" : "删除失败！", deleteResult ? AlertTypes.Success : AlertTypes.Error);
            await InitDataList();
        }
    }

    /// <summary>
    /// 添加或修改
    /// </summary>
    private async Task SaveAsync()
    {
        var data = new RegisterOrganUserRequestModel();
        if (DialogData.RoleId == null) {
            await PopupService.EnqueueSnackbarAsync("请选择角色", AlertTypes.Error);
            return;
        }
        
        var success = false;
        if (string.IsNullOrEmpty(DialogData.Id) )
        {
            data.Phone = string.Empty;
            data.Email = string.Empty;
            data.Roles = new List<string>() { DialogData.RoleId.ToString() };
            data.Method = RegisterMethod.Name;
            data.Name = DialogData.Name;
            data.Password = DialogData.Password;
            data.RealName = DialogData.RealName;
            var result = await _StateService.PostAsJsonAsync<bool>($"/api/v2/HRV_HX/OrgUser/AddOrganUser?", data);
            success= result.Success;
        }
        else
        {
            data.Name = DialogData.Name;
            data.Password = DialogData.Password;
            data.RealName = DialogData.RealName;
            data.Roles = new List<string>() { DialogData.RoleId.ToString() };
            //开始
            var result = await _StateService.PostAsJsonAsync<bool>($"/api/v2/HRV_HX/OrgUser/EditOrganUser?userId={DialogData.Id}", data);
            success= result.Success;
        }
        var message = success? "用户" + (string.IsNullOrEmpty(DialogData.Id) ? "添加" : "修改") + "成功" : "用户" + (string.IsNullOrEmpty(DialogData.Id) ? "添加" : "修改") + "失败";
        var alertType = success ? AlertTypes.Success : AlertTypes.Error;
        await PopupService.EnqueueSnackbarAsync(message, alertType);
        CloseAddUserDialog();
        await InitDataList();
    }
    [Inject] IConfiguration Configuration { get; set; }
    private async Task ResetPasswordAsync(string id)
    {
        var qs = new Dictionary<string, string> { { "userId", id }, { "password",Configuration.GetValue<string>("HrvVariable:DefaultPassword")??"123456" } };
        var ChangeState = await _StateService.PostAsJsonAsync<UserModel>("/api/v2/Core/Users/<USER>", null, qs);
        if (ChangeState.Success)
        {
            await PopupService.EnqueueSnackbarAsync("密码重置成功", AlertTypes.Success);
        }
        else
        {
            await PopupService.EnqueueSnackbarAsync(ChangeState.Errors[0].Error, AlertTypes.Error);
        }
        
    }
    
    

    private async Task HandleOnValidSubmit()
    {
        await SaveAsync();
    }



    private void AddAccountCallback()
    {
        DialogTitle = "添加用户";
        DialogData = new SimpleUserModel();
        ShowAddUserDialog();
    }

    private void EditAccountCallback(UserOrganModel userModel)
    {
        DialogTitle = "修改用户";
        DialogData = new SimpleUserModel(userModel);
        StateHasChanged();
        ShowAddUserDialog();
    }

    private void ShowAddUserDialog()
    {
        isShowAddUser = true;
    }

    private void CloseAddUserDialog()
    {
        isShowAddUser = false;
    }

}