﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UFU.CoreFX.Data;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Services;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace UFU.IoT.Areas.IoT.Controllers.V1
{
    /// <summary>
    /// 场地设备管理
    /// </summary>
    [Area("IoT")]
    [EnableCors("any")]
    public class PlaceDevicesController : Controller
    {
        private readonly DataRepository _context;
        private readonly CoreDbContext _coreContext;

        /// <summary>
        /// 场地设备管理
        /// </summary>
        /// <param name="context"></param>
        /// <param name="coreContext"></param>
        public PlaceDevicesController(DataRepository context, CoreDbContext coreContext)
        {
            _context = context;
            _coreContext = coreContext;
        }

        #region 接口

        /// <summary>
        /// 场地设备管理接口/列表
        /// </summary>
        /// <param name="id">用户设备编号</param>
        /// <param name="userPlaceId">用户场地编号</param>
        /// <param name="placeIds">场地编号，逗号隔开，精确匹配</param>
        /// <param name="organPath">组织机构路径</param>
        /// <param name="name">用户设备名称或设备名称</param>
        /// <param name="deviceId">设备编号</param>
        /// <param name="deviceSN">设备序列号</param>
        /// <param name="mac">设备硬件编号</param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        [Permission("场地设备管理接口/列表", IsAPI = true, AllowAllUser = true)]
        public async Task<PageList<PlaceDeviceModel>> List(string id, string userPlaceId, string placeIds, string organPath, string name, string deviceId, string deviceSN, string mac, int? page, int? pageSize = null, string orderBy = null)
        {
            var result = new PageList<PlaceDeviceModel>();
            UserInfo userInfo = ViewBag.User;
            //使用LEFT JOIN 查出所有场地及场地用户
            var query = from Place in _context.Query<PlaceModel>()
                        from UserPlace in _context.Query<PlaceUserModel>(PermissionSwitch.Default).Where(m => m.Data.PlaceId == Place.Id).DefaultIfEmpty()
                        select new PlaceUserModel
                        {
                            Id = UserPlace.Id,
                            Name = UserPlace.Data.Name,
                            Place = Place.Data,
                            PlaceId = Place.Id,
                            IsAdmin = UserPlace.Data.IsAdmin,
                            IsDefault = UserPlace.Data.IsDefault,
                            UserId = UserPlace.UserId,
                            AddTime = UserPlace.AddTime,
                            UpdateTime = UserPlace.UpdateTime,
                            Version = UserPlace.Version,
                        };  //new { Place, UserPlace };
            //筛选出有权限的场地或者关联的用户场地
            //List<string> organs = await OrganService.GetMyOrganIdsAsync(userInfo, null, organPath, _coreContext);
            //query = query.Where(m => organs.Contains(m.Place.OrganId) || m.UserId == userInfo.Id);

            //其他查询条件
            if (!string.IsNullOrWhiteSpace(userPlaceId))
            {
                query = query.Where(m => m.Id.Contains(userPlaceId));
            }
            if (!string.IsNullOrWhiteSpace(placeIds))
            {
                var idlist = placeIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Place.Id));
            }

            var myPlaceIds = query.Select(m => m.Place.Id).Distinct().ToList();
            if (myPlaceIds.Count > 0)
            {
                var query1 = _context.Query<PlaceDeviceModel>().Where(m => myPlaceIds.Contains(m.Data.PlaceId));
                if (!string.IsNullOrWhiteSpace(id))
                {
                    query1 = query1.Where(m => m.Id.Contains(id));
                }
                if (!string.IsNullOrWhiteSpace(name))
                {
                    query1 = query1.Where(m => m.Data.Name.Contains(name));
                }
                if (!string.IsNullOrWhiteSpace(deviceId))
                {
                    query1 = query1.Where(m => m.Data.DeviceId.Contains(deviceId));
                }


                result = await query1.Select(m => m.Data).OrderBy(model => model.Id).ToPageListAsync(page, pageSize, orderBy);

                //最新数据
                var dataids = result.Select(m => m.DeviceId);
                var latestData = await _context.Query<LatestDataModel>().Where(m => dataids.Contains(m.Data.DeviceId)).ToListAsync();
                foreach (var item in latestData)
                {
                    result.First(m => m.DeviceId == item.Data.DeviceId).Device.LatestData = item.Data;
                }
                result.AddPageInfo(Response);
            }
            return result;
        }


        /// <summary>
        /// 场地设备管理接口/详情
        /// </summary>
        /// <param name="id">场地设备编号</param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]/{id?}")]
        [Permission("场地设备管理接口/详情", IsAPI = true)]
        public async Task<PlaceDeviceModel> Get(string id)
        {
            var model = await _context.Query<PlaceDeviceModel>().Select(m=>m.Data).FirstOrDefaultAsync(m => m.Id == id);
            //最新数据
            return model;
        }

        /// <summary>
        /// 场地设备管理接口/添加
        /// </summary>
        /// <param name="model">设备信息</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("场地设备管理接口/添加", IsAPI = true)]
        public async Task<IActionResult> Add([FromBody] PlaceDeviceModel model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            UserInfo userInfo = ViewBag.User;
            model.Id = UId.GetNewId();
            model.AddTime = DateTime.Now;
            model.UpdateTime = DateTime.Now;
            model.Version = Guid.NewGuid().ToString();

            var temp = new DataModel<PlaceDeviceModel>()
            {
                Id = model.Id,
                Data = model,
                OrganId = userInfo.Organ.GetTopOrganId(),
                UserId = userInfo.Id,

                AddTime = model.AddTime,
                UpdateTime = model.UpdateTime,
                Version = model.Version
            };

            _context.Add(temp);
            await _context.SaveChangesAsync();
            return Ok();
        }

        /// <summary>
        /// 场地设备管理接口/编辑
        /// </summary>
        /// <param name="id">场地设备编号</param>
        /// <param name="placeDeviceModel">设备信息</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]/{id?}")]
        [Permission("场地设备管理接口/编辑", IsAPI = true)]
        public async Task<IActionResult> Edit(string id, [FromBody] PlaceDeviceModel placeDeviceModel)
        {
            if (id != placeDeviceModel.Id)
            {
                return NotFound();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var model = await _context.Query<PlaceDeviceModel>().FirstOrDefaultAsync(m => m.Id == id);
            if (model == null)
            {
                return NotFound();
            }
            else if (model.Version != placeDeviceModel.Version)
            {
                ModelState.AddModelError("", "该信息已经被其他用户修改，请刷新后重试。");
                var result = BadRequest(ModelState);
                result.StatusCode = 409;
                return result;
            }

            model.Data.Name = placeDeviceModel.Name;
            model.Data.DeviceId = placeDeviceModel.DeviceId;
            model.Data.PlaceId = placeDeviceModel.PlaceId;

            model.Data.UpdateTime = DateTime.Now;
            model.Data.Version = Guid.NewGuid().ToString();
            model.UpdateTime = model.Data.UpdateTime;
            model.Version = model.Data.Version;
            _context.Update(model);
            await _context.SaveChangesAsync();

            return Ok(model);
        }

        /// <summary>
        /// 场地设备管理接口/删除
        /// </summary>
        /// <param name="id">场地编号</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]/{id?}")]
        [Permission("场地设备管理接口/删除", IsAPI = true)]
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var model = await _context.Query<PlaceDeviceModel>().FirstOrDefaultAsync(m => m.Id == id);
            if (model == null)
            {
                return NotFound();
            }

            _context.Remove(model);
            await _context.SaveChangesAsync();
            return Ok(model);
        }

        /// <summary>
        /// 场地设备管理接口/批量删除
        /// </summary>
        /// <param name="ids">场地编号</param>
        /// <returns></returns>
        [HttpPost("api/[area]/[controller]/[action]")]
        [Permission("场地设备管理接口/批量删除", IsAPI = true)]
        public async Task<IActionResult> BatchDelete([FromBody] string[] ids)
        {
            if (ids == null || ids.Count() == 0)
            {
                return NotFound();
            }
            var list = await _context.Query<PlaceDeviceModel>().Where(m => ids.Contains(m.Id)).ToListAsync();
            _context.RemoveRange(list);
            await _context.SaveChangesAsync();
            return Ok(list.Count);
        }


        /// <summary>
        /// 我的设备列表
        /// </summary>
        /// <param name="id">用户设备编号</param>
        /// <param name="userPlaceId">用户场地编号</param>
        /// <param name="placeIds">场地编号，逗号隔开，精确匹配</param>
        /// <param name="organPath">组织机构路径</param>
        /// <param name="name">用户设备名称或设备名称</param>
        /// <param name="deviceId">设备编号</param>
        /// <param name="deviceSN">设备序列号</param>
        /// <param name="mac">设备硬件编号</param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]")]
        public async Task<PageList<PlaceDeviceModel>> MyList(string id, string userPlaceId, string placeIds, string organPath, string name, string deviceId, string deviceSN, string mac, int? page, int? pageSize = null, string orderBy = null)
        {
            var result = new PageList<PlaceDeviceModel>();
            UserInfo userInfo = ViewBag.User;
            //使用LEFT JOIN 查出所有场地及场地用户
            var query = from Place in _context.Query<PlaceModel>()
                        from UserPlace in _context.Query<PlaceUserModel>(PermissionSwitch.Default).Where(m => m.Data.PlaceId == Place.Id).DefaultIfEmpty()
                        select new PlaceUserModel
                        {
                            Id = UserPlace.Id,
                            Name = UserPlace.Data.Name,
                            Place = Place.Data,
                            PlaceId = Place.Id,
                            IsAdmin = UserPlace.Data.IsAdmin,
                            IsDefault = UserPlace.Data.IsDefault,
                            UserId = UserPlace.UserId,
                            AddTime = UserPlace.AddTime,
                            UpdateTime = UserPlace.UpdateTime,
                            Version = UserPlace.Version,
                        };  //new { Place, UserPlace };
            //筛选出有权限的场地或者关联的用户场地
            //List<string> organs = await OrganService.GetMyOrganIdsAsync(userInfo, null, organPath, _coreContext);
            //query = query.Where(m => organs.Contains(m.Place.OrganId) || m.UserId == userInfo.Id);

            //其他查询条件
            if (!string.IsNullOrWhiteSpace(userPlaceId))
            {
                query = query.Where(m => m.Id.Contains(userPlaceId));
            }
            if (!string.IsNullOrWhiteSpace(placeIds))
            {
                var idlist = placeIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(m => idlist.Contains(m.Place.Id));
            }

            var myPlaceIds = query.Select(m => m.Place.Id).Distinct().ToList();
            if (myPlaceIds.Count > 0)
            {
                var query1 = _context.Query<PlaceDeviceModel>().Where(m => myPlaceIds.Contains(m.Data.PlaceId));
                if (!string.IsNullOrWhiteSpace(id))
                {
                    query1 = query1.Where(m => m.Id.Contains(id));
                }
                if (!string.IsNullOrWhiteSpace(name))
                {
                    query1 = query1.Where(m => m.Data.Name.Contains(name));
                }
                if (!string.IsNullOrWhiteSpace(deviceId))
                {
                    query1 = query1.Where(m => m.Data.DeviceId.Contains(deviceId));
                }
                
                result = await query1.Select(m=>m.Data).OrderBy(model => model.Id).ToPageListAsync(page, pageSize, orderBy);
                result.AddPageInfo(Response);
            }
            return result;
        }

        /// <summary>
        /// 我的设备详情
        /// </summary>
        /// <param name="id">场地设备编号</param>
        /// <returns></returns>
        [HttpGet("api/[area]/[controller]/[action]/{id?}")]
        public async Task<PlaceDeviceModel> MyGet(string id)
        {
            var model = await _context.Query<PlaceDeviceModel>().Where(m => m.Id == id).Select(m=>m.Data).FirstOrDefaultAsync();
            if (model == null)
            {
                return null;
            }
            return model;
        }
        #endregion
    }
}
